<template>
  <a-modal
    v-model:open="visible"
    title="Chèn subtitle tại vị trí timeline"
    :ok-text="'Chèn'"
    :cancel-text="'Hủy'"
    @ok="handleOk"
    @cancel="handleCancel"
    :width="600"
  >
    <a-form :model="form" layout="vertical">
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="Thời gian bắt đầu">
            <a-input v-model:value="form.startTime" placeholder="00:00:00,000" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="Thời gian kết thúc">
            <a-input v-model:value="form.endTime" placeholder="00:00:00,000" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-form-item label="Văn bản gốc">
        <a-textarea
          v-model:value="form.text"
          :rows="3"
          placeholder="Nhập văn bản gốc..."
        />
      </a-form-item>

      <a-form-item label="Văn bản dịch (tùy chọn)">
        <a-textarea
          v-model:value="form.translatedText"
          :rows="3"
          placeholder="Nhập văn bản dịch..."
        />
      </a-form-item>

      <a-form-item label="Thời lượng mặc định">
        <a-slider
          v-model:value="form.defaultDuration"
          :min="0.5"
          :max="10"
          :step="0.1"
          :marks="{ 0.5: '0.5s', 2: '2s', 5: '5s', 10: '10s' }"
          @change="updateEndTime"
        />
        <div class="text-xs text-gray-500 mt-1">
          {{ form.defaultDuration }}s
        </div>
      </a-form-item>

      <a-form-item>
        <a-checkbox v-model:checked="form.adjustTiming">
          Tự động điều chỉnh thời gian các subtitle sau
        </a-checkbox>
      </a-form-item>

      <a-form-item>
        <a-checkbox v-model:checked="form.autoPosition">
          Tự động tìm vị trí trống phù hợp
        </a-checkbox>
      </a-form-item>
    </a-form>

    <a-alert
      v-if="validationError"
      :message="validationError"
      type="error"
      show-icon
      class="mt-4"
    />

    <div class="mt-4 p-3 bg-gray-100 rounded">
      <div class="text-sm text-gray-600 mb-2">
        <strong>Vị trí chèn:</strong> {{ formatTime(clickTime) }}
      </div>
      <div class="text-xs text-gray-500">
        Subtitle sẽ được chèn tại thời điểm này trên timeline
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { parseTimeToSeconds } from '@/lib/utils'
import { secondsToSRTTime } from '@/lib/subtitleUtils'
import { useTimelineStore } from '@/stores/timeline-store'
import { useTTSStore } from '@/stores/ttsStore'

const props = defineProps({
  open: {
    type: Boolean,
    default: false
  },
  clickTime: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits(['update:open', 'confirm'])

const timelineStore = useTimelineStore()
const ttsStore = useTTSStore()

const visible = ref(false)
const validationError = ref('')

const form = ref({
  startTime: '',
  endTime: '',
  text: '',
  translatedText: '',
  defaultDuration: 2.0,
  adjustTiming: false,
  autoPosition: true
})

// Computed
const existingSubtitles = computed(() => {
  return timelineStore.subtitleItems.map(item => ({
    startTime: item.startTime,
    endTime: item.endTime
  })).sort((a, b) => a.startTime - b.startTime)
})

// Watch for prop changes
watch(() => props.open, (newVal) => {
  visible.value = newVal
  if (newVal) {
    resetForm()
    calculateDefaultTiming()
  }
})

watch(visible, (newVal) => {
  emit('update:open', newVal)
})

// Methods
const resetForm = () => {
  form.value = {
    startTime: '',
    endTime: '',
    text: '[New subtitle]',
    translatedText: '',
    defaultDuration: 2.0,
    adjustTiming: false,
    autoPosition: true
  }
  validationError.value = ''
}

const calculateDefaultTiming = () => {
  const clickTime = props.clickTime
  let startTime = clickTime
  let endTime = clickTime + form.value.defaultDuration

  if (form.value.autoPosition) {
    // Find a suitable position that doesn't overlap with existing subtitles
    const position = findBestPosition(clickTime, form.value.defaultDuration)
    startTime = position.startTime
    endTime = position.endTime
  }

  form.value.startTime = secondsToSRTTime(startTime)
  form.value.endTime = secondsToSRTTime(endTime)
}

const findBestPosition = (preferredTime, duration) => {
  const subtitles = existingSubtitles.value
  const buffer = 0.1 // 100ms buffer between subtitles

  // If no subtitles exist, use preferred time
  if (subtitles.length === 0) {
    return {
      startTime: Math.max(0, preferredTime),
      endTime: Math.max(duration, preferredTime + duration)
    }
  }

  // Try to place at preferred time first
  let startTime = Math.max(0, preferredTime)
  let endTime = startTime + duration

  // Check if this position overlaps with any existing subtitle
  const hasOverlap = (start, end) => {
    return subtitles.some(sub => 
      !(end + buffer <= sub.startTime || start - buffer >= sub.endTime)
    )
  }

  if (!hasOverlap(startTime, endTime)) {
    return { startTime, endTime }
  }

  // Find the closest available gap
  for (let i = 0; i < subtitles.length; i++) {
    const current = subtitles[i]
    const next = subtitles[i + 1]

    // Try before the first subtitle
    if (i === 0 && current.startTime - buffer >= duration) {
      const gapEnd = current.startTime - buffer
      const gapStart = Math.max(0, gapEnd - duration)
      if (Math.abs(gapStart - preferredTime) < Math.abs(startTime - preferredTime)) {
        startTime = gapStart
        endTime = gapEnd
      }
    }

    // Try between current and next subtitle
    if (next) {
      const gapStart = current.endTime + buffer
      const gapEnd = next.startTime - buffer
      const availableSpace = gapEnd - gapStart

      if (availableSpace >= duration) {
        const candidateStart = Math.min(gapEnd - duration, Math.max(gapStart, preferredTime))
        const candidateEnd = candidateStart + duration

        if (candidateEnd <= gapEnd && Math.abs(candidateStart - preferredTime) < Math.abs(startTime - preferredTime)) {
          startTime = candidateStart
          endTime = candidateEnd
        }
      }
    }

    // Try after the last subtitle
    if (i === subtitles.length - 1) {
      const gapStart = current.endTime + buffer
      const candidateStart = Math.max(gapStart, preferredTime)
      const candidateEnd = candidateStart + duration

      if (Math.abs(candidateStart - preferredTime) < Math.abs(startTime - preferredTime)) {
        startTime = candidateStart
        endTime = candidateEnd
      }
    }
  }

  return { startTime, endTime }
}

const updateEndTime = () => {
  if (form.value.startTime) {
    try {
      const startSeconds = parseTimeToSeconds(form.value.startTime)
      const endSeconds = startSeconds + form.value.defaultDuration
      form.value.endTime = secondsToSRTTime(endSeconds)
    } catch (error) {
      // Invalid start time, recalculate from click time
      calculateDefaultTiming()
    }
  }
}

const validateForm = () => {
  validationError.value = ''

  if (!form.value.text.trim()) {
    validationError.value = 'Văn bản gốc không được để trống'
    return false
  }

  try {
    const startTime = parseTimeToSeconds(form.value.startTime)
    const endTime = parseTimeToSeconds(form.value.endTime)

    if (startTime >= endTime) {
      validationError.value = 'Thời gian bắt đầu phải nhỏ hơn thời gian kết thúc'
      return false
    }

    if (endTime - startTime < 0.1) {
      validationError.value = 'Thời lượng subtitle phải ít nhất 0.1 giây'
      return false
    }

    if (startTime < 0) {
      validationError.value = 'Thời gian bắt đầu không được âm'
      return false
    }

    // Check for overlaps if auto-position is disabled
    if (!form.value.autoPosition) {
      const hasOverlap = existingSubtitles.value.some(sub => 
        !(endTime <= sub.startTime || startTime >= sub.endTime)
      )

      if (hasOverlap) {
        validationError.value = 'Thời gian bị trùng với subtitle khác. Bật "Tự động tìm vị trí trống" để tránh trùng lặp.'
        return false
      }
    }

  } catch (error) {
    validationError.value = 'Định dạng thời gian không hợp lệ (HH:MM:SS,mmm)'
    return false
  }

  return true
}

const formatTime = (seconds) => {
  if (!seconds || isNaN(seconds)) return '00:00.000'
  
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  const ms = Math.floor((seconds % 1) * 1000)
  
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(3, '0')}`
}

const handleOk = () => {
  if (!validateForm()) return

  const newSubtitle = {
    startTime: form.value.startTime,
    endTime: form.value.endTime,
    text: form.value.text.trim(),
    translatedText: form.value.translatedText.trim(),
    adjustTiming: form.value.adjustTiming,
    clickTime: props.clickTime,
    isEnabled: true
  }

  emit('confirm', newSubtitle)
  visible.value = false
}

const handleCancel = () => {
  visible.value = false
}
</script>

<style scoped>
.ant-slider {
  margin: 8px 0;
}
</style>
