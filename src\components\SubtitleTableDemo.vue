<template>
  <div class="p-4">
    <h2 class="text-xl font-bold mb-4">Subtitle Table với các tính năng mới</h2>
    
    <SubtitleTable
      :subtitles="subtitles"
      :on-retry="handleRetry"
      :on-update-translation="handleUpdateTranslation"
      :on-delete="handleDelete"
      :on-insert="handleInsert"
      :on-split="handleSplit"
      :on-merge="handleMerge"
      :on-reorder="handleReorder"
      :translating="false"
      :batch-size="10"
    />
  </div>
</template>

<script>
import { defineComponent, ref } from 'vue';
import SubtitleTable from './SubtitleTable.vue';
import { message } from 'ant-design-vue';
import { 
  reorderSubtitleIds, 
  adjustSubtitleTimes, 
  mergeSubtitles,
  fixOverlappingSubtitles 
} from '@/lib/subtitleUtils';

export default defineComponent({
  name: 'SubtitleTableDemo',
  components: {
    SubtitleTable
  },
  setup() {
    // Sample subtitle data
    const subtitles = ref([
      {
        id: 1,
        index: 1,
        start: '00:00:01,000',
        end: '00:00:03,000',
        startTime: 1.0,
        endTime: 3.0,
        text: 'Hello world',
        translatedText: 'Xin chào thế giới',
        status: 'translated',
        isEnabled: true,
        isGenerated: false,
        isGenerated1: false,
        isGenerated2: false,
        isGenerated3: false,
        audioUrl: '',
        audioUrl1: '',
        audioUrl2: '',
        audioUrl3: '',
        duration: 0,
        selectedSpeaker: '',
        speechRate: 0,
        isPlayable: false,
        isVoice: 1
      },
      {
        id: 2,
        index: 2,
        start: '00:00:04,000',
        end: '00:00:06,000',
        startTime: 4.0,
        endTime: 6.0,
        text: 'This is a test subtitle',
        translatedText: 'Đây là subtitle thử nghiệm',
        status: 'translated',
        isEnabled: true,
        isGenerated: false,
        isGenerated1: false,
        isGenerated2: false,
        isGenerated3: false,
        audioUrl: '',
        audioUrl1: '',
        audioUrl2: '',
        audioUrl3: '',
        duration: 0,
        selectedSpeaker: '',
        speechRate: 0,
        isPlayable: false,
        isVoice: 1
      },
      {
        id: 3,
        index: 3,
        start: '00:00:07,000',
        end: '00:00:09,000',
        startTime: 7.0,
        endTime: 9.0,
        text: 'Another subtitle for testing',
        translatedText: 'Một subtitle khác để thử nghiệm',
        status: 'translated',
        isEnabled: true,
        isGenerated: false,
        isGenerated1: false,
        isGenerated2: false,
        isGenerated3: false,
        audioUrl: '',
        audioUrl1: '',
        audioUrl2: '',
        audioUrl3: '',
        duration: 0,
        selectedSpeaker: '',
        speechRate: 0,
        isPlayable: false,
        isVoice: 1
      }
    ]);

    const handleRetry = (id) => {
      console.log('Retry subtitle:', id);
      message.info(`Retrying subtitle ${id}`);
    };

    const handleUpdateTranslation = (id, newTranslation) => {
      console.log('Update translation:', id, newTranslation);
      const subtitle = subtitles.value.find(s => s.id === id);
      if (subtitle) {
        subtitle.translatedText = newTranslation;
        message.success(`Updated subtitle ${id}`);
      }
    };

    const handleDelete = (id) => {
      console.log('Delete subtitle:', id);
      const index = subtitles.value.findIndex(s => s.id === id);
      if (index !== -1) {
        subtitles.value.splice(index, 1);
        // Reorder IDs
        subtitles.value = reorderSubtitleIds(subtitles.value);
        message.success(`Deleted subtitle ${id}`);
      }
    };

    const handleInsert = (newSubtitle, targetId, position, adjustTiming = true) => {
      console.log('Insert subtitle:', newSubtitle, targetId, position, adjustTiming);
      
      const targetIndex = subtitles.value.findIndex(s => s.id === targetId);
      if (targetIndex === -1) return;

      const insertIndex = position === 'before' ? targetIndex : targetIndex + 1;
      
      // Insert new subtitle
      subtitles.value.splice(insertIndex, 0, newSubtitle);
      
      // Reorder IDs
      subtitles.value = reorderSubtitleIds(subtitles.value);
      
      // Adjust timing if requested
      if (adjustTiming) {
        const insertDuration = newSubtitle.endTime - newSubtitle.startTime;
        subtitles.value = adjustSubtitleTimes(subtitles.value, insertIndex + 1, insertDuration);
      }
      
      // Fix any overlapping subtitles
      subtitles.value = fixOverlappingSubtitles(subtitles.value);
      
      message.success(`Inserted new subtitle ${position} subtitle ${targetId}`);
    };

    const handleSplit = (id, splitSubtitles) => {
      console.log('Split subtitle:', id, splitSubtitles);
      
      const index = subtitles.value.findIndex(s => s.id === id);
      if (index === -1) return;

      // Replace original subtitle with split parts
      subtitles.value.splice(index, 1, ...splitSubtitles);
      
      // Reorder IDs
      subtitles.value = reorderSubtitleIds(subtitles.value);
      
      message.success(`Split subtitle ${id} into 2 parts`);
    };

    const handleMerge = (ids, mergedSubtitle) => {
      console.log('Merge subtitles:', ids, mergedSubtitle);
      
      // Find indices of subtitles to merge
      const indices = ids.map(id => subtitles.value.findIndex(s => s.id === id)).sort((a, b) => b - a);
      
      if (indices.some(i => i === -1)) return;

      // Remove the subtitles (in reverse order to maintain indices)
      indices.forEach(index => {
        subtitles.value.splice(index, 1);
      });
      
      // Insert merged subtitle at the first position
      subtitles.value.splice(Math.min(...indices), 0, mergedSubtitle);
      
      // Reorder IDs
      subtitles.value = reorderSubtitleIds(subtitles.value);
      
      message.success(`Merged subtitles ${ids.join(', ')}`);
    };

    const handleReorder = (newOrder) => {
      console.log('Reorder subtitles:', newOrder);
      subtitles.value = reorderSubtitleIds(newOrder);
      message.success('Reordered subtitles');
    };

    return {
      subtitles,
      handleRetry,
      handleUpdateTranslation,
      handleDelete,
      handleInsert,
      handleSplit,
      handleMerge,
      handleReorder
    };
  }
});
</script>
