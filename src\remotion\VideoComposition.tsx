import React from 'react';
import { AbsoluteFill, Video, Audio, Sequence, useVideoConfig, interpolate, useCurrentFrame } from 'remotion';

export const VideoComposition = ({ srtArray }) => {
  const { fps } = useVideoConfig();
  const frame = useCurrentFrame();

  let cumulativeTime = 0;
  const segmentsWithTiming = srtArray.map((srt) => {
    const startTime = cumulativeTime;
    const duration = srt.duration;
    cumulativeTime = startTime + duration;

    return {
      ...srt,
      absoluteStartTime: startTime,
      absoluteEndTime: cumulativeTime,
      finalDuration: duration,
    };
  });

  return (
    <AbsoluteFill>
      {segmentsWithTiming.map((srt, index) => {
        const durationInFrames = Math.round(srt.finalDuration * fps);
        const videoStartFrame = Math.round(srt.startTime * fps);
        const sequenceStartFrame = Math.round(srt.absoluteStartTime * fps);

        console.log(`Segment ${index}: startFrame=${sequenceStartFrame}, durationInFrames=${durationInFrames}, videoStartFrame=${videoStartFrame}`);

        return (
          <Sequence
            key={index}
            from={sequenceStartFrame}
            durationInFrames={durationInFrames}
          >
            <AbsoluteFill>
              <Video
                src={srt.videoPath.replace('file://', '')}
                startFrom={videoStartFrame}
                volume={0.3}
                style={{
                  width: '100%',
                  height: '100%',
                }}
              />
              <Audio
                src={srt.audioUrl.replace('file://', '')}
                volume={1}
              />
              <div
                style={{
                  position: 'absolute',
                  bottom: 50,
                  left: 20,
                  right: 20,
                  textAlign: 'center',
                  color: 'white',
                  fontSize: 28,
                  fontWeight: 'bold',
                  textShadow: '3px 3px 6px rgba(0,0,0,0.9)',
                  padding: '15px 20px',
                  backgroundColor: 'rgba(0,0,0,0.7)',
                  borderRadius: '8px',
                  lineHeight: 1.4,
                  opacity: interpolate(
                    frame - sequenceStartFrame,
                    [0, Math.min(15, durationInFrames * 0.1), durationInFrames * 0.9, durationInFrames],
                    [0, 1, 1, 0],
                    { extrapolateRight: 'clamp', extrapolateLeft: 'clamp' }
                  ),
                }}
              >
                {srt.text}
              </div>
            </AbsoluteFill>
          </Sequence>
        );
      })}
    </AbsoluteFill>
  );
};