import { fileSystem } from "./fileSystem";

const common = class commonHandler {
    static generateId(t=1e6) {
        return Math.floor(Math.random() * t)
    }
    static makeid(t) {
        let r = "";
        const n = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
          , o = n.length;
        for (let a = 0; a < t; a++)
            r += n.charAt(Math.floor(Math.random() * o));
        return r
    }
    static uuidv4() {
        return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function(t) {
            var r = Math.random() * 16 | 0
              , n = t == "x" ? r : r & 3 | 8;
            return n.toString(16)
        })
    }
    static generateUniqueId() {
        const t = new Date().getTime()
          , r = common.makeid(7);
        return `${t}_${r}`
    }
    static sleep(t) {
        return new Promise(r => setTimeout(r, t))
    }
    static async generatePath(t, r="file", n) {
        let o = t
          , a = n || 1
          , i = 0;
        for (; ; ) {
            if (i > 500)
                throw new Error("Lỗi tạo đường dẫn");
            if (i++,
            !await fileSystem.existsSync(o))
                break;
            r === "folder" ? (t.endsWith("/") && (t = t.slice(0, -1)),
            o = `${t}(${a})`) : o = `${t.replace(/\.\w+$/, "")}(${a}).${t.split(".").pop()}`,
            a++
        }
        return o
    }
    static getMessageError(t) {
        try {
            let r = "";
            return typeof t.message == "string" ? r = t.message : typeof t == "string" && (r = t),
            this.listKeyErrorNetwork.some(n => r.includes(n)) ? "Vui lòng kiểm tra kết nối mạng" : r.includes("RequestTimeout") || r.includes("ESOCKETTIMEDOUT") || r.includes("ETIMEDOUT") ? "Đường truyền yếu vui lòng kiểm tra lại kết nối mạng" : r.includes("socket hang up") ? "Xảy ra vấn đề với kết nối mạng. Vui lòng thử lại sau" : r.toLowerCase().includes("no such file or directory") ? "Không tìm thấy tệp hoặc thư mục" : r.includes("ffmpeg was killed with signal SIGKILL") ? "Tác vụ đã bị hủy" : r.includes(`ffmpeg exited with code 1: Error initializing complex filters.
Invalid argument
`) ? "Tham số không hợp lệ khi khởi tạo bộ lọc ffmpeg" : r.includes(`exiting
FFmpeg cannot edit existing files in-place.
`) ? "Không thể chỉnh sửa các tệp hiện có cùng đầu vào và đầu ra" : r.includes("We restrict certain activity to protect our community. Let us know if you think we made a mistake") || r.includes("We limit how often you can do certain things on Instagram, like following people, to protect our community. Let us know if you think we made a mistake") ? "Tài khoản của bạn đã bị hạn chế một số hoạt động nhất định." : r.includes("checkpoint_required") ? "Tài khoản của bạn đã bị checkpoint." : r || "Lỗi ngoại lệ"
        } catch (r) {
            return r instanceof Error && r.message || "Lỗi ngoại lệ"
        }
    }
    static convertToSignedString(t) {
        const r = Math.round((t - 1) * 100);
        return r >= 0 ? `+${r}` : `${r}`
    }
    static cutStringStartEnd(t, r, n) {
        let o = t.split(r);
        return !o[1] || !o[1].includes(n) ? "" : o[1].split(n)[0]
    }
    static handleGetValue(t) {
        try {
            return t()
        } catch {
            return null
        }
    }
    static parseAIStudioCodeHistory(t) {
        t.startsWith("[") || (t = common.cutStringStartEnd(t, "history: ", `,
  });`));
        const r = CY(t);
        return JSON.parse(r)
    }
    static splitBySpaceAndChunk(t, r) {
        const n = t.trim().split(/\s+/)
          , o = [];
        for (let a = 0; a < n.length; a += r)
            o.push(n.slice(a, a + r).join(" "));
        return o
    }
}
export default common;