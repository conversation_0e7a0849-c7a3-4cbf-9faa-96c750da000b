// Tên command cho ffmpeg/ffprobe (thườ<PERSON> là alias)
const FFMPEG_CMD = "_ffmpeg";
const FFPROBE_CMD = "_ffprobe";

// Hàm chạy command ffmpeg qua electronAPI
const runFfmpegCmd = async (cmd) => {
  return await window.electronAPI.invoke("run-cmd-ffmpeg", cmd);
};

const getEncoder = async ()=>{
  return await window.electronAPI.invoke('device:getEncoder')
}

// 1. Trích xuất audio từ video và xử lý filter, lưu ra file .mp3
const extractAndProcessAudio = async (videoPath, outMp3Path) => {
  const encoder = await getEncoder()
  const ffmpegArgs = [
    FFMPEG_CMD,
    `-i "${videoPath}"`,
    `-af "highpass=f=100, lowpass=f=4000, afftdn=nf=-20, loudnorm, acompressor=threshold=-12dB:ratio=2:attack=5:release=50, equalizer=f=1000:width_type=o:width=2:g=2"`,
    `-c:v ${encoder} -ar 16000 -ac 1 -c:a libmp3lame -b:a 128k "${outMp3Path}"`
  ].join(" ");
  return await runFfmpegCmd(ffmpegArgs);
};

// 2. Lấy duration (thời lượng) của file audio/video
const getMediaDuration = async (mediaPath) => {
  const cmd = `${FFPROBE_CMD} -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 "${mediaPath}"`;
  const output = await runFfmpegCmd(cmd);
  const duration = parseFloat(output);
  if (Number.isNaN(duration)) throw new Error("Lỗi lấy thông tin video.");
  return duration;
};

// 3. Tạo video intro bằng ffmpeg với các hiệu ứng và filter
const createIntroVideo = async (inputVideo, outVideo, introDuration, options) => {
  const { speedVideoIntro, flipIntroVideo, scaleFactor, backgroundMusic, backgroundMusicVolume } = options;
  const encoder = await getEncoder()
  const videoDuration = await getMediaDuration(inputVideo);
  if (introDuration > videoDuration) throw new Error("Thời lượng video gốc ngắn hơn nội dung bản tóm tắt! Vui lòng chọn nội dung bản tóm tắt ngắn hơn");

  const offset = (videoDuration - introDuration) / 2;
  const flipFilter = flipIntroVideo ? "hflip" : "";
  const speedFilter = `setpts=${1 / speedVideoIntro}*PTS`;
  const scaleFilter = `scale=ceil((iw*${scaleFactor})/2)*2:ceil((ih*${scaleFactor})/2)*2`;
  const filters = [speedFilter, flipFilter, scaleFilter].filter(Boolean).join(", ");

  // If no background music, create video without audio as before
  if (!backgroundMusic) {
    const cmd = `${FFMPEG_CMD} -ss ${offset} -i "${inputVideo}" -t ${introDuration} -vf "${filters}" -c:v ${encoder} -preset fast -an "${outVideo}"`;
    return await runFfmpegCmd(cmd);
  }

  // With background music, create video with processed background music
  const musicVolume = (backgroundMusicVolume || 30) / 100; // Convert percentage to decimal
  const cmd = `${FFMPEG_CMD} -ss ${offset} -i "${inputVideo}" -i "${backgroundMusic}" -t ${introDuration} -filter_complex "[0:v]${filters}[v];[1:a]volume=${musicVolume},aloop=loop=-1:size=2e+09,atrim=duration=${introDuration},afade=t=out:st=${Math.max(0, introDuration - 3)}:d=3[a]" -map "[v]" -map "[a]" -c:v ${encoder} -preset fast "${outVideo}"`;
  return await runFfmpegCmd(cmd);
};

// 4. Lấy width, height của video (frame size)
const getVideoResolution = async (videoPath) => {
  const cmd = `${FFPROBE_CMD} -v error -select_streams v:0 -show_entries stream=width,height -of csv=p=0 "${videoPath}"`;
  const output = await runFfmpegCmd(cmd);
  const [width, height] = output.split(",").map(Number);
  if (isNaN(width) || isNaN(height)) throw new Error("Lỗi lấy kích thước video.");
  return { width, height };
};

// 5. Ghép intro video + audio + gốc thành video hoàn chỉnh
const concatIntroAndMainVideo = async (introVideo, introAudio, mainVideo, outVideo, speedVideoOriginal, hasBackgroundMusic = false) => {
  // Lấy size video gốc để giữ nguyên tỷ lệ khi ghép
  const { width, height } = await getVideoResolution(mainVideo);
  const encoder = await getEncoder()
  const scaleMain = `scale=${width}:${height},setsar=1`;
  const scaleIntro = `scale=${width}:${height},setsar=1`;

  let cmd;
  if (hasBackgroundMusic) {
    // If intro video already has background music, mix it with voice audio
    cmd = `${FFMPEG_CMD} -i "${introVideo}" -i "${introAudio}" -i "${mainVideo}" \
-filter_complex "[0:v]${scaleIntro}[v0];[0:a][1:a]amix=inputs=2:duration=shortest:weights=0.7 0.3[a0];[2:v]${scaleMain},setpts=PTS/${speedVideoOriginal}[v2];[2:a]atempo=${speedVideoOriginal}[a2]; \
[v0][a0][v2][a2]concat=n=2:v=1:a=1[outv][outa]" \
-map "[outv]" -map "[outa]" -c:v ${encoder} -vsync 2 "${outVideo}"`;
  } else {
    // Original logic without background music
    cmd = `${FFMPEG_CMD} -i "${introVideo}" -i "${introAudio}" -i "${mainVideo}" \
-filter_complex "[0:v]${scaleIntro}[v0];[2:v]${scaleMain},setpts=PTS/${speedVideoOriginal}[v2];[2:a]atempo=${speedVideoOriginal}[a2]; \
[v0][1:a][v2][a2]concat=n=2:v=1:a=1[outv][outa]" \
-map "[outv]" -map "[outa]" -c:v ${encoder} -vsync 2 "${outVideo}"`;
  }

  return await runFfmpegCmd(cmd);
};

// 6. Xử lý background music cho intro video
const processBackgroundMusic = async (musicPath, targetDuration, volume = 0.3, outputPath) => {
  const musicDuration = await getMediaDuration(musicPath);
  const volumeFilter = `volume=${volume}`;

  let audioFilter;
  if (musicDuration < targetDuration) {
    // Music is shorter than target - loop it
    audioFilter = `${volumeFilter},aloop=loop=-1:size=2e+09,atrim=duration=${targetDuration}`;
  } else {
    // Music is longer than target - trim and fade out
    const fadeStartTime = Math.max(0, targetDuration - 3); // Start fade 3 seconds before end
    audioFilter = `${volumeFilter},atrim=duration=${targetDuration},afade=t=out:st=${fadeStartTime}:d=3`;
  }

  const cmd = `${FFMPEG_CMD} -i "${musicPath}" -af "${audioFilter}" -c:a libmp3lame -b:a 128k "${outputPath}"`;
  return await runFfmpegCmd(cmd);
};

// 7. Tạo intro video với background music đã xử lý
const createIntroVideoWithMusic = async (inputVideo, introAudio, backgroundMusic, outVideo, introDuration, options) => {
  const { speedVideoIntro, flipIntroVideo, scaleFactor, backgroundMusicVolume } = options;
  const encoder = await getEncoder();
  const videoDuration = await getMediaDuration(inputVideo);

  if (introDuration > videoDuration) {
    throw new Error("Thời lượng video gốc ngắn hơn nội dung bản tóm tắt! Vui lòng chọn nội dung bản tóm tắt ngắn hơn");
  }

  const offset = (videoDuration - introDuration) / 2;
  const flipFilter = flipIntroVideo ? "hflip" : "";
  const speedFilter = `setpts=${1 / speedVideoIntro}*PTS`;
  const scaleFilter = `scale=ceil((iw*${scaleFactor})/2)*2:ceil((ih*${scaleFactor})/2)*2`;
  const videoFilters = [speedFilter, flipFilter, scaleFilter].filter(Boolean).join(", ");

  const musicVolume = (backgroundMusicVolume || 30) / 100;
  const voiceVolume = 1.0; // Keep voice at full volume

  // Mix background music with voice audio
  const cmd = `${FFMPEG_CMD} -ss ${offset} -i "${inputVideo}" -i "${introAudio}" -i "${backgroundMusic}" -t ${introDuration} \
-filter_complex "[0:v]${videoFilters}[v]; \
[2:a]volume=${musicVolume},aloop=loop=-1:size=2e+09,atrim=duration=${introDuration},afade=t=out:st=${Math.max(0, introDuration - 3)}:d=3[music]; \
[1:a]volume=${voiceVolume}[voice]; \
[music][voice]amix=inputs=2:duration=shortest:weights=0.7 0.3[a]" \
-map "[v]" -map "[a]" -c:v ${encoder} -preset fast "${outVideo}"`;

  return await runFfmpegCmd(cmd);
};

// Xuất các hàm
export {
  runFfmpegCmd,
  extractAndProcessAudio,
  getMediaDuration,
  createIntroVideo,
  getVideoResolution,
  concatIntroAndMainVideo,
  processBackgroundMusic,
  createIntroVideoWithMusic
};
