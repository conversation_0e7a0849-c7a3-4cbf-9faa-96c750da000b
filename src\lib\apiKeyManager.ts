
import { getProviderForModel } from "./modelUtils";
import { setApi<PERSON><PERSON> as setGeminiApi<PERSON><PERSON>, getApi<PERSON><PERSON> as getGeminiApi<PERSON><PERSON> } from "@/lib/geminiApi";
import { setApi<PERSON><PERSON> as setOpenAIApi<PERSON>ey, getA<PERSON><PERSON><PERSON> as getOpenAIApi<PERSON>ey } from "@/lib/openaiApi";
import { setApi<PERSON>ey as setDeepseek<PERSON>pi<PERSON>ey, getApi<PERSON>ey as getDeepseekApiKey } from "@/lib/deepseekApi";

// Store API keys for different providers
const apiKeys: Record<string, string> = {
  gemini: "",
  openai: "",
  deepseek: ""
};

/**
 * Set API key for a specific provider
 * @param provider The provider to set the API key for
 * @param apiKey The API key to set
 */
export function setApiKeyForProvider(provider: string, apiKey: string): void {
  apiKeys[provider] = apiKey;
  
  // Also set the API key in the provider-specific module
  switch (provider) {
    case "gemini":
      setGeminiApiKey(apiKey);
      break;
    case "openai":
      setOpenAIApiKey(apiKey);
      break;
    case "deepseek":
      setDeepseekApiKey(apiKey);
      break;
  }
}

/**
 * Get API key for a specific provider
 * @param provider The provider to get the API key for
 * @returns The API key for the provider
 */
export function getApiKeyForProvider(provider: string): string {
  return apiKeys[provider] || "";
}

/**
 * Set API key for the current model
 * @param apiKey The API key to set
 * @param modelId The model ID to set the API key for
 */
export function setApiKey(apiKey: string, modelId: string): void {
  const provider = getProviderForModel(modelId);
  if (provider) {
    setApiKeyForProvider(provider, apiKey);
  }
}

/**
 * Get API key for the current model
 * @param modelId The model ID to get the API key for
 * @returns The API key for the model
 */
export function getApiKey(modelId: string): string {
  const provider = getProviderForModel(modelId);
  if (provider) {
    return getApiKeyForProvider(provider);
  }
  return "";
}

/**
 * Check if API key is provided for a specific model
 * @param modelId The model ID to check
 * @returns True if API key is provided, false otherwise
 */
export function hasApiKey(modelId: string): boolean {
  return !!getApiKey(modelId);
}
