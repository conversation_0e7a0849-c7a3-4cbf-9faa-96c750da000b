const moment = require("moment");
const { HumanMessage, SystemMessage, AIMessage } = require("@langchain/core/messages");

class GptRoles {
  constructor(knex) {
    this.knex = knex;
  }
  insert(data) {
    data.inserted_at = moment().format("YYYY-MM-DD HH:mm:ss");
    return this.knex("gpt_roles").insert(data).returning("*");
  }

  update(id, data) {
    data.inserted_at = moment().format("YYYY-MM-DD HH:mm:ss");
    return this.knex("gpt_roles").where("id", "=", id).update(data);
  }

  deleteImage(id) {
    return this.knex("gpt_roles").where("id", "=", id).del();
  }

  getDataById(id) {
    return this.knex.select("*").from("gpt_roles").where("id", "=", id).first();
  }

  getDataByStatus(id) {
    return this.knex
      .select("*")
      .from("gpt_roles")
      .where("status", "=", id)
      .first();
  }

  getData() {
    return this.knex
      .select("*")
      .from("gpt_roles")
      .orderBy("inserted_at", "desc");
  }

  getAllData() {
    return this.knex.select("*").from("gpt_roles");
  }

  //输入gpt 的标识id，获取到预设数据
  async getGptMsgByStatusId(id) {
    let data = await this.knex
      .select("*")
      .from("gpt_roles")
      .where("status", "=", id)
      .first();
    let sys_prompt = data.prompt;
    let model = data.model;
    //
    let anlis = await this.knex
      .select("*")
      .from("gpt_roles_anli")
      .where("role_id", "=", data.id);
    let sys_msg = [];
    let anli_msg = [];
    sys_msg.push(new SystemMessage(sys_prompt));

    for (let anli of anlis) {
      if (anli.type == 0) {
        anli_msg.push(new HumanMessage(anli.text));
      } else {
        anli_msg.push(new AIMessage(anli.text));
      }
    }

    return { gpt_msg: [...sys_msg, ...anli_msg], model: model };
  }
};

module.exports = GptRoles;