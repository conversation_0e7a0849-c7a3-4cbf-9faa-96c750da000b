<template>
  <a-modal
    v-model:open="visible"
    title="Quản lý Prompt Templates"
    width="1200px"
    :footer="null"
    :destroyOnClose="true"
    @cancel="handleClose"
  >
    <PromptTemplateManager @template-selected="handleTemplateSelected" />
  </a-modal>
</template>

<script setup>
import { ref, watch } from 'vue'
import PromptTemplateManager from './PromptTemplateManager.vue'

// Props
const props = defineProps({
  open: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:open', 'template-selected', 'close'])

// Local state
const visible = ref(props.open)

// Watch for prop changes
watch(() => props.open, (newVal) => {
  visible.value = newVal
})

// Watch for local changes
watch(visible, (newVal) => {
  emit('update:open', newVal)
})

// Methods
const handleClose = () => {
  visible.value = false
  emit('close')
}

const handleTemplateSelected = (template) => {
  emit('template-selected', template)
}

// Expose methods
defineExpose({
  open: () => {
    visible.value = true
  },
  close: () => {
    visible.value = false
  }
})
</script>
