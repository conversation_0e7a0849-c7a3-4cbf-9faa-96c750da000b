import { reactive } from 'vue';

export const state = reactive({
  contentStream: null,
  videoPlayer: null,
  currentPlayingSubtitleId: null,
  currentTime: 0,
  cropData: null,
  cropText: null,
  assOptionsForVideo: {},
  activeTab: 'editor',
  // Synchronized playback settings
  originalVideoSettings: null,
  isSynchronizedPlayback: false,
  // Global playback system
  isGlobalPlayback: false,
  globalAudioElements: new Map(), // Map of subtitle ID to audio element
  globalPlaybackStartTime: 0,
  audioEnabled: false, // Track if audio has been enabled by user

  // Speed balancing system
  speedBalancingEnabled: false, // Enable/disable speed balancing
  joinTime: 0.5, // Join time for speed calculations in seconds
  webAudioSupported: false, // Track if Web Audio API is supported
  tabs: [
    {
      key: '/',
      label: 'Editor',
      icon: () =>
        h(
          'svg',
          {
            width: 16,
            height: 16,
            viewBox: '0 0 24 24',
            fill: 'none',
            stroke: 'currentColor',
            strokeWidth: 2,
          },
          [
            // video icon path
            h('path', { d: 'M5 4l14 0' }),
            h('path', { d: 'M5 8l14 0' }),
            h('path', { d: 'M5 12l14 0' }),
            h('path', { d: 'M5 16l14 0' }),
          ],
        ),
    },
    {
      key: '/dashboard',
      label: 'Tools',
      icon: () =>
        h(
          'svg',
          {
            width: 16,
            height: 16,
            viewBox: '0 0 24 24',
            fill: 'none',
            stroke: 'currentColor',
            strokeWidth: 2,
          },
          [
            // tools ai icon path
            h('path', { d: 'M5 4l14 0' }),
            h('path', { d: 'M5 8l14 0' }),
            h('path', { d: 'M5 12l14 0' }),
            h('path', { d: 'M5 16l14 0' }),
          ],
        ),
    },  {
    key: 'summary',
    label: 'Summary Novel',
    icon: () => h('svg', {
      width: 16,
      height: 16,
      viewBox: '0 0 24 24',
      fill: 'none',
      stroke: 'currentColor',
      strokeWidth: 2
    }, [
      // summary icon path
      h('path', { d: 'M5 4l14 0' }),
      h('path', { d: 'M5 8l14 0' }),
      h('path', { d: 'M5 12l14 0' }),
      h('path', { d: 'M5 16l14 0' }),
    ])
  },
    {
      key: '/summary-intro',
      label: 'Summary Intro Video',
      icon: () =>
        h(
          'svg',
          {
            width: 16,
            height: 16,
            viewBox: '0 0 24 24',
            fill: 'none',
            stroke: 'currentColor',
            strokeWidth: 2,
          },
          [
            // summary intro icon path
            h('path', { d: 'M5 4l14 0' }),
            h('path', { d: 'M5 8l14 0' }),
            h('path', { d: 'M5 12l14 0' }),
            h('path', { d: 'M5 16l14 0' }),
          ],
        ),
    },
    {
      key: '/train-gpt',
      label: 'Train GPT',
      icon: () =>
        h(
          'svg',
          {
            width: 16,
            height: 16,
            viewBox: '0 0 24 24',
            fill: 'none',
            stroke: 'currentColor',
            strokeWidth: 2,
          },
          [
            // train gpt icon path
            h('path', { d: 'M5 4l14 0' }),
            h('path', { d: 'M5 8l14 0' }),
            h('path', { d: 'M5 12l14 0' }),
            h('path', { d: 'M5 16l14 0' }),
          ],
        ),
    },
  ],
  videoElement: null,
  tabContentRef: null,
  key: 0,
  isRender: false,
  useTerm: false,
  useNovel: true
});

window.STATE = state;
