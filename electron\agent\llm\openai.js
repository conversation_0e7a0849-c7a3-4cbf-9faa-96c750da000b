const OpenAI = require('openai');
const { defaultOutputCallback } = require('../utils.js');

let openaiClient = null;

/**
 * Get OpenAI client instance
 * @param {string} apiKey 
 * @param {string} baseUrl 
 * @returns {OpenAI}
 */
function getOpenAIClient(apiKey = null, baseUrl = null) {
    if (!openaiClient) {
        openaiClient = new OpenAI({
            apiKey: apiKey || process.env.OPENAI_API_KEY,
            baseURL: baseUrl || process.env.OPENAI_BASE_URL
        });
    }
    return openaiClient;
}

/**
 * Process messages for OpenAI API
 * @param {Array} messages 
 * @returns {Array}
 */
function processMessages(messages) {
    return messages.map(msg => {
        if (Array.isArray(msg.content)) {
            // Handle multimodal content
            return {
                role: msg.role,
                content: msg.content.map(item => {
                    if (typeof item === 'string') {
                        return { type: 'text', text: item };
                    } else if (item.image) {
                        return {
                            type: 'image_url',
                            image_url: { url: item.image }
                        };
                    }
                    return item;
                })
            };
        }
        return msg;
    });
}

/**
 * LLM inference with streaming
 * @param {OpenAI} client 
 * @param {Array} messages 
 * @param {string} model 
 * @param {Function} outputCallback 
 * @param {Object} args 
 * @returns {Promise<string>}
 */
async function llmInferenceWithStream(client, messages, model, outputCallback, args = {}) {
    const stream = await client.chat.completions.create({
        model: model,
        messages: messages,
        stream: true,
        ...args
    });

    let result = '';
    for await (const chunk of stream) {
        const content = chunk.choices[0]?.delta?.content || '';
        if (content) {
            result += content;
            if (outputCallback) {
                outputCallback(content);
            }
        }
    }
    
    if (outputCallback) {
        outputCallback(null); // Signal end of stream
    }
    
    return result;
}

/**
 * LLM inference without streaming
 * @param {OpenAI} client 
 * @param {Array} messages 
 * @param {string} model 
 * @param {Object} args 
 * @returns {Promise<string>}
 */
async function llmInferenceWithoutStream(client, messages, model, args = {}) {
    const response = await client.chat.completions.create({
        model: model,
        messages: messages,
        stream: false,
        ...args
    });

    return response.choices[0].message.content;
}

/**
 * Main LLM inference function
 * @param {Array} messages 
 * @param {string} model 
 * @param {boolean} stream 
 * @param {number} temperature 
 * @param {string} apiKey 
 * @param {string} baseUrl 
 * @param {Function} outputCallback 
 * @param {Object} args 
 * @returns {Promise<string>}
 */
async function llmInference(
    messages,
    model = 'gpt-3.5-turbo',
    stream = false,
    temperature = null,
    apiKey = null,
    baseUrl = null,
    outputCallback = null,
    args = {}
) {
    console.log('LLM Inference:', { model, stream, messagesCount: messages.length });

    // Model mapping
    if (model === 'smart') model = 'gpt-4o';
    if (model === 'long') model = 'gpt-4o';
    if (model === 'normal') model = 'gpt-3.5-turbo';

    const client = getOpenAIClient(apiKey, baseUrl);
    const processedMessages = processMessages(messages);

    const requestArgs = { ...args };
    if (temperature !== null) {
        requestArgs.temperature = temperature;
    }
    
    if (stream) {
        return await llmInferenceWithStream(
            client, 
            processedMessages, 
            model, 
            outputCallback || defaultOutputCallback,
            requestArgs
        );
    } else {
        return await llmInferenceWithoutStream(
            client, 
            processedMessages, 
            model, 
            requestArgs
        );
    }
}

module.exports = { llmInference };