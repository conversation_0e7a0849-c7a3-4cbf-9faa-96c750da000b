const path = require('path');
const { exec } = require('child_process');
const { app } = require('electron');
const os = require('os');
const ffmpeg = require('fluent-ffmpeg');
class FFmpegManager {
  constructor() {
    this._ffmpeg = ffmpeg;
    this.ffmpegPath = undefined;
    this.ffprobePath = undefined;
    
    if (app.isPackaged) {
      this.ffmpegPath = path.join(process.resourcesPath, 'app.asar.unpacked',"static", "ffmpeg", 'ffmpeg');
      this.ffprobePath = path.join(process.resourcesPath, 'app.asar.unpacked',"static", "ffmpeg", "ffprobe");
    } else {
      const appPath = app.getAppPath();
      this.ffmpegPath = path.join(appPath, "static", "ffmpeg", "ffmpeg");
      this.ffprobePath = path.join(appPath, "static", "ffmpeg", 'ffprobe');
    }
    
    this._ffmpeg.setFfmpegPath(this.ffmpegPath);
    this._ffmpeg.setFfprobePath(this.ffprobePath);
  }
  
  get ffmpeg() {
    return this._ffmpeg;
  }
  
  set ffmpeg(value) {
    this._ffmpeg = value;
  }
  
  executeFFmpegCommand(command) {
    return new Promise((resolve, reject) => {
      command = command.replaceAll("_ffmpeg", `"${this.ffmpegPath}"`);
      command = command.replaceAll("_ffprobe", `"${this.ffprobePath}"`);
      console.log(command);
      
      exec(command, (error, stdout, stderr) => {
        if (error) {
          reject("Error: " + error.message);
          return;
        }
        resolve(stdout);
      });
    });
  }
}

function setFfmpegPaths(resourcesPath, isPackaged) {
    let ffmpegPath;
    let ffprobePath;

    switch (os.platform()) {
        case 'win32':
            // Windows
            if(isPackaged) {
                ffmpegPath = path.join(resourcesPath, 'app.asar.unpacked', 'static/ffmpeg/ffmpeg.exe');
                ffprobePath = path.join(resourcesPath, 'app.asar.unpacked', 'static/ffmpeg/ffprobe.exe');
            } else {
                ffmpegPath = path.join(ROOT_DIR, '/static/ffmpeg/ffmpeg.exe');
                ffprobePath = path.join(ROOT_DIR, '/static/ffmpeg/ffprobe.exe');    
            }
            break;

        case 'darwin':
            // MacOS
            if(isPackaged) {
                ffmpegPath = path.join(resourcesPath, 'app.asar.unpacked', 'static/ffmpeg/mac/ffmpeg');
                ffprobePath = path.join(resourcesPath, 'app.asar.unpacked', 'static/ffmpeg/mac/ffprobe');
            } else {
                ffmpegPath = path.join(ROOT_DIR, '/static/ffmpeg/mac/ffmpeg');
                ffprobePath = path.join(ROOT_DIR, '/static/ffmpeg/mac/ffprobe');    
            }
            break;

        default:
            throw new Error('Unsupported platform: ' + os.platform());
    }

    ffmpeg.setFfmpegPath(ffmpegPath);
    ffmpeg.setFfprobePath(ffprobePath);
}
const ffmpegManager = new FFmpegManager();

module.exports = { setFfmpegPaths, ffmpeg, ffmpegManager };
