import { defineStore } from 'pinia'
import { reactive } from 'vue'

export interface LoadingStates {
  uploadVideoList: boolean
  convertAudioToText: boolean
  handleSummarizeContent: boolean
  handleCreateIntroVideo: boolean
  handleProcessAll: boolean
  handleStartSpeechRecognition: boolean
  handleStartTTS: boolean
  getVoices: boolean
  [key: string]: boolean
}

export const useLoadingStore = defineStore('loading', () => {
  // State
  const loadingStates = reactive<LoadingStates>({
    uploadVideoList: false,
    convertAudioToText: false,
    handleSummarizeContent: false,
    handleCreateIntroVideo: false,
    handleProcessAll: false,
    handleStartSpeechRecognition: false,
    handleStartTTS: false,
    getVoices: false
  })

  // Actions
  const startLoading = (key: keyof LoadingStates) => {
    loadingStates[key] = true
  }

  const stopLoading = (key: keyof LoadingStates) => {
    loadingStates[key] = false
  }

  const isLoading = (key: keyof LoadingStates): boolean => {
    return loadingStates[key]
  }

  const stopAllLoading = () => {
    Object.keys(loadingStates).forEach(key => {
      loadingStates[key as keyof LoadingStates] = false
    })
  }

  return {
    // State
    loadingStates,
    
    // Actions
    startLoading,
    stopLoading,
    isLoading,
    stopAllLoading
  }
})
