import { defineStore } from 'pinia';
import { message } from 'ant-design-vue';
import { parseSRT } from '../lib/utils';

export const useSRTStore = defineStore('srt', {
    state: () => ({
        srtFile: null,
        srtContent: null,
        srtItems: [],
        isProcessing: false,
        isLoading: false,
        error: null,
        generatedCount: 0,
        totalItems: 0,
        selectedItems: [],
        bulkSpeaker: '',
        bulkSpeechRate: 0,
        itemLoadingStates: {},
        currentPage: 1,
        pageSize: 10,
        totalPages: 0,
        outputFileName: 'combined-audio.mp3',
        isJoining: false,
        hasGeneratedAudios: false,
        activeRow: null,
        currentPlayingSubtitleId: null,
        layoutMode: 'default',
        modelDir: '',
      }),
      persist: {
        storage: localStorage,
        pick: ['modelDir']
      },
      actions: {
        setSrtFile(file) {
          // if not Flile => convert to File
          // if (!(file instanceof File)) {
          //   file = new File(
          //     [file.content],
          //     file.name,
          //     { type: 'application/x-subrip' }
          //   );
          //   // file.path = file.path;
          // }
          this.srtFile = file;
        },
        setSrtContent(content) {
          this.srtContent = content;
        },
        setSrtItems(items) {
          this.srtItems = items;
        },
        setIsProcessing(value) {
          this.isProcessing = value;
        },
        setIsLoading(value) {
          this.isLoading = value;
        },
        setError(error) {
          this.error = error;
        },
        setModelDir(dir) {
          this.modelDir = dir;
        },
        processSrtFile(file) {
          this.setSrtFile(file);
          this.setSrtContent(file.content);
          this.setSrtItems(parseSRT(file.content));
          this.setIsLoading(false);
          this.setError(null);
        },
      },
    });



