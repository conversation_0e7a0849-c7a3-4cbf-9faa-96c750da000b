<template>
  <div class="prompt-template-demo p-6">
    <h1 class="text-2xl font-bold mb-6">Demo Prompt Template System</h1>
    
    <!-- Prompt Template Selector -->
    <div class="mb-6">
      <PromptTemplateSelector
        ref="promptSelector"
        :sourceLang="sourceLang"
        :targetLang="targetLang"
        :dictionary="dictionary"
        @template-changed="onTemplateChanged"
      />
    </div>

    <!-- Configuration Panel -->
    <a-card title="Cấu hình Demo" class="mb-6">
      <a-row :gutter="16">
        <a-col :span="8">
          <a-form-item label="Ngôn ngữ nguồn">
            <a-select v-model:value="sourceLang" style="width: 100%">
              <a-select-option value="auto">Tự động</a-select-option>
              <a-select-option value="Chinese">Tiếng Trung</a-select-option>
              <a-select-option value="English">Tiếng <PERSON></a-select-option>
              <a-select-option value="Japanese">Tiếng <PERSON></a-select-option>
              <a-select-option value="Korean">Tiế<PERSON></a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        
        <a-col :span="8">
          <a-form-item label="Ngôn ngữ đích">
            <a-select v-model:value="targetLang" style="width: 100%">
              <a-select-option value="Vietnamese">Tiếng Việt</a-select-option>
              <a-select-option value="English">Tiếng Anh</a-select-option>
              <a-select-option value="Chinese">Tiếng Trung</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        
        <a-col :span="8">
          <a-form-item label="Từ điển thuật ngữ">
            <a-switch 
              v-model:checked="useDictionary" 
              checked-children="Có" 
              un-checked-children="Không" 
            />
          </a-form-item>
        </a-col>
      </a-row>
    </a-card>

    <!-- Dictionary Editor -->
    <a-card v-if="useDictionary" title="Từ điển thuật ngữ" class="mb-6">
      <div class="mb-4">
        <a-row :gutter="8">
          <a-col :span="10">
            <a-input 
              v-model:value="newTerm.source" 
              placeholder="Thuật ngữ gốc"
              @pressEnter="addTerm"
            />
          </a-col>
          <a-col :span="10">
            <a-input 
              v-model:value="newTerm.target" 
              placeholder="Bản dịch"
              @pressEnter="addTerm"
            />
          </a-col>
          <a-col :span="4">
            <a-button type="primary" @click="addTerm" block>Thêm</a-button>
          </a-col>
        </a-row>
      </div>
      
      <div class="dictionary-list">
        <a-tag 
          v-for="(translation, term) in dictionary" 
          :key="term"
          closable
          @close="removeTerm(term)"
          class="mb-2"
        >
          {{ term }} → {{ translation }}
        </a-tag>
      </div>
    </a-card>

    <!-- Generated Prompt Preview -->
    <a-card title="Prompt được tạo" class="mb-6">
      <div class="prompt-preview bg-gray-50 p-4 rounded border font-mono text-sm whitespace-pre-wrap max-h-96 overflow-y-auto">
        {{ generatedPrompt }}
      </div>
      
      <div class="mt-4 flex gap-2">
        <a-button @click="copyPrompt" type="primary">
          <template #icon><CopyOutlined /></template>
          Copy Prompt
        </a-button>
        <a-button @click="refreshPrompt">
          <template #icon><ReloadOutlined /></template>
          Refresh
        </a-button>
      </div>
    </a-card>

    <!-- Test Translation -->
    <a-card title="Test Dịch thuật">
      <a-form-item label="Văn bản test">
        <a-textarea 
          v-model:value="testText"
          placeholder="Nhập văn bản để test prompt..."
          :rows="4"
        />
      </a-form-item>
      
      <a-form-item label="Kết quả prompt đầy đủ">
        <div class="test-result bg-gray-50 p-4 rounded border font-mono text-sm whitespace-pre-wrap max-h-64 overflow-y-auto">
          {{ fullPromptWithText }}
        </div>
      </a-form-item>
      
      <a-button @click="copyFullPrompt" type="primary">
        <template #icon><CopyOutlined /></template>
        Copy Full Prompt
      </a-button>
    </a-card>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import PromptTemplateSelector from './PromptTemplateSelector.vue'
import { CopyOutlined, ReloadOutlined } from '@ant-design/icons-vue'

// Reactive data
const promptSelector = ref(null)
const sourceLang = ref('auto')
const targetLang = ref('Vietnamese')
const useDictionary = ref(false)
const dictionary = ref({})
const newTerm = ref({ source: '', target: '' })
const testText = ref('1. 你好，世界！\n2. 这是一个测试。\n3. 欢迎使用我们的应用程序。')

// Computed properties
const generatedPrompt = computed(() => {
  if (promptSelector.value) {
    return promptSelector.value.getCurrentPrompt()
  }
  return ''
})

const fullPromptWithText = computed(() => {
  if (!generatedPrompt.value || !testText.value) return ''
  return `${generatedPrompt.value}\n${testText.value}`
})

// Methods
const onTemplateChanged = (template) => {
  console.log('Template changed:', template)
  message.success(`Đã chọn template: ${template.name}`)
}

const addTerm = () => {
  if (!newTerm.value.source.trim() || !newTerm.value.target.trim()) {
    message.warning('Vui lòng nhập đầy đủ thuật ngữ và bản dịch')
    return
  }
  
  dictionary.value[newTerm.value.source.trim()] = newTerm.value.target.trim()
  newTerm.value = { source: '', target: '' }
  message.success('Đã thêm thuật ngữ')
}

const removeTerm = (term) => {
  delete dictionary.value[term]
  message.success('Đã xóa thuật ngữ')
}

const copyPrompt = async () => {
  try {
    await navigator.clipboard.writeText(generatedPrompt.value)
    message.success('Đã copy prompt')
  } catch (error) {
    message.error('Lỗi copy: ' + error.message)
  }
}

const copyFullPrompt = async () => {
  try {
    await navigator.clipboard.writeText(fullPromptWithText.value)
    message.success('Đã copy full prompt')
  } catch (error) {
    message.error('Lỗi copy: ' + error.message)
  }
}

const refreshPrompt = () => {
  // Force re-render by updating a reactive property
  const temp = sourceLang.value
  sourceLang.value = ''
  setTimeout(() => {
    sourceLang.value = temp
  }, 10)
  message.success('Đã refresh prompt')
}

// Watch for changes to update dictionary
watch(useDictionary, (newVal) => {
  if (!newVal) {
    dictionary.value = {}
  } else {
    // Add some sample terms
    dictionary.value = {
      '你好': 'xin chào',
      '世界': 'thế giới',
      '应用程序': 'ứng dụng'
    }
  }
})

// Initialize with sample dictionary
dictionary.value = {
  '你好': 'xin chào',
  '世界': 'thế giới',
  '应用程序': 'ứng dụng'
}
useDictionary.value = true
</script>

<style scoped>
.prompt-preview {
  word-break: break-word;
  line-height: 1.5;
}

.test-result {
  word-break: break-word;
  line-height: 1.5;
}

.dictionary-list {
  min-height: 40px;
}

.dictionary-list .ant-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}
</style>
