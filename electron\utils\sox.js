const { exec } = require('child_process');
const path = require('path');
const fs = require('fs');
const { convertAnyToWav } = require('../ffmpegHandler');

function convertPitchFactorToCents(pitchFactor) {
  if (pitchFactor <= 0) {
    throw new Error('pitchFactor phải > 0');
  }
  const cents = 1200 * Math.log2(pitchFactor);
  return Math.round(cents); // làm tròn gần nhất
}

function sliderPitchToCents(sliderValue) {
  return Math.round(sliderValue * 100); // Giả sử sliderValue từ 0 đến 100, với 50 là không thay đổi
}

async function runSoxCommand(inputFile, outputFile, options = {}) {
  const soxPath = path.join(__dirname, 'sox');
  const command = `sox ${inputFile} ${outputFile} ${options.join(' ')}`;
  console.log(`Executing sox command: ${command}`);
  return new Promise((resolve, reject) => {
    exec(command, (error, stdout, stderr) => {
      if (error) {
        console.error(`Error executing sox command: ${stderr}`);
        return reject(error);
      }
      console.log(`Sox command output: ${stdout}`);
      resolve(stdout);
    });
  });
}

async function soxStart(inputFile, outputFile, options = {}) {
  if (!inputFile || !outputFile) {
    throw new Error('Invalid input or output file');
  }
  // convert to wav
  const inputFileExt = path.extname(inputFile).toLowerCase();
  const tempWavFile = path.join(path.dirname(inputFile), `${path.basename(inputFile, inputFileExt)}_converted.wav`);
  await convertAnyToWav(null, inputFile, tempWavFile);
  fs.unlinkSync(inputFile); // Xóa file gốc
  fs.renameSync(tempWavFile, inputFile); // Đổi tên file tạm thành file gốc
  const pitchIsNumber = typeof options?.pitch == 'number' ? options.pitch : parseInt(options.pitch);
  const pitchCents = options.pitch ? sliderPitchToCents(pitchIsNumber) : 0;
  const soxOptions = ['norm'];
  if (pitchCents !== 0) {
    soxOptions.push(`pitch ${pitchCents}`);
  }
  if (options.speed) {
    soxOptions.push(`tempo ${options.speed}`);
  }
  if (options.reverb) {
    soxOptions.push(`reverb ${options.reverb}`);
  }
  if (options.trim) {
    soxOptions.push(`trim ${options.trim.start} ${options.trim.end}`);
  }
  if (options.fade) {
    soxOptions.push(`fade t ${options.fade.start} ${options.fade.duration}`);
  }
  if (options.reverse) {
    soxOptions.push(`reverse`);
  }
  if (options.vol) {
    soxOptions.push(`vol ${options.vol}`);
  }
  if (options.echo) {
    soxOptions.push(`echo ${options.echo.gainIn} ${options.echo.gainOut} ${options.echo.delay} ${options.echo.decay}`);
  }
  await runSoxCommand(inputFile, outputFile, soxOptions);
}

module.exports = {
  soxStart,
};

// soxStart("F:\\ReviewDao\\20-nam\\p4.wav", 'F:\\ReviewDao\\20-nam\\p4.wav', {
//     pitch: 1.1, // Tăng cao độ lên 20%
//     speed: 1.2,
//     // echo: {
//     //     gainIn: 0.8,
//     //     gainOut: 0.88,
//     //     delay: 60, // Thời gian trễ của echo
//     //     decay: 0.5 // Độ suy giảm của echo

//     // },
// }).then(() => {
//   console.log('Audio extraction and processing completed successfully.');
// }).catch(err => {
//   console.error('Error during audio extraction:', err);
// });
