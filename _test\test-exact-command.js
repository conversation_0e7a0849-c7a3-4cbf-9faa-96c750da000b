const { spawn, exec } = require('child_process');
const path = require('path');
const fs = require('fs');

// The exact command that's failing
const executablePath = path.normalize('G:/CODE/Capcut-TTS-app/capcut-tts-app/static/merge_audio_v2.exe');
const audioFilesJsonPath = path.normalize('F:/ReviewDao/11-05/ran-quy/test_audio/temp/audio_files.json');
const subsJsonPath = path.normalize('F:/ReviewDao/11-05/ran-quy/test_audio/temp/subs.json');
const outputPath = path.normalize('F:/ReviewDao/11-05/ran-quy/test_audio/combined-audio.mp3');

// Check if files exist
console.log('Checking if files exist:');
console.log(`Executable: ${executablePath} - ${fs.existsSync(executablePath) ? 'Exists' : 'Not found'}`);
console.log(`Audio files JSON: ${audioFilesJsonPath} - ${fs.existsSync(audioFilesJsonPath) ? 'Exists' : 'Not found'}`);
console.log(`Subs JSON: ${subsJsonPath} - ${fs.existsSync(subsJsonPath) ? 'Exists' : 'Not found'}`);
console.log(`Output directory: ${path.dirname(outputPath)} - ${fs.existsSync(path.dirname(outputPath)) ? 'Exists' : 'Not found'}`);

// Try different ways to run the command

// Test 1: Using exec
console.log('\n--- Test 1: Using exec ---');
const cmdString = `"${executablePath}" "${audioFilesJsonPath}" "${subsJsonPath}" "${outputPath}"`;
console.log('Command:', cmdString);

exec(cmdString, (error, stdout, stderr) => {
  console.log('Stdout:', stdout);
  console.log('Stderr:', stderr);
  
  if (error) {
    console.error('Exec error:', error);
  } else {
    console.log('Success with exec!');
  }
  
  // Test 2: Using spawn with shell=true
  console.log('\n--- Test 2: Using spawn with shell=true ---');
  const args = [audioFilesJsonPath, subsJsonPath, outputPath];
  console.log('Command args:', args);
  
  const process = spawn(executablePath, args, {
    stdio: ['inherit', 'pipe', 'pipe'],
    shell: true
  });
  
  let spawnStdout = '';
  let spawnStderr = '';
  
  process.stdout.on('data', (data) => {
    const output = data.toString();
    spawnStdout += output;
    console.log('Process stdout:', output);
  });
  
  process.stderr.on('data', (data) => {
    const output = data.toString();
    spawnStderr += output;
    console.error('Process stderr:', output);
  });
  
  process.on('error', (error) => {
    console.error('Spawn error:', error);
  });
  
  process.on('exit', (code) => {
    console.log(`Process exited with code ${code}`);
    
    if (code === 0) {
      console.log('Success with spawn!');
    } else {
      console.error('Error with spawn!');
      console.error('Stdout:', spawnStdout);
      console.error('Stderr:', spawnStderr);
      
      // Test 3: Using spawn without shell
      console.log('\n--- Test 3: Using spawn without shell ---');
      
      const process2 = spawn(executablePath, args, {
        stdio: ['inherit', 'pipe', 'pipe']
      });
      
      let spawn2Stdout = '';
      let spawn2Stderr = '';
      
      process2.stdout.on('data', (data) => {
        const output = data.toString();
        spawn2Stdout += output;
        console.log('Process2 stdout:', output);
      });
      
      process2.stderr.on('data', (data) => {
        const output = data.toString();
        spawn2Stderr += output;
        console.error('Process2 stderr:', output);
      });
      
      process2.on('error', (error) => {
        console.error('Spawn2 error:', error);
      });
      
      process2.on('exit', (code) => {
        console.log(`Process2 exited with code ${code}`);
        
        if (code === 0) {
          console.log('Success with spawn without shell!');
        } else {
          console.error('Error with spawn without shell!');
          console.error('Stdout:', spawn2Stdout);
          console.error('Stderr:', spawn2Stderr);
        }
      });
    }
  });
});
