<template>
    <div class='console-log rounded w-36' @click="visible = true" v-if="logs.length > 0 || state.contentStream" :style="{ height: props.size === 'small' ? '30px' : '50px' }">
        <p v-for="(log, index) in logs" :key="index" class="log-item">
            <span :class="log?.code === 0 ? 'success' : 'error'">{{ log?.data }}</span>
        </p>
    </div>
    <a-modal
      v-model:open="visible"
      :width="700"
      :footer="null"
      @cancel="handleCancel"
    >
      <div class="srt-list-container">
        <div class="mb-4 flex justify-between items-center">
          <h3 class="text-lg font-medium">Console Log</h3>
        </div>
        <div class="console-log" style="height: 500px; width: 100%; overflow-y: auto;">
            <p v-for="(log, index) in logs.slice().reverse()" :key="index" class="log-item">
                <span :class="log?.code === 0 ? 'success' : 'error'">{{ log?.data }}</span>
            </p>
            <p class="log-item">
                <span>{{ state.contentStream }}</span>
            </p>
        </div>
      </div>
    </a-modal>

</template>
<script setup>
import { onMounted,ref } from 'vue';
import { state } from '@/lib/state';


const props = defineProps({
  size: {
    type: String,
    default: 'default'
  }
});

const logs = ref([]);
const visible = ref(false);

const maxLogs = 500;

const setLogs = (newLogs) => {
    logs.value.unshift(newLogs);
    logs.value = logs.value.slice(0, maxLogs);
};

const allListeners = {
    'get-srt-task-res': setLogs,
    'adjust-speed-res': setLogs,
    'merge-audio-files-res': setLogs,
    'synthesize-service-res': setLogs,
    'process-audio-whisper-res': setLogs,
    'video-task': setLogs
};

let removeListener = []

onMounted(() => {
    Object.entries(allListeners).forEach(([event, listener]) => {
      const res=  window.electronAPI.on(event, listener);
      removeListener.push(res);
    });
});
onUnmounted(() => {
    removeListener.forEach((off) => off());
});

const handleCancel = () => {
  visible.value = false;
};


</script>
<style>
.console-log {
  height: 50px;
  background-color: black;
  color: white;
  /* padding: 2px; */
  overflow-x: hidden;
  word-wrap: break-word;
  overflow-y: auto;
  /* margin: 2px 0; */
}

.log-item {
  margin-left: 4px;
  margin-bottom: 0;
}

.success {
  color: green;
}

.error {
  color: red;
}
</style>