<template>
    <a-form-item label="Subtitles Settings" class="border border-gray-600 p-2 rounded-lg">
        <!-- Voice Selection -->
        <div class="voice-selector mb-4">
            <a-radio-group v-model:value="subtitleStore.selectedVoice" button-style="solid" class="mb-3">
                <a-radio-button value="isVoice1">Voice 1</a-radio-button>
                <a-radio-button value="isVoice2">Voice 2</a-radio-button>
                <a-radio-button value="isVoice3">Voice 3</a-radio-button>
                <a-radio-button value="isVoice4">Voice 4</a-radio-button>
                <a-radio-button value="isVoice5">Voice 5</a-radio-button>
            </a-radio-group>
            
            <!-- Quick Actions -->
            <div class="voice-actions mb-2">
                <!-- <a-button size="small" @click="copyVoiceSettings" :disabled="!hasSettingsToCopy">
                    Copy Settings
                </a-button>
                <a-button size="small" @click="pasteVoiceSettings" :disabled="!copiedSettings">
                    Paste Settings
                </a-button> -->
                <a-button size="small" @click="resetVoiceSettings" danger>
                    Reset
                </a-button>
            </div>
        </div>

        <a-checkbox v-model:checked="currentVoiceSettings.showSubtitle">
            Show subtitles for {{ getVoiceLabel(subtitleStore.selectedVoice) }}
        </a-checkbox>
        
        <div v-if="currentVoiceSettings.showSubtitle" class="mt-2">
            <!-- Subtitle Preview -->
            <div class="subtitle-preview h-12 mb-1">
                <p class="subtitle-preview-content" :style="getPreviewStyle()">
                    {{ getPreviewText() }}
                </p>
            </div>
            
            <!-- Color Presets -->
            <div class="color-presets-section mb-4">
                <h4 class="mb-2">Color Presets for {{ getVoiceLabel(subtitleStore.selectedVoice) }}:</h4>
                <div class="color-presets-grid">
                    <div v-for="preset in subtitleColorPresets" :key="preset.name" 
                         class="color-preset-item"
                         @click="applyColorPreset(preset)" 
                         :class="{ 'active': isPresetActive(preset) }">
                        <div class="color-preview" :style="getPresetStyle(preset)">
                            {{ preset.name === 'None' ? '⊘' : getVoiceLabel(subtitleStore.selectedVoice) }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Font Selection -->
            <a-row :gutter="16" class="mb-4">
                <a-col :span="24">
                    <a-form-item label="Font Family">
                        <a-select
                            v-model:value="currentVoiceSettings.fontFamily"
                            placeholder="Select font"
                            :loading="!fontService.isReady()"
                        >
                            <a-select-option
                                v-for="font in fontService.getFonts()"
                                :key="font.name"
                                :value="font.name"
                            >
                                <span :style="{ fontFamily: font.isSystem ? font.name : (loadedFonts.has(font.path) ? font.name : 'inherit') }">
                                    {{ font.name }}
                                    <span v-if="!font.isSystem" class="text-gray-400 text-xs ml-2">(Custom)</span>
                                </span>
                            </a-select-option>
                        </a-select>
                    </a-form-item>
                </a-col>
            </a-row>

            <!-- Settings Form -->
            <a-row :gutter="16">
                <a-col :span="5">
                    <a-form-item label="Font Size">
                        <a-input-number v-model:value="currentVoiceSettings.subtitleFontSize"
                                      :min="12" :max="72" addon-after="px" />
                    </a-form-item>
                </a-col>
                <a-col :span="5">
                    <a-form-item label="Text Color">
                        <a-input v-model:value="currentVoiceSettings.subtitleTextColor" type="color" />
                    </a-form-item>
                </a-col>
                <a-col :span="4">
                    <a-form-item label="Background">
                        <a-input v-model:value="currentVoiceSettings.subtitleBackgroundColor" type="color" />
                    </a-form-item>
                </a-col>
                <a-col :span="4">
                    <a-form-item label="Border Color">
                        <a-input v-model:value="currentVoiceSettings.subtitleBorderColor" type="color" />
                    </a-form-item>
                </a-col>
                <a-col :span="3">
                    <a-form-item label="Bold">
                        <a-checkbox v-model:checked="currentVoiceSettings.subtitleBold">
                            Bold
                        </a-checkbox>
                    </a-form-item>
                </a-col>
            </a-row>
            
            <!-- Position Controls -->
            <a-row :gutter="16">
                <a-col :span="12">
                    <a-form-item label="Left/Right">
                        <a-slider v-model:value="currentVoiceSettings.assOptions.posX" 
                                :min="-100" :max="100"
                                :marks="{ '-100': 'Left', '0': 'Center', '100': 'Right' }" 
                                :step="1" />
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="Up/Down">
                        <a-slider v-model:value="currentVoiceSettings.assOptions.posY" 
                                :min="-100" :max="100"
                                :marks="{ '-100': 'Top', '0': 'Center', '100': 'Bottom' }" 
                                :step="1" />
                    </a-form-item>
                </a-col>
            </a-row>

            <!-- Additional ASS Options -->
            <a-row :gutter="16" class="mt-2">
                <a-col :span="8">
                    <a-form-item label="Alignment">
                        <a-select v-model:value="currentVoiceSettings.assOptions.align">
                            <a-select-option :value="7">Top Left</a-select-option>
                            <a-select-option :value="8">Top Center</a-select-option>
                            <a-select-option :value="9">Top Right</a-select-option>
                            <a-select-option :value="4">Middle Left</a-select-option>
                            <a-select-option :value="5">Middle Center</a-select-option>
                            <a-select-option :value="6">Middle Right</a-select-option>
                            <a-select-option :value="1">Bottom Left</a-select-option>
                            <a-select-option :value="2">Bottom Center</a-select-option>
                            <a-select-option :value="3">Bottom Right</a-select-option>
                        </a-select>
                    </a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="Font Size Override">
                        <a-input-number v-model:value="currentVoiceSettings.assOptions.fontSize" 
                                      :min="12" :max="200" addon-after="px" placeholder="Auto" />
                    </a-form-item>
                </a-col>
            </a-row>
        </div>
    </a-form-item>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { useSubtitleStore } from '@/stores/subtitle-store';
import { useTTSStore } from '@/stores/ttsStore';
import fontService from '@/services/fontService';

const subtitleStore = useSubtitleStore();
const ttsStore = useTTSStore();

// Font loading state
const loadedFonts = ref(new Set());

const loadFontForPreview = (font) => {
  if (!font || font.isSystem || loadedFonts.value.has(font.path)) return;

  const fontName = font.name;
  const fontUrl = `http://localhost:8082/fonts/${font.path}`;
  const fontFace = new FontFace(fontName, `url('${fontUrl}')`);

  fontFace.load().then(() => {
    document.fonts.add(fontFace);
    loadedFonts.value.add(font.path);
    console.log(`Font loaded: ${fontName}`);
  }).catch(error => {
    console.error(`Error loading font ${font.path}:`, error);
  });
};

// Current selected voice
// const selectedVoice = ref('isVoice1');

// Clipboard for copying settings between voices
const copiedSettings = ref(null);

// Voice settings access
const renderVoiceOptions = subtitleStore.renderVoiceOptions || {};

// Computed property for current voice settings
const currentVoiceSettings = computed(() => {
    return renderVoiceOptions[subtitleStore.selectedVoice] || getDefaultVoiceSettings();
});

// Check if we have settings to copy (current voice has non-default settings)
// const hasSettingsToCopy = computed(() => {
//     const current = currentVoiceSettings.value;
//     const defaults = getDefaultVoiceSettings();
//     return JSON.stringify(current) !== JSON.stringify(defaults);
// });

// Default voice settings factory
function getDefaultVoiceSettings() {
    return {
        showSubtitle: true,
        subtitleFontSize: 48,
        subtitleTextColor: '#ffffff',
        subtitleBackgroundColor: '#000000',
        subtitleBorderColor: '#000000',
        subtitleBold: true,
        shadowSize: 2,
        fontFamily: 'Arial',
        currentPlayingSubtitleId: null,
        activeSubtitleId: null,
        assOptions: {
            posX: 0,
            posY: 33,
            rotation: 0,
            align: 5,
            fontSize: 48,
        },
    };
}

// Voice label helper
function getVoiceLabel(voice) {
    const labels = {
        'isVoice1': 'V1',
        'isVoice2': 'V2', 
        'isVoice3': 'V3',
        'isVoice4': 'V4',
        'isVoice5': 'V5',
    };
    return labels[voice] || voice;
}

// Subtitle color presets
const subtitleColorPresets = [
    { name: 'White', textColor: '#FFFFFF', backgroundColor: '#000000', borderColor: '#000000' },
    { name: 'Black', textColor: '#000000', backgroundColor: '#FFFFFF', borderColor: '#FFFFFF' },
    { name: 'Blue', textColor: '#FFFFFF', backgroundColor: '#0066CC', borderColor: 'transparent' },
    { name: 'Purple', textColor: '#FFFFFF', backgroundColor: '#6600CC', borderColor: 'transparent' },
    { name: 'Yellow', textColor: '#000000', backgroundColor: '#FFCC00', borderColor: 'transparent' },
    { name: 'Blue Glow', textColor: '#00CCFF', backgroundColor: 'transparent', borderColor: '#0066CC' },
    { name: 'White Glow', textColor: '#FFFFFF', backgroundColor: 'transparent', borderColor: '#000000' },
    { name: 'Green', textColor: '#00FF00', backgroundColor: 'transparent', borderColor: '#006600' },
    { name: 'Pink', textColor: '#FF66CC', backgroundColor: 'transparent', borderColor: '#CC3399' },
    { name: 'Rainbow', textColor: '#FF6600', backgroundColor: 'transparent', borderColor: '#CC3300' },
    { name: 'Gold', textColor: '#FFD700', backgroundColor: 'transparent', borderColor: '#B8860B' }
];

// Preview text based on current voice
function getPreviewText() {
    const voiceTexts = {
        'isVoice1': 'Voice 1 Sample Text',
        'isVoice2': 'Voice 2 Sample Text', 
        'isVoice3': 'Voice 3 Sample Text',
        'isVoice4': 'Voice 4 Sample Text',
        'isVoice5': 'Voice 5 Sample Text',
    };
    return ttsStore.currentSrtList?.items[0]?.translatedText || 
           ttsStore.currentSrtList?.items[0]?.text || 
           voiceTexts[subtitleStore.selectedVoice];
}

// Get preview style for current voice
function getPreviewStyle() {
    const settings = currentVoiceSettings.value;
    return {
        fontSize: `${settings.subtitleFontSize / 2}px`,
        fontFamily: settings.fontFamily,
        color: settings.subtitleTextColor || '#ffffff',
        backgroundColor: settings.subtitleBackgroundColor === 'transparent' ? 'rgba(0,0,0,0.0)' : settings.subtitleBackgroundColor || '#000000',
        textAlign: 'center',
        fontWeight: settings.subtitleBold ? 'bold' : 'normal',
        padding: '2px',
        textShadow: settings.subtitleBorderColor === 'transparent' ? 'none' : `
            -1px -1px 0 ${settings.subtitleBorderColor},
            1px -1px 0 ${settings.subtitleBorderColor},
            -1px  1px 0 ${settings.subtitleBorderColor},
            1px  1px 0 ${settings.subtitleBorderColor},
            -2px  0px 0 ${settings.subtitleBorderColor},
            2px  0px 0 ${settings.subtitleBorderColor},
            0px -2px 0 ${settings.subtitleBorderColor},
            0px  2px 0 ${settings.subtitleBorderColor}
        `,
    };
}

// Get preset style for preview
function getPresetStyle(preset) {
    return {
        color: preset.textColor,
        backgroundColor: preset.backgroundColor === 'transparent' ? 'rgba(0,0,0,0.1)' : preset.backgroundColor,
        textShadow: preset.borderColor === 'transparent' ? 'none' : `
            -1px -1px 0 ${preset.borderColor},
            1px -1px 0 ${preset.borderColor},
            -1px  1px 0 ${preset.borderColor},
            1px  1px 0 ${preset.borderColor},
            -2px  0px 0 ${preset.borderColor},
            2px  0px 0 ${preset.borderColor},
            0px -2px 0 ${preset.borderColor},
            0px  2px 0 ${preset.borderColor}
        `,
        fontWeight: 'bold',
        fontSize: '18px',
        padding: '4px 8px',
        borderRadius: '4px',
        textAlign: 'center',
        cursor: 'pointer',
        minHeight: '24px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
    };
}

// Check if preset is currently active
function isPresetActive(preset) {
    const settings = currentVoiceSettings.value;
    return settings.subtitleTextColor === preset.textColor &&
           settings.subtitleBackgroundColor === preset.backgroundColor &&
           settings.subtitleBorderColor === preset.borderColor;
}

// Apply color preset to current voice
function applyColorPreset(preset) {
    const settings = currentVoiceSettings.value;
    settings.subtitleTextColor = preset.textColor;
    settings.subtitleBackgroundColor = preset.backgroundColor;
    settings.subtitleBorderColor = preset.borderColor;
    
    message.success(`Applied ${preset.name} preset to ${getVoiceLabel(subtitleStore.selectedVoice)}`);
}

// Copy current voice settings
function copyVoiceSettings() {
    copiedSettings.value = JSON.parse(JSON.stringify(currentVoiceSettings.value));
    message.success(`Copied ${getVoiceLabel(subtitleStore.selectedVoice)} settings`);
}

// Paste settings to current voice
function pasteVoiceSettings() {
    if (!copiedSettings.value) return;
    
    const currentSettings = currentVoiceSettings.value;
    Object.assign(currentSettings, JSON.parse(JSON.stringify(copiedSettings.value)));
    
    message.success(`Pasted settings to ${getVoiceLabel(subtitleStore.selectedVoice)}`);
}

// Reset current voice to defaults
function resetVoiceSettings() {
    const defaults = getDefaultVoiceSettings();
    const currentSettings = currentVoiceSettings.value;
    Object.assign(currentSettings, defaults);
    
    message.success(`Reset ${getVoiceLabel(subtitleStore.selectedVoice)} to defaults`);
}




// Initialize with proper voice selection handling
onMounted(async () => {
    // Load fonts
    await fontService.loadFonts();

    // Preload all fonts for preview
    fontService.getFonts().forEach(font => {
      loadFontForPreview(font);
    });

    // Ensure all voice settings exist
    ['isVoice1', 'isVoice2', 'isVoice3', 'isVoice4', 'isVoice5'].forEach(voice => {
        if (!renderVoiceOptions[voice]) {
            renderVoiceOptions[voice] = getDefaultVoiceSettings();
        }
    });
});

// Watch for voice changes and provide feedback
watch(() => subtitleStore.selectedVoice, (newVoice, oldVoice) => {
    if (oldVoice) {
        message.info(`Switched to ${getVoiceLabel(newVoice)} settings`);
    }
});
</script>

<style scoped>
/* Color Presets Styles */
.color-presets-section {
    margin-bottom: 16px;
}

.color-presets-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 8px;
    margin-bottom: 16px;
}

.color-preset-item {
    position: relative;
}

.color-preset-item.active .color-preview {
    box-shadow: 0 0 0 3px #1890ff;
    transform: scale(1.05);
}

.color-preview {
    transition: all 0.2s ease;
    user-select: none;
}

.color-preview:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.subtitle-preview {
    border-radius: 4px;
    padding: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%), 
                linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), 
                linear-gradient(45deg, transparent 75%, #f0f0f0 75%), 
                linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
    background-size: 10px 10px;
    background-position: 0 0, 0 5px, 5px -5px, -5px 0px;
}

.subtitle-preview-content {
    margin: 0;
    max-width: 100%;
    word-wrap: break-word;
}

.voice-selector {
    border-bottom: 1px solid #d9d9d9;
    padding-bottom: 12px;
}

.voice-actions {
    display: flex;
    gap: 8px;
}

.voice-actions .ant-btn {
    height: 28px;
    padding: 0 12px;
    font-size: 12px;
}
</style>