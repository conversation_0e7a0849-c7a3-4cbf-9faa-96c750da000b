const pinyin = require("pinyin");

function chineseToSlug(text) {
  const py = pinyin(text, {
    style: pinyin.STYLE_TONE, // Không dấu
    heteronym: false            // Không nhiều cách đọc
  });

  // <PERSON><PERSON><PERSON><PERSON> lạ<PERSON>, lo<PERSON><PERSON> bỏ khoảng trắng thừa, nối bằng dấu "-"
  const slug = py.flat()
    .map(syllable => syllable.toLowerCase())
    .filter(s => /^[a-z]+$/.test(s)) // Bỏ ký tự lạ (nếu có)
    .join('-');

  return slug;
}

module.exports = chineseToSlug;