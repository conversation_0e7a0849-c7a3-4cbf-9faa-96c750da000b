// Quick test for book insertion fix
const path = require('path');
const Database = require('./index');

async function testBookInsert() {
    console.log('🧪 Testing Book Insert Fix...\n');
    
    const dbPath = path.join(__dirname, '../../data/novels.db');
    const db = new Database(dbPath);
    
    try {
        // Initialize database
        await db.initializeTables();
        console.log('✅ Database initialized\n');
        
        // Test data - same book that was causing the error
        const testBookData = {
            title: '消失20年，我归来即最强天师免费阅读',
            author: '冷言leng语',
            category: '都市小说',
            sourceUrl: 'https://44xw.com/a/149/148289/',
            baseUrl: 'https://44xw.com',
            status: 'ongoing',
            lastUpdate: '2024-08-15 08:22:09',
            totalChapters: 635,
            extractedChapters: 635,
            extractionSession: 'test_session_' + Date.now()
        };
        
        console.log('📚 Test Book Data:');
        console.log(`   Title: ${testBookData.title}`);
        console.log(`   Author: ${testBookData.author}`);
        console.log(`   Source: ${testBookData.sourceUrl}`);
        console.log('');
        
        // Test 1: First insertion
        console.log('🔄 Test 1: First insertion...');
        const bookId1 = await db.books.insertBook(testBookData);
        console.log(`✅ Result: Book ID = ${bookId1}\n`);
        
        // Test 2: Second insertion (should update, not fail)
        console.log('🔄 Test 2: Second insertion (should update existing)...');
        testBookData.extractionSession = 'test_session_2_' + Date.now();
        testBookData.totalChapters = 640; // Updated value
        const bookId2 = await db.books.insertBook(testBookData);
        console.log(`✅ Result: Book ID = ${bookId2}\n`);
        
        // Test 3: Third insertion with different data
        console.log('🔄 Test 3: Third insertion with updated data...');
        testBookData.extractionSession = 'test_session_3_' + Date.now();
        testBookData.totalChapters = 645;
        testBookData.status = 'completed';
        const bookId3 = await db.books.insertBook(testBookData);
        console.log(`✅ Result: Book ID = ${bookId3}\n`);
        
        // Verify all IDs are the same
        if (bookId1 === bookId2 && bookId2 === bookId3) {
            console.log('🎉 SUCCESS: All insertions returned the same book ID!');
            console.log(`   Book ID: ${bookId1}`);
        } else {
            console.log('❌ FAILURE: Different book IDs returned');
            console.log(`   ID1: ${bookId1}, ID2: ${bookId2}, ID3: ${bookId3}`);
        }
        
        // Show final book state
        console.log('\n📊 Final Book State:');
        const finalBook = await db.books.getBookById(bookId1);
        console.log(`   Title: ${finalBook.title}`);
        console.log(`   Author: ${finalBook.author}`);
        console.log(`   Category: ${finalBook.category}`);
        console.log(`   Status: ${finalBook.status}`);
        console.log(`   Total Chapters: ${finalBook.total_chapters}`);
        console.log(`   Last Update: ${finalBook.last_update_info}`);
        console.log(`   Last Extraction: ${finalBook.last_extraction_at}`);
        
        console.log('\n✅ Book insertion fix is working correctly!');
        
    } catch (error) {
        console.error('❌ Test failed:', error);
        console.error('Error details:', error.message);
        
        if (error.message.includes('SQLITE_CONSTRAINT')) {
            console.log('\n🔧 The fix is not working. The constraint error still occurs.');
            console.log('This means the getBookBySourceUrl function might not be working properly.');
        }
        
    } finally {
        await db.close();
        console.log('\n🔌 Database connection closed');
    }
}

// Test the getBookBySourceUrl function specifically
async function testGetBookBySourceUrl() {
    console.log('🔍 Testing getBookBySourceUrl function...\n');
    
    const dbPath = path.join(__dirname, '../../data/novels.db');
    const db = new Database(dbPath);
    
    try {
        await db.initializeTables();
        
        const testUrl = 'https://44xw.com/a/149/148289/';
        console.log(`🔗 Looking for book with URL: ${testUrl}`);
        
        const book = await db.books.getBookBySourceUrl(testUrl);
        
        if (book) {
            console.log('✅ Book found:');
            console.log(`   ID: ${book.id}`);
            console.log(`   Title: ${book.title}`);
            console.log(`   Author: ${book.author}`);
            console.log(`   Source URL: ${book.source_url}`);
        } else {
            console.log('❌ No book found with this URL');
            
            // Show all books to debug
            const allBooks = await db.books.getAllBooks(10);
            console.log(`\n📚 All books in database (${allBooks.length}):`);
            allBooks.forEach((book, index) => {
                console.log(`   ${index + 1}. ${book.title} - ${book.source_url}`);
            });
        }
        
    } catch (error) {
        console.error('❌ Error testing getBookBySourceUrl:', error);
    } finally {
        await db.close();
    }
}

// Command line interface
async function runTest() {
    const args = process.argv.slice(2);
    const testType = args[0] || 'insert';
    
    switch (testType) {
        case 'get':
            await testGetBookBySourceUrl();
            break;
        case 'insert':
        default:
            await testBookInsert();
            break;
    }
}

if (require.main === module) {
    runTest().then(() => {
        console.log('\n🏁 Test completed');
        process.exit(0);
    }).catch(error => {
        console.error('\n💥 Test failed:', error);
        process.exit(1);
    });
}

module.exports = { testBookInsert, testGetBookBySourceUrl };
