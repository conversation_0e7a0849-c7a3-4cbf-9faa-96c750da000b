const moment = require("moment");

class GptRolesAnli {
  constructor(knex) {
    this.knex = knex;
  }
  insert(data) {
    data.inserted_at = moment().format("YYYY-MM-DD HH:mm:ss");
    return this.knex("gpt_roles_anli").insert(data);
  }

  update(id, data) {
    data.inserted_at = moment().format("YYYY-MM-DD HH:mm:ss");
    return this.knex("gpt_roles_anli").where("id", "=", id).update(data);
  }

  deleteImagesByRoleId(roleId) {
    return this.knex("gpt_roles_anli").where("role_id", "=", roleId).del();
  }
  deleteImagesById(id) {
    return this.knex("gpt_roles_anli").where("id", "=", id).del();
  }

  getDataById(id) {
    return this.knex
      .select("*")
      .from("gpt_roles_anli")
      .where("role_id", "=", id);
  }

  getData() {
    return this.knex
      .select("*")
      .from("gpt_roles_anli")
      .orderBy("inserted_at", "desc");
  }

  getAllData() {
    return this.knex.select("*").from("gpt_roles_anli");
  }
};

module.exports = GptRolesAnli;