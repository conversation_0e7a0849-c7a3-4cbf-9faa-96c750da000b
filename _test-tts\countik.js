async function main() {
    const response = await fetch("https://countik.com/api/text/speech", {
  "headers": {
    "accept": "*/*",
    "accept-language": "vi-VN,vi;q=0.9,yue-HK;q=0.8,yue;q=0.7,zh-TW;q=0.6,zh;q=0.5,fr-FR;q=0.4,fr;q=0.3,en-US;q=0.2,en;q=0.1,kri;q=0.1",
    "content-type": "application/json",
    "priority": "u=1, i",
    "sec-ch-ua": "\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
    "sec-ch-ua-arch": "\"x86\"",
    "sec-ch-ua-bitness": "\"64\"",
    "sec-ch-ua-full-version": "\"136.0.7103.114\"",
    "sec-ch-ua-full-version-list": "\"Chromium\";v=\"136.0.7103.114\", \"Google Chrome\";v=\"136.0.7103.114\", \"Not.A/Brand\";v=\"99.0.0.0\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-model": "\"\"",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-ch-ua-platform-version": "\"10.0.0\"",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "sec-gpc": "1",
    "cookie": "cf_clearance=cLFKA4auaeMtk6Ah2RbpDdwFLMFfqDRJt8UUuCl9HsM-1747659819-1.2.1.1-c3pR1JGv_a7GnEqTOoYxThFcMWZbNPdOGyloI1zYIQ_dq65uSM_egYkGwB.soHdLPAYMRzI52G_ylTsvtzO52BvktvieLiHLuW2B9uwPvfaUl_JYIDepGJuhW6Q7U9YRPwvTBmzcZv.lHULKN3gyHhw81KJV5F_uFFZiVh3XJxB.l4Y6BuUBjqt8NH9vPz1JrjfaRwWEhF6JyeSL5eWjlDJ9k63rqBRL0OSnfjua3TEy1.Yvy.I6qcpTh.3SGwNu7YSa_t6xE64ispoLOLEXVQBzFCSe7COZD6JVL4wAP2HbNg7AJ9sw.EzzLnR6GrnOJt5H8R6Rz2_USRgIb0axr1r.hWzLgqTJ.IYhuTo_gU.7zm3gTMHhFRJPRe35ZkhA",
    "Referer": "https://countik.com/tiktok-voice-generator",
    "Referrer-Policy": "no-referrer-when-downgrade"
  },
  "body": "{\"text\":\"hihi chào em\",\"voice\":\"BV074_streaming\"}",
  "method": "POST"
});
return response.json();
}

main().then(data => {
  console.log(data);
});
