// translator.js
import { ChatOpenAI } from '@langchain/openai';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import { JsonOutputParser } from '@langchain/core/output_parsers';
import dotenv from 'dotenv';
dotenv.config();

const MAIN_INSTRUCTION = `
# Main task
You are given a chunk of text in Chinese that needs to be translated to Vietnamese.

# Input data format
You will be given a JSON with the following structure:
{
    "memory": str,
    "main_text": {
        "1": str,
        "2": str,
        ...
    }
}

## memory

This field contains your memory of previous translations as markdown-formatted text. This block is crucial for you to generate high quality translations to Vietnamese. It tracks character information, terminology, and contextual details needed for consistent translation.

### How to use the memory:

1. **Read the memory carefully** before starting translation to understand:
   - Character names and their established Vietnamese translations
   - Relationships between characters (affects formal/informal address)
   - Established translations for recurring terms
   - Overall narrative style and tone

2. **Apply memory information** during translation:
   - Use consistent character name translations
   - Maintain consistent terminology
   - Apply appropriate formality levels based on context and relationships
   - Use established Vietnamese names and forms of address

3. **Update the memory** after translation:
   - Add new characters with their Vietnamese names
   - Note new relationships and appropriate address forms
   - Add new recurring terms and their translations
   - NEVER remove existing information - only add or clarify
   - CRITICAL: NEVER use placeholders like "(không thay đổi)" or "unchanged" in any section
   - ALWAYS include the FULL content of EVERY section, even if nothing has changed
   - Remember: The memory does not accumulate between iterations - what you put in the memory field is ALL that will be available in the next iteration

If the memory is empty, fill it with all the necessary details for yourself in the next translation iterations.

### Memory format (markdown):

The memory should be structured in markdown with clear sections. **Always list the original name first, followed by the Vietnamese translation in parentheses.**

Example memory structure:
# Characters
## Main Characters
- **哈利·波特** (Harry Potter) - nam, nhân vật chính
  - Gọi Dumbledore một cách trang trọng (thầy/giáo sư)
  - Bạn bè với Ron và Hermione (gọi tên)

- **赫敏·格兰杰** (Hermione Granger) - nữ, bạn của Harry
  - Dùng cách xưng hô trang trọng với giáo viên (thầy/cô)
  - Thân mật với bạn bè (gọi tên)

## Secondary Characters
- **麦格教授** (Giáo sư McGonagall) - nữ, giáo viên nghiêm khắc
  - Học sinh gọi bà một cách trang trọng (thầy/cô giáo)

# Narrator
- Người kể chuyện ngôi thứ nhất, nữ
- Phong cách kể chuyện thân mật
- Kể ở thì quá khứ

# Terminology
## Common Terms
- **魔法棒** → đũa phép
- **咒语** → phép thuật
- **施法** → thi triển phép thuật
- **黑魔法** → Nghệ thuật Hắc ám

## Places
- **霍格沃茨** → Hogwarts
- **大礼堂** → Đại sảnh
- **魔法部** → Bộ Pháp thuật

# Relationships and Forms of Address
- Học sinh → Giáo viên: trang trọng (thầy/cô), danh hiệu (giáo sư, thầy/cô)
- Bạn bè → Bạn bè: thân mật, gọi tên
- Người lớn → Trẻ em: thân mật hoặc trang trọng tùy mối quan hệ

# Style Notes
- Đối thoại: kết hợp trang trọng và thân mật dựa trên mối quan hệ
- Tường thuật: tiếng Việt văn chương, thì quá khứ
- Thời gian: tiếng Việt hiện đại (không dùng từ cổ)

**Important**: Always record both the original Chinese term and its Vietnamese translation. This helps maintain consistency - when you encounter the Chinese term in future chunks, you'll know exactly which Vietnamese translation to use.

**WARNING**: NEVER use placeholders like "(không thay đổi)" or "no changes" in ANY section of the memory. Every section must contain FULL information, even if nothing new was added. Using placeholders or "without changes" notations will cause critical information loss because only the memory field propagates to future iterations.

The above is only an example of how memory can be formatted. You are free to use the format that most suits your needs and the specifics of the text you are translating at the moment.

## main_text

This part of the input JSON contains chunks of the text that must be translated.

# Output format

You have to respond with a JSON with the following structure:
{
  "memory": str,
  "translated_text": {
    "1": str,
    "2": str,
    ...
  }
}

The memory field must contain the updated memory with information for further translation in the upcoming iterations.
Remember, you won't see your translation from the current iteration. The only thing that will propagate to the next iteration is the "memory".
Make sure it contains all the necessary information to ensure authentic, consistent and high-quality translation to Vietnamese.

The translated_text field mirrors the "main_text" field in the input JSON with corresponding chunk keys. They must contain corresponding translations to Vietnamese.

# Translation Guidelines

- Translate from Chinese to Vietnamese naturally and fluently
- Maintain the original tone and style of the Chinese text
- Use appropriate Vietnamese grammar and sentence structure
- For Chinese proper names, use established Vietnamese transliterations or keep the original if no standard exists
- Use modern Vietnamese (avoid archaic terms unless the Chinese source text is classical)
- Maintain consistency with previous translations using the memory
- Pay attention to Chinese cultural context and adapt appropriately for Vietnamese readers
`;

export class Translator {
    constructor({
        model = 'gpt-4o-mini',
        temperature = 0.1,
        windowSize = 30,
        overlap = 10,
        apiKey = process.env.OPENAI_API_KEY,
        baseURL = 'https://api.openai.com/v1'
    } = {}) {
        this.memory = "";
        this.model = model;
        this.windowSize = windowSize;
        this.overlap = overlap;
        this.apiKey = apiKey;
        this.baseURL = baseURL;

        // Validate parameters
        if (windowSize >= 80) {
            throw new Error('Window size must be less than 80 due to API limitations');
        }
        if (overlap >= windowSize) {
            throw new Error('Overlap cannot be more than or equal to window size');
        }

        // Initialize chat model based on service type
        this.initializeChatModel(model, temperature, apiKey, baseURL);

        // Create prompt template - use raw string to avoid template parsing issues
        this.systemPrompt = MAIN_INSTRUCTION + "\n\nIMPORTANT: You must respond with valid JSON only. Do not include any text outside the JSON structure.";

        // Initialize JSON parser (not used directly but kept for compatibility)
        this.outputParser = new JsonOutputParser();
    }

    initializeChatModel(model, temperature, apiKey, baseURL) {
        // Determine service type from baseURL or model name
        const isOpenAICompatible = this.isOpenAICompatibleService(baseURL);

        if (isOpenAICompatible) {
            // Use OpenAI-compatible API (OpenAI, DeepSeek, Claude via OpenRouter, etc.)
            this.chatModel = new ChatOpenAI({
                modelName: model,
                temperature: temperature,
                openAIApiKey: apiKey,
                configuration: {
                    baseURL: baseURL
                },
                modelKwargs: {
                    response_format: { type: "json_object" }
                }
            });
        } else {
            // For other services, we'll use a generic approach
            throw new Error(`Unsupported service for baseURL: ${baseURL}`);
        }
    }

    isOpenAICompatibleService(baseURL) {
        // Check if the service uses OpenAI-compatible API
        const openAICompatibleServices = [
            'api.openai.com',
            'api.deepseek.com',
            'openrouter.ai',
            'api.anthropic.com' // Claude via OpenRouter
        ];

        return openAICompatibleServices.some(service => baseURL.includes(service));
    }

    buildPrompt(chunks) {
        const chunksDict = {};
        chunks.forEach((chunk, index) => {
            chunksDict[String(index + 1)] = chunk;
        });

        const promptDict = {
            memory: this.memory,
            main_text: chunksDict
        };

        return JSON.stringify(promptDict, null, 2);
    }

    async generateResponse(prompt) {
        const maxRetries = 3;
        let lastError = null;
        
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                console.log(`Attempt ${attempt}/${maxRetries} - Generating response...`);
                
                // Create messages manually to avoid template parsing issues
                const messages = [
                    { role: "system", content: this.systemPrompt },
                    { role: "user", content: prompt }
                ];
                
                // Use ChatOpenAI directly with messages
                const response = await this.chatModel.invoke(messages);
                
                console.log(`Response received, length: ${response.content.length} characters`);
                
                // Clean the response content
                let cleanContent = response.content.trim();
                
                // Remove any markdown code blocks if present
                if (cleanContent.startsWith('```json')) {
                    cleanContent = cleanContent.replace(/^```json\s*/, '').replace(/\s*```$/, '');
                }
                if (cleanContent.startsWith('```')) {
                    cleanContent = cleanContent.replace(/^```\s*/, '').replace(/\s*```$/, '');
                }
                
                // Try to find JSON object in the response
                const jsonStart = cleanContent.indexOf('{');
                const jsonEnd = cleanContent.lastIndexOf('}');
                
                if (jsonStart === -1 || jsonEnd === -1 || jsonStart >= jsonEnd) {
                    throw new Error('No valid JSON object found in response');
                }
                
                const jsonString = cleanContent.substring(jsonStart, jsonEnd + 1);
                
                // Parse JSON response
                let parsedResponse;
                try {
                    parsedResponse = JSON.parse(jsonString);
                } catch (parseError) {
                    console.error('JSON parse error:', parseError.message);
                    console.error('Attempting to parse:', jsonString.substring(0, 1000) + '...');
                    throw parseError;
                }
                
                // Validate response structure
                if (!parsedResponse.memory || !parsedResponse.translated_text) {
                    throw new Error('Response missing required fields: memory or translated_text');
                }
                
                if (typeof parsedResponse.memory !== 'string') {
                    throw new Error('Memory field must be a string');
                }
                
                if (typeof parsedResponse.translated_text !== 'object') {
                    throw new Error('Translated_text field must be an object');
                }
                
                console.log(`Response parsed successfully, memory length: ${parsedResponse.memory.length}`);
                return parsedResponse;
                
            } catch (error) {
                console.error(`Attempt ${attempt} failed:`, error.message);
                lastError = error;
                
                if (attempt < maxRetries) {
                    console.log(`Retrying in 2 seconds...`);
                    await new Promise(resolve => setTimeout(resolve, 2000));
                }
            }
        }
        
        console.error('All attempts failed. Last error:', lastError);
        throw lastError;
    }

    generateSlidingWindows(chunks) {
        const windows = [];
        let i = 0;
        
        while (i < chunks.length) {
            const window = chunks.slice(i, i + this.windowSize);
            windows.push({ window, startIdx: i });
            
            i += this.windowSize - this.overlap;
        }
        
        return windows;
    }

    extractRelevantTranslations(windowTranslated, startIdx) {
        if (startIdx === 0) {
            return windowTranslated;
        }
        
        return windowTranslated.slice(this.overlap);
    }

    async translateChunks(chunks, onProgress = null) {
        const translatedChunks = [];
        const windows = this.generateSlidingWindows(chunks);
        
        console.log(`Processing ${windows.length} windows for ${chunks.length} chunks`);
        
        for (let i = 0; i < windows.length; i++) {
            const { window, startIdx } = windows[i];
            
            if (onProgress) {
                onProgress(i + 1, windows.length, `Processing window ${i + 1}/${windows.length}`, this.memory);
            }
            
            console.log(`Translating window ${i + 1}/${windows.length} (${window.length} chunks)`);
            
            const prompt = this.buildPrompt(window);
            
            try {
                const modelResponse = await this.generateResponse(prompt);
                const { translated_text: translation, memory } = modelResponse;
                
                this.memory = memory;
                
                // Convert numbered dictionary to ordered list
                const windowTranslated = [];
                for (let j = 1; j <= window.length; j++) {
                    if (translation[String(j)]) {
                        windowTranslated.push(translation[String(j)]);
                    } else {
                        console.warn(`Missing translation for chunk ${j} in window ${i + 1}`);
                        // windowTranslated.push(`[MISSING TRANSLATION ${j}]`);
                    }
                }
                
                // Skip overlapping chunks to avoid duplicates
                const relevantTranslations = this.extractRelevantTranslations(windowTranslated, startIdx);
                translatedChunks.push(...relevantTranslations);
                
                console.log(`Completed window ${i + 1}/${windows.length}`);
                console.log(`Memory length: ${this.memory.length} characters`);
                console.log(`Translated chunks in this window: ${relevantTranslations.length}`);
                
            } catch (error) {
                console.error(`Error processing window ${i + 1}:`, error);
                
                // Add placeholder translations for this window to continue processing
                const windowTranslated = window.map((_, idx) => `[ERROR TRANSLATING CHUNK ${startIdx + idx + 1}]`);
                const relevantTranslations = this.extractRelevantTranslations(windowTranslated, startIdx);
                translatedChunks.push(...relevantTranslations);
                
                console.log(`Added ${relevantTranslations.length} error placeholders for window ${i + 1}`);
            }
        }
        
        return translatedChunks;
    }

    // Utility method to split text into sentences/paragraphs
    static splitTextIntoChunks(text, chunkType = 'paragraph') {
        if (chunkType === 'paragraph') {
            return text.split(/\n\s*\n/).filter(chunk => chunk.trim().length > 0);
        } else if (chunkType === 'sentence') {
            return text.split(/[.!?]+/).filter(chunk => chunk.trim().length > 0);
        } else {
            throw new Error('chunkType must be either "paragraph" or "sentence"');
        }
    }

    // Utility method to join translated chunks back into text
    static joinTranslatedChunks(translatedChunks, chunkType = 'paragraph') {
        if (chunkType === 'paragraph') {
            return translatedChunks.join('\n\n');
        } else if (chunkType === 'sentence') {
            return translatedChunks.join('. ');
        } else {
            throw new Error('chunkType must be either "paragraph" or "sentence"');
        }
    }
}