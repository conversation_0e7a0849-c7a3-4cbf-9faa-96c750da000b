import { parseTimeToSeconds } from './utils';

/**
 * Convert seconds to SRT time format (HH:MM:SS,mmm)
 */
export function secondsToSRTTime(seconds) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  const milliseconds = Math.floor((seconds % 1) * 1000);
  
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')},${milliseconds.toString().padStart(3, '0')}`;
}

/**
 * Reorder subtitle IDs after insertion or deletion
 */
export function reorderSubtitleIds(subtitles) {
  return subtitles.map((subtitle, index) => ({
    ...subtitle,
    id: index + 1,
    index: index + 1
  }));
}

/**
 * Adjust subtitle times after insertion
 */
export function adjustSubtitleTimes(subtitles, insertIndex, insertDuration = 2.0) {
  return subtitles.map((subtitle, index) => {
    if (index >= insertIndex) {
      const startTime = parseTimeToSeconds(subtitle.start) + insertDuration;
      const endTime = parseTimeToSeconds(subtitle.end) + insertDuration;
      
      return {
        ...subtitle,
        start: secondsToSRTTime(startTime),
        end: secondsToSRTTime(endTime),
        startTime,
        endTime
      };
    }
    return subtitle;
  });
}

/**
 * Insert a new subtitle at specified position
 */
export function insertSubtitle(subtitles, targetId, position = 'after') {
  const targetIndex = subtitles.findIndex(s => s.id === targetId);
  if (targetIndex === -1) return null;
  
  const targetSubtitle = subtitles[targetIndex];
  const insertIndex = position === 'before' ? targetIndex : targetIndex + 1;
  
  // Calculate new subtitle timing
  let newStartTime, newEndTime;
  const defaultDuration = 2.0; // 2 seconds default
  
  if (position === 'before') {
    // Insert before: new subtitle ends where target starts
    newEndTime = parseTimeToSeconds(targetSubtitle.start);
    newStartTime = Math.max(0, newEndTime - defaultDuration);
    
    // Check if there's a previous subtitle
    if (targetIndex > 0) {
      const prevSubtitle = subtitles[targetIndex - 1];
      const prevEndTime = parseTimeToSeconds(prevSubtitle.end);
      newStartTime = Math.max(prevEndTime + 0.1, newStartTime);
    }
  } else {
    // Insert after: new subtitle starts where target ends
    newStartTime = parseTimeToSeconds(targetSubtitle.end) + 0.1;
    newEndTime = newStartTime + defaultDuration;
    
    // Check if there's a next subtitle
    if (targetIndex < subtitles.length - 1) {
      const nextSubtitle = subtitles[targetIndex + 1];
      const nextStartTime = parseTimeToSeconds(nextSubtitle.start);
      newEndTime = Math.min(nextStartTime - 0.1, newEndTime);
      newStartTime = Math.min(newStartTime, newEndTime - 0.5);
    }
  }
  
  const newSubtitle = {
    id: targetId + (position === 'before' ? 0 : 1), // Temporary ID, will be reordered
    index: targetId + (position === 'before' ? 0 : 1),
    start: secondsToSRTTime(newStartTime),
    end: secondsToSRTTime(newEndTime),
    startTime: newStartTime,
    endTime: newEndTime,
    text: '[New subtitle]',
    translatedText: '',
    status: 'pending',
    isEnabled: true,
    isGenerated: false,
    isGenerated1: false,
    isGenerated2: false,
    isGenerated3: false,
    audioUrl: '',
    audioUrl1: '',
    audioUrl2: '',
    audioUrl3: '',
    duration: 0,
    selectedSpeaker: '',
    speechRate: 0,
    isPlayable: false,
    isVoice: 1
  };
  
  return newSubtitle;
}

/**
 * Split a subtitle into two parts
 */
export function splitSubtitle(subtitle) {
  const startTime = parseTimeToSeconds(subtitle.start);
  const endTime = parseTimeToSeconds(subtitle.end);
  const midTime = (startTime + endTime) / 2;
  
  const text = subtitle.text || '';
  const translatedText = subtitle.translatedText || '';
  
  // Try to split text at a reasonable point
  const textMidPoint = Math.floor(text.length / 2);
  const translatedMidPoint = Math.floor(translatedText.length / 2);
  
  // Find a good split point (space, punctuation)
  let textSplitPoint = textMidPoint;
  let translatedSplitPoint = translatedMidPoint;
  
  // Look for space or punctuation near the middle
  for (let i = textMidPoint; i < text.length && i < textMidPoint + 20; i++) {
    if (/[\s.,!?;:]/.test(text[i])) {
      textSplitPoint = i;
      break;
    }
  }
  
  for (let i = translatedMidPoint; i < translatedText.length && i < translatedMidPoint + 20; i++) {
    if (/[\s.,!?;:]/.test(translatedText[i])) {
      translatedSplitPoint = i;
      break;
    }
  }
  
  const firstPart = {
    ...subtitle,
    end: secondsToSRTTime(midTime),
    endTime: midTime,
    text: text.substring(0, textSplitPoint).trim(),
    translatedText: translatedText.substring(0, translatedSplitPoint).trim()
  };
  
  const secondPart = {
    ...subtitle,
    id: subtitle.id + 1, // Temporary ID, will be reordered
    index: subtitle.index + 1,
    start: secondsToSRTTime(midTime + 0.1),
    end: subtitle.end,
    startTime: midTime + 0.1,
    endTime: endTime,
    text: text.substring(textSplitPoint).trim(),
    translatedText: translatedText.substring(translatedSplitPoint).trim(),
    // Reset audio generation status for the new part
    isGenerated: false,
    isGenerated1: false,
    isGenerated2: false,
    isGenerated3: false,
    audioUrl: '',
    audioUrl1: '',
    audioUrl2: '',
    audioUrl3: '',
    duration: 0
  };
  
  return [firstPart, secondPart];
}

/**
 * Merge two consecutive subtitles
 */
export function mergeSubtitles(firstSubtitle, secondSubtitle) {
  const mergedSubtitle = {
    ...firstSubtitle,
    end: secondSubtitle.end,
    endTime: parseTimeToSeconds(secondSubtitle.end),
    text: `${firstSubtitle.text} ${secondSubtitle.text}`.trim(),
    translatedText: `${firstSubtitle.translatedText || ''} ${secondSubtitle.translatedText || ''}`.trim(),
    // Keep the first subtitle's audio generation status
    // Reset if either subtitle wasn't generated
    isGenerated: firstSubtitle.isGenerated && secondSubtitle.isGenerated,
    isGenerated1: firstSubtitle.isGenerated1 && secondSubtitle.isGenerated1,
    isGenerated2: firstSubtitle.isGenerated2 && secondSubtitle.isGenerated2,
    isGenerated3: firstSubtitle.isGenerated3 && secondSubtitle.isGenerated3,
    // Reset audio URLs since we need to regenerate for merged content
    audioUrl: '',
    audioUrl1: '',
    audioUrl2: '',
    audioUrl3: '',
    duration: 0
  };
  
  return mergedSubtitle;
}

/**
 * Calculate time gap between subtitles
 */
export function calculateTimeGap(subtitle1, subtitle2) {
  const end1 = parseTimeToSeconds(subtitle1.end);
  const start2 = parseTimeToSeconds(subtitle2.start);
  return start2 - end1;
}

/**
 * Validate subtitle timing
 */
export function validateSubtitleTiming(subtitle) {
  const startTime = parseTimeToSeconds(subtitle.start);
  const endTime = parseTimeToSeconds(subtitle.end);
  
  const errors = [];
  
  if (startTime >= endTime) {
    errors.push('Start time must be before end time');
  }
  
  if (endTime - startTime < 0.5) {
    errors.push('Subtitle duration must be at least 0.5 seconds');
  }
  
  if (startTime < 0) {
    errors.push('Start time cannot be negative');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Fix overlapping subtitles
 */
export function fixOverlappingSubtitles(subtitles) {
  const fixed = [...subtitles];
  
  for (let i = 0; i < fixed.length - 1; i++) {
    const current = fixed[i];
    const next = fixed[i + 1];
    
    const currentEnd = parseTimeToSeconds(current.end);
    const nextStart = parseTimeToSeconds(next.start);
    
    if (currentEnd > nextStart) {
      // Overlap detected, adjust current subtitle's end time
      const newEndTime = nextStart - 0.1;
      fixed[i] = {
        ...current,
        end: secondsToSRTTime(newEndTime),
        endTime: newEndTime
      };
    }
  }
  
  return fixed;
}
