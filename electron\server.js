const express = require('express');
const cors = require('cors');
const fs = require('fs');
const path = require('path');
const base64 = require("base64-js");

// Import the capcut-tts-v3.js functionality
const { getSpeakers, synthesizeService } = require('./services');
const morgan = require('morgan');
const { getJson, createUrl } = require('./utils');


// Express server for TTS API
async function startServer(mainWindow) {
  const app = express();
  app.use(cors());
  app.use(express.json());
  app.use(morgan("combined"))
  app.use(express.static('public'));

  // Define the API endpoint for TTS
  app.get('/api/get-speakers', async (req, res) => {
    try {
        const type = req.query.type;
      const speakers = getSpeakers(type);
      res.json({
        success: true,
        data: speakers,
      });
    } catch (error) {
      console.error(error);
      res.status(500).send('Internal Server Error');
    }
  });

  app.post('/api/generate-tts', async (req, res) => {
    try {
    const { text, speaker, typeEngine, audio_config={} } = req.body;
      const data = await synthesizeService(null,{ text, speaker, typeEngine, audio_config });
      res.json(data);
    } catch (error) {
      console.error(error);
        res.status(500).send('Internal Server Error');
    }
  });
  app.post("/api/tiktok-tts", async (req, res) => {
    const { voice, text, sessionId } = req.body;
    const fileName = `${voice}_${Date.now().toString()}`;
    const url = await createUrl(voice, text);

    try {
      const json = await getJson(url, sessionId);
      console.log(json);
      const data = json.data.v_str;
      const bytes = base64.toByteArray(data);
      res
        .setHeader("Content-Disposition", `attachment; filename=${fileName}.mp3`)
        .setHeader("Content-Type", "audio/mpeg")
        .status(200)
        .send(Buffer.from(bytes));
    } catch (error) {
      console.error("Error:", error);
      res.status(500).send("An error occurred.");
    }
  });

app.get('/assets', (req, res) => {
  let filePath = req.query.path;

  if (!filePath) {
    return res.status(400).send('Missing file path');
  }

  // Normalize đường dẫn
  filePath = path.normalize(filePath);

  // Kiểm tra tồn tại
  if (!fs.existsSync(filePath)) {
    return res.status(404).send('File not found');
  }

  // Gửi file về client
  res.sendFile(filePath, (err) => {
    if (err) {
      console.error('Error sending file:', err);
      res.status(500).send('Failed to send file');
    }
  });
});

// Font serving endpoint
app.get('/fonts/:fontName', (req, res) => {
  const { fontName } = req.params;
  const { app } = require('electron');

  // Determine fonts directory path
  const fontsDir = app.isPackaged
    ? path.join(process.resourcesPath, 'app.asar.unpacked', 'static', 'fonts')
    : path.join(app.getAppPath(), 'static', 'fonts');

  const fontPath = path.join(fontsDir, fontName);

  // Security check - ensure the file is within fonts directory
  if (!fontPath.startsWith(fontsDir)) {
    return res.status(403).send('Access denied');
  }

  // Check if file exists
  if (!fs.existsSync(fontPath)) {
    return res.status(404).send('Font not found');
  }

  // Set appropriate content type based on file extension
  const ext = path.extname(fontName).toLowerCase();
  const contentTypes = {
    '.ttf': 'font/ttf',
    '.otf': 'font/otf',
    '.woff': 'font/woff',
    '.woff2': 'font/woff2'
  };

  const contentType = contentTypes[ext] || 'application/octet-stream';
  res.setHeader('Content-Type', contentType);
  res.setHeader('Access-Control-Allow-Origin', '*');

  // Send font file
  res.sendFile(fontPath, (err) => {
    if (err) {
      console.error('Error sending font:', err);
      res.status(500).send('Failed to send font');
    }
  });
});
  // Start the server
  const port = 8082;
  app.listen(port, () => {
    console.log(`Server started on port ${port}`);
  });

  return app;
}

module.exports = startServer;