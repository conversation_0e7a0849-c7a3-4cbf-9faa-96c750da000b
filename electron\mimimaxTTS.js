const axios = require('axios');
const fs = require('fs');
const path = require('path');
const { getCurrentDir } = require('./utils');
const { slugifyText } = require('./utils');

class MimimaxTTS {
  constructor() {
    this.apiUrl = 'https://api.minimax.io/v1/t2a_v2';
    this.defaultVoices = {
      female: 'moss_audio_5214a9b1-1545-11f0-9885-1a8691f98463',
      male: 'moss_audio_e662b4eb-4fe9-11f0-9e4a-3ee691490080'
    };
  }

  /**
   * Generate TTS audio using Mimimax API
   * @param {Object} options - TTS generation options
   * @param {string} options.text - Text to synthesize
   * @param {string} options.apiKey - Mimimax API key
   * @param {string} options.groupId - Mimimax Group ID
   * @param {string} options.voiceId - Voice ID to use
   * @param {Object} options.voiceSettings - Voice settings (speed, volume, pitch)
   * @param {Object} options.audioSettings - Audio settings (sample_rate, bitrate, format, channel)
   * @returns {Promise<Object>} Result with success status and audio file path
   */
  async generateTTS(options) {
    const {
      text,
      apiKey,
      groupId,
      voiceId = this.defaultVoices.female,
      voiceSettings = {},
      audioSettings = {}
    } = options;

    if (!text || !apiKey || !groupId) {
      throw new Error('Missing required parameters: text, apiKey, or groupId');
    }

    const apiUrl = `${this.apiUrl}?GroupId=${groupId}`;

    // Default voice settings
    const defaultVoiceSettings = {
      voice_id: voiceId,
      speed: 1,
      vol: 1,
      pitch: 0
    };

    // Default audio settings
    const defaultAudioSettings = {
      sample_rate: 32000,
      bitrate: 128000,
      format: 'mp3',
      channel: 1
    };

    const payload = {
      model: 'speech-02-hd', // or 'speech-02-turbo' for faster generation
      text: text,
      stream: false,
      voice_setting: { ...defaultVoiceSettings, ...voiceSettings },
      audio_setting: { ...defaultAudioSettings, ...audioSettings }
    };

    const headers = {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json'
    };

    try {
      console.log('Calling Mimimax TTS API...', { text: text.substring(0, 50) + '...', voiceId });

      const response = await axios.post(apiUrl, payload, { headers });
      const res = response.data;

      if (res.base_resp?.status_code !== 0) {
        throw new Error(`Mimimax API error: ${res.base_resp?.status_msg || 'Unknown error'}`);
      }

      // Create output directory
      const outputDir = getCurrentDir();
      fs.mkdirSync(outputDir, { recursive: true });

      // Save audio file from hex data
      const hexAudio = res.data.audio;
      const format = res.extra_info?.audio_format || audioSettings.format || 'mp3';
      const audioFileName = `mimimax_${slugifyText(text).slice(0, 24)}_${Date.now()}.${format}`;
      const audioFilePath = path.join(outputDir, audioFileName);

      fs.writeFileSync(audioFilePath, Buffer.from(hexAudio, 'hex'));
      console.log('✅ Mimimax audio saved at:', audioFilePath);

      // Handle subtitle file if available
      let subtitleFilePath = null;
      const subtitleUrl = res.data.subtitle_file;
      if (subtitleUrl) {
        try {
          const subtitleFileName = `mimimax_subtitle_${Date.now()}.srt`;
          subtitleFilePath = path.join(outputDir, subtitleFileName);
          const subtitleResponse = await axios.get(subtitleUrl, { responseType: 'stream' });

          const writer = fs.createWriteStream(subtitleFilePath);
          subtitleResponse.data.pipe(writer);

          await new Promise((resolve, reject) => {
            writer.on('finish', resolve);
            writer.on('error', reject);
          });

          console.log('✅ Mimimax subtitle saved at:', subtitleFilePath);
        } catch (subtitleError) {
          console.warn('⚠️ Failed to download subtitle:', subtitleError.message);
        }
      }

      return {
        success: true,
        audioFilePath,
        audioUrl: `file://${audioFilePath}`,
        subtitleFilePath,
        format,
        voiceId: voiceSettings.voice_id || voiceId
      };

    } catch (error) {
      console.error('❌ Mimimax TTS error:', error.message);
      throw new Error(`Mimimax TTS failed: ${error.message}`);
    }
  }

  /**
   * Get available voices (including custom voice clones)
   * @param {string} apiKey - Mimimax API key
   * @param {string} groupId - Mimimax Group ID
   * @returns {Promise<Array>} List of available voices
   */
  async getVoices(apiKey, groupId) {
    // For now, return default voices
    // In the future, this could be extended to fetch custom voice clones from Mimimax API
    return [
      {
        id: this.defaultVoices.female,
        name: 'Female Voice (Default)',
        gender: 'female',
        language: 'vi',
        isDefault: true
      },
      {
        id: this.defaultVoices.male,
        name: 'Male Voice (Default)',
        gender: 'male',
        language: 'vi',
        isDefault: true
      }
    ];
  }

  /**
   * Validate Mimimax API credentials
   * @param {string} apiKey - API key to validate
   * @param {string} groupId - Group ID to validate
   * @returns {Promise<boolean>} True if credentials are valid
   */
  async validateCredentials(apiKey, groupId) {
    try {
      // Test with a simple text
      await this.generateTTS({
        text: 'Test',
        apiKey,
        groupId,
        voiceId: this.defaultVoices.female
      });
      return true;
    } catch (error) {
      console.error('Mimimax credentials validation failed:', error.message);
      return false;
    }
  }
}

module.exports = new MimimaxTTS();