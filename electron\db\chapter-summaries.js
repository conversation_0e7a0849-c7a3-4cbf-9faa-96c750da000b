const moment = require('moment');

class ChapterSummaries {
    constructor(knex) {
        this.knex = knex;
    }

    async createTable() {
        return this.knex.schema.hasTable('chapter_summaries').then((exists) => {
            if (!exists) {
                return this.knex.schema.createTable('chapter_summaries', (table) => {
                    table.increments('id').primary();
                    table.integer('chapter_id').notNullable();
                    table.integer('book_id').notNullable();
                    table.text('original_text').nullable(); // Nội dung gốc từ article.txtBody
                    table.text('summary_text').nullable(); // Nội dung đã tóm tắt
                    table.string('language', 10).defaultTo('vi'); // Ngôn ngữ tóm tắt
                    table.string('status', 20).defaultTo('pending'); // pending, processing, completed, failed
                    table.text('error_message').nullable(); // Lỗi nếu có
                    table.timestamp('created_at').defaultTo(this.knex.fn.now());
                    table.timestamp('updated_at').defaultTo(this.knex.fn.now());
                    table.timestamp('processed_at').nullable();
                    
                    // Indexes
                    table.index(['chapter_id']);
                    table.index(['book_id']);
                    table.index(['status']);
                    table.unique(['chapter_id']); // Mỗi chapter chỉ có 1 summary
                    
                    // Foreign key references
                    table.foreign('chapter_id').references('id').inTable('chapters').onDelete('CASCADE');
                    table.foreign('book_id').references('id').inTable('books').onDelete('CASCADE');
                });
            }
        });
    }

    // Insert or update chapter summary
    async upsertSummary(chapterId, bookId, originalText = null, summaryText = null) {
        const existing = await this.knex('chapter_summaries')
            .where('chapter_id', chapterId)
            .first();

        const data = {
            chapter_id: chapterId,
            book_id: bookId,
            updated_at: moment().format('YYYY-MM-DD HH:mm:ss')
        };

        if (originalText !== null) {
            data.original_text = originalText;
        }

        if (summaryText !== null) {
            data.summary_text = summaryText;
            data.status = 'completed';
            data.processed_at = moment().format('YYYY-MM-DD HH:mm:ss');
        }

        if (existing) {
            // Update existing
            await this.knex('chapter_summaries')
                .where('chapter_id', chapterId)
                .update(data);
            return existing.id;
        } else {
            // Insert new
            data.created_at = moment().format('YYYY-MM-DD HH:mm:ss');
            data.status = originalText ? 'pending' : 'pending';
            
            const [id] = await this.knex('chapter_summaries').insert(data);
            return id;
        }
    }

    // Update status
    async updateStatus(chapterId, status, errorMessage = null) {
        const updateData = {
            status: status,
            updated_at: moment().format('YYYY-MM-DD HH:mm:ss')
        };

        if (errorMessage) {
            updateData.error_message = errorMessage;
        }

        if (status === 'completed') {
            updateData.processed_at = moment().format('YYYY-MM-DD HH:mm:ss');
        }

        return await this.knex('chapter_summaries')
            .where('chapter_id', chapterId)
            .update(updateData);
    }

    // Update summary - specific function for Novel Summarization
    async updateSummary(data) {
        try {
            const { bookId, chapterId, summaryText, memory } = data;

            if (!bookId || !chapterId) {
                throw new Error('Book ID and Chapter ID are required');
            }

            // Check if summary exists
            const existing = await this.knex('chapter_summaries')
                .where('chapter_id', chapterId)
                .where('book_id', bookId)
                .first();

            const updateData = {
                book_id: bookId,
                chapter_id: chapterId,
                summary_text: summaryText || '',
                status: summaryText ? 'completed' : 'pending',
                updated_at: moment().format('YYYY-MM-DD HH:mm:ss')
            };

            if (summaryText) {
                updateData.processed_at = moment().format('YYYY-MM-DD HH:mm:ss');
            }

            // Add memory if provided (for future use)
            if (memory !== undefined) {
                updateData.memory = memory;
            }

            if (existing) {
                // Update existing summary
                const result = await this.knex('chapter_summaries')
                    .where('chapter_id', chapterId)
                    .where('book_id', bookId)
                    .update(updateData);

                console.log(`✅ Summary updated for chapter ${chapterId}, book ${bookId}`);
                return { success: true, chapterId, bookId, updated: true };
            } else {
                // Insert new summary
                updateData.created_at = moment().format('YYYY-MM-DD HH:mm:ss');

                const [id] = await this.knex('chapter_summaries').insert(updateData);

                console.log(`✅ New summary created for chapter ${chapterId}, book ${bookId}, ID: ${id}`);
                return { success: true, chapterId, bookId, id, created: true };
            }
        } catch (error) {
            console.error('Error updating summary:', error);
            throw error;
        }
    }

    // Get summaries by book
    async getSummariesByBook(bookId, limit = 100, offset = 0) {
        return await this.knex('chapter_summaries as cs')
            .join('chapters as c', 'cs.chapter_id', 'c.id')
            .select(
                'cs.*',
                'c.title as chapter_title',
                'c.chapter_index',
                'c.href as chapter_href'
            )
            .where('cs.book_id', bookId)
            .orderBy('c.chapter_index', 'asc')
            .limit(limit)
            .offset(offset);
    }

    // Get summaries by status
    async getSummariesByStatus(status, limit = 50) {
        return await this.knex('chapter_summaries as cs')
            .join('chapters as c', 'cs.chapter_id', 'c.id')
            .join('books as b', 'cs.book_id', 'b.id')
            .select(
                'cs.*',
                'c.title as chapter_title',
                'c.chapter_index',
                'c.href as chapter_href',
                'b.title as book_title'
            )
            .where('cs.status', status)
            .orderBy('cs.created_at', 'desc')
            .limit(limit);
    }

    // Get summary by chapter ID
    async getSummaryByChapter(chapterId) {
        return await this.knex('chapter_summaries')
            .where('chapter_id', chapterId)
            .first();
    }

    // Get chapters without summaries for a book
    async getChaptersWithoutSummaries(bookId, startIndex = null, endIndex = null) {
        let query = this.knex('chapters as c')
            .leftJoin('chapter_summaries as cs', 'c.id', 'cs.chapter_id')
            .select('c.*')
            .where('c.book_id', bookId)
            .whereNull('cs.id'); // Chapters without summaries

        if (startIndex !== null) {
            query = query.where('c.chapter_index', '>=', startIndex);
        }

        if (endIndex !== null) {
            query = query.where('c.chapter_index', '<=', endIndex);
        }

        return await query.orderBy('c.chapter_index', 'asc');
    }

    // Get summary statistics
    async getSummaryStats(bookId = null) {
        let query = this.knex('chapter_summaries');
        
        if (bookId) {
            query = query.where('book_id', bookId);
        }

        const stats = await query
            .select('status')
            .count('id as count')
            .groupBy('status');

        const result = {
            total: 0,
            pending: 0,
            processing: 0,
            completed: 0,
            failed: 0
        };

        stats.forEach(stat => {
            result[stat.status] = stat.count;
            result.total += stat.count;
        });

        return result;
    }

    // Delete summaries by book
    async deleteSummariesByBook(bookId) {
        return await this.knex('chapter_summaries')
            .where('book_id', bookId)
            .del();
    }

    // Get chapters with summaries for export
    async getCompletedSummaries(bookId, format = 'json') {
        const summaries = await this.knex('chapter_summaries as cs')
            .join('chapters as c', 'cs.chapter_id', 'c.id')
            .select(
                'c.chapter_index',
                'c.title as chapter_title',
                'cs.original_text',
                'cs.summary_text',
                'cs.processed_at'
            )
            .where('cs.book_id', bookId)
            .where('cs.status', 'completed')
            .orderBy('c.chapter_index', 'asc');

        if (format === 'text') {
            return summaries.map(s => 
                `Chương ${s.chapter_index}: ${s.chapter_title}\n\n${s.summary_text}\n\n---\n\n`
            ).join('');
        }

        return summaries;
    }

    // Batch update summaries for Novel Summarization
    async batchUpdateSummaries(summaries) {
        try {
            if (!Array.isArray(summaries) || summaries.length === 0) {
                throw new Error('Summaries array is required and cannot be empty');
            }

            const results = [];

            // Use transaction for batch operations
            await this.knex.transaction(async (trx) => {
                for (const summary of summaries) {
                    const { bookId, chapterId, summaryText, memory } = summary;

                    if (!bookId || !chapterId) {
                        throw new Error(`Book ID and Chapter ID are required for summary: ${JSON.stringify(summary)}`);
                    }

                    const updateData = {
                        book_id: bookId,
                        chapter_id: chapterId,
                        summary_text: summaryText || '',
                        status: summaryText ? 'completed' : 'pending',
                        updated_at: moment().format('YYYY-MM-DD HH:mm:ss')
                    };

                    if (summaryText) {
                        updateData.processed_at = moment().format('YYYY-MM-DD HH:mm:ss');
                    }

                    if (memory !== undefined) {
                        updateData.memory = memory;
                    }

                    // Check if exists
                    const existing = await trx('chapter_summaries')
                        .where('chapter_id', chapterId)
                        .where('book_id', bookId)
                        .first();

                    if (existing) {
                        // Update
                        await trx('chapter_summaries')
                            .where('chapter_id', chapterId)
                            .where('book_id', bookId)
                            .update(updateData);

                        results.push({ chapterId, bookId, updated: true });
                    } else {
                        // Insert
                        updateData.created_at = moment().format('YYYY-MM-DD HH:mm:ss');
                        const [id] = await trx('chapter_summaries').insert(updateData);

                        results.push({ chapterId, bookId, id, created: true });
                    }
                }
            });

            console.log(`✅ Batch updated ${results.length} summaries`);
            return { success: true, results, count: results.length };
        } catch (error) {
            console.error('Error in batch update summaries:', error);
            throw error;
        }
    }

    // Get chapters with original text for summarization
    async getChaptersForSummarization(bookId, startChapter = null, endChapter = null) {
        try {
            let query = this.knex('chapters as c')
                .leftJoin('chapter_summaries as cs', 'c.id', 'cs.chapter_id')
                .select(
                    'c.id as chapter_id',
                    'c.chapter_index',
                    'c.title as chapter_title',
                    'c.txtBody as original_text',
                    'cs.summary_text',
                    'cs.status as summary_status'
                )
                .where('c.book_id', bookId)
                .whereNotNull('c.txtBody') // Only chapters with content
                .where('c.txtBody', '!=', ''); // Not empty content

            if (startChapter !== null) {
                query = query.where('c.chapter_index', '>=', startChapter);
            }

            if (endChapter !== null) {
                query = query.where('c.chapter_index', '<=', endChapter);
            }

            const chapters = await query.orderBy('c.chapter_index', 'asc');

            console.log(`📚 Found ${chapters.length} chapters with content for summarization`);
            return chapters;
        } catch (error) {
            console.error('Error getting chapters for summarization:', error);
            throw error;
        }
    }

    // Get summary progress for a book
    async getSummaryProgress(bookId) {
        try {
            const totalChapters = await this.knex('chapters')
                .where('book_id', bookId)
                .whereNotNull('txtBody')
                .where('txtBody', '!=', '')
                .count('id as count')
                .first();

            const completedSummaries = await this.knex('chapter_summaries')
                .where('book_id', bookId)
                .where('status', 'completed')
                .count('id as count')
                .first();

            const total = totalChapters.count || 0;
            const completed = completedSummaries.count || 0;
            const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;

            return {
                bookId,
                total,
                completed,
                remaining: total - completed,
                percentage
            };
        } catch (error) {
            console.error('Error getting summary progress:', error);
            throw error;
        }
    }
}

module.exports = ChapterSummaries;
