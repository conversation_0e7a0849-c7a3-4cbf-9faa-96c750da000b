<template>
  <div class="space-y-4">
    <h2 class="text-xl font-semibold">Whisper Audio Transcription</h2>

    <!-- Video/Audio upload area -->
    <div v-if="!mediaFile">
      <DragDropUpload
        accept="video/*,audio/*"
        :max-size="10 * 1024 * 1024 * 1024"
        :show-preview="false"
        drag-text="Drag and drop video or audio file here"
        drop-text="Drop file here"
        click-text="or click to select file"
        @files-selected="handleMediaSelected"
      />
    </div>

    <!-- Media info and controls -->
    <div v-if="mediaFile" class="space-y-4">
      <!-- Media info -->
      <div class="flex justify-between items-center">
        <div class="text-sm flex items-center px-2 py-1 bg-gray-50 rounded-md flex-1 mr-2">
          <div class="font-medium truncate">
            <span class="text-gray-500 mr-1">File:</span> {{ mediaFile.name }}
          </div>
          <div class="flex items-center gap-1 text-gray-500 ml-2 flex-shrink-0">
            <span class="text-gray-500 mr-1">Type:</span>
            <span>{{ mediaFile.type || 'Unknown' }}</span>
          </div>
        </div>
        <a-button @click="resetMedia" size="small">Reset</a-button>
      </div>

      <!-- Video preview (if it's a video file) -->
      <div v-if="isVideoFile" class="relative rounded-md overflow-hidden bg-black">
        <video
          ref="videoRef"
          :src="mediaUrl"
          controls
          class="w-full max-h-[300px]"
        ></video>
      </div>

      <!-- Audio preview (if it's an audio file) -->
      <div v-if="isAudioFile" class="relative rounded-md overflow-hidden bg-gray-100 p-4">
        <audio
          ref="audioRef"
          :src="mediaUrl"
          controls
          class="w-full"
        ></audio>
      </div>

      <!-- Whisper settings -->
      <div class="space-y-4 p-4 border border-gray-200 rounded-md">
      <a-button
              size="small"
              @click="convertVideo"
              class="ml-2"
              :loading="isConvert"
            >
              Convert Video
            </a-button>
        <h3 class="text-lg font-medium">Transcription Settings</h3>

        <!-- Language selection -->
        <div class="space-y-1">
          <label class="text-sm font-medium">Language</label>
          <a-select v-model:value="language" style="width: 100%">
            <a-select-option value="auto">Auto-detect</a-select-option>
            <a-select-option value="en">English</a-select-option>
            <a-select-option value="zh">Chinese</a-select-option>
            <a-select-option value="ja">Japanese</a-select-option>
            <a-select-option value="ko">Korean</a-select-option>
            <a-select-option value="fr">French</a-select-option>
            <a-select-option value="de">German</a-select-option>
            <a-select-option value="es">Spanish</a-select-option>
            <a-select-option value="it">Italian</a-select-option>
            <a-select-option value="ru">Russian</a-select-option>
            <a-select-option value="pt">Portuguese</a-select-option>
            <a-select-option value="vi">Vietnamese</a-select-option>
          </a-select>
        </div>

        <!-- Model selection -->
        <div class="space-y-1">
          <label class="text-sm font-medium">Model</label>
          <a-select v-model:value="model" style="width: 100%">
            <a-select-option value="tiny">Tiny (fastest, least accurate)</a-select-option>
            <a-select-option value="base">Base</a-select-option>
            <a-select-option value="small">Small</a-select-option>
            <a-select-option value="medium">Medium</a-select-option>
            <a-select-option value="large-v2">Large v2</a-select-option>
            <a-select-option value="large-v3">Large v3 (slowest, most accurate)</a-select-option>
          </a-select>
        </div>

        <!-- Model directory -->
        <div class="space-y-1">
          <label class="text-sm font-medium">Model Directory</label>
          <div class="flex space-x-2">
            <a-input v-model:value="srtStore.modelDir" placeholder="Path to Whisper models directory" />
            <a-button @click="selectModelDir">Browse</a-button>
          </div>
          <p class="text-xs text-gray-500">Directory containing Whisper model files</p>
        </div>

        <!-- Convert to WAV option -->
        <div class="flex items-center space-x-2">
          <a-checkbox v-model:checked="convertToWav">Convert to high-quality WAV first</a-checkbox>
          <a-tooltip title="Recommended for better transcription quality">
            <InfoCircleOutlined />
          </a-tooltip>
        </div>
      </div>

      <!-- Process buttons -->
      <div class="flex justify-end space-x-2">
        <a-button
          v-if="isProcessing && currentProcessId"
          danger
          @click="stopProcessing"
        >
          Stop Processing
        </a-button>
        <a-button
          type="primary"
          @click="processMedia"
          :loading="isProcessing"
          :disabled="!mediaFile || isProcessing || !srtStore.modelDir"
        >
          {{ isProcessing ? 'Processing...' : 'Transcribe with Whisper' }}
        </a-button>
      </div>

      <!-- Processing status -->
      <div v-if="isProcessing" class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
        <div class="flex items-center">
          <div class="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-blue-500 mr-2"></div>
          <p class="text-blue-700">{{ processingStatus }}</p>
        </div>
      </div>

      <!-- Result -->
      <div v-if="srtFilePath" class="mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
        <div class="flex justify-between items-center">
          <div>
            <p class="text-green-700 font-medium">Transcription complete!</p>
            <p class="text-sm text-gray-600">SRT file saved to: {{ srtFilePath }}</p>
          </div>
          <div class="space-x-2">
            <a-button type="primary" @click="openSrtFile">Open SRT</a-button>
            <a-button @click="importToSrtTable">Import to SRT Table</a-button>
          </div>
        </div>
      </div>

      <!-- Error -->
      <div v-if="error" class="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
        <p class="text-red-700">Error: {{ error }}</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onBeforeUnmount } from 'vue';
import { message } from 'ant-design-vue';
import { InfoCircleOutlined } from '@ant-design/icons-vue';
import DragDropUpload from './DragDropUpload.vue';
import { useSRTStore } from '../stores/srtStore';
import { useTTSStore } from '../stores/ttsStore';

const srtStore = useSRTStore();
const ttsStore = useTTSStore();

// Reactive state
const mediaFile = ref(null);
const mediaUrl = ref(null);
const language = ref('zh');
const model = ref('large-v3');
const convertToWav = ref(true);
const isProcessing = ref(false);
const processingStatus = ref('');
const currentProcessId = ref(null);
const srtFilePath = ref(null);
const error = ref(null);
const wavFilePath = ref(null);
const isConvert = ref(false);

// Computed properties
const isVideoFile = computed(() => {
  return mediaFile.value && mediaFile.value.type.startsWith('video/');
});

const isAudioFile = computed(() => {
  return mediaFile.value && mediaFile.value.type.startsWith('audio/');
});

// Handle media selection
const handleMediaSelected = (files) => {
  if (files && files.length > 0) {
    handleMediaFile(files[0]);
  }
};

// Process media file
const handleMediaFile = (file) => {
  // Check file type
  if (!file.type.startsWith('video/') && !file.type.startsWith('audio/')) {
    message.error('Invalid file type. Please select a video or audio file.');
    return;
  }

  // Revoke old URL to avoid memory leaks
  if (mediaUrl.value) {
    URL.revokeObjectURL(mediaUrl.value);
  }

  mediaFile.value = file;
  mediaUrl.value = `file://${file.path}`;

  // Reset result and error
  srtFilePath.value = null;
  error.value = null;
  wavFilePath.value = null;
};

// Reset media
const resetMedia = () => {
  if (mediaUrl.value) {
    URL.revokeObjectURL(mediaUrl.value);
  }

  mediaFile.value = null;
  mediaUrl.value = null;
  srtFilePath.value = null;
  error.value = null;
  wavFilePath.value = null;

  // If there's an active process, stop it
  if (currentProcessId.value && isProcessing.value) {
    stopProcessing();
  } else {
    currentProcessId.value = null;
  }
};

// Select model directory
const selectModelDir = async () => {
  try {
    const result = await window.electronAPI.openFileDialog({
      properties: ['openDirectory']
    });

    if (!result.canceled && result.filePaths && result.filePaths.length > 0) {
      // modelDir.value = result.filePaths[0];
      srtStore.setModelDir(result.filePaths[0]);
    }
  } catch (err) {
    console.error('Error selecting model directory:', err);
    message.error('Error selecting model directory');
  }
};

// Process media with Whisper
const processMedia = async () => {
  if (!mediaFile.value || !srtStore.modelDir) return;

  isProcessing.value = true;
  error.value = null;
  srtFilePath.value = null;
  currentProcessId.value = null;

  try {
    let inputFile = mediaFile.value.path;

    // Step 1: Convert to WAV if needed
    // if (convertToWav.value) {
    //   processingStatus.value = 'Converting to high-quality WAV...';

    //   const conversionResult = await window.electronAPI.convertVideoToWav({
    //     input: mediaFile.value.path
    //   });

    //   if (conversionResult.success) {
    //     currentProcessId.value = conversionResult.processId;
    //     wavFilePath.value = conversionResult.outputPath;

    //     // Wait for conversion to complete
    //     await checkProcessStatus();

    //     // Use the WAV file as input
    //     inputFile = wavFilePath.value;
    //   } else {
    //     throw new Error(conversionResult.error || 'Failed to convert to WAV');
    //   }
    // }
    // Step 2: Run Demucs to separate vocals
    processingStatus.value = 'Separating vocals...';
    const outputFile = inputFile//wavFilePath.value;

    // check if demucs file vocalsFile exists, not call demucs
    const res = await electronAPI.checkHtdemucsFileExists(inputFile)
    if (res.vocalsFile) {
      inputFile = res.vocalsFile;
      processingStatus.value = 'Vocals separated';
    } else {
      const demucsResult = await window.electronAPI.demucs({
        fileInput: inputFile
      });

      if (demucsResult.success) {
        currentProcessId.value = demucsResult.processId;
        inputFile = demucsResult.vocalsFile;

        // Wait for demucs to complete
        await checkProcessStatus();
      } else {
        throw new Error(demucsResult.error || 'Failed to run Demucs');
      }
    }



    // Step 3: Process with Whisper
    processingStatus.value = 'Transcribing with Whisper...';
    // file output
    console.log('Whisper input', inputFile,outputFile);
    const whisperResult = await window.electronAPI.processAudioWhisper({
      fileInput: inputFile,
      language: language.value,
      model: model.value,
      model_dir: srtStore.modelDir,
      outputFile
    });

    if (whisperResult.success) {
      currentProcessId.value = whisperResult.processId;
      srtFilePath.value = whisperResult.srtFilePath;

      // Wait for processing to complete
      await checkProcessStatus();

      message.success('Transcription completed successfully');
    } else {
      throw new Error(whisperResult.error || 'Failed to process with Whisper');
    }
  } catch (err) {
    error.value = err.message || 'Unknown error occurred';
    message.error('Error processing media: ' + error.value);
  } finally {
    isProcessing.value = false;
    processingStatus.value = '';
  }
};

// Stop processing
const stopProcessing = async () => {
  if (!currentProcessId.value) return;

  try {
    const result = await window.electronAPI.stopProcess(currentProcessId.value);

    if (result.success) {
      message.success('Processing stopped');
      isProcessing.value = false;
      currentProcessId.value = null;
    } else {
      message.error('Failed to stop processing: ' + (result.error || 'Unknown error'));
    }
  } catch (err) {
    message.error('Error stopping process: ' + (err.message || 'Unknown error'));
  }
};

// Check process status periodically
const checkProcessStatus = async () => {
  if (!currentProcessId.value || !isProcessing.value) return;

  try {
    const result = await window.electronAPI.getActiveProcesses();

    if (result.success) {
      // Check if our process is still running
      const isRunning = result.processes.some(p => p.processId === currentProcessId.value);

      if (!isRunning) {
        // Process completed
        return;
      } else {
        // Check again after 2 seconds
        await new Promise(resolve => setTimeout(resolve, 2000));
        return await checkProcessStatus();
      }
    }
  } catch (err) {
    console.error('Error checking process status:', err);
    // Continue checking even if there's an error
    await new Promise(resolve => setTimeout(resolve, 2000));
    return await checkProcessStatus();
  }
};

// Open SRT file
const openSrtFile = async () => {
  if (srtFilePath.value) {
    await window.electronAPI.openFile(srtFilePath.value);
  }
};

// Import to SRT Table
const importToSrtTable = async () => {
  if (!srtFilePath.value) return;

  try {
    // Read the SRT file
    const result = await window.electronAPI.readFile({ filePath: srtFilePath.value });

    if (result.success) {
      // Create a File object from the content
      const fileName = srtFilePath.value.split(/[\/\\]/).pop(); // Get filename without path
      const file = new File(
        [result.content],
        fileName,
        { type: 'application/x-subrip' }
      );
      const pseudoFile = {
        name: fileName,
        type: 'application/x-subrip',
        content: result.content,
        path: srtFilePath.value,
      };
      // Set the path property for the file
      // file.path = srtFilePath.value;
      console.log('File path:', pseudoFile);

      // Import to SRT Table
      await srtStore.processSrtFile(pseudoFile);

      const data = {
          "name": "Ẩn mình dưới lòng đất.srt",
          "type": "application/x-subrip",
          "content": "1\r\n00:00:00,000 --> 00:00:02,620\r\nNgay lúc này, tôi đang ẩn sâu dưới lòng đất 800 mét.\r\n\r\n2\r\n00:00:02,680 --> 00:00:04,560\r\nThở hồn hèn, toàn thân toát mồ hôi lạnh.\r\n\r\n3\r\n00:00:04,700 --> 00:00:07,100\r\nVừa rồi sau khi tăng hình, tôi thực sự đã quay lại.\r\n\r\n4\r\n00:00:07,200 --> 00:00:10,160\r\nQua vài lần thăm dò, tôi đã xác định mình không phải là đối thủ của hắn.\r\n\r\n5\r\n00:00:10,620 --> 00:00:13,400\r\nDù có dốc toàn lực, tỷ lệ thắng cũng không quá 3 phần 10.\r\n\r\n",
          "path": "F:\\ReviewDao\\11-05\\ran-quy\\export\\Ẩn mình dưới lòng đất.srt"
      }

      // Switch to SRT Table tab
      // This would require a way to communicate with the parent component
      message.success('Imported to SRT Table');
    } else {
      throw new Error(result.error || 'Failed to read SRT file');
    }
  } catch (err) {
    message.error('Error importing to SRT Table: ' + (err.message || 'Unknown error'));
  }
};
async function convertVideo() {
  try {
    isConvert.value = true;
    const result = await window.electronAPI.convertVideoToVideo({
      input: mediaFile.value.path
    });

    if (result.success) {
      // currentProcessId.value = result.processId;

      videoUrl.value = `file://${mediaFile.value.path}`;
      message.success('Convert video successfully!');
 
    } else {
      throw new Error(result.error || 'Failed to convert to WAV');
    }
    isConvert.value = false;
  } catch (err) {
    message.error('Error converting video: ' + (err.message || 'Unknown error'));
    isConvert.value = false;
  }

}

// Clean up on component unmount
onBeforeUnmount(() => {
  if (mediaUrl.value) {
    URL.revokeObjectURL(mediaUrl.value);
  }

  // If there's an active process, stop it
  if (currentProcessId.value && isProcessing.value) {
    stopProcessing();
  }
});
</script>
