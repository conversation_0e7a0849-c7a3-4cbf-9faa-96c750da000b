<template>
  <div class="path-settings">
    <a-card title="Path Settings" class="settings-card">
      <div class="settings-section">
        <!-- Whisper Transcription Path -->
        <div class="setting-item">
          <div class="setting-header">
            <h3 class="setting-title">
              <SoundOutlined class="setting-icon" />
              Whisper Transcription Path
            </h3>
            <p class="setting-description">
              Set the path to Whisper executable for audio transcription
            </p>
          </div>
          
          <div class="path-input-group">
            <a-input
              v-model:value="whisperPath"
              placeholder="Select Whisper executable path..."
              class="path-input"
              :disabled="whisperLoading"
            />
            <a-button
              type="primary"
              @click="selectWhisperPath"
              :loading="whisperLoading"
              class="browse-btn"
            >
              <FolderOpenOutlined />
              Browse
            </a-button>
          </div>
          
          <div class="path-actions">
            <a-button
              @click="saveWhisperPath"
              :disabled="!whisperPath || whisperLoading"
              :loading="whisperSaving"
              type="default"
              size="small"
            >
              <SaveOutlined />
              Save
            </a-button>
            <a-button
              @click="testWhisperPath"
              :disabled="!whisperPath || whisperLoading"
              :loading="whisperTesting"
              size="small"
            >
              <PlayCircleOutlined />
              Test
            </a-button>
          </div>
        </div>

        <a-divider />

        <!-- Video OCR Path -->
        <div class="setting-item">
          <div class="setting-header">
            <h3 class="setting-title">
              <EyeOutlined class="setting-icon" />
              Video OCR Path
            </h3>
            <p class="setting-description">
              Set the path to OCR executable for video text recognition
            </p>
          </div>
          
          <div class="path-input-group">
            <a-input
              v-model:value="ocrPath"
              placeholder="Select OCR executable path..."
              class="path-input"
              :disabled="ocrLoading"
            />
            <a-button
              type="primary"
              @click="selectOcrPath"
              :loading="ocrLoading"
              class="browse-btn"
            >
              <FolderOpenOutlined />
              Browse
            </a-button>
          </div>
          
          <div class="path-actions">
            <a-button
              @click="saveOcrPath"
              :disabled="!ocrPath || ocrLoading"
              :loading="ocrSaving"
              type="default"
              size="small"
            >
              <SaveOutlined />
              Save
            </a-button>
            <a-button
              @click="testOcrPath"
              :disabled="!ocrPath || ocrLoading"
              :loading="ocrTesting"
              size="small"
            >
              <PlayCircleOutlined />
              Test
            </a-button>
          </div>
        </div>
      </div>

      <!-- Status Messages -->
      <div v-if="statusMessage" class="status-message" :class="statusType">
        <CheckCircleOutlined v-if="statusType === 'success'" />
        <ExclamationCircleOutlined v-if="statusType === 'error'" />
        <InfoCircleOutlined v-if="statusType === 'info'" />
        {{ statusMessage }}
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  SoundOutlined,
  EyeOutlined,
  FolderOpenOutlined,
  SaveOutlined,
  PlayCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined
} from '@ant-design/icons-vue'
import {useDialogStore} from '@/stores'

// Reactive data
const whisperPath = ref('')
const ocrPath = ref('')
const dialogStore = useDialogStore()

// Loading states
const whisperLoading = ref(false)
const whisperSaving = ref(false)
const whisperTesting = ref(false)
const ocrLoading = ref(false)
const ocrSaving = ref(false)
const ocrTesting = ref(false)

// Status message
const statusMessage = ref('')
const statusType = ref('info') // 'success', 'error', 'info'

// Load existing paths on mount
onMounted(async () => {
  await loadExistingPaths()
})

// Load existing paths from database
const loadExistingPaths = async () => {
  try {
    const whisperPathValue = await electronAPI.invoke('database', 'Config.getValueByName', 'whisper_path')
    const ocrPathValue = await electronAPI.invoke('database', 'Config.getValueByName', 'ocr_path')
    
    whisperPath.value = whisperPathValue || ''
    ocrPath.value = ocrPathValue || ''
  } catch (error) {
    console.error('Error loading existing paths:', error)
    showStatus('Failed to load existing paths', 'error')
  }
}

// Select Whisper path
const selectWhisperPath = async () => {
  whisperLoading.value = true
  try {
    const result = await dialogStore.showOpenFolder()
    
    if (result) {
      whisperPath.value = result
      showStatus('Whisper path selected', 'success')
    }
  } catch (error) {
    console.error('Error selecting Whisper path:', error)
    showStatus('Failed to select Whisper path', 'error')
  } finally {
    whisperLoading.value = false
  }
}

// Select OCR path
const selectOcrPath = async () => {
  ocrLoading.value = true
  try {
    const result = await dialogStore.showOpenFolder()
    console.log(result);
    
    if (result) {
      ocrPath.value = result
      showStatus('OCR path selected', 'success')
    }
  } catch (error) {
    console.error('Error selecting OCR path:', error)
    showStatus('Failed to select OCR path', 'error')
  } finally {
    ocrLoading.value = false
  }
}

// Save Whisper path
const saveWhisperPath = async () => {
  whisperSaving.value = true
  try {
    await electronAPI.invoke('database', 'Config.updateValueByName', 'whisper_path', whisperPath.value)
    message.success('Whisper path saved successfully')
    showStatus('Whisper path saved successfully', 'success')
  } catch (error) {
    console.error('Error saving Whisper path:', error)
    message.error('Failed to save Whisper path')
    showStatus('Failed to save Whisper path', 'error')
  } finally {
    whisperSaving.value = false
  }
}

// Save OCR path
const saveOcrPath = async () => {
  ocrSaving.value = true
  try {
    await electronAPI.invoke('database', 'Config.updateValueByName', 'ocr_path', ocrPath.value)
    message.success('OCR path saved successfully')
    showStatus('OCR path saved successfully', 'success')
  } catch (error) {
    console.error('Error saving OCR path:', error)
    message.error('Failed to save OCR path')
    showStatus('Failed to save OCR path', 'error')
  } finally {
    ocrSaving.value = false
  }
}

// Test Whisper path
const testWhisperPath = async () => {
  whisperTesting.value = true
  try {
    // Test if file exists and is executable
    const exists = await electronAPI.invoke('fs:existsSync', whisperPath.value)
    if (exists) {
      showStatus('Whisper path is valid', 'success')
      message.success('Whisper path test passed')
    } else {
      showStatus('Whisper path does not exist', 'error')
      message.error('Whisper path test failed')
    }
  } catch (error) {
    console.error('Error testing Whisper path:', error)
    showStatus('Failed to test Whisper path', 'error')
    message.error('Failed to test Whisper path')
  } finally {
    whisperTesting.value = false
  }
}

// Test OCR path
const testOcrPath = async () => {
  ocrTesting.value = true
  try {
    // Test if file exists and is executable
    const exists = await electronAPI.invoke('fs:existsSync', ocrPath.value)
    if (exists) {
      showStatus('OCR path is valid', 'success')
      message.success('OCR path test passed')
    } else {
      showStatus('OCR path does not exist', 'error')
      message.error('OCR path test failed')
    }
  } catch (error) {
    console.error('Error testing OCR path:', error)
    showStatus('Failed to test OCR path', 'error')
    message.error('Failed to test OCR path')
  } finally {
    ocrTesting.value = false
  }
}

// Show status message
const showStatus = (message, type) => {
  statusMessage.value = message
  statusType.value = type
  
  // Auto hide after 3 seconds
  setTimeout(() => {
    statusMessage.value = ''
  }, 3000)
}
</script>

<style scoped>
.path-settings {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.settings-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.settings-section {
  padding: 16px 0;
}

.setting-item {
  margin-bottom: 24px;
}

.setting-header {
  margin-bottom: 16px;
}

.setting-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.setting-icon {
  color: #3b82f6;
  font-size: 18px;
}

.setting-description {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.path-input-group {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.path-input {
  flex: 1;
}

.browse-btn {
  min-width: 100px;
}

.path-actions {
  display: flex;
  gap: 8px;
}

.status-message {
  margin-top: 16px;
  padding: 12px 16px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.status-message.success {
  background-color: #f0f9ff;
  border: 1px solid #bfdbfe;
  color: #1e40af;
}

.status-message.error {
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
}

.status-message.info {
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  color: #475569;
}

/* Responsive design */
@media (max-width: 768px) {
  .path-settings {
    padding: 16px;
  }
  
  .path-input-group {
    flex-direction: column;
  }
  
  .browse-btn {
    width: 100%;
  }
  
  .path-actions {
    flex-direction: column;
  }
  
  .path-actions .ant-btn {
    width: 100%;
  }
}
</style>
