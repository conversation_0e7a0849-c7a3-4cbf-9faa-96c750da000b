import { fileSystem } from "./fileSystem";

class Device {
    constructor() {
        Reflect.defineProperty(this, "md5Main", { value: "827de483c96d4ff4b2e7f552798bda15" });
    }
    async getPlatform() {
        return await window.electronAPI.invoke("process:platform")
    }
    async getHwid() {
        return await window.electronAPI.invoke("pkg:machineIdSync")
    }
    async dnsLookup(t) {
        return await window.electronAPI.invoke("dns:lookup", t)
    }
    async closeApp() {
        return await window.electronAPI.invoke("close-app")
    }
    async appPath() {
        return await window.electronAPI.invoke("app:app-path")
    }
    async getAppProductMode() {
        return await window.electronAPI.invoke("app:app-product-mode")
    }
    async isRunningAsAdmin() {
        try {
            if (await this.getPlatform() !== "win32")
                return !0;
            const r = await this.executeCommand("net session");
            return !0
        } catch {
            return !1
        }
    }
    async executeCommand(t) {
        return await window.electronAPI.invoke("app:child_process:executeCommand", t)
    }
    async spawnCommand(t, r) {
        F.l('spawnCommand',t,r)
        return await window.electronAPI.invoke("app:child_process:spawnCommand", t, r)
    }
    async updateMainApp() {
        return await window.electronAPI.invoke("app:updateMainApp")
    }
    async restartApp() {
        return await window.electronAPI.invoke("app:restartApp")
    }
    async clearCacheAndExit() {
        return await window.electronAPI.invoke("app:clear-cache-and-exit")
    }
    async clipboardReadText() {
        return await window.electronAPI.invoke("electron:clipboard:readText")
    }
    async clipboardReadImage() {
        return await window.electronAPI.invoke("electron:clipboard:readImage")
    }
    async bufferData(t, r="base64") {
        return await window.electronAPI.invoke("app:Buffer", t, r)
    }
    async getRegistryValue(t, r) {
        return await window.electronAPI.invoke("app:winreg:getRegistryValue", t, r)
    }
    async setRegistryValue(t, r, n) {
        return await window.electronAPI.invoke("app:winreg:setRegistryValue", t, r, n)
    }
    async openExternal(t) {
        return await window.electronAPI.invoke("app:shell:openExternal", t)
    }
    async showItemInFolder(t) {
        if (!await fileSystem.existsSync(t))
            throw new Error(`Path ${t} is not exist`);
        return await window.electronAPI.invoke("app:shell:showItemInFolder", t)
    }
}

export const device = new Device();