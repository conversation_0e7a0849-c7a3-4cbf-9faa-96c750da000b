<template>
  <div
    v-if="ttsStore.currentSrtList?.items"
    ref="draggableBox"
    :style="style"
    class="fixed left-4 dark:bg-gray-800 shadow-lg rounded-lg p-2 z-50"
    :class="isHide ? 'h-8' : ''"
  >
    <div class="flex justify-between items-center mb-1  cursor-move" @mousedown="startDrag">
      <h3 class="text-md font-medium">Process {{ countAll }} items</h3>
      <div class="flex items-center gap-2">
        <!-- Icon drag -->
        <div class="cursor-move p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded">
          <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="9" cy="12" r="1"/>
            <circle cx="9" cy="5" r="1"/>
            <circle cx="9" cy="19" r="1"/>
            <circle cx="15" cy="12" r="1"/>
            <circle cx="15" cy="5" r="1"/>
            <circle cx="15" cy="19" r="1"/>
          </svg>
        </div>
        <!-- Nút ẩn xuống -->
        <button @click="hide" class="text-sm text-gray-500 hover:text-gray-700">
          <ChevronDown size="16" v-if="!isHide" />
          <ChevronUp size="16" v-else />
        </button>
      </div>
    </div>
    
    <VideoPlayer
        v-if="!isHide && ttsStore.currentSrtList"
        ref="videoPlayer"
        :src="ttsStore.currentSrtList?.path.replace('-ocr.srt', '.mp4').replace('.srt', '.mp4')"
        @timeupdate="handleVideoTimeUpdate"
        size="50"
        />
    <div class="flex gap-1">
        Dịch <a-progress :percent="translatedText" :format="(percent) => `${percent}%`" :percentPosition="{ align: 'start', type: 'inner' }" :size="[60,10]"  />
    </div>
    <div class="flex gap-1">
        TTS <a-progress :percent="audioProgress" :format="(percent) => `${percent}%`" :percentPosition="{ align: 'start', type: 'inner' }" :size="[60,10]" strokeColor="#B7EB8F" />
    </div>

    <!-- Resize handle -->
    <div 
      v-if="!isHide"
      @mousedown="startResize"
      class="absolute bottom-0 right-0 w-4 h-4 cursor-se-resize bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500 rounded-tl-lg"
      style="clip-path: polygon(100% 0%, 0% 100%, 100% 100%);"
    >
      <!-- Resize icon -->
      <svg class="absolute bottom-0 right-0 w-3 h-3 text-gray-600 dark:text-gray-300" viewBox="0 0 24 24" fill="currentColor">
        <path d="M22,22H20V20H22V22M22,18H20V16H22V18M18,22H16V20H18V22M18,18H16V16H18V18M14,22H12V20H14V22M22,14H20V12H22V14Z" />
      </svg>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, reactive, computed, onBeforeUnmount,onMounted } from 'vue'
import { useI18n } from '@/i18n/i18n';
import { useTTSStore } from '@/stores/ttsStore';
import { Progress } from 'ant-design-vue';
import { ChevronDown, ChevronUp } from 'lucide-vue-next';
import VideoPlayer from './VideoPlayer.vue';
import {state } from '@/lib/state';
import { useSubtitleStore } from '@/stores/subtitle-store';

const { t } = useI18n()
const ttsStore = useTTSStore()
const subtitleStore = useSubtitleStore();

const audioProgress = ref(0)
const translatedText = ref(0)
const isHide = ref(false)
const countAll = ref(0)
const videoPlayer = ref(null)
const activeSubtitleId = ref(null);
const isManualSeek = ref(false);

state.videoPlayer = videoPlayer

watch(() => ttsStore.currentSrtList?.items, (val) => {
  if (val) {
    const translated = val.filter(item => item.status === 'translated').length;
    const total = val.length;
    translatedText.value = Math.floor((translated / total) * 100);
    const generated = val.filter(item => item.isVoice && item.isVoice > 0 && item[`isGenerated${item.isVoice}`]).length;
    audioProgress.value = Math.floor((generated / total) * 100);
    countAll.value = total;
  }
})


// Watch cho external changes (từ UI clicks, buttons, etc.)
watch(() => state.currentPlayingSubtitleId, (newId, oldId) => {
  // Chỉ xử lý khi không phải từ video timeupdate
  if (!isManualSeek.value && newId !== activeSubtitleId.value) {
    activeSubtitleId.value = newId;

    // Seek video đến subtitle được chọn từ bên ngoài
    if (state.videoPlayer?.$refs.video && newId) {
      const subtitle = ttsStore.currentSrtList?.items.find(sub => sub.id === newId);
      if (subtitle) {
        const startTime = timeToSeconds(subtitle.start);
        state.videoPlayer.$refs.video.currentTime = startTime;
      }
    }
  }
  
  // Reset flag
  isManualSeek.value = false;
});


const updateActiveSubtitle = (currentTime) => {
  if (ttsStore.currentSrtList?.items.length === 0) return;

  // Find the subtitle currently being displayed
  const activeSubtitle = ttsStore.currentSrtList?.items.find(sub => {
    const startTime = timeToSeconds(sub.start);
    const endTime = timeToSeconds(sub.end);
    // Extend a bit to avoid boundary cases
    return currentTime >= startTime - 0.1 && currentTime <= endTime + 0.1;
  });

  // Update active subtitle ID
  const newActiveId = activeSubtitle?.id || null;
  if (newActiveId !== activeSubtitleId.value) {
    activeSubtitleId.value = newActiveId;
    // Notify parent component
    // emit('subtitleChange', newActiveId);
  }
};


let timeTrackingInterval = null;

onMounted(() => {
    
  // timeTrackingInterval = setInterval(() => {
  //   if (state.videoPlayer && state.videoPlayer.$refs.video && ttsStore.currentSrtList?.items.length > 0 && !state.videoPlayer.$refs.video.paused) {
  //     updateActiveSubtitle(state.videoPlayer.$refs.video.currentTime);
  //   }
  // }, 100);
});

onBeforeUnmount(() => {
  // Clear interval
  if (timeTrackingInterval) {
    clearInterval(timeTrackingInterval);
  }
  
});




function handleVideoTimeUpdate(time) {
  const currentSubtitle = ttsStore.currentSrtList?.items.find(item => item.startTime <= time && item.endTime >= time);
  
  if (currentSubtitle && currentSubtitle.id !== state.currentPlayingSubtitleId) {
    // console.log('handleVideoTimeUpdate', time, currentSubtitle);
    
    // Đánh dấu rằng đây là thay đổi từ video, không phải manual
    isManualSeek.value = true;
    
    // Cập nhật state
    state.currentPlayingSubtitleId = currentSubtitle.id;
    activeSubtitleId.value = currentSubtitle.id;
  }
}
// === Drag and Resize logic ===
const position = reactive({ bottom: 4, left: 4 });
const size = reactive({ width: 420, height: 'auto' }); // 320px = w-80
const isDragging = ref(false);
const isResizing = ref(false);
let dragOffset = { x: 0, y: 0 };
let resizeStartSize = { width: 0, height: 0 };
let resizeStartPos = { x: 0, y: 0 };
const draggableBox = ref(null);

const style = computed(() => ({
  bottom: `${position.bottom}px`,
  left: `${position.left}px`,
  width: `${size.width}px`,
  minWidth: '280px', // Minimum width
  maxWidth: '600px', // Maximum width
  position: 'fixed',
}));

// Drag functionality
function startDrag(e) {
  // Don't start drag if clicking on resize handle or buttons
  if (e.target.closest('[data-no-drag]') || isResizing.value) return;
  
  isDragging.value = true;
  dragOffset.x = e.clientX - position.left;
  dragOffset.y = window.innerHeight - e.clientY - position.bottom;
  
  document.addEventListener('mousemove', onDrag);
  document.addEventListener('mouseup', stopDrag);
  e.preventDefault();
}

function onDrag(e) {
  if (!isDragging.value) return;
  
  position.left = Math.max(0, Math.min(window.innerWidth - size.width, e.clientX - dragOffset.x));
  position.bottom = Math.max(0, Math.min(window.innerHeight - draggableBox.value?.offsetHeight || 0, window.innerHeight - e.clientY - dragOffset.y));
}

function stopDrag() {
  isDragging.value = false;
  document.removeEventListener('mousemove', onDrag);
  document.removeEventListener('mouseup', stopDrag);
}

// Resize functionality
function startResize(e) {
  isResizing.value = true;
  resizeStartSize.width = size.width;
  resizeStartSize.height = draggableBox.value?.offsetHeight || 0;
  resizeStartPos.x = e.clientX;
  resizeStartPos.y = e.clientY;
  
  document.addEventListener('mousemove', onResize);
  document.addEventListener('mouseup', stopResize);
  e.preventDefault();
  e.stopPropagation();
}

function onResize(e) {
  if (!isResizing.value) return;
  
  const deltaX = e.clientX - resizeStartPos.x;
  const deltaY = e.clientY - resizeStartPos.y;
  
  // Update width
  const newWidth = Math.max(280, Math.min(600, resizeStartSize.width + deltaX));
  size.width = newWidth;
  
  // Adjust position if hitting screen boundaries
  const maxLeft = window.innerWidth - size.width;
  if (position.left > maxLeft) {
    position.left = maxLeft;
  }
}

function stopResize() {
  isResizing.value = false;
  document.removeEventListener('mousemove', onResize);
  document.removeEventListener('mouseup', stopResize);
}

onBeforeUnmount(() => {
  document.removeEventListener('mousemove', onDrag);
  document.removeEventListener('mouseup', stopDrag);
  document.removeEventListener('mousemove', onResize);
  document.removeEventListener('mouseup', stopResize);
});

function hide() {
  isHide.value = !isHide.value;
}



// Convert time from "00:00:00,000" format to seconds
const timeToSeconds = (timeString) => {
  const [time, milliseconds] = timeString.split(',');
  const [hours, minutes, seconds] = time.split(':').map(Number);
  return hours * 3600 + minutes * 60 + seconds + Number(milliseconds) / 1000;
};
</script>