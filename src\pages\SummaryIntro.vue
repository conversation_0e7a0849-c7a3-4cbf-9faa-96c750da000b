<template>
  <div class="flex-1 flex-column p-4 bg-gray-100 dark:bg-gray-900 h-full">
    
    <h1 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
      <a-button type="primary" danger class="mb-1 mr-2" @click="S.$router.replace('/')">Back</a-button> Summary Intro Videos
    </h1>
    <!-- Save Path and Settings Section -->
    <div class="mb-4">
      <!-- Save Path -->
      <div class="mb-3">
        <p class="text-sm font-medium text-gray-900 dark:text-white">
          
      <a-button @click="showSettings">
        <template #icon>
          <setting-outlined />
        </template>
        Settings
      </a-button> Save Path: <span class="font-bold text-blue-600 dark:text-blue-400">{{ settings.pathSave || '<None>' }}</span>
        </p>
      </div>

      <!-- Settings Grid -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <!-- Left Column: Voice & AI Settings -->
        <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-sm p-3 rounded-md">
          <h3 class="text-sm font-semibold text-gray-900 dark:text-white mb-3 border-b border-gray-200 dark:border-gray-600 pb-1">
            Voice & AI Settings
          </h3>

          <a-spin v-if="loadingLanguages" :spinning="true">
            <span>Đang tải ngôn ngữ...</span>
          </a-spin>

          <template v-else>
            <!-- Row 1: Language, Voice Engine, Voice -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-3 mb-3">
              <div>
                <label class="block mb-1 text-xs font-medium text-gray-700 dark:text-gray-300">Language:</label>
                <a-select
                  v-model:value="settings.language"
                  placeholder="Language"
                  size="small"
                  class="w-full"
                  @change="handleLanguageChange"
                >
                  <a-select-option v-for="lang in languages" :key="lang.code" :value="lang.code">
                    {{ lang.name }}
                  </a-select-option>
                </a-select>
              </div>

              <div>
                <label class="block mb-1 text-xs font-medium text-gray-700 dark:text-gray-300">Voice Engine:</label>
                <a-select
                  v-model:value="settings.voiceEngine"
                  placeholder="Engine"
                  size="small"
                  class="w-full"
                  @change="handleVoiceEngineChange"
                >
                  <a-select-option value="edgetts">EdgeTTS</a-select-option>
                  <a-select-option value="capcut">CapCut</a-select-option>
                  <a-select-option value="tiktok">TikTok</a-select-option>
                  <a-select-option value="openai">OpenAI</a-select-option>
                </a-select>
              </div>

              <div>
                <label class="block mb-1 text-xs font-medium text-gray-700 dark:text-gray-300">Voice:</label>
                <a-select
                  v-model:value="settings.voice"
                  placeholder="Voice"
                  size="small"
                  class="w-full"
                  @change="handleVoiceChange"
                >
                  <a-select-option
                    v-for="voice in availableVoices"
                    :key="voice.voiceId || voice.id"
                    :value="voice.voiceId || voice.id"
                  >
                    {{ voice.name }}
                  </a-select-option>
                </a-select>
              </div>
            </div>

            <!-- Row 2: AI Provider, AI Model, Prompt Role -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-3 mb-3">
              <div>
                <label class="block mb-1 text-xs font-medium text-gray-700 dark:text-gray-300">AI Provider:</label>
                <a-select
                  v-model:value="settings.aiProvider"
                  placeholder="Provider"
                  size="small"
                  class="w-full"
                  @change="handleAiProviderChange"
                >
                  <a-select-option
                    v-for="(service, key) in ttsStore.aiServices"
                    :key="key"
                    :value="key"
                    :disabled="!service.enabled && !service.apiKey"
                  >
                    {{ service.name }}
                  </a-select-option>
                </a-select>
              </div>

              <div>
                <label class="block mb-1 text-xs font-medium text-gray-700 dark:text-gray-300">AI Model:</label>
                <a-select
                  v-model:value="settings.aiModel"
                  placeholder="Model"
                  size="small"
                  class="w-full"
                  @change="handleAiModelChange"
                  :loading="loadingModels"
                >
                  <a-select-option v-for="model in availableModels" :key="model" :value="model">
                    {{ model }}
                  </a-select-option>
                </a-select>
              </div>

              <div>
                <label class="block mb-1 text-xs font-medium text-gray-700 dark:text-gray-300">Prompt Role:</label>
                <a-select
                  v-model:value="aiManager.settings.fenjinRoleId"
                  size="small"
                  class="w-full"
                  @change="(value) => changeRoleId(0, value)"
                >
                  <a-select-option v-for="(model, index) in train_list" :key="index" :value="model.id">
                    {{ model.name }}
                  </a-select-option>
                </a-select>
              </div>
            </div>

            <!-- EdgeTTS Settings (if applicable) -->
            <template v-if="settings.voiceEngine === 'edgetts'">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-3 pt-2 border-t border-gray-200 dark:border-gray-600">
                <div>
                  <label class="block mb-1 text-xs font-medium text-gray-700 dark:text-gray-300">
                    Speaking Rate: {{ audioSettings.speakingRate }}
                  </label>
                  <a-slider
                    v-model:value="audioSettings.speakingRate"
                    :min="0.5"
                    :max="2"
                    :step="0.05"
                    size="small"
                  />
                </div>
                <div>
                  <label class="block mb-1 text-xs font-medium text-gray-700 dark:text-gray-300">
                    Voice Pitch: {{ audioSettings.voicePitch }}
                  </label>
                  <a-slider
                    v-model:value="audioSettings.voicePitch"
                    :min="0.1"
                    :max="2"
                    :step="0.05"
                    size="small"
                  />
                </div>
              </div>
            </template>
          <!-- Same sliders as Voice 1 and 2 -->
          <div class="grid grid-cols-2 gap-4" v-if="['tiktok', 'capcut', 'openai'].includes(settings.voiceEngine)">
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.speed') }}
              </label>
              <span class="text-sm text-gray-500">{{ audioSettings.audio_config.speed }}</span>
            </div>
            <a-slider
              v-model:value="audioSettings.audio_config.speed"
              :min="0"
              :max="2"
              :step="0.1"
            />
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.pitch') }}
              </label>
              <span class="text-sm text-gray-500">{{ audioSettings.audio_config.pitch }}</span>
            </div>
            <a-slider
              v-model:value="audioSettings.audio_config.pitch"
              :min="-12"
              :max="12"
              :step="1"
            />
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.reverb') }}
              </label>
              <span class="text-sm text-gray-500">{{ audioSettings.audio_config.reverb }}</span>
            </div>
            <a-slider
              v-model:value="audioSettings.audio_config.reverb"
              :min="0"
              :max="100"
              :step="10"
            />
          </div>
          </template>
        </div>

        <!-- Right Column: Video Settings -->
        <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-sm p-3 rounded-md">
          <h3 class="text-sm font-semibold text-gray-900 dark:text-white mb-3 border-b border-gray-200 dark:border-gray-600 pb-1">
            Video Settings
          </h3>

          <div class="grid grid-cols-1 gap-3">
            <div>
              <label class="block mb-1 text-xs font-medium text-gray-700 dark:text-gray-300">
                Speed Original Video: {{ audioSettings.speedVideoOriginal }}
              </label>
              <a-slider
                v-model:value="audioSettings.speedVideoOriginal"
                :min="0.5"
                :max="2"
                :step="0.05"
                size="small"
              />
            </div>

            <div>
              <label class="block mb-1 text-xs font-medium text-gray-700 dark:text-gray-300">
                Speed Intro Video: {{ audioSettings.speedVideoIntro }}
              </label>
              <a-slider
                v-model:value="audioSettings.speedVideoIntro"
                :min="0.5"
                :max="2"
                :step="0.05"
                size="small"
              />
            </div>

            <div>
              <label class="block mb-1 text-xs font-medium text-gray-700 dark:text-gray-300">
                Transform Intro Video: {{ Math.round(audioSettings.scaleFactor * 100) }}%
              </label>
              <a-slider
                v-model:value="audioSettings.scaleFactor"
                :min="1"
                :max="2"
                :step="0.1"
                size="small"
              />
            </div>

            <!-- Background Music -->
            <div class="pt-2 border-t border-gray-200 dark:border-gray-600">
              <div class="flex items-center gap-2 mb-2">
                <label class="text-xs font-medium text-gray-700 dark:text-gray-300">
                  Background Music:
                </label>
                <a-button
                  size="small"
                  @click="handleChooseBackgroundMusic"
                  :type="audioSettings.backgroundMusic ? 'primary' : 'default'"
                >
                  {{ audioSettings.backgroundMusic ? 'Change Music' : 'Choose Music' }}
                </a-button>
                <a-button
                  v-if="audioSettings.backgroundMusic"
                  size="small"
                  danger
                  @click="handleRemoveBackgroundMusic"
                >
                  Remove
                </a-button>
              </div>

              <div v-if="audioSettings.backgroundMusic" class="text-xs text-gray-600 dark:text-gray-400 mb-2 truncate">
                {{ audioSettings.backgroundMusic.split(/[/\\]/).pop() }}
              </div>

              <div v-if="audioSettings.backgroundMusic">
                <label class="block mb-1 text-xs font-medium text-gray-700 dark:text-gray-300">
                  Music Volume: {{ audioSettings.backgroundMusicVolume }}%
                </label>
                <a-slider
                  v-model:value="audioSettings.backgroundMusicVolume"
                  :min="0"
                  :max="100"
                  :step="5"
                  size="small"
                />
              </div>
            </div>

            <div class="flex items-center gap-2 pt-2">
              <a-checkbox
                id="flipIntroVideo"
                v-model:checked="audioSettings.flipIntroVideo"
                class="cursor-pointer"
              />
              <label
                for="flipIntroVideo"
                class="text-xs font-medium text-gray-700 dark:text-gray-300 cursor-pointer"
              >
                Flip Intro Video
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex flex-wrap gap-2">
      <a-button 
        type="primary"
        :loading="loadingStates.uploadVideoList"
        @click="handleUploadVideoList"
        class="text-white bg-gradient-to-r from-blue-500 to-green-500 hover:from-blue-600 hover:to-green-600 items-center"
      >
        <template #icon>
          <upload-outlined />
        </template>
        Upload Video List
      </a-button>

      <a-button 
        type="primary"
        @click="handleChooseSaveFolder"
        class="text-white bg-gradient-to-r from-green-600 to-teal-600 hover:from-green-700 hover:to-teal-700"
      >
        <template #icon>
          <folder-outlined />
        </template>
        Choose save folder
      </a-button>

      <a-button 
        type="primary"
        :loading="loadingStates.convertAudioToText"
        @click="handleConvertAudioToText"
        class="text-white bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600"
      >
        <template #icon>
          <audio-outlined />
        </template>
        Convert Audio to Text
      </a-button>

      <a-button 
        type="primary"
        :loading="loadingStates.handleSummarizeContent"
        @click="handleSummarizeContent"
        class="text-white bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600"
      >
        <template #icon>
          <file-text-outlined />
        </template>
        Summarize Content
      </a-button>

      <a-button 
        type="primary"
        :loading="loadingStates.handleCreateIntroVideo"
        @click="handleCreateIntroVideo"
        class="text-white bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
      >
        <template #icon>
          <video-camera-outlined />
        </template>
        Create Intro Video
      </a-button>

      <a-button 
        type="primary"
        :loading="loadingStates.handleProcessAll"
        @click="handleProcessAll"
        class="text-white bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700"
      >
        <template #icon>
          <play-circle-outlined />
        </template>
        Process All
      </a-button>


      <a-button 
        type="primary"
        danger
        @click="handleRemoveVideoChecked"
        class="text-white bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700"
      >
        <template #icon>
          <delete-outlined />
        </template>
        Remove Video Checked
      </a-button>

      <!-- TXT File Management Dropdown -->
      <a-dropdown>
        <a-button class="text-white bg-purple-600 hover:bg-purple-700">
          <template #icon>
            <file-text-outlined />
          </template>
          Manage TXT Files
          <down-outlined />
        </a-button>
        <template #overlay>
          <a-menu>
            <a-menu-item @click="handleExportToTxt">
              <template #icon>
                <export-outlined />
              </template>
              Export to TXT
            </a-menu-item>
            <a-menu-item @click="handleImportFromTxt">
              <template #icon>
                <import-outlined />
              </template>
              Import from TXT
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </div>

    <!-- Video List Component -->
    <VideoList
      :video-list="videoList"
      :video-map-checked="videoMapChecked"
      @remove-item="handleRemoveItem"
      @checkbox-change="handleCheckboxChange"
      @text-change="handleTextChange"
      @check-all="handleCheckAll"
    />
    <SettingSummaryModal
      v-model:visible="aiManager.showSettingsModal"
      :onSaveSettings="onSaveSettings"
      :onCancel="() => aiManager.hideSettings()"
    />
  </div>
</template>

<script setup lang="ts">
import {
  UploadOutlined,
  FolderOutlined,
  AudioOutlined,
  FileTextOutlined,
  VideoCameraOutlined,
  PlayCircleOutlined,
  SettingOutlined,
  DeleteOutlined,
  DownOutlined,
  ExportOutlined,
  ImportOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { useVideoSummaryStore } from '@/stores/videoSummary'
import { useLoadingStore } from '@/stores/loading'
import { useErrorStore } from '@/stores/error'
import { useDialogStore } from '@/stores/dialog'
import {edgeVoices,getVoicesByLanguage as edgeGetVoicesByLanguage} from '@/lib/edgettsvoices'
import {useVideoSummary} from '@/hooks/useVideoSummary'
import {useAiManager} from '@/stores/useAiManager'
import { useTTSStore } from '@/stores/ttsStore'
import {fileSystem} from '@/logic/fileSystem'
// import { useAudioStore } from '@/stores/audio'

// Stores
const videoSummaryStore = useVideoSummaryStore()
const loadingStore = useLoadingStore()
const errorStore = useErrorStore()
const dialogStore = useDialogStore()
const aiManager = useAiManager()
const train_list = ref([]);

// const audioStore = useAudioStore()

// Reactive data
const loadingLanguages = ref(false)
const loadingModels = ref(false)
const languages = ref<any[]>([])
const voices = ref(edgeVoices)
const ttsStore = useTTSStore()

const settings = reactive({
  language: 'vi',
  voice: 'vi-VN-NamMinhNeural',
  voiceEngine: 'edgetts', // Default to EdgeTTS to maintain original behavior
  aiProvider: 'gemini', // Default AI provider
  aiModel: 'gemini-1.5-flash', // Default AI model
  pathSave: null as string | null
})

const audioSettings = reactive({
  speakingRate: 1,
  voicePitch: 1,
  speedVideoOriginal: 1,
  speedVideoIntro: 1,
  flipIntroVideo: false,
  scaleFactor: 1,
  backgroundMusic: null as string | null,
  backgroundMusicVolume: 30,
  audio_config:{
    speed: 1,
    pitch: 0,
    reverb: 0
  }
})

// Computed properties
const videoList = computed(() => videoSummaryStore.videoList)
const videoMapChecked = computed(() => videoSummaryStore.videoMapChecked)
const loadingStates = computed(() => loadingStore.loadingStates)
const optionAPITextToSpeech = computed(() => aiManager.settings.optionAPITextToSpeech)
const elevenLabs = computed(() => aiManager.settings.elevenLabs)

// Available voices based on selected engine and existing logic
const availableVoices = computed(() => {
  // If using voice engine selection
  if (settings.voiceEngine && settings.voiceEngine !== 'edgetts') {
    switch (settings.voiceEngine) {
      case 'capcut':
      case 'tiktok':
        return ttsStore.speakers || []
      case 'openai':
        const languageMap: Record<string, string> = {
          'vi': 'vi',
          'en': 'en',
          'zh': 'zh'
        }
        const langCode = languageMap[settings.language] || 'en'
        return ttsStore.getOpenAIVoicesByLanguage(langCode) || []
      default:
        return []
    }
  }

  // Default EdgeTTS logic (keep original behavior)
  if (optionAPITextToSpeech.value === 'EdgeTTS') {
    return voices.value || []
  }

  if (optionAPITextToSpeech.value === 'ElevenLabs') {
    return elevenLabs.value ? elevenLabs.value.filter((voice: any) => voice.language === settings.language) : []
  }

  return voices.value || []
})

// Available models based on selected AI provider
const availableModels = computed(() => {
  if (!settings.aiProvider) return []

  const service = ttsStore.aiServices[settings.aiProvider]
  if (!service) return []

  // Return models if available, otherwise return default models
  if (service.models && service.models.length > 0) {
    return service.models
  }

  return service.defaultModels || []
})

// Methods
const handleTextChange = (id: string, field: string, value: string) => {
  videoSummaryStore.updateVideoField(id, field, value)
}
const { summarizeContent,getTranscriptionVideo,createAndAttachVideoIntro,processVideoSummary } = useVideoSummary({handleTextChange})


const handleRemoveItem = async (video: any) => {
  const confirmed = await dialogStore.showMessageBox({
    message: `Bạn có chắc muốn xóa video '${video.name}'?`,
    buttons: ['Xoá', 'Đóng'],
    defaultId: 0,
    type: 'warning'
  })

  if (confirmed === 0) {
    videoSummaryStore.removeVideo(video.id)
  }
}

const addPathOutput = (id: string, path: string) => {
  videoSummaryStore.addPathOutput(id, path)
}

const handleCheckboxChange = (checkedKeys: string[]) => {
  // Reset all videos to unchecked first
  videoList.value.forEach(video => {
    videoSummaryStore.videoMapChecked[video.id] = false
  })

  // Set checked status for selected keys
  checkedKeys.forEach(id => {
    videoSummaryStore.videoMapChecked[id] = true
  })
}

const handleCheckAll = async (checked: boolean) => {
  if (videoList.value.length === 0) {
    message.warning('Chưa có danh sách video!')
    return
  }
  videoSummaryStore.checkAllVideos(checked)
}

const getSelectedVideos = () => {
  if (videoList.value.length === 0) {
    throw new Error('Chưa có danh sách video!')
  }

  const selectedVideos = videoList.value.filter(video => videoMapChecked.value[video.id])
  if (selectedVideos.length === 0) {
    throw new Error('Vui lòng chọn video!')
  }

  return selectedVideos
}

const generateId = () => {
  return Math.random().toString(36).substr(2, 9)
}

const isApiKeyValid = () => {
  const aiServices:any[] = [
    'gemini',
    'openai',
    'deepseek',
    'openrouter',
    'claude',
  ]
  return aiServices.some(service => {
    const serviceConfig = ttsStore.aiServices[service]
    return serviceConfig && serviceConfig.apiKey && serviceConfig.apiKey.trim()
  })
}

const handleUploadVideoList = async () => {
  try {
    if (!isApiKeyValid()) {
      await dialogStore.showMessageBox({
        title: 'Thông báo',
        message: 'Vui lòng thêm api key cho ít nhất một AI provider trước khi tải video lên.',
      })
      aiManager.showSettings()
      return
    }

    loadingStore.startLoading('uploadVideoList')
    const files = await dialogStore.showOpenFiles(['mp4'])

    if (!files) return

    const videos = await Promise.all(files.map(async (filePath: string) => {
      const name = await window.electronAPI.invoke('path:basename', filePath)
      return {
        id: generateId(),
        name,
        path: filePath,
        pathOutput: []
      }
    }))
    console.log('Uploaded videos:', videos)

    videoSummaryStore.addVideos(videos)
  } catch (error) {
    errorStore.handleError(error, 'alert')
  } finally {
    loadingStore.stopLoading('uploadVideoList')
  }
}

const handleChooseSaveFolder = async () => {
  const folder = await dialogStore.showOpenFolder()
  if (folder) {
    settings.pathSave = folder
  }
}

const handleConvertAudioToText = async () => {
  try {
    loadingStore.startLoading('convertAudioToText')
    const selectedVideos = getSelectedVideos()

    if (!settings.language) {
      throw new Error('Vui lòng chọn ngôn ngữ!')
    }

    for (const video of selectedVideos) {
      const transcription = await getTranscriptionVideo(video.path, settings.language)
      handleTextChange(video.id, 'textAudio', transcription)
    }
  } catch (error) {
    errorStore.handleError(error)
  } finally {
    loadingStore.stopLoading('convertAudioToText')
  }
}

const handleSummarizeContent = async () => {
  try {
    loadingStore.startLoading('handleSummarizeContent')
    const selectedVideos = getSelectedVideos()

    if (!settings.language) {
      throw new Error('Vui lòng chọn ngôn ngữ!')
    }

    if (!settings.aiProvider || !settings.aiModel) {
      throw new Error('Vui lòng chọn AI provider và model!')
    }

    if (selectedVideos.some(video => !video.textAudio)) {
      throw new Error('Vui lòng chọn những video đã được lấy nội dung video!')
    }

    // Update AI settings before summarizing
    aiManager.settings.provider = settings.aiProvider as any
    ttsStore.setSelectedAiService(settings.aiProvider)
    ttsStore.setSelectedModel(settings.aiModel)

    for (const video of selectedVideos) {
      if (!video.textAudio) continue

      const summary = await summarizeContent(video.textAudio, settings.language)
      handleTextChange(video.id, 'textSummary', summary)
    }
  } catch (error) {
    errorStore.handleError(error)
  } finally {
    loadingStore.stopLoading('handleSummarizeContent')
  }
}

const handleCreateIntroVideo = async () => {
  try {
    loadingStore.startLoading('handleCreateIntroVideo')
    const selectedVideos = getSelectedVideos()

    if (!settings.voice || !settings.voiceEngine) {
      throw new Error('Vui lòng chọn voice engine và voice!')
    }

    if (selectedVideos.some(video => !video.textSummary)) {
      throw new Error('Vui lòng chọn những video đã có bản tóm tắt!')
    }

    // Update ttsStore with current settings only if not EdgeTTS
    if (settings.voiceEngine !== 'edgetts') {
      ttsStore.setTypeEngine(settings.voiceEngine)
      ttsStore.setSelectedSpeaker(settings.voice)
    }

    for (const video of selectedVideos) {
      if (!video.textSummary) continue

      const outputPath = await createAndAttachVideoIntro(
        video.textSummary,
        video.path,
        settings.voice,
        settings.pathSave,
        {...audioSettings,typeEngine: settings.voiceEngine}
      )
      addPathOutput(video.id, outputPath)
    }
  } catch (error) {
    errorStore.handleError(error)
  } finally {
    loadingStore.stopLoading('handleCreateIntroVideo')
  }
}

const handleProcessAll = async () => {
  try {
    loadingStore.startLoading('handleProcessAll')
    const selectedVideos = getSelectedVideos()

    if (!settings.language || !settings.voice || !settings.voiceEngine) {
      throw new Error('Vui lòng chọn ngôn ngữ, voice engine và giọng nói!')
    }

    const confirmed = await dialogStore.showMessageBox({
      message: 'Tác vụ này sẽ chạy lại toàn bộ tiến trình, bạn có chắc muốn chạy lại toàn bộ những video đã chọn!',
      buttons: ['Cancel', 'OK'],
      defaultId: 1
    })

    if (confirmed !== 1) return

    // Update ttsStore with current settings only if not EdgeTTS
    if (settings.voiceEngine !== 'edgetts') {
      ttsStore.setTypeEngine(settings.voiceEngine)
      ttsStore.setSelectedSpeaker(settings.voice)
    }

    for (const video of selectedVideos) {
      const outputPath = await processVideoSummary(
        video,
        settings.language,
        settings.voice,
        settings.pathSave,
        {...audioSettings,typeEngine: settings.voiceEngine}
      )
      addPathOutput(video.id, outputPath)
    }
  } catch (error) {
    errorStore.handleError(error)
  } finally {
    loadingStore.stopLoading('handleProcessAll')
  }
}

const handleRemoveVideoChecked = async () => {
  try {
    const selectedVideos = getSelectedVideos()

    const confirmed = await dialogStore.showMessageBox({
      message: 'Bạn có chắc muốn xóa toàn bộ video được chọn?',
      buttons: ['Xoá', 'Đóng'],
      defaultId: 0,
      type: 'warning'
    })

    if (confirmed !== 0) return

    const idsToRemove = selectedVideos.map(video => video.id)
    videoSummaryStore.removeVideos(idsToRemove)
  } catch (error) {
    errorStore.handleError(error)
  }
}

const showSettings = () => {
  aiManager.showSettings()
}

const handleExportToTxt = async () => {
  try {
    if (videoList.value.length === 0) {
      message.info('Chưa có danh sách video!')
      return
    }

    const content = videoList.value.map(video =>
      `VideoName: ${video.name};
Transcription: ${video.textAudio || ''};
Summary: ${video.textSummary || ''};
---
`).join('')

    const randomId = Math.random().toString(36).substr(2, 7)
    const filePath = await dialogStore.showSaveFile(`TranscriptionSummary_${randomId}.txt`, {
      name: 'Text',
      extensions: ['txt']
    })

    if (!filePath) return

    await window.electronAPI.invoke("fs:writeFileSync", filePath, content)
    message.success('Lưu file txt thành công!')
  } catch (error) {
    errorStore.handleError(error)
  }
}

const handleImportFromTxt = async () => {
  try {
    const files = await dialogStore.showOpenFiles(['txt'])
    if (!files) return

    const content = await fileSystem.readFileSyncUtf8(files[0])
    const sections = content.split('---\n')

    const videoData = sections.reduce((acc, section) => {
      const videoName = extractBetween(section, 'VideoName: ', ';\n')
      if (!videoName) return acc

      return {
        ...acc,
        [videoName]: {
          transcription: extractBetween(section, 'Transcription: ', ';\n'),
          summary: extractBetween(section, 'Summary: ', ';\n')
        }
      }
    }, {})

    if (!videoList.value.some(video => videoData[video.name])) {
      message.info('Không tìm thấy thông tin video nào trong tệp!')
      return
    }

    videoSummaryStore.updateVideosFromImport(videoData)
    message.success('Nhập dữ liệu file txt thành công!')
  } catch (error) {
    errorStore.handleError(error)
  }
}

const handleLanguageChange = (value: string) => {
  settings.language = value

  if (optionAPITextToSpeech.value !== 'EdgeTTS') return

  settings.voice = ''
  const languageVoices = edgeGetVoicesByLanguage(value)
  voices.value = languageVoices
}

const handleVoiceEngineChange = async (value: string) => {
  settings.voiceEngine = value
  // Note: The watcher will handle the rest of the logic to avoid duplication
}

const handleAiProviderChange = async (value: any) => {
  if (!value) return

  settings.aiProvider = value
  settings.aiModel = ''

  // Update aiManager settings
  aiManager.settings.provider = value as any

  // Load models for the selected provider
  loadingModels.value = true
  try {
    const service = ttsStore.aiServices[value]
    if (service && service.apiKey) {
      await ttsStore.loadModels(value)

      // Set default model
      if (availableModels.value.length > 0) {
        settings.aiModel = availableModels.value[0]
      }
    } else {
      // Use default models if no API key
      if (service && service.defaultModels && service.defaultModels.length > 0) {
        settings.aiModel = service.defaultModels[0]
      }
    }
  } catch (error) {
    console.error('Error loading models for provider:', value, error)
  } finally {
    loadingModels.value = false
  }
}

const handleAiModelChange = (value: any) => {
  if (!value) return

  settings.aiModel = value
  // Update aiManager or ttsStore if needed
}

const handleVoiceChange = (value: string) => {
  const voice = voices.value.find(v => v.voiceId === value)
  if (voice) {
    let previewUrl = voice.previewUrl
    if (optionAPITextToSpeech.value === 'EdgeTTS') {
      previewUrl = `https://crm.alosoft.vn/accept-tools/AudioEdgeTTS/${voice.voiceId.replaceAll('-', '_')}.mp3`
    }
    // audioStore.playAudio(previewUrl)
  }
  settings.voice = value
}

const onSaveSettings = (newSettings: any) => {
//   settingsStore.updateSettings(newSettings)
}

// Utility functions
const extractBetween = (text: string, start: string, end: string): string => {
  const startIndex = text.indexOf(start)
  if (startIndex === -1) return ''

  const startPos = startIndex + start.length
  const endIndex = text.indexOf(end, startPos)
  if (endIndex === -1) return ''

  return text.substring(startPos, endIndex)
}





const changeRoleId = (index: number, value: number) => {
  aiManager.settings.fenjinRoleId = value
  window.electronAPI.invoke('database', 'Config.set_fenjin_role_id', value);
}

const handleChooseBackgroundMusic = async () => {
  try {
    const files = await dialogStore.showOpenFiles(['mp3', 'wav', 'aac', 'flac', 'm4a', 'ogg'])
    if (files && files.length > 0) {
      audioSettings.backgroundMusic = files[0]
      message.success('Background music selected successfully!')
    }
  } catch (error) {
    errorStore.handleError(error)
  }
}

const handleRemoveBackgroundMusic = () => {
  audioSettings.backgroundMusic = null
  audioSettings.backgroundMusicVolume = 30
  message.success('Background music removed!')
}

const initTrainList = async () => {
  try {
    const gptList = await window.electronAPI.invoke('database', 'GptRoles.getData');
    train_list.value = gptList
    let tmp_fenjin_role_id = aiManager.settings.fenjinRoleId
    if (tmp_fenjin_role_id) {
        for (let tmp_gpt of gptList) {
            if (tmp_fenjin_role_id == tmp_gpt.id) {
                F.l(tmp_gpt.id, 'fenjin');
                aiManager.settings.fenjinRoleId = tmp_gpt.id;
            }
        }
    }
  } catch (error) {
    errorStore.handleError(error)
  }
}

// Lifecycle hooks
onMounted(async () => {
  try {
    // Initialize train list
    await initTrainList()
    // loadApiConfig in
    await aiManager.loadApiConfig()
    loadingLanguages.value = true

    // Initialize AI provider and model
    aiManager.settings.provider = settings.aiProvider as any
    ttsStore.setSelectedAiService(settings.aiProvider)
    ttsStore.setSelectedModel(settings.aiModel)

    // Load models for default AI provider
    try {
      await ttsStore.loadModels(settings.aiProvider)
    } catch (error) {
      console.error('Error loading models for default provider:', error)
    }

    // Initialize voice engine settings
    if (settings.voiceEngine === 'edgetts') {
      // Set EdgeTTS as default
      aiManager.settings.optionAPITextToSpeech = 'EdgeTTS'
      languages.value = await aiManager.getFreeLanguages()
      voices.value = edgeVoices
    } else {
      // Initialize other TTS engines
      aiManager.settings.optionAPITextToSpeech = '3in1'
      ttsStore.setTypeEngine(settings.voiceEngine)
      await ttsStore.fetchSpeakers()

      // Set default voice if available
      if (availableVoices.value.length > 0) {
        const firstVoice = availableVoices.value[0]
        settings.voice = firstVoice.voiceId || firstVoice.id || firstVoice.speaker || ''
      }

      // Load languages for non-EdgeTTS engines
      languages.value = await aiManager.getFreeLanguages()
    }
  } catch (error) {
    errorStore.handleError(error)
  } finally {
    loadingLanguages.value = false
  }
})

// Watchers
watch(() => settings.language, (newLanguage) => {
  if (!newLanguage) return

  // Only update voices for EdgeTTS when language changes
  if (settings.voiceEngine === 'edgetts') {
    settings.voice = ''
    const languageVoices = edgeGetVoicesByLanguage(newLanguage)
    voices.value = languageVoices

    // Set default voice for the language
    if (languageVoices.length > 0) {
      settings.voice = languageVoices[0].voiceId
    }
  }
})

// Watch for voice engine changes to update available voices
watch(() => settings.voiceEngine, async (newEngine, oldEngine) => {
  if (!newEngine || newEngine === oldEngine) return

  // Reset voice selection
  settings.voice = ''

  // If EdgeTTS, keep original behavior
  if (newEngine === 'edgetts') {
    settings.voice = 'vi-VN-NamMinhNeural'
    aiManager.settings.optionAPITextToSpeech = 'EdgeTTS'
    voices.value = edgeVoices
    return
  }

  // Update ttsStore for other engines
  aiManager.settings.optionAPITextToSpeech = '3in1'
  ttsStore.setTypeEngine(newEngine)

  // Fetch speakers for new engine (only if engine actually changed)
  try {
    // Clear existing speakers first to prevent duplication
    ttsStore.speakers = []

    await ttsStore.fetchSpeakers()

    // Wait a bit for speakers to be loaded and then set default voice
    setTimeout(() => {
      if (availableVoices.value.length > 0) {
        const firstVoice = availableVoices.value[0]
        settings.voice = firstVoice.voiceId || firstVoice.id || firstVoice.speaker || ''
      }
    }, 200)

  } catch (error) {
    console.error('Error fetching speakers for engine:', newEngine, error)
  }
})
</script>


