// Hàm mô phỏng di chuyển chuột ngẫu nhiên
async function simulateMouseMovement(page) {
    try {
        // <PERSON><PERSON><PERSON> kích thước trang
        const dimensions = await page.evaluate(() => {
            return {
                width: Math.max(document.documentElement.clientWidth, window.innerWidth || 0),
                height: Math.max(document.documentElement.clientHeight, window.innerHeight || 0),
            };
        });

        // Di chuyển chuột đến 2-3 vị trí
        const numMoves = Math.floor(Math.random() * 2) + 2;

        for (let i = 0; i < numMoves; i++) {
            // Tọa độ ngẫu nhiên
            const x = Math.floor(Math.random() * dimensions.width * 0.8) + dimensions.width * 0.1;
            const y = Math.floor(Math.random() * dimensions.height * 0.8) + dimensions.height * 0.1;

            // Tốc độ di chuyển phù hợp
            const steps = Math.floor(Math.random() * 20) + 10;

            await page.mouse.move(x, y, { steps: steps });

            // Dừng trong khoảng thời gian ngắn
            await page.waitForTimeout(Math.random() * 200 + 50);
        }
    } catch (error) {
        F.l('Error during mouse movement simulation:', error);
    }
}

// Hàm mô phỏng cuộn trang ngẫu nhiên
async function simulateRandomScrolling(page) {
    try {
        // Xác định chiều cao của trang
        const scrollHeight = await page.evaluate(() => document.body.scrollHeight);
        const viewportHeight = await page.evaluate(() => window.innerHeight);

        // Số lần cuộn ngẫu nhiên
        const scrollCount = Math.floor(Math.random() * 3) + 2; // 2-4 lần cuộn

        for (let i = 0; i < scrollCount; i++) {
            // Khoảng cách cuộn ngẫu nhiên (pixel)
            const scrollAmount = Math.floor(Math.random() * viewportHeight * 0.7) + viewportHeight * 0.2;

            // Tốc độ cuộn ngẫu nhiên
            const duration = Math.floor(Math.random() * 300) + 200; // 200-500ms

            // Cuộn xuống
            await page.evaluate(
                (scrollAmount, duration) => {
                    return new Promise((resolve) => {
                        const startPosition = window.pageYOffset;
                        const startTime = performance.now();

                        function scrollStep(timestamp) {
                            const elapsed = timestamp - startTime;
                            const progress = Math.min(elapsed / duration, 1);
                            // Tạo hiệu ứng easing để cuộn tự nhiên hơn
                            const easeInOutQuad =
                                progress < 0.5 ? 2 * progress * progress : 1 - Math.pow(-2 * progress + 2, 2) / 2;

                            window.scrollTo(0, startPosition + scrollAmount * easeInOutQuad);

                            if (progress < 1) {
                                window.requestAnimationFrame(scrollStep);
                            } else {
                                resolve();
                            }
                        }

                        window.requestAnimationFrame(scrollStep);
                    });
                },
                scrollAmount,
                duration,
            );

            // Dừng trong khoảng thời gian ngẫu nhiên
            await page.waitForTimeout(Math.random() * 800 + 300);
        }
    } catch (error) {
        F.l('Error during scroll simulation:', error);
    }
}

// Hàm tạo độ trễ ngẫu nhiên giữa các tác vụ
function getRandomDelay(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

// Hàm mô phỏng viết từng ký tự giống người thật
async function humanTyping(page, selector, text) {
    try {
        // Đặt focus vào input
        await page.click(selector);

        // Xóa nội dung hiện tại (nếu có)
        await page.keyboard.press('Control+A');
        await page.keyboard.press('Backspace');

        // Đợi một chút trước khi bắt đầu gõ
        await page.waitForTimeout(getRandomDelay(300, 800));

        // Gõ từng ký tự với tốc độ khác nhau
        for (let i = 0; i < text.length; i++) {
            // Tốc độ gõ ngẫu nhiên
            const typingDelay = getRandomDelay(30, 150);

            // Gõ ký tự
            await page.keyboard.type(text[i], { delay: typingDelay });

            // Thỉnh thoảng dừng lại lâu hơn (giống người thật thỉnh thoảng dừng để suy nghĩ)
            if (Math.random() < 0.1) {
                // 10% cơ hội dừng lâu hơn
                await page.waitForTimeout(getRandomDelay(400, 1200));
            }
        }

        // Đợi một chút trước khi nhấn Enter
        await page.waitForTimeout(getRandomDelay(300, 1000));
    } catch (error) {
        F.l('Error during human typing simulation:', error);
        // Nếu có lỗi, thử phương pháp đơn giản hơn
        await page.fill(selector, text);
    }
}

// Hàm mô phỏng xem xét kết quả sau khi tìm kiếm
async function simulateResultsExamination(page) {
    try {
        // Di chuyển chuột và tương tác với kết quả tìm kiếm
        const resultItems = await page.$$('.eA0Zlc.WghbWd');

        if (resultItems.length > 0) {
            // Chọn ngẫu nhiên 1-3 kết quả để xem xét
            const numItemsToExamine = Math.min(resultItems.length, Math.floor(Math.random() * 3) + 1);

            // Xem xét từng kết quả được chọn
            for (let i = 0; i < numItemsToExamine; i++) {
                const randomIndex = Math.floor(Math.random() * resultItems.length);
                const item = resultItems[randomIndex];

                // Cuộn đến kết quả
                await item.scrollIntoViewIfNeeded();
                await page.waitForTimeout(getRandomDelay(500, 1200));

                // Di chuyển chuột đến kết quả
                const box = await item.boundingBox();
                if (box) {
                    // Di chuyển đến một vị trí ngẫu nhiên trong kết quả
                    const x = box.x + Math.random() * box.width;
                    const y = box.y + Math.random() * box.height;
                    await page.mouse.move(x, y, { steps: getRandomDelay(20, 40) });
                }

                // Đôi khi dừng lâu hơn như đang đọc kết quả
                await page.waitForTimeout(getRandomDelay(1000, 3000));

                // Tương tác thêm (đôi khi hover vào các phần tử con)
                const childElements = await item.$$('img, .toI8Rb, .EZAeBe');
                if (childElements.length > 0 && Math.random() < 0.7) {
                    const randomChild = childElements[Math.floor(Math.random() * childElements.length)];
                    const childBox = await randomChild.boundingBox();
                    if (childBox) {
                        await page.mouse.move(childBox.x + childBox.width / 2, childBox.y + childBox.height / 2, {
                            steps: getRandomDelay(10, 20),
                        });
                        await page.waitForTimeout(getRandomDelay(300, 800));
                    }
                }
            }
        }
    } catch (error) {
        F.l('Error during results examination simulation:', error);
    }
}

// Hàm kiểm tra nếu đang ở trang CAPTCHA và chờ đợi người dùng giải quyết
async function waitForCaptchaResolution(page) {
    try {
        const hasCaptcha = await page.evaluate(() => {
            return !!document.querySelector('form#captcha-form, img[src*="captcha"], #recaptcha, .g-recaptcha');
        });

        if (hasCaptcha) {
            F.l('CAPTCHA detected! Waiting for user to solve it...');

            // Thông báo cho người dùng
            await page.evaluate(() => {
                if (!document.getElementById('captcha-notification')) {
                    const notification = document.createElement('div');
                    notification.id = 'captcha-notification';
                    notification.style.cssText =
                        'position:fixed;top:0;left:0;right:0;background:red;color:white;padding:15px;text-align:center;z-index:9999;';
                    notification.innerText = 'CAPTCHA detected! Please solve it to continue.';
                    document.body.appendChild(notification);
                }
            });

            // Kiểm tra liên tục nếu CAPTCHA vẫn còn
            let captchaResolved = false;
            let attempts = 0;
            const maxAttempts = 60; // Kiểm tra trong tối đa 5 phút (5 giây * 60)

            while (!captchaResolved && attempts < maxAttempts) {
                await page.waitForTimeout(5000); // Kiểm tra mỗi 5 giây

                captchaResolved = await page.evaluate(() => {
                    // Kiểm tra xem CAPTCHA còn hiển thị không
                    return !document.querySelector('form#captcha-form, img[src*="captcha"], #recaptcha, .g-recaptcha');
                });

                attempts++;
            }

            // Xóa thông báo
            await page.evaluate(() => {
                const notification = document.getElementById('captcha-notification');
                if (notification) notification.remove();
            });

            if (captchaResolved) {
                F.l('CAPTCHA has been resolved by user');
                return true;
            } else {
                F.l('CAPTCHA resolution timeout - user did not solve it in time');
                return false;
            }
        }
        return true; // Không có CAPTCHA
    } catch (error) {
        F.l('Error checking for CAPTCHA:', error);
        return false;
    }
}

// Cập nhật hàm performSearch để sử dụng các tương tác giống người thật
async function humanLikeSearch(page, searchQuery) {
    // Di chuyển chuột trước khi tìm kiếm
    await simulateMouseMovement(page);

    // Thỉnh thoảng cuộn trang trước khi tìm kiếm
    if (Math.random() < 0.4) {
        await simulateRandomScrolling(page);
    }

    // Tìm input tìm kiếm
    const searchInputExists = await page.evaluate(() => {
        return !!document.querySelector('input[name="q"], textarea[name="q"]');
    });

    if (searchInputExists) {
        // Tìm đến input search với chuột
        const searchInput = await page.$('input[name="q"], textarea[name="q"]');
        if (searchInput) {
            const box = await searchInput.boundingBox();
            if (box) {
                // Di chuyển đến gần input trước
                await page.mouse.move(
                    box.x + box.width / 2 + (Math.random() * 20 - 10),
                    box.y - 20 + Math.random() * 10,
                    { steps: getRandomDelay(15, 30) },
                );
                await page.waitForTimeout(getRandomDelay(100, 300));

                // Di chuyển đến input search
                await page.mouse.move(
                    box.x + box.width / 2 + (Math.random() * 20 - 10),
                    box.y + box.height / 2 + (Math.random() * 10 - 5),
                    { steps: getRandomDelay(5, 15) },
                );

                // Nhập từ khóa kiểu người thật
                await humanTyping(page, 'input[name="q"], textarea[name="q"]', searchQuery);

                // Đợi một chút trước khi nhấn Enter
                await page.waitForTimeout(getRandomDelay(300, 800));

                // Nhấn Enter
                await page.keyboard.press('Enter');
            }
        }
    } else {
        // Phương pháp dự phòng
        await page.evaluate((query) => {
            const input = document.querySelector('input[name="q"], textarea[name="q"]');
            if (input) {
                input.value = query;
                input.focus();
                const form = input.closest('form');
                if (form) form.submit();
            }
        }, searchQuery);
    }

    // Đợi trang chuyển hướng
    await page.waitForLoadState('networkidle', { timeout: 30000 });

    // Kiểm tra và chờ CAPTCHA nếu cần
    const captchaResolved = await waitForCaptchaResolution(page);
    if (!captchaResolved) {
        throw new Error('CAPTCHA could not be resolved');
    }

    // Thêm độ trễ để giống hơn
    await page.waitForTimeout(getRandomDelay(800, 1500));

    // Mô phỏng xem xét kết quả sau khi tìm kiếm
    await simulateResultsExamination(page);

    // Có thể thực hiện cuộn trang sau khi xem kết quả
    if (Math.random() < 0.7) {
        await simulateRandomScrolling(page);
    }
}

// Xuất các hàm mô phỏng người dùng để có thể sử dụng ở nơi khác
module.exports = {
    simulateMouseMovement,
    simulateRandomScrolling,
    humanTyping,
    simulateResultsExamination,
    waitForCaptchaResolution,
    humanLikeSearch,
    getRandomDelay,
};
