<template>
  <div
    class="timeline-layer-item absolute cursor-pointer select-none"
    :class="{
      'selected': isSelected,
      'dragging': isDragging,
      'hover': isHovered
    }"
    :style="itemStyle"
    @mousedown="handleMouseDown"
    @mouseenter="isHovered = true"
    @mouseleave="isHovered = false"
    @contextmenu="handleContextMenu"
  >
    <!-- Left resize handle -->
    <div
      class="resize-handle resize-left absolute left-0 top-0 bottom-0 w-2 cursor-ew-resize opacity-0 hover:opacity-100 bg-purple-500"
      @mousedown.stop="handleResizeStart('left', $event)"
    ></div>
    
    <!-- Item content -->
    <div class="item-content h-full px-2 py-1 overflow-hidden">
      <!-- Item header -->
      <div class="flex items-center justify-between text-xs mb-1">
        <span class="font-medium text-white">{{ layer.name }}</span>
        <span class="text-gray-300">{{ formatDuration(duration) }}</span>
      </div>
      
      <!-- Layer type indicator -->
      <div class="text-xs text-gray-200 leading-tight">
        {{ layer.type.toUpperCase() }}
      </div>
      
      <!-- Layer properties preview -->
      <div v-if="layer.properties" class="text-xs text-gray-400 mt-1 truncate">
        <span v-if="layer.type === 'text'">{{ layer.properties.text }}</span>
        <span v-else-if="layer.type === 'image'">{{ layer.properties.src || 'No image' }}</span>
        <span v-else>{{ layer.type }} layer</span>
      </div>
    </div>
    
    <!-- Right resize handle -->
    <div
      class="resize-handle resize-right absolute right-0 top-0 bottom-0 w-2 cursor-ew-resize opacity-0 hover:opacity-100 bg-purple-500"
      @mousedown.stop="handleResizeStart('right', $event)"
    ></div>
    
    <!-- Selection indicator -->
    <div
      v-if="isSelected"
      class="absolute inset-0 border-2 border-purple-500 pointer-events-none"
    ></div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useTimelineStore } from '@/stores/timeline-store'

const props = defineProps({
  layer: {
    type: Object,
    required: true
  }
})

const emit = defineEmits([
  'select',
  'drag-start',
  'drag',
  'drag-end',
  'resize-start',
  'resize',
  'resize-end',
  'context-menu'
])

const timelineStore = useTimelineStore()

// Local state
const isHovered = ref(false)
const isDragging = ref(false)
const dragStartX = ref(0)

// Computed
const isSelected = computed(() => {
  return timelineStore.selectedLayers?.includes(props.layer.id) || false
})

const duration = computed(() => {
  return (props.layer.endTime || props.layer.timeRange?.end || 0) - 
         (props.layer.startTime || props.layer.timeRange?.start || 0)
})

const itemStyle = computed(() => {
  const startTime = props.layer.startTime || props.layer.timeRange?.start || 0
  const endTime = props.layer.endTime || props.layer.timeRange?.end || timelineStore.duration
  const layerDuration = endTime - startTime
  
  const left = timelineStore.timeToPixel(startTime)
  const width = timelineStore.timeToPixel(layerDuration)
  const minWidth = 40 // Minimum width in pixels
  
  return {
    left: left + 'px',
    width: Math.max(width, minWidth) + 'px',
    top: '2px',
    height: '24px',
    backgroundColor: getLayerColor(),
    border: `1px solid ${getLayerBorderColor()}`,
    borderRadius: '4px',
    zIndex: isSelected.value ? 10 : 1
  }
})

// Methods
const getLayerColor = () => {
  if (isSelected.value) {
    return '#8b5cf6' // Purple for selected
  } else if (props.layer.type === 'text') {
    return '#7c3aed' // Purple for text
  } else if (props.layer.type === 'image') {
    return '#ea580c' // Orange for image
  } else if (props.layer.type === 'video') {
    return '#059669' // Green for video
  } else {
    return '#6b7280' // Gray for other types
  }
}

const getLayerBorderColor = () => {
  if (isSelected.value) {
    return '#7c3aed'
  } else if (isHovered.value) {
    return '#a78bfa'
  } else {
    return '#374151'
  }
}

const formatDuration = (seconds) => {
  if (!seconds || isNaN(seconds)) return '0.0s'
  return seconds.toFixed(1) + 's'
}

const handleMouseDown = (event) => {
  if (event.target.classList.contains('resize-handle')) return
  
  emit('select', props.layer, event)
  
  if (event.button === 0) { // Left mouse button
    isDragging.value = true
    dragStartX.value = event.clientX
    
    emit('drag-start', props.layer, event)
    
    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
    document.body.style.cursor = 'grabbing'
    document.body.style.userSelect = 'none'
  }
}

const handleMouseMove = (event) => {
  if (!isDragging.value) return
  
  const deltaX = event.clientX - dragStartX.value
  emit('drag', deltaX)
}

const handleMouseUp = () => {
  if (isDragging.value) {
    isDragging.value = false
    emit('drag-end')
    
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
    document.body.style.cursor = ''
    document.body.style.userSelect = ''
  }
}

const handleResizeStart = (edge, event) => {
  event.preventDefault()
  event.stopPropagation()
  
  emit('resize-start', props.layer, edge, event)
  
  const handleResizeMove = (moveEvent) => {
    const deltaX = moveEvent.clientX - event.clientX
    emit('resize', deltaX)
  }
  
  const handleResizeEnd = () => {
    emit('resize-end')
    document.removeEventListener('mousemove', handleResizeMove)
    document.removeEventListener('mouseup', handleResizeEnd)
    document.body.style.cursor = ''
    document.body.style.userSelect = ''
  }
  
  document.addEventListener('mousemove', handleResizeMove)
  document.addEventListener('mouseup', handleResizeEnd)
  document.body.style.cursor = 'ew-resize'
  document.body.style.userSelect = 'none'
}

const handleContextMenu = (event) => {
  emit('context-menu', event, props.layer)
}
</script>

<style scoped>
.timeline-layer-item {
  transition: all 0.1s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.timeline-layer-item:hover {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
}

.timeline-layer-item.selected {
  box-shadow: 0 0 0 2px #8b5cf6, 0 2px 6px rgba(0, 0, 0, 0.4);
}

.timeline-layer-item.dragging {
  opacity: 0.8;
  transform: scale(1.02);
}

.resize-handle {
  transition: opacity 0.2s ease;
}

.timeline-layer-item:hover .resize-handle {
  opacity: 0.7;
}

.resize-handle:hover {
  opacity: 1 !important;
}

.item-content {
  pointer-events: none;
}
</style>
