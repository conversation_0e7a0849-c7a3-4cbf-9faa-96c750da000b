const path = require('path');
const fs = require('fs');
const { execSync, spawn } = require('child_process');
const { tmpdir } = require('os');
const { v4: uuidv4 } = require('uuid');
const { getAudioDuration } = require('./ffmpegHandler');
const { ffmpegManager } = require('./ffmpeg-config');

/**
 * Xử lý mảng SRT và render video với nhiều tùy chọn nâng cao
 * @param {Array} srtArray - Mảng các đối tượng SRT
 * @param {string} videoPath - Đường dẫn đến video gốc
 * @param {Object} options - <PERSON><PERSON><PERSON> tùy chọn render
 * @returns {Promise<string>} - Đường dẫn đến video đã render
 */
async function processSRTAndRender(srtArray, videoPath, options = {}) {
  // T<PERSON><PERSON> thư mục tạm thời
  const tempDir = path.join(tmpdir(), 'srt_video_' + uuidv4());
  fs.mkdirSync(tempDir, { recursive: true });

  // Mặc định các tùy chọn
  const defaultOptions = {
    outputPath: path.join(path.dirname(videoPath), `output_${path.basename(videoPath)}`),
    subtitleStyle: 'yellow', // yellow, white, black
    addLogo: false,
    logoPath: '',
    logoPosition: 'top-right', // top-left, top-right, bottom-left, bottom-right
    addText: '',
    textPosition: 'bottom', // top, bottom
    videoCodec: 'libx264',
    videoBitrate: '2M',
    audioBitrate: '192k',
    videoQuality: 23, // CRF value (lower is better quality, 18-28 is good range)
    videoPreset: 'medium', // ultrafast, superfast, veryfast, faster, fast, medium, slow, slower, veryslow
    width: -1, // -1 means keep original
    height: -1, // -1 means keep original
    fps: -1, // -1 means keep original
    cleanup: true // xóa thư mục tạm sau khi hoàn thành
  };

  // Kết hợp tùy chọn mặc định với tùy chọn người dùng
  const renderOptions = { ...defaultOptions, ...options };

  try {
    console.log('🔄 Bắt đầu xử lý SRT và render video...');
    console.log('📂 Thư mục tạm:', tempDir);

    // 1. Cập nhật duration từ audio và kiểm tra tính hợp lệ của audio
    console.log('🔄 Đang lấy thông tin thời lượng audio...');
    for (let i = 0; i < srtArray.length; i++) {
      const segment = srtArray[i];

      // Kiểm tra và cập nhật thời lượng audio
      if (segment.audioUrl && segment.audioUrl.trim() !== '' && !segment.audioUrl.includes('undefined')) {
        try {
          const audioPath = segment.audioUrl.replace('file://', '');
          if (fs.existsSync(audioPath)) {
            const dur = await getAudioDuration(audioPath);
            segment.audioDuration = dur;
            segment.hasValidAudio = true;
          } else {
            console.warn(`⚠️ File audio không tồn tại: ${audioPath}`);
            segment.hasValidAudio = false;
          }
        } catch (error) {
          console.error(`❌ Lỗi khi lấy thời lượng audio cho segment ${segment.index}:`, error);
          segment.hasValidAudio = false;
        }
      } else {
        segment.hasValidAudio = false;
      }

      // Tính toán thời lượng video
      segment.videoDuration = segment.endTime - segment.startTime;
    }

    // Đã loại bỏ phần tạo file phụ đề ASS
    console.log('🔄 Bỏ qua việc tạo file phụ đề ASS...');

    // 3. Xử lý từng đoạn video
    console.log('🔄 Đang xử lý từng đoạn video...');
    const segments = [];

    for (let i = 0; i < srtArray.length; i++) {
      const segment = srtArray[i];
      console.log(`🔄 Đang xử lý đoạn ${i+1}/${srtArray.length} (index: ${segment.index})...`);

      try {
        // Xử lý đoạn video
        const segmentResult = await processVideoSegment(
          segment,
          videoPath,
          tempDir,
          renderOptions
        );

        if (segmentResult) {
          segments.push(segmentResult);
        }
      } catch (error) {
        console.error(`❌ Lỗi khi xử lý đoạn ${segment.index}:`, error);
      }
    }

    if (segments.length === 0) {
      throw new Error('Không có đoạn video nào được xử lý thành công');
    }

    // 4. Ghép các đoạn video lại
    console.log('🔄 Đang ghép các đoạn video...');
    const finalOutput = await mergeVideoSegments(
      segments,
      null, // Không sử dụng phụ đề
      renderOptions
    );

    // 5. Dọn dẹp thư mục tạm nếu cần
    if (renderOptions.cleanup) {
      console.log('🔄 Đang dọn dẹp thư mục tạm...');
      try {
        fs.rmSync(tempDir, { recursive: true, force: true });
      } catch (error) {
        console.warn('⚠️ Không thể xóa thư mục tạm:', error);
      }
    }

    console.log('✅ Video đã render xong:', finalOutput);
    return finalOutput;
  } catch (error) {
    console.error('❌ Lỗi khi xử lý SRT và render video:', error);
    throw error;
  }
}

/**
 * Xử lý một đoạn video dựa trên thông tin SRT
 * @param {Object} segment - Đối tượng SRT
 * @param {string} videoPath - Đường dẫn đến video gốc
 * @param {string} tempDir - Thư mục tạm
 * @param {Object} options - Các tùy chọn render
 * @returns {Promise<Object>} - Thông tin về đoạn video đã xử lý
 */
async function processVideoSegment(segment, videoPath, tempDir, options) {
  const segmentIndex = segment.index;
  const segmentPath = path.join(tempDir, `segment_${segmentIndex.toString().padStart(4, '0')}.mp4`);
  const finalSegmentPath = path.join(tempDir, `final_segment_${segmentIndex.toString().padStart(4, '0')}.mp4`);

  try {
    // Tính toán thời lượng và tốc độ
    let duration = segment.videoDuration;
    let targetDuration = duration;
    let speedFactor = 1.0;

    // Đảm bảo thời lượng hợp lệ
    if (duration <= 0) {
      console.warn(`⚠️ Đoạn ${segmentIndex}: Thời lượng không hợp lệ (${duration}s), đặt thành 0.1s`);
      duration = 0.1;
    }

    // Nếu có audio hợp lệ và audio dài hơn video, điều chỉnh tốc độ video
    if (segment.hasValidAudio && segment.audioDuration > duration) {
      targetDuration = segment.audioDuration;
      speedFactor = duration / targetDuration;
      console.log(`🔄 Đoạn ${segmentIndex}: Audio dài hơn video (${segment.audioDuration.toFixed(2)}s > ${duration.toFixed(2)}s), điều chỉnh tốc độ video xuống ${speedFactor.toFixed(3)}`);
    }

    // Cắt đoạn video từ video gốc
    try {
      await cutVideoSegment(
        videoPath,
        segmentPath,
        segment.startTime,
        duration,
        speedFactor,
        options
      );
    } catch (cutError) {
      console.error(`❌ Lỗi khi cắt đoạn video ${segmentIndex}:`, cutError);

      // Thử lại với cài đặt đơn giản hơn
      console.log(`🔄 Thử lại với cài đặt đơn giản hơn cho đoạn ${segmentIndex}...`);

      try {
        // Sử dụng execSync để thực hiện lệnh FFmpeg đơn giản hơn
        const simpleCmd = `${ffmpegManager.ffmpegPath} -y -ss ${segment.startTime} -i "${videoPath}" -t ${duration} -c:v libx264 -preset ultrafast -crf 28 "${segmentPath}"`;
        execSync(simpleCmd);
      } catch (retryError) {
        console.error(`❌ Vẫn không thể cắt đoạn video ${segmentIndex}:`, retryError);
        throw new Error(`Không thể cắt đoạn video ${segmentIndex}`);
      }
    }

    // Kiểm tra xem file đã được tạo thành công chưa
    if (!fs.existsSync(segmentPath) || fs.statSync(segmentPath).size === 0) {
      throw new Error(`File segment không tồn tại hoặc rỗng: ${segmentPath}`);
    }

    // Nếu có audio hợp lệ, ghép audio vào video
    if (segment.hasValidAudio) {
      const audioPath = segment.audioUrl.replace('file://', '');

      try {
        await addAudioToVideo(
          segmentPath,
          audioPath,
          finalSegmentPath,
          options
        );
      } catch (audioError) {
        console.error(`❌ Lỗi khi ghép audio vào video ${segmentIndex}:`, audioError);

        // Nếu không thể ghép audio, sử dụng video gốc
        console.log(`🔄 Sử dụng video không có audio cho đoạn ${segmentIndex}`);
        fs.copyFileSync(segmentPath, finalSegmentPath);
      }
    } else {
      // Nếu không có audio, chỉ sao chép video
      fs.copyFileSync(segmentPath, finalSegmentPath);
    }

    // Kiểm tra xem file đã được tạo thành công chưa
    if (!fs.existsSync(finalSegmentPath) || fs.statSync(finalSegmentPath).size === 0) {
      throw new Error(`File final segment không tồn tại hoặc rỗng: ${finalSegmentPath}`);
    }

    return {
      path: finalSegmentPath,
      index: segmentIndex,
      startTime: segment.startTime,
      endTime: segment.endTime,
      duration: targetDuration
    };
  } catch (error) {
    console.error(`❌ Lỗi khi xử lý đoạn video ${segmentIndex}:`, error);

    // Tạo một file video trống nếu không thể xử lý
    try {
      console.log(`🔄 Tạo file video trống cho đoạn ${segmentIndex}`);
      const emptyCmd = `${ffmpegManager.ffmpegPath} -y -f lavfi -i color=c=black:s=320x240:d=1 -c:v libx264 "${finalSegmentPath}"`;
      execSync(emptyCmd);

      return {
        path: finalSegmentPath,
        index: segmentIndex,
        startTime: segment.startTime,
        endTime: segment.endTime,
        duration: 1.0,
        isEmpty: true
      };
    } catch (emptyError) {
      console.error(`❌ Không thể tạo file video trống cho đoạn ${segmentIndex}:`, emptyError);
      return null;
    }
  }
}

/**
 * Cắt một đoạn video từ video gốc
 * @param {string} inputPath - Đường dẫn đến video gốc
 * @param {string} outputPath - Đường dẫn đến video đầu ra
 * @param {number} startTime - Thời điểm bắt đầu (giây)
 * @param {number} duration - Thời lượng (giây)
 * @param {number} speedFactor - Hệ số tốc độ (1.0 = tốc độ bình thường)
 * @param {Object} options - Các tùy chọn render
 * @returns {Promise<void>}
 */
async function cutVideoSegment(inputPath, outputPath, startTime, duration, speedFactor, options) {
  return new Promise((resolve, reject) => {
    try {
      // Kiểm tra xem file có tồn tại không
      if (!fs.existsSync(inputPath)) {
        throw new Error(`File video không tồn tại: ${inputPath}`);
      }

      // Đảm bảo thời lượng hợp lệ
      if (duration <= 0) {
        duration = 0.1; // Đặt thời lượng tối thiểu
      }

      // Chuẩn bị các tham số FFmpeg
      const ffmpegArgs = [
        '-y',                      // Ghi đè file đầu ra nếu tồn tại
        '-ss', startTime.toString(), // Thời điểm bắt đầu
        '-i', inputPath,           // File đầu vào
        '-t', duration.toString(), // Thời lượng
        '-c:v', options.videoCodec // Codec video
      ];

      // Thêm các tham số chất lượng video
      if (options.videoCodec === 'libx264') {
        ffmpegArgs.push(
          '-preset', options.videoPreset,
          '-crf', options.videoQuality.toString()
        );
      } else {
        ffmpegArgs.push('-b:v', options.videoBitrate);
      }

      // Xác định bộ lọc video
      let videoFilters = [];

      // Điều chỉnh kích thước nếu cần
      if (options.width > 0 && options.height > 0) {
        videoFilters.push(`scale=${options.width}:${options.height}`);
      }

      // Điều chỉnh tốc độ video nếu cần
      if (speedFactor !== 1.0) {
        videoFilters.push(`setpts=${1/speedFactor}*PTS`);
      }

      // Thêm bộ lọc video nếu có
      if (videoFilters.length > 0) {
        ffmpegArgs.push('-vf', videoFilters.join(','));
      }

      // Điều chỉnh FPS nếu cần
      if (options.fps > 0) {
        ffmpegArgs.push('-r', options.fps.toString());
      }

      // Thêm các tham số audio
      ffmpegArgs.push(
        '-c:a', 'aac',
        '-b:a', options.audioBitrate,
        outputPath
      );

      // Thực thi lệnh FFmpeg
      const ffmpegProcess = spawn('ffmpeg', ffmpegArgs);

      let stdoutData = '';
      let stderrData = '';

      ffmpegProcess.stdout.on('data', (data) => {
        stdoutData += data.toString();
      });

      ffmpegProcess.stderr.on('data', (data) => {
        stderrData += data.toString();
      });

      ffmpegProcess.on('close', (code) => {
        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`FFmpeg exited with code ${code}: ${stderrData}`));
        }
      });

      ffmpegProcess.on('error', (err) => {
        reject(err);
      });
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * Thêm audio vào video
 * @param {string} videoPath - Đường dẫn đến video
 * @param {string} audioPath - Đường dẫn đến audio
 * @param {string} outputPath - Đường dẫn đến file đầu ra
 * @param {Object} options - Các tùy chọn render
 * @returns {Promise<void>}
 */
async function addAudioToVideo(videoPath, audioPath, outputPath, options) {
  return new Promise((resolve, reject) => {
    try {
      // Kiểm tra xem file có tồn tại không
      if (!fs.existsSync(videoPath)) {
        throw new Error(`File video không tồn tại: ${videoPath}`);
      }
      if (!fs.existsSync(audioPath)) {
        throw new Error(`File audio không tồn tại: ${audioPath}`);
      }

      // Chuẩn bị các tham số FFmpeg
      const ffmpegArgs = [
        '-y',                // Ghi đè file đầu ra nếu tồn tại
        '-i', videoPath,     // File video đầu vào
        '-i', audioPath,     // File audio đầu vào
        '-c:v', 'copy',      // Sao chép video không mã hóa lại
        '-c:a', 'aac',       // Mã hóa audio thành AAC
        '-b:a', options.audioBitrate, // Bitrate audio
        '-map', '0:v?',      // Lấy luồng video từ file đầu vào thứ nhất (nếu có)
        '-map', '1:a?',      // Lấy luồng audio từ file đầu vào thứ hai (nếu có)
        '-shortest',         // Kết thúc khi luồng ngắn nhất kết thúc
        outputPath
      ];

      // Thực thi lệnh FFmpeg
      const ffmpegProcess = spawn('ffmpeg', ffmpegArgs);

      let stdoutData = '';
      let stderrData = '';

      ffmpegProcess.stdout.on('data', (data) => {
        stdoutData += data.toString();
      });

      ffmpegProcess.stderr.on('data', (data) => {
        stderrData += data.toString();
      });

      ffmpegProcess.on('close', (code) => {
        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`FFmpeg exited with code ${code}: ${stderrData}`));
        }
      });

      ffmpegProcess.on('error', (err) => {
        reject(err);
      });
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * Ghép các đoạn video thành video hoàn chỉnh
 * @param {Array} segments - Mảng các đoạn video
 * @param {string} subtitlePath - Đường dẫn đến file phụ đề
 * @param {Object} options - Các tùy chọn render
 * @returns {Promise<string>} - Đường dẫn đến video hoàn chỉnh
 */
/**
 * Tạo file phụ đề ASS từ mảng SRT
 * @param {Array} srtArray - Mảng các đối tượng SRT
 * @param {string} outputPath - Đường dẫn đến file phụ đề đầu ra
 * @param {string} style - Kiểu phụ đề (yellow, white, black)
 * @returns {void}
 */
function generateASSFile(srtArray, outputPath, style = 'yellow') {
  // Xác định màu sắc dựa trên kiểu
  let primaryColor, outlineColor, backgroundColor;

  switch (style) {
    case 'white':
      primaryColor = '&HFFFFFF&'; // Trắng
      outlineColor = '&H000000&'; // Viền đen
      backgroundColor = '&H80000000&'; // Nền đen trong suốt
      break;
    case 'black':
      primaryColor = '&H000000&'; // Đen
      outlineColor = '&HFFFFFF&'; // Viền trắng
      backgroundColor = '&H80FFFFFF&'; // Nền trắng trong suốt
      break;
    case 'yellow':
    default:
      primaryColor = '&H00FFFF&'; // Vàng
      outlineColor = '&H000000&'; // Viền đen
      backgroundColor = '&H80000000&'; // Nền đen trong suốt
      break;
  }

  // Tạo header ASS
  const header = `[Script Info]
ScriptType: v4.00+
PlayResX: 1920
PlayResY: 1080
ScaledBorderAndShadow: yes

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: Default,Arial,48,${primaryColor},&H000000&,${outlineColor},${backgroundColor},1,0,0,0,100,100,0,0,1,2,1,2,20,20,20,1

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text`;

  // Tạo các sự kiện phụ đề
  const events = srtArray.map(segment => {
    // Chuyển đổi định dạng thời gian từ SRT sang ASS
    const start = convertTimeFormat(segment.start);
    const end = convertTimeFormat(segment.end);

    // Sử dụng translatedText nếu có, nếu không thì sử dụng text
    const text = (segment.translatedText || segment.text || '').replace(/\n/g, '\\N');

    return `Dialogue: 0,${start},${end},Default,,0,0,0,,${text}`;
  }).join('\n');

  // Ghi file ASS
  fs.writeFileSync(outputPath, `${header}\n${events}`);
}

/**
 * Chuyển đổi định dạng thời gian từ SRT (00:00:00,000) sang ASS (0:00:00.00)
 * @param {string} time - Thời gian định dạng SRT
 * @returns {string} - Thời gian định dạng ASS
 */
function convertTimeFormat(time) {
  if (!time) return '0:00:00.00';

  // Xử lý trường hợp time là số
  if (typeof time === 'number') {
    const hours = Math.floor(time / 3600);
    const minutes = Math.floor((time % 3600) / 60);
    const seconds = Math.floor(time % 60);
    const milliseconds = Math.floor((time % 1) * 100);

    return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}.${milliseconds.toString().padStart(2, '0')}`;
  }

  // Xử lý trường hợp time là chuỗi
  const parts = time.split(',');
  if (parts.length !== 2) return '0:00:00.00';

  const timeParts = parts[0].split(':');
  if (timeParts.length !== 3) return '0:00:00.00';

  const hours = parseInt(timeParts[0], 10);
  const milliseconds = parts[1].substring(0, 2); // Chỉ lấy 2 chữ số đầu tiên

  return `${hours}:${timeParts[1]}:${timeParts[2]}.${milliseconds}`;
}

async function mergeVideoSegments(segments, _subtitlePath, options) { // Không sử dụng subtitlePath
  return new Promise(async (resolve, reject) => {
    try {
      // Kiểm tra xem có đoạn video nào không
      if (!segments || segments.length === 0) {
        throw new Error('Không có đoạn video nào để ghép');
      }

      // Lọc bỏ các đoạn video null hoặc không tồn tại
      const validSegments = segments.filter(segment => {
        if (!segment || !segment.path) return false;
        return fs.existsSync(segment.path);
      });

      if (validSegments.length === 0) {
        throw new Error('Không có đoạn video hợp lệ nào để ghép');
      }

      const tempDir = path.dirname(validSegments[0].path);

      // Sắp xếp các đoạn video theo thứ tự index
      const sortedSegments = validSegments.sort((a, b) => a.index - b.index);

      // Thay vì sử dụng concat demuxer, chúng ta sẽ sử dụng concat filter
      // để có kiểm soát tốt hơn về thời gian và đồng bộ audio

      // Chuẩn bị các tham số FFmpeg
      const ffmpegArgs = ['-y']; // Ghi đè file đầu ra nếu tồn tại

      // Thêm tất cả các file đầu vào
      for (const segment of sortedSegments) {
        ffmpegArgs.push('-i', segment.path);
      }

      // Tạo filter complex để ghép video và audio
      let concatFilter = '';

      // Ghép video
      for (let i = 0; i < sortedSegments.length; i++) {
        concatFilter += `[${i}:v]`;
      }
      concatFilter += `concat=n=${sortedSegments.length}:v=1:a=0[outv]; `;

      // Ghép audio
      for (let i = 0; i < sortedSegments.length; i++) {
        concatFilter += `[${i}:a]`;
      }
      concatFilter += `concat=n=${sortedSegments.length}:v=0:a=1[outa]`;

      // Thêm filter complex
      ffmpegArgs.push('-filter_complex', concatFilter);

      // Map output
      ffmpegArgs.push('-map', '[outv]', '-map', '[outa]');

      // Thêm codec video
      ffmpegArgs.push('-c:v', options.videoCodec);

      // Thêm các tham số chất lượng video
      if (options.videoCodec === 'libx264') {
        ffmpegArgs.push(
          '-preset', options.videoPreset,
          '-crf', options.videoQuality.toString()
        );
      } else {
        ffmpegArgs.push('-b:v', options.videoBitrate);
      }

      // Thêm các tham số audio
      ffmpegArgs.push(
        '-c:a', 'aac',
        '-b:a', options.audioBitrate
      );

      // Xác định số lượng input hiện tại
      let inputIndex = validSegments.length + 1;

      // Thêm logo nếu cần
      let filterComplex = '';
      let filterIndex = 0;
      let hasLogo = false;

      if (options.addLogo && options.logoPath && fs.existsSync(options.logoPath)) {
        ffmpegArgs.push('-i', options.logoPath);
        hasLogo = true;

        // Xác định vị trí logo
        let logoPosition;
        switch (options.logoPosition) {
          case 'top-left':
            logoPosition = '10:10';
            break;
          case 'top-right':
            logoPosition = 'main_w-overlay_w-10:10';
            break;
          case 'bottom-left':
            logoPosition = '10:main_h-overlay_h-10';
            break;
          case 'bottom-right':
            logoPosition = 'main_w-overlay_w-10:main_h-overlay_h-10';
            break;
          default:
            logoPosition = 'main_w-overlay_w-10:10';
        }

        filterComplex += `[0:v][${inputIndex}:v] overlay=${logoPosition} [v${filterIndex}]; `;
        filterIndex++;
        inputIndex++;
      }

      // Thêm text nếu cần
      if (options.addText && options.addText.trim() !== '') {
        const yPosition = options.textPosition === 'top' ? '10' : 'main_h-text_h-10';
        const prevIndex = filterIndex > 0 ? `[v${filterIndex-1}]` : '[0:v]';

        // Escape các ký tự đặc biệt trong text
        const escapedText = options.addText
          .replace(/'/g, "\\'")
          .replace(/:/g, '\\:')
          .replace(/\\/g, '\\\\');

        filterComplex += `${prevIndex} drawtext=text='${escapedText}':fontcolor=white:fontsize=24:box=1:boxcolor=black@0.5:boxborderw=5:x=(w-text_w)/2:y=${yPosition} [v${filterIndex}]; `;
        filterIndex++;
      }

      // Đã loại bỏ phần xử lý phụ đề

      // Hoàn thiện filter complex
      if (filterComplex) {
        try {
          // Không cần xóa dấu ; và khoảng trắng ở cuối nữa vì chúng ta đã thêm filter subtitles
          ffmpegArgs.push('-filter_complex', filterComplex);
          ffmpegArgs.push('-map', `[v${filterIndex-1}]`);
          ffmpegArgs.push('-map', '0:a');
        } catch (filterError) {
          console.error('❌ Lỗi khi thiết lập filter complex:', filterError);

          // Nếu có lỗi, bỏ qua filter complex và sử dụng video gốc
          console.log('🔄 Bỏ qua filter complex và sử dụng video gốc');

          // Xóa các input logo nếu đã thêm
          if (hasLogo) {
            // Không thể xóa input đã thêm, nhưng có thể bỏ qua chúng
            // bằng cách không sử dụng filter complex
          }

          // Đã loại bỏ phần xử lý phụ đề
        }
      }

      // Đã loại bỏ phần xử lý phụ đề

      // Thêm đường dẫn đầu ra
      ffmpegArgs.push(options.outputPath);

      // Thực thi lệnh FFmpeg
      const ffmpegProcess = spawn('ffmpeg', ffmpegArgs);

      let stdoutData = '';
      let stderrData = '';

      ffmpegProcess.stdout.on('data', (data) => {
        stdoutData += data.toString();
      });

      ffmpegProcess.stderr.on('data', (data) => {
        stderrData += data.toString();
      });

      ffmpegProcess.on('close', (code) => {
        if (code === 0) {
          resolve(options.outputPath);
        } else {
          reject(new Error(`FFmpeg exited with code ${code}: ${stderrData}`));
        }
      });

      ffmpegProcess.on('error', (err) => {
        reject(err);
      });
    } catch (error) {
      reject(error);
    }
  });
}

module.exports = {
  processSRTAndRender,
  processVideoSegment,
  cutVideoSegment,
  addAudioToVideo,
  mergeVideoSegments,
  generateASSFile,
  convertTimeFormat
};