const path = require('path');
const fs = require('fs');
const { execWithLog } = require('../utils');

async function demucs(event, { fileInput }) {
  const type = 'video-task';
  const outputDirectory = path.dirname(fileInput);
  const baseName = path.basename(fileInput, path.extname(fileInput));
  const outputDir = path.join(outputDirectory, 'htdemucs', baseName);
  event?.sender?.send(type, { data: 'Running Demucs...', code: 0 });
  const vocalsFile = path.join(outputDir, 'vocals.wav');
  const noVocalsFile = path.join(outputDir, 'no_vocals.wav');

  try {
    const args = ['--two-stems', 'vocals', fileInput, '-o', outputDirectory];

    const result = await execWithLog.bind({ type: type })(event, 'demucs', args);

    if (!result.success) {
      event?.sender?.send(type, { data: `Demucs error: ${result}`, code: 1 });
      return { success: false, error: result };
    }

    event?.sender?.send(type, { data: 'Demucs done', code: 0 });
    const res = { success: true, outputPath: outputDirectory, processId: result.processId, vocalsFile, noVocalsFile };
    console.log('Demucs done', res);
    return res;
  } catch (err) {
    console.error('Demucs error:', err);
    event?.sender?.send(type, { data: `Demucs error: ${err.message}`, code: 1 });
    return { success: false, error: err.message };
  }
}

async function checkFileHtdemucsFileExists(event, { fileInput }) {
  const type = 'video-task';
  const outputDirectory = path.dirname(fileInput);
  const baseName = path.basename(fileInput, path.extname(fileInput));
  const outputDir = path.join(outputDirectory, 'htdemucs', baseName);
  const vocalsFile = path.join(outputDir, 'vocals.wav');
  const noVocalsFile = path.join(outputDir, 'no_vocals.wav');
console.log('checkFileHtdemucsFileExists', { outputDir, vocalsFile, noVocalsFile });

  const exists = await Promise.all([vocalsFile, noVocalsFile].map((file) => fs.promises.access(file, fs.constants.F_OK).then(() => true).catch(() => false)));

  const isFileExists = exists.every(Boolean);
  if(isFileExists){
    return { success: true, outputPath: outputDirectory, vocalsFile, noVocalsFile };
  }else{
    return { success: false };
  }
}





module.exports = {
  demucs,
  checkFileHtdemucsFileExists,
};

if (require.main === module) {
  const [, , fileInput] = process.argv;

  demucs(null, {
    fileInput,
  });
}
