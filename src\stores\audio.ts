import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useAudioStore = defineStore('audio', () => {
  // State
  const currentAudio = ref<HTMLAudioElement | null>(null)
  const isPlaying = ref(false)
  const currentUrl = ref<string | null>(null)

  // Actions
  const playAudio = async (url: string) => {
    try {
      // Stop current audio if playing
      if (currentAudio.value) {
        currentAudio.value.pause()
        currentAudio.value = null
      }

      // Create new audio element
      const audio = new Audio(url)
      currentAudio.value = audio
      currentUrl.value = url
      isPlaying.value = true

      // Set up event listeners
      audio.addEventListener('ended', () => {
        isPlaying.value = false
        currentAudio.value = null
        currentUrl.value = null
      })

      audio.addEventListener('error', (e) => {
        console.error('Audio playback error:', e)
        isPlaying.value = false
        currentAudio.value = null
        currentUrl.value = null
      })

      // Play the audio
      await audio.play()
    } catch (error) {
      console.error('Failed to play audio:', error)
      isPlaying.value = false
      currentAudio.value = null
      currentUrl.value = null
    }
  }

  const stopAudio = () => {
    if (currentAudio.value) {
      currentAudio.value.pause()
      currentAudio.value = null
    }
    isPlaying.value = false
    currentUrl.value = null
  }

  const pauseAudio = () => {
    if (currentAudio.value && !currentAudio.value.paused) {
      currentAudio.value.pause()
      isPlaying.value = false
    }
  }

  const resumeAudio = async () => {
    if (currentAudio.value && currentAudio.value.paused) {
      try {
        await currentAudio.value.play()
        isPlaying.value = true
      } catch (error) {
        console.error('Failed to resume audio:', error)
      }
    }
  }

  return {
    // State
    currentAudio,
    isPlaying,
    currentUrl,
    
    // Actions
    playAudio,
    stopAudio,
    pauseAudio,
    resumeAudio
  }
})
