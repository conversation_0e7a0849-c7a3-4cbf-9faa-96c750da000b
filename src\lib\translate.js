export const createTranslationPrompt = (sourceLang = 'auto', targetLang = 'Vietnamese', glossary = null) => {
  let dictionaryText = '';

  // Nếu có từ điển thuật ngữ, thêm vào prompt
  if (glossary && glossary.terms && glossary.terms.length > 0) {
    dictionaryText = 'Từ điển thuật ngữ (hãy sử dụng nhất quán):\n';
    glossary.terms.forEach((term) => {
      if (term.source && term.target) {
        dictionaryText += `- ${term.source}: ${term.target}\n`;
      }
    });
    dictionaryText += '\n';
  }

  return `Bạn là một dịch giả chuyên nghiệp. Nhiệm vụ của bạn là dịch văn bản từ ${
    sourceLang === 'auto' ? 'ngôn ngữ được phát hiện' : sourceLang
  } sang ${targetLang}.

${dictionaryText}Hướng dẫn:
1. Dịch toàn bộ văn bản đượ<PERSON> cung cấp, không bỏ sót nội dung.
2. Giữ nguyên giọng điệu, phong cách và sắc thái ban đầu. Giữ ngắn gọn để phù hợp với thời gian phụ đề.
3. KHÔNG giữ lại bất kỳ từ nào bằng ngôn ngữ gốc, trừ tên riêng.
4. KHÔNG thêm bất kỳ ghi chú hay giải thích nào.
5. Sử dụng từ điển thuật ngữ ở trên để đảm bảo tính nhất quán khi dịch tên nhân vật và thuật ngữ chuyên ngành.
6. Nếu văn bản đầu vào đã bằng ${targetLang}, hãy trả về văn bản gốc.

Hãy dịch văn bản sau đây thành ${targetLang} một cách tự nhiên và chính xác:`;
};
