// Database management script for novel extraction data
const path = require('path');
const Database = require('./index');

class DatabaseManager {
    constructor(dbPath = null) {
        this.dbPath = dbPath || path.join(__dirname, '../../data/novels.db');
        this.db = null;
    }

    async initialize() {
        try {
            this.db = new Database(this.dbPath);
            await this.db.initializeTables();
            console.log(`Database initialized: ${this.dbPath}`);
            return true;
        } catch (error) {
            console.error('Failed to initialize database:', error);
            return false;
        }
    }

    // Display all books
    async listBooks() {
        try {
            const books = await this.db.books.getAllBooks(50);
            console.log('\n=== Books in Database ===');
            
            if (books.length === 0) {
                console.log('No books found in database.');
                return;
            }

            books.forEach((book, index) => {
                console.log(`\n${index + 1}. ${book.title}`);
                console.log(`   ID: ${book.id}`);
                console.log(`   Author: ${book.author || 'Unknown'}`);
                console.log(`   Category: ${book.category || 'Unknown'}`);
                console.log(`   Status: ${book.status}`);
                console.log(`   Chapters: ${book.extracted_chapters}/${book.total_chapters}`);
                console.log(`   Last Update Info: ${book.last_update_info || 'N/A'}`);
                console.log(`   Source: ${book.source_url}`);
                console.log(`   Created: ${book.created_at}`);
                console.log(`   Last Updated: ${book.updated_at}`);
            });

            console.log(`\nTotal books: ${books.length}`);
        } catch (error) {
            console.error('Error listing books:', error);
        }
    }

    // Display chapters for a specific book
    async listChapters(bookId, limit = 20) {
        try {
            const book = await this.db.books.getBookById(bookId);
            if (!book) {
                console.log(`Book with ID ${bookId} not found.`);
                return;
            }

            const chapters = await this.db.chapters.getChaptersByBookId(bookId);
            console.log(`\n=== Chapters for "${book.title}" ===`);
            
            if (chapters.length === 0) {
                console.log('No chapters found for this book.');
                return;
            }

            chapters.slice(0, limit).forEach((chapter, index) => {
                console.log(`${chapter.chapter_index}. ${chapter.title}`);
                console.log(`   URL: ${chapter.full_url}`);
                console.log(`   Page: ${chapter.page_number || 'N/A'}`);
            });

            if (chapters.length > limit) {
                console.log(`\n... and ${chapters.length - limit} more chapters`);
            }

            console.log(`\nTotal chapters: ${chapters.length}`);
        } catch (error) {
            console.error('Error listing chapters:', error);
        }
    }

    // Display recent extraction sessions
    async listSessions() {
        try {
            const sessions = await this.db.chapters.getExtractionSessions(10);
            console.log('\n=== Recent Extraction Sessions ===');
            
            if (sessions.length === 0) {
                console.log('No extraction sessions found.');
                return;
            }

            sessions.forEach((session, index) => {
                console.log(`\n${index + 1}. Session: ${session.extraction_session}`);
                console.log(`   Book: ${session.book_title || 'Unknown'}`);
                console.log(`   Chapters: ${session.chapter_count}`);
                console.log(`   Source: ${session.source_url || 'N/A'}`);
                console.log(`   Last Updated: ${session.last_updated}`);
            });
        } catch (error) {
            console.error('Error listing sessions:', error);
        }
    }

    // Display database statistics
    async showStats() {
        try {
            const stats = await this.db.getExtractionStats();
            console.log('\n=== Database Statistics ===');
            console.log(`Total Books: ${stats.totalBooks}`);
            console.log(`Total Chapters: ${stats.totalChapters}`);
            console.log(`Recent Sessions: ${stats.recentSessions.length}`);

            if (stats.recentSessions.length > 0) {
                console.log('\nRecent Activity:');
                stats.recentSessions.slice(0, 3).forEach(session => {
                    console.log(`  - ${session.book_title}: ${session.chapter_count} chapters`);
                });
            }
        } catch (error) {
            console.error('Error getting stats:', error);
        }
    }

    // Search books by title
    async searchBooks(searchTerm) {
        try {
            const books = await this.db.books.searchBooksByTitle(searchTerm);
            console.log(`\n=== Search Results for "${searchTerm}" ===`);
            
            if (books.length === 0) {
                console.log('No books found matching the search term.');
                return;
            }

            books.forEach((book, index) => {
                console.log(`\n${index + 1}. ${book.title}`);
                console.log(`   ID: ${book.id}`);
                console.log(`   Chapters: ${book.extracted_chapters}/${book.total_chapters}`);
                console.log(`   Status: ${book.status}`);
            });
        } catch (error) {
            console.error('Error searching books:', error);
        }
    }

    // Delete a book and its chapters
    async deleteBook(bookId) {
        try {
            const book = await this.db.books.getBookById(bookId);
            if (!book) {
                console.log(`Book with ID ${bookId} not found.`);
                return;
            }

            console.log(`Deleting book: "${book.title}" and all its chapters...`);
            await this.db.books.deleteBook(bookId);
            console.log('Book deleted successfully.');
        } catch (error) {
            console.error('Error deleting book:', error);
        }
    }

    // Delete an extraction session
    async deleteSession(sessionId) {
        try {
            console.log(`Deleting extraction session: ${sessionId}...`);
            const result = await this.db.chapters.deleteChaptersBySession(sessionId);
            console.log(`Deleted ${result} chapters from session.`);
        } catch (error) {
            console.error('Error deleting session:', error);
        }
    }

    // Close database connection
    async close() {
        if (this.db) {
            await this.db.close();
            console.log('Database connection closed.');
        }
    }
}

// Command line interface
async function runCLI() {
    const args = process.argv.slice(2);
    const command = args[0];
    
    const manager = new DatabaseManager();
    const initialized = await manager.initialize();
    
    if (!initialized) {
        console.error('Failed to initialize database manager.');
        process.exit(1);
    }

    try {
        switch (command) {
            case 'books':
                await manager.listBooks();
                break;
            case 'chapters':
                const bookId = parseInt(args[1]);
                if (!bookId) {
                    console.log('Usage: node db-manager.js chapters <book_id>');
                    break;
                }
                await manager.listChapters(bookId);
                break;
            case 'sessions':
                await manager.listSessions();
                break;
            case 'stats':
                await manager.showStats();
                break;
            case 'search':
                const searchTerm = args[1];
                if (!searchTerm) {
                    console.log('Usage: node db-manager.js search <search_term>');
                    break;
                }
                await manager.searchBooks(searchTerm);
                break;
            case 'delete-book':
                const deleteBookId = parseInt(args[1]);
                if (!deleteBookId) {
                    console.log('Usage: node db-manager.js delete-book <book_id>');
                    break;
                }
                await manager.deleteBook(deleteBookId);
                break;
            case 'delete-session':
                const sessionId = args[1];
                if (!sessionId) {
                    console.log('Usage: node db-manager.js delete-session <session_id>');
                    break;
                }
                await manager.deleteSession(sessionId);
                break;
            default:
                console.log('Available commands:');
                console.log('  books                    - List all books');
                console.log('  chapters <book_id>       - List chapters for a book');
                console.log('  sessions                 - List extraction sessions');
                console.log('  stats                    - Show database statistics');
                console.log('  search <term>            - Search books by title');
                console.log('  delete-book <book_id>    - Delete a book and its chapters');
                console.log('  delete-session <id>      - Delete an extraction session');
        }
    } catch (error) {
        console.error('Command failed:', error);
    } finally {
        await manager.close();
    }
}

// Run CLI if this file is executed directly
if (require.main === module) {
    runCLI();
}

module.exports = DatabaseManager;
