const { ffmpeg, ffmpegManager } = require('../ffmpeg-config');
const fs = require('fs');
const path = require('path');
const util = require('util');
const { getEncoder } = require('../ffmpegHandler');
const exec = util.promisify(require('child_process').exec);

async function addAudioToVideo(event, videoPath, musicPath, outputPath, options) {
  const volume = options.volume ?? 1;
  const audioBitrate = options.audioBitrate ?? '192k';

  const tmpDir = path.dirname(outputPath);
  const musicLoopedPath = path.join(tmpDir, 'music_looped.mp3');
  const musicFinalPath = path.join(tmpDir, 'music_final.mp3');
  const originalAudioPath = path.join(tmpDir, 'original_audio.aac');
  const mixedAudioPath = path.join(tmpDir, 'mixed_audio.aac');

  const getDuration = async (filePath) => {
    const { stdout } = await exec(`${ffmpegManager.ffprobePath} -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 "${filePath}"`);
    return parseFloat(stdout.trim());
  };

  const videoDuration = await getDuration(videoPath);
  const musicDuration = await getDuration(musicPath);
  const encoder = await getEncoder();

  // Bước 1: Trích audio gốc từ video
  console.log('Trích audio gốc từ video', videoPath);
  await new Promise((resolve, reject) => {
    ffmpeg(videoPath)
    .outputOptions(['-c:v', encoder])
      .noVideo()
      .audioCodec('aac')
      .save(originalAudioPath)
      .on('end', resolve)
      .on('error', reject);
  });

  // Bước 2: Lặp nhạc nếu cần
  console.log('Lặp nhạc nếu cần', musicPath);
  if (musicDuration < videoDuration) {
    const loopCount = Math.ceil(videoDuration / musicDuration);
    const inputList = Array(loopCount).fill(musicPath).join('|');
    await new Promise((resolve, reject) => {
      ffmpeg()
        .input(`concat:${inputList}`)
        .outputOptions(['-c:v', encoder])
        .audioCodec('copy')
        .save(musicLoopedPath)
        .on('end', resolve)
        .on('error', reject);
    });
  } else {
    fs.copyFileSync(musicPath, musicLoopedPath);
  }

  // Bước 3: Cắt nhạc đúng độ dài + fade out + chỉnh âm lượng
  console.log('Cắt nhạc đúng độ dài + fade out + chỉnh âm lượng', musicLoopedPath);
  const fadeDuration = 2;
  await new Promise((resolve, reject) => {
    ffmpeg(musicLoopedPath)
      .setDuration(videoDuration)
      .audioFilter(`afade=t=out:st=${Math.max(0, videoDuration - fadeDuration)}:d=${fadeDuration},volume=${volume}`)
      .audioBitrate(audioBitrate)
      .outputOptions(['-c:v', encoder])
      .on('end', resolve)
      .on('error', reject)
      .save(musicFinalPath);
  });

  // Bước 4: Mix original audio + background music
  console.log('Mix original audio + background music', originalAudioPath);
  
await new Promise((resolve, reject) => {
  ffmpeg()
    .input(originalAudioPath)
    .input(musicFinalPath)
    .complexFilter([
      `[0:a]volume=1[a0]; [1:a]volume=${volume}[a1]; [a0][a1]amix=inputs=2:duration=first:dropout_transition=2[a]`,
    ])
      .outputOptions([
        '-map', '[a]',
        '-c:a', 'aac',
        '-c:v', encoder,
        '-b:a', audioBitrate,
        '-shortest',
      ])
    .on('stderr', line => console.log('[ffmpeg mix]', line))
    .on('end', resolve)
    .on('error', reject)
    .save(mixedAudioPath);
});


  // Bước 5: Ghép lại audio vào video
  console.log('Ghép lại audio vào video', mixedAudioPath);
  await new Promise((resolve, reject) => {
    ffmpeg()
      .input(videoPath)
      .input(mixedAudioPath)
      .outputOptions([
        '-map', '0:v:0',
        '-map', '1:a:0',
        '-c:v', encoder,
        '-c:a', 'aac',
        '-shortest',
      ])
      .save(outputPath)
      .on('end', resolve)
      .on('error', reject);
  });

  // Cleanup
  fs.unlinkSync(originalAudioPath);
  fs.unlinkSync(musicLoopedPath);
  fs.unlinkSync(musicFinalPath);
  fs.unlinkSync(mixedAudioPath);
}

async function upVolume(event, videoPath, outputPath, volume) {
  const encoder = await getEncoder();
  await new Promise((resolve, reject) => {
    ffmpeg(videoPath)
      .audioFilter(`volume=${volume}`)
      .outputOptions(['-c:v', encoder])
      .save(outputPath)
      .on('end', resolve)
      .on('error', reject);
  });
}

module.exports = {
  addAudioToVideo,
  upVolume,
};
