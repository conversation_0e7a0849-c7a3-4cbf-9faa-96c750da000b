<template>
  <div class="image-overlay absolute inset-0 pointer-events-none">
    <div
      v-if="isVisible && layer.properties.src"
      class="image-element absolute transition-all duration-300"
      :style="imageStyle"
      @click.stop="handleImageClick"
    >
      <img
        :src="layer.properties.src"
        :alt="layer.name"
        class="w-full h-full object-contain"
        @load="onImageLoad"
        @error="onImageError"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const props = defineProps({
  layer: {
    type: Object,
    required: true
  },
  currentTime: {
    type: Number,
    required: true
  },
  videoDimensions: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['image-click', 'image-load', 'image-error'])

const imageLoaded = ref(false)
const imageError = ref(false)

// Check if image should be visible at current time
const isVisible = computed(() => {
  if (!props.layer.enabled || imageError.value) return false
  
  const { timeRange } = props.layer
  return props.currentTime >= timeRange.start && props.currentTime <= timeRange.end
})

// Calculate image styling
const imageStyle = computed(() => {
  if (!isVisible.value) return { display: 'none' }
  
  const { properties } = props.layer
  const { width, height } = props.videoDimensions
  
  // Calculate position
  const x = (properties.position.x / 100) * width
  const y = (properties.position.y / 100) * height
  
  // Calculate size based on scale
  const scale = (properties.scale || 100) / 100
  const scaleFactor = Math.min(width / 1920, height / 1080)
  const finalScale = scale * scaleFactor
  
  // Calculate rotation
  const rotation = properties.rotation || 0
  
  // Animation based on time
  const timeInLayer = props.currentTime - props.layer.timeRange.start
  const layerDuration = props.layer.timeRange.end - props.layer.timeRange.start
  
  // Apply animation
  let animationTransform = ''
  let animationOpacity = (props.layer.opacity / 100) * ((properties.opacity || 100) / 100)
  
  if (properties.animation) {
    switch (properties.animation.type) {
      case 'fade':
        if (timeInLayer < properties.animation.duration) {
          animationOpacity *= timeInLayer / properties.animation.duration
        } else if (timeInLayer > layerDuration - properties.animation.duration) {
          animationOpacity *= (layerDuration - timeInLayer) / properties.animation.duration
        }
        break
        
      case 'slide':
        if (timeInLayer < properties.animation.duration) {
          const slideProgress = timeInLayer / properties.animation.duration
          animationTransform += ` translateX(${(1 - slideProgress) * -100}px)`
        }
        break
        
      case 'zoom':
        if (timeInLayer < properties.animation.duration) {
          const zoomProgress = timeInLayer / properties.animation.duration
          const zoomScale = 0.5 + (zoomProgress * 0.5)
          animationTransform += ` scale(${zoomScale})`
        }
        break
        
      case 'rotate':
        const rotateProgress = timeInLayer / layerDuration
        animationTransform += ` rotate(${rotateProgress * 360}deg)`
        break
    }
  }
  
  return {
    left: x + 'px',
    top: y + 'px',
    width: (properties.width || 200) * finalScale + 'px',
    height: (properties.height || 200) * finalScale + 'px',
    transform: `translate(-50%, -50%) rotate(${rotation}deg) ${animationTransform}`,
    transformOrigin: 'center',
    pointerEvents: 'auto',
    cursor: 'pointer',
    zIndex: 15,
    opacity: animationOpacity,
    transition: 'all 0.1s ease',
    filter: properties.filters ? getFilterString(properties.filters) : 'none'
  }
})

// Generate CSS filter string
const getFilterString = (filters) => {
  const filterParts = []
  
  if (filters.brightness !== undefined && filters.brightness !== 0) {
    filterParts.push(`brightness(${1 + filters.brightness})`)
  }
  
  if (filters.contrast !== undefined && filters.contrast !== 1) {
    filterParts.push(`contrast(${filters.contrast})`)
  }
  
  if (filters.saturation !== undefined && filters.saturation !== 1) {
    filterParts.push(`saturate(${filters.saturation})`)
  }
  
  if (filters.blur !== undefined && filters.blur > 0) {
    filterParts.push(`blur(${filters.blur}px)`)
  }
  
  if (filters.hue !== undefined && filters.hue !== 0) {
    filterParts.push(`hue-rotate(${filters.hue}deg)`)
  }
  
  return filterParts.join(' ') || 'none'
}

const onImageLoad = () => {
  imageLoaded.value = true
  imageError.value = false
  emit('image-load', props.layer)
}

const onImageError = () => {
  imageLoaded.value = false
  imageError.value = true
  emit('image-error', props.layer)
}

const handleImageClick = () => {
  emit('image-click', props.layer)
}
</script>

<style scoped>
.image-element:hover {
  filter: brightness(1.1);
  transform: translate(-50%, -50%) scale(1.05);
}

.image-element img {
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}
</style>
