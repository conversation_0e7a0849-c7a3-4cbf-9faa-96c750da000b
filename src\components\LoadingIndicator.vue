<template>
  <div class="flex items-center space-x-3">
    <div class="flex-1">
      <div class="w-full bg-gray-100 rounded-full h-2">
        <div
          class="h-2 rounded-full transition-all duration-300 ease-out"
          :class="isPaused ? 'bg-amber-400' : 'bg-blue-500'"
          :style="{ width: `${progress}%` }"
        ></div>
      </div>
    </div>
    <div
      v-if="showPercentage"
      class="text-sm font-medium w-12 text-right tabular-nums"
      :class="isPaused ? 'text-amber-600' : 'text-gray-600'"
    >
      {{ progress }}%
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps } from 'vue'

const props = defineProps<{
  progress?: number
  showPercentage?: boolean
  isPaused?: boolean
}>()

const progress = props.progress ?? 0
const showPercentage = props.showPercentage ?? true
const isPaused = props.isPaused ?? false
</script>
