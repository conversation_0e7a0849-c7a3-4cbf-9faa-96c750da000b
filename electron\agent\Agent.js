const { NormalMemory } = require('./memory/NormalMemory.js');
const { JavaScriptInterpreter } = require('./interpreter/JavaScriptInterpreter.js');
const { llmInference } = require('./llm/openai.js');
const { cutMessages, defaultOutputCallback, defaultCheck } = require('./utils.js');
const path = require('path');

/**
 * Main Agent class
 */
class Agent {
    constructor(options = {}) {
        // Handle both string role and options object
        if (typeof options === 'string') {
            options = { role: options };
        }

        const {
            role = null,
            functions = [],
            knowledgeFiles = [],
            ragFunction = null,
            workspace = null,
            model = null,
            tokenLimit = null,
            apiKey = null,
            baseUrl = null,
            selfCall = false,
            continueRun = false,
            outputCallback = defaultOutputCallback,
            disablePythonRun = false,
            hidePythonCode = false,
            messages = [],
            ...args
        } = options;

        // Core properties
        this.role = role;
        this.functions = functions;
        this.knowledgeFiles = knowledgeFiles;
        this.ragFunction = ragFunction;
        this.workspace = workspace;
        this.model = model || 'gpt-3.5-turbo';
        this.tokenLimit = tokenLimit || 4000;
        this.apiKey = apiKey;
        this.baseUrl = baseUrl;
        this.selfCall = selfCall;
        this.continueRun = continueRun;
        this.outputCallback = outputCallback;
        this.disableJavaScriptRun = disablePythonRun; // Renamed for JS
        this.hideJavaScriptCode = hidePythonCode; // Renamed for JS
        this.llmArgs = args;

        // Runtime properties
        this.jsRunResult = null;
        this.runLevel = 0;
        this.enterIndex = null;
        this.tmpOutputCallback = null;

        // Initialize memory
        const memoryPath = workspace ? path.join(workspace, 'memory.json') : null;
        this.memory = new NormalMemory(memoryPath);

        // Initialize JavaScript interpreter
        const interpreterPath = workspace ? path.join(workspace, 'interpreter.json') : null;
        this.jsInterpreter = new JavaScriptInterpreter(this, interpreterPath);

        // Add initial role message if provided
        if (this.role && messages.length === 0) {
            this.memory.addMessage('system', this.role);
        }

        // Add any initial messages
        messages.forEach(msg => {
            this.memory.addMessage(msg.role, msg.content);
        });
    }

    /**
     * Main run method
     * @param {string|Array} command 
     * @param {*} returnType 
     * @param {boolean} display 
     * @param {boolean} verbose 
     * @param {boolean} userCheck 
     * @param {Function} checkRender 
     * @returns {Promise<any>}
     */
    async run(command, returnType = String, display = false, verbose = true, userCheck = false, checkRender = null) {
        this.runLevel++;
        
        try {
            const result = await this._run(command, returnType, verbose);
            
            if (userCheck) {
                const checkContent = checkRender ? checkRender(result) : result;
                const userResponse = defaultCheck(checkContent);
                
                if (userResponse !== null) {
                    // User provided feedback, run again with their input
                    return await this.run(userResponse, returnType, display, verbose, userCheck, checkRender);
                }
            }
            
            return result;
        } finally {
            this.runLevel--;
        }
    }

    /**
     * Internal run method
     * @param {string|Array} input 
     * @param {*} returnType 
     * @param {boolean} verbose 
     * @returns {Promise<any>}
     */
    async _run(input, returnType = String, verbose = false) {
        let result = '';

        const localOutput = (token) => {
            if (token !== null) {
                result += token;
            } else {
                result += '\n';
            }
            if (this.outputCallback) {
                this.outputCallback(token);
            }
        };

        // Add type hints for non-string return types
        if (this.runLevel !== 0) {
            if (returnType === String) {
                const addContent = "Directly answer the question, no need to run JavaScript\n";
                if (Array.isArray(input)) {
                    input = [addContent, ...input];
                } else {
                    input = addContent + input;
                }
            } else {
                const addContent = `\nYou should return JavaScript values in type ${returnType.name} by running JavaScript code.\n`;
                if (Array.isArray(input)) {
                    input = [...input, addContent];
                } else {
                    input = input + addContent;
                }
            }
        }

        this._memoryAddInput(input);

        let tryCount = 0;
        while (true) {
            const messages = this._getLLMMessages();
            const outputStop = await this._llmAndParseOutput(messages, localOutput, verbose);
            
            if (outputStop) {
                localOutput(null);

                // If we have a JavaScript result and expecting non-string type, use it
                if (this.jsRunResult !== null && this.jsRunResult !== undefined) {
                    const jsResult = this.jsRunResult;
                    this.jsRunResult = null;

                    if (returnType !== String) {
                        // For non-string types, prefer JavaScript result
                        if (returnType === Array && Array.isArray(jsResult)) {
                            return jsResult;
                        } else if (typeof jsResult === returnType.name.toLowerCase()) {
                            return jsResult;
                        } else if (returnType === Object && typeof jsResult === 'object') {
                            return jsResult;
                        }
                    }

                    // If JavaScript result doesn't match expected type, continue with text result
                    result = jsResult;
                }

                if (returnType === String) {
                    return result;
                }

                // Try to parse result to expected type
                if (typeof result !== returnType.name.toLowerCase() && !Array.isArray(result) && tryCount < 1) {
                    console.log(`Return type should be: ${returnType.name}`);
                    tryCount++;
                    this._memoryAddInput(`return type should be ${returnType.name}`);
                    result = '';
                    continue;
                }

                return result;
            }
        }
    }

    /**
     * Add input to memory
     * @param {string|Array} input 
     */
    _memoryAddInput(input) {
        this.memory.addMessage('user', input);
    }

    /**
     * Get messages for LLM
     * @returns {Array}
     */
    _getLLMMessages() {
        let messages = this.memory.getMessages();
        
        // Cut messages if they exceed token limit
        if (this.tokenLimit) {
            messages = cutMessages(messages, this.tokenLimit);
        }
        
        return messages;
    }

    /**
     * Process LLM output and handle JavaScript code execution
     * @param {Array} messages 
     * @param {Function} outputCallback 
     * @param {boolean} verbose 
     * @returns {Promise<boolean>}
     */
    async _llmAndParseOutput(messages, outputCallback, verbose) {
        const response = await llmInference(
            messages,
            this.model,
            true, // stream
            null, // temperature
            this.apiKey,
            this.baseUrl,
            outputCallback,
            this.llmArgs
        );

        this.memory.addMessage('assistant', response);

        // Check for JavaScript code blocks
        const codeBlocks = this._extractCodeBlocks(response);

        if (codeBlocks.length > 0 && !this.disableJavaScriptRun) {
            let lastResult = null;
            for (const code of codeBlocks) {
                try {
                    const result = this.jsInterpreter.execute(code);
                    lastResult = result;

                    if (!this.hideJavaScriptCode && verbose) {
                        console.log('\n--- JavaScript Execution Result ---');
                        console.log(result);
                        console.log('--- End Result ---\n');
                    }
                } catch (error) {
                    console.error('JavaScript execution error:', error.message);
                    this.memory.addMessage('system', `JavaScript execution error: ${error.message}`);
                    return false; // Continue conversation to handle error
                }
            }
            // Store the last result
            if (lastResult !== null && lastResult !== undefined) {
                this.jsRunResult = lastResult;
            }
        }

        return true; // Stop conversation
    }

    /**
     * Extract JavaScript code blocks from text
     * @param {string} text 
     * @returns {Array<string>}
     */
    _extractCodeBlocks(text) {
        const codeBlocks = [];
        const regex = /```(?:javascript|js)?\n([\s\S]*?)```/g;
        let match;
        
        while ((match = regex.exec(text)) !== null) {
            codeBlocks.push(match[1].trim());
        }
        
        return codeBlocks;
    }

    /**
     * Disable JavaScript execution
     */
    disableJavaScript() {
        this.disableJavaScriptRun = true;
    }

    /**
     * Enable JavaScript execution
     */
    enableJavaScript() {
        this.disableJavaScriptRun = false;
    }

    /**
     * Disable Python execution (alias for compatibility)
     */
    disablePython() {
        this.disableJavaScript();
    }

    /**
     * Enable Python execution (alias for compatibility)
     */
    enablePython() {
        this.enableJavaScript();
    }

    /**
     * User input method
     * @param {string|Array} input 
     * @param {boolean} verbose 
     * @returns {Promise<any>}
     */
    async userInput(input, verbose = true) {
        const result = await this._run(input, String, verbose);
        
        if (this.continueRun && this.runLevel === 0) {
            // Check if should continue
            const messages = this.memory.getMessages();
            const cutMessagesResult = cutMessages(messages, 2000);
            const prompt = "For the current status, no user input or confirmation is required. To continue the task, please reply yes. Otherwise, reply no.";
            cutMessagesResult.push({ role: 'system', content: prompt });

            const response = await llmInference(
                cutMessagesResult,
                'smart',
                false,
                null,
                this.apiKey,
                this.baseUrl,
                null,
                this.llmArgs
            );

            if (response.toLowerCase().includes('yes')) {
                return await this.run('ok');
            }
        }
        
        return result;
    }

    /**
     * Clear agent memory and state
     */
    clear() {
        this.memory.clear();
        this.jsInterpreter.clearGlobals();
        this.jsInterpreter.clearHistory();
    }

    /**
     * Enter temporary context
     */
    enter() {
        this.memory.enter();
        this.enterIndex = this.memory.enterIndex;
    }

    /**
     * Exit temporary context
     */
    exit() {
        this.memory.recover();
        this.enterIndex = null;
    }
}

module.exports = { Agent };