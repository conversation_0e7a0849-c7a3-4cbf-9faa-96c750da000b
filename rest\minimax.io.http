@apiKey=eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************.B6HbWRa_5cxuVnz1tN5x0Z-P7AGRXSpzkGDDD7lY480iPMEX9SY1_vvw-Dy5ehRVNSWvaKPvVJGAO7DENb4bucDJCKdCF749zvEYVM-mmbnF0tKoO2M77BqEFfoaM-jmH27SF7lUnx0-VVtY9jFyMTDBAebY62lK2TiiDgw2DWSdRkuVX9ThLXEaKrQiYIkTfqAhBmIUaHeYxvrhpbdCNrRmOYO55wmR76KEadYu_BJnKSVwEQaRyDC0O1PYPTNQt8h_SB5sm2HL75i0dm3qSgixIQ6z2mG1qfRzx5vwoTOgEFoWOpJTxkYJkbltpvgawu9FtgRVB3fmdpTez_tk1w
@groupId=1909853211993314096
###

curl --location 'https://api.minimax.io/v1/t2a_v2?GroupId={{groupId}}' \
--header 'Authorization: Bearer {{apiKey}}' \
--header 'Content-Type: application/json' \
--data '{
    "model":"speech-02-hd",
    "text":"Nửa đêm tỉnh dậy, tôi thấy bà cố đang gọi hàng trăm con chuột từ chum gạo",
    "stream":false,
    "voice_setting":{
        "voice_id":"moss_audio_e662b4eb-4fe9-11f0-9e4a-3ee691490080",
        "speed":1,
        "vol":1,
        "pitch":0
    },
    "audio_setting":{
        "sample_rate":32000,
        "bitrate":128000,
        "format":"mp3",
        "channel":1
    }
  }'


###
curl --location 'https://api.minimax.io/v1/t2a_v2?GroupId={{groupId}}' \
--header 'Authorization: Bearer {{apiKey}}' \
--header 'Content-Type: application/json' \
--data '{
    "model":"speech-02-hd",
    "text":"Nửa đêm tỉnh dậy, tôi thấy bà cố đang gọi hàng trăm con chuột từ chum gạo",
    "stream":false,
    "voice_setting":{
        "voice_id":"moss_audio_e662b4eb-4fe9-11f0-9e4a-3ee691490080",
        "speed":1,
        "vol":1,
        "pitch":0
    },
    "audio_setting":{
        "sample_rate":32000,
        "bitrate":128000,
        "format":"mp3",
        "channel":1
    }
  }'

###
curl --location 'https://api.minimax.io/v1/get_voice' \
--header 'content-type: application/json' \
--header 'authorization: Bearer {{apiKey}}' \
--data '{
    "voice_type":"voice_cloning"
}'

###

curl --location 'https://api.minimax.io/v1/t2a_async_v2?GroupId={{groupId}}' \
--header 'authorization: Bearer {{apiKey}}' \
--header 'Content-Type: application/json' \
--data '{
    "model": "speech-01-turbo",
    "text": "Nửa đêm tỉnh dậy, tôi thấy bà cố đang gọi hàng trăm con chuột từ chum gạo",
    "voice_setting": {
        "voice_id": "moss_audio_e662b4eb-4fe9-11f0-9e4a-3ee691490080",
        "speed": 1,
        "vol": 1,
        "pitch": 1,
        "emotion": "happy",
        "english_normalization": True
    },
    "pronunciation_dict":{
        "tone": ["omg/oh my god"]
    },
    "audio_setting": {
        "audio_sample_rate": 32000,
        "bitrate": 128000,
        "format": "mp3",
        "channel": 2
    }
}'

### status 
{
    "task_id": 95157322514444,
    "task_token": "eyJhbGciOiJSUz",
    "file_id": 95157322514444,
    "base_resp": {
        "status_code": 0,
        "status_msg": "success"
    }
}

### get
curl --location 'https://api.minimax.io/v1/query/t2a_async_query_v2?GroupId=${GroupId}&task_id=${task_id}' \
--header 'authorization: Bearer ${API_KEY}' \
--header 'content-type: application/json' \

###
{
    "task_id": 95157322514444,
    "status": "Processing", // Processing Success Failed  Expired
    "file_id": 95157322514496,
    "base_resp": {
        "status_code": 0,
        "status_msg": "success"
    }
}

###
curl --location 'https://api.minimax.io/v1/files/retrieve?GroupId=${group_id}&file_id=${file_id}' \
--header 'authority: api.minimax.io' \
--header 'content-type: application/json' \
--header 'Authorization: Bearer ${api_key}' 

###
{
    "file": {
        "file_id": {file_id},
        "bytes": 5896337,
        "created_at": 1700469398,
        "filename": "XXXXXX",
        "purpose": "retrieval",
        "download_url":"XXXXXXXXXXX"
    },
    "base_resp": {
        "status_code": 0,
        "status_msg": "success"
    }
}