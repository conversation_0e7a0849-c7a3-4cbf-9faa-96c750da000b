// Test script for the new extraction functionality with database support
const path = require('path');
const { testExtraction, testMultiPageExtraction } = require('./puppeteerService');


async function runTests() {
    console.log('=== Testing Novel Chapter Extraction with Database ===\n');

    // Initialize database
    const db = S.db

    try {
        // Test 1: Single page extraction
        console.log('Test 1: Single page extraction');
        console.log('URL: https://44xw.com/a/149/148289/');
        console.log('---');

        const singlePageResult = await testExtraction(null, 'https://44xw.com/a/149/148289/');

        console.log('\n=== Single Page Results ===');
        console.log(`Chapters found: ${singlePageResult.chapters.length}`);
        console.log(`Current page: ${singlePageResult.pagination.currentPage}`);
        console.log(`Next pages: ${singlePageResult.pagination.nextPages.length}`);

        if (singlePageResult.chapters.length > 0) {
            console.log('\nFirst 3 chapters:');
            singlePageResult.chapters.slice(0, 3).forEach(chapter => {
                console.log(`  ${chapter.index}. ${chapter.title}`);
                console.log(`     URL: ${chapter.fullUrl}`);
            });
        }

        if (singlePageResult.pagination.nextPages.length > 0) {
            console.log('\nNext pages available:');
            singlePageResult.pagination.nextPages.slice(0, 3).forEach(page => {
                console.log(`  Page ${page.pageNumber}: ${page.text} -> ${page.fullUrl}`);
            });
        }

        console.log('\n' + '='.repeat(50) + '\n');

        // Test 2: Multi-page extraction with database save (limited to 2 pages for testing)
        console.log('Test 2: Multi-page extraction with database save (2 pages)');
        console.log('Starting URL: https://44xw.com/a/149/148289/');
        console.log('---');

        const multiPageResult = await testMultiPageExtraction(null, 'https://44xw.com/a/149/148289/', 20, true);

        console.log('\n=== Multi-Page Results ===');
        console.log(`Total chapters: ${multiPageResult.totalChapters}`);
        console.log(`Pages processed: ${multiPageResult.totalPages}`);

        // Group chapters by page
        const chaptersByPage = {};
        multiPageResult.chapters.forEach(chapter => {
            if (!chaptersByPage[chapter.pageNumber]) {
                chaptersByPage[chapter.pageNumber] = [];
            }
            chaptersByPage[chapter.pageNumber].push(chapter);
        });

        Object.keys(chaptersByPage).forEach(pageNum => {
            console.log(`\nPage ${pageNum}: ${chaptersByPage[pageNum].length} chapters`);
            chaptersByPage[pageNum].slice(0, 2).forEach(chapter => {
                console.log(`  ${chapter.index}. ${chapter.title}`);
            });
            if (chaptersByPage[pageNum].length > 2) {
                console.log(`  ... and ${chaptersByPage[pageNum].length - 2} more chapters`);
            }
        });

        // Display database results if available
        if (multiPageResult.databaseResult) {
            console.log('\n=== Database Save Results ===');
            console.log(`Session ID: ${multiPageResult.databaseResult.sessionId}`);
            console.log(`Book ID: ${multiPageResult.databaseResult.bookId}`);
            console.log(`Chapters saved: ${multiPageResult.databaseResult.chaptersInserted}`);
        }

        if (multiPageResult.databaseStats) {
            console.log('\n=== Database Statistics ===');
            console.log(`Total books in DB: ${multiPageResult.databaseStats.totalBooks}`);
            console.log(`Total chapters in DB: ${multiPageResult.databaseStats.totalChapters}`);
        }

        console.log('\n=== Tests Completed Successfully! ===');
        return multiPageResult
    } catch (error) {
        console.error('Test failed:', error.message);
        console.error('Full error:', error);
    } finally {
        // Close database connection
        if (db) {
            await db.close();
            console.log('\nDatabase connection closed.');
        }
    }
}


module.exports = { runTests };
