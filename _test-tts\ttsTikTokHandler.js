const axios = require('axios');
const fs = require('fs');
const path = require('path');

const logger = console

const tiktok = {
  enabled: true,
  session_id: 'f99b95b348771c84efde83a0d457cc4d',
};
let BASE_URL = 'https://api16-normal-v6.tiktokv.com/media/api/text/speech/invoke';
let BASE_URL2 = 'https://api16-normal-c-useast2a.tiktokv.com/media/api/text/speech/invoke';

function hasOwnProperty(obj, prop) {
  return Object.prototype.hasOwnProperty.call(obj, prop);
}

const ttsTikTokHandler = async (voice, text) => {
  if (tiktok.session_id) {
    try {
      const url = `${BASE_URL}/?text_speaker=${voice}&req_text=${encodeURI(
        text,
      )}&speaker_map_type=0&aid=1233`;
      const res = await axios.post(url,{}, {
        headers: {
          'User-Agent': 'com.zhiliaoapp.musically/2022600030 (Linux; U; Android 7.1.2; es_ES; SM-G988N; Build/NRD90M;tt-ok/*********)',
          Cookie: `sessionid=${tiktok.session_id}`,
          'Accept-Encoding': 'gzip,deflate,compress'
        },
      });
      const json = res.data;
      console.log('json: ', json.data);
      if (hasOwnProperty(json, 'data') && hasOwnProperty(json.data, 'v_str') && typeof json.data.v_str === 'string') {
        // base64 decode data.v_str
        const buffer = Buffer.from(json.data.v_str, 'base64');
        return buffer;
      }
    } catch (error) {
      logger.error(error);
    }
  } else {
    logger.error('TikTok is not enabled or session_id is not set');
  }
  return null;
};

(async () => {
  const buffer = await ttsTikTokHandler('BV074_streaming', 'Tạo chuỗi ký và sinh hash');
  if (buffer) {
    fs.writeFileSync('./_test-tts/cache/tts/output33.mp3', buffer);
  }
})();
