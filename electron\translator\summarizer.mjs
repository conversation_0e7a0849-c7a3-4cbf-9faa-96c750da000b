import { ChatOpenAI } from '@langchain/openai';
import { SystemMessage, HumanMessage } from '@langchain/core/messages';
import { JsonOutputParser } from '@langchain/core/output_parsers';
import dotenv from 'dotenv';
dotenv.config();

const SUMMARY_INSTRUCTION = `
# Main task
You are a specialized assistant for summarizing Chinese stories into concise Vietnamese paragraphs while preserving the core plot and engaging storytelling style.

# Input data format
You will be given a JSON with the following structure:
{
    "memory": str,
    "main_text": {
        "1": str,
        "2": str,
        ...
    }
}

## memory

This field contains your memory of previous summaries as markdown-formatted text. This block is crucial for you to generate high quality summaries in Vietnamese. It tracks character information, plot points, and contextual details needed for consistent summarization.

### How to use the memory:

1. **Read the memory carefully** before starting summarization to understand:
   - Character names and their established Vietnamese translations
   - Key plot points and story progression
   - Important themes and narrative elements
   - Overall story context and setting

2. **Apply memory information** during summarization:
   - Use consistent character name translations
   - Reference previous plot developments
   - Maintain narrative continuity
   - Build upon established story elements

3. **Update the memory** after summarization:
   - Add new characters with their Vietnamese names
   - Note new plot developments and key events
   - Add important themes or story elements
   - NEVER remove existing information - only add or clarify
   - CRITICAL: NEVER use placeholders like "(không thay đổi)" or "unchanged" in any section
   - ALWAYS include the FULL content of EVERY section, even if nothing has changed
   - Remember: The memory does not accumulate between iterations - what you put in the memory field is ALL that will be available in the next iteration

If the memory is empty, fill it with all the necessary details for yourself in the next summarization iterations.

### Memory format (markdown):

The memory should be structured in markdown with clear sections. **Always list the original Chinese name first, followed by the Vietnamese translation in parentheses.**

Example memory structure:
# Characters
## Main Characters
- **路老** (Lộ Lão) - nam, nhân vật bí ẩn đã mất tích 10 năm
  - Có thể đã trở thành nô lệ hoặc vượt qua giới hạn sức mạnh
  - Tuổi thọ được kéo dài theo thông tin từ Địa Phủ

- **胡三老太爷** (Hồ Tam Lão Thái Gia) - nam, trưởng lão gia tộc
  - Có quyền điều tra tại Địa Phủ với tư cách Địa Tiên
  - Đang tìm kiếm sự thật về Lộ Lão

## Secondary Characters
- **黄三老太爷** (Hoàng Tam Lão Thái Gia) - nam, trưởng lão khác
  - Có mâu thuẫn với các trưởng lão khác về việc tìm kiếm Lộ Lão

# Plot Summary
## Current Arc: Bí ẩn về Lộ Lão
- Các trưởng lão tụ họp tại Thung Lũng Nhỏ để thảo luận về Lộ Lão
- Địa Phủ xác nhận Lộ Lão vẫn sống và tuổi thọ được kéo dài
- Họ đang tranh cãi về nguyên nhân và cách giải cứu Lộ Lão

# Key Themes
- Bí ẩn và sự thật ẩn giấu
- Mối quan hệ phức tạp giữa các gia tộc
- Quyền lực và trách nhiệm của các trưởng lão

# Important Locations
- **东北黄家小谷** (Thung Lũng Nhỏ Gia Đình Hoàng ở Đông Bắc) - nơi họp mặt
- **地府** (Địa Phủ) - nơi có thông tin về tuổi thọ
- **三个洞穴** (Ba hang động) - nơi các trưởng lão cư trú

**Important**: Always record both the original Chinese term and its Vietnamese translation. This helps maintain consistency in character names, locations, and key terms throughout the summary.

**WARNING**: NEVER use placeholders like "(không thay đổi)" or "no changes" in ANY section of the memory. Every section must contain FULL information, even if nothing new was added. Using placeholders or "without changes" notations will cause critical information loss because only the memory field propagates to future iterations.

The above is only an example of how memory can be formatted. You are free to use the format that most suits your needs and the specifics of the story you are summarizing at the moment.

## main_text

This part of the input JSON contains chunks of the Chinese text that must be summarized.

# Output format

You have to respond with a JSON with the following structure:
{
  "memory": str,
  "summary": {
    "1": str,
    "2": str,
    "3": str,
    ...
  }
}

**Example**: If the input has main_text with keys "1", "2", "3", "4", "5", then your response MUST be:
{
  "memory": "updated memory content...",
  "summary": {
    "1": "Hồng Lão Gia tò mò về cảm xúc kỳ lạ của người có duyên.",
    "2": "Trương Dương Lão Đạo Sĩ và những người khác đến tham gia cuộc trò chuyện về Trương Tắc.",
    "3": "Cảnh chuyển sang thung lũng nhỏ gia tộc Hoàng ở Đông Bắc với không khí huyền bí.",
    "4": "Ba hang động xuất hiện những đôi mắt lấp lánh của các sinh vật cổ đại.",
    "5": "Giọng nói uy quyền từ hang động thể hiện sự không hài lòng về việc đến muộn."
  }
}

**Note**: Each summary should be 1-2 sentences maximum, focusing on the main action or development in that chunk.

The memory field must contain the updated memory with information for further summarization in the upcoming iterations.
Remember, you won't see your summary from the current iteration. The only thing that will propagate to the next iteration is the "memory".
Make sure it contains all the necessary information to ensure consistent and high-quality summarization in Vietnamese.

The summary field mirrors the "main_text" field in the input JSON with corresponding chunk keys. They must contain corresponding summaries in Vietnamese.

**CRITICAL**: You MUST provide a summary for EVERY chunk in the main_text. If main_text has chunks "1", "2", "3", ..., "N", then your summary field MUST have summaries for "1", "2", "3", ..., "N". Do not skip any chunks.

# Summarization Guidelines

You are a specialized assistant for summarizing stories into concise Vietnamese paragraphs while preserving the core plot and engaging storytelling style.

**Core Principles:**
- **MANDATORY**: Provide a summary for EVERY single chunk in the input
- Keep important content and plot developments
- Remove unnecessary details or redundant information
- Condense long dialogues into one or two concise sentences
- Use smooth storytelling flow, can change narrative perspective to increase engagement
- **ALWAYS translate Chinese names to Vietnamese** (e.g., 张三 → Trương Tam, 李明 → Lý Minh)

**Summary Style:**
- **Concise but engaging**: Each summary should be 1-2 sentences maximum
- **Narrative flow**: Use smooth storytelling that flows naturally
- **Focus on action and progression**: What happens, who does what, key developments
- **Preserve atmosphere**: Keep the original tone and mood
- **Cultural adaptation**: Explain Chinese cultural elements when necessary for Vietnamese readers

**Length Guidelines:**
- Each chunk summary: 1-2 sentences (about 20-40 words)
- Focus on the most important event or development in each chunk
- If a chunk has minimal content, provide a brief one-sentence summary
`;

export class Summarizer {
    constructor({
        model = 'gpt-4o-mini',
        temperature = 0.3,
        windowSize = 10,  // Further reduced to 10 for better AI handling
        overlap = 0,      // No overlap to avoid duplicates
        apiKey = process.env.OPENAI_API_KEY
    } = {}) {
        this.memory = "";
        this.model = model;
        this.windowSize = windowSize;
        this.overlap = overlap;
        
        // Validate parameters
        if (windowSize >= 80) {
            throw new Error('Window size must be less than 80 due to API limitations');
        }
        if (overlap >= windowSize) {
            throw new Error('Overlap cannot be more than or equal to window size');
        }

        // Initialize OpenAI chat model with JSON mode
        this.chatModel = new ChatOpenAI({
            modelName: model,
            temperature: temperature,
            openAIApiKey: apiKey,
            modelKwargs: {
                response_format: { type: "json_object" }
            }
        });

        // Create prompt template - use raw string to avoid template parsing issues
        this.systemPrompt = SUMMARY_INSTRUCTION + "\n\nIMPORTANT: You must respond with valid JSON only. Do not include any text outside the JSON structure.";

        // Initialize JSON parser (not used directly but kept for compatibility)
        this.outputParser = new JsonOutputParser();
    }

    buildPrompt(chunks) {
        const chunksDict = {};
        chunks.forEach((chunk, index) => {
            chunksDict[String(index + 1)] = chunk;
        });

        const promptDict = {
            memory: this.memory,
            main_text: chunksDict
        };

        return JSON.stringify(promptDict, null, 2);
    }

    async generateResponse(prompt) {
        const maxRetries = 3;
        let lastError = null;
        
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                console.log(`Attempt ${attempt}/${maxRetries} - Generating summary...`);
                
                // Create messages manually to avoid template parsing issues
                const messages = [
                    { role: "system", content: this.systemPrompt },
                    { role: "user", content: prompt }
                ];
                
                // Use ChatOpenAI directly with messages
                const response = await this.chatModel.invoke(messages);
                
                console.log(`Response received, length: ${response.content.length} characters`);
                
                // Clean the response content
                let cleanContent = response.content.trim();
                
                // Remove any markdown code blocks if present
                if (cleanContent.startsWith('```json')) {
                    cleanContent = cleanContent.replace(/^```json\s*/, '').replace(/\s*```$/, '');
                }
                if (cleanContent.startsWith('```')) {
                    cleanContent = cleanContent.replace(/^```\s*/, '').replace(/\s*```$/, '');
                }
                
                // Try to find JSON object in the response
                const jsonStart = cleanContent.indexOf('{');
                const jsonEnd = cleanContent.lastIndexOf('}');
                
                if (jsonStart === -1 || jsonEnd === -1 || jsonStart >= jsonEnd) {
                    throw new Error('No valid JSON object found in response');
                }
                
                const jsonString = cleanContent.substring(jsonStart, jsonEnd + 1);
                
                // Parse JSON response
                let parsedResponse;
                try {
                    parsedResponse = JSON.parse(jsonString);
                } catch (parseError) {
                    console.error('JSON parse error:', parseError.message);
                    console.error('Attempting to parse:', jsonString.substring(0, 1000) + '...');
                    throw parseError;
                }
                
                // Validate response structure
                if (!parsedResponse.memory || !parsedResponse.summary) {
                    throw new Error('Response missing required fields: memory or summary');
                }

                if (typeof parsedResponse.memory !== 'string') {
                    throw new Error('Memory field must be a string');
                }

                if (typeof parsedResponse.summary !== 'object') {
                    throw new Error('Summary field must be an object');
                }

                // Debug: Log the summary structure
                console.log(`🔍 Summary keys received:`, Object.keys(parsedResponse.summary));
                console.log(`🔍 Summary content:`, JSON.stringify(parsedResponse.summary, null, 2));

                console.log(`Response parsed successfully, memory length: ${parsedResponse.memory.length}`);
                return parsedResponse;
                
            } catch (error) {
                console.error(`Attempt ${attempt} failed:`, error.message);
                lastError = error;
                
                if (attempt < maxRetries) {
                    console.log(`Retrying in 2 seconds...`);
                    await new Promise(resolve => setTimeout(resolve, 2000));
                }
            }
        }
        
        console.error('All attempts failed. Last error:', lastError);
        throw lastError;
    }

    generateSlidingWindows(chunks) {
        const windows = [];
        let i = 0;

        while (i < chunks.length) {
            const window = chunks.slice(i, i + this.windowSize);
            windows.push({ window, startIdx: i });

            // Move to next window without overlap for simplicity
            i += this.windowSize;
        }

        return windows;
    }

    extractRelevantSummaries(windowSummary) {
        // Since we're not using overlap anymore, always return all summaries
        return windowSummary;
    }

    async summarizeChunks(chunks, onProgress = null) {
        const summarizedChunks = [];
        const windows = this.generateSlidingWindows(chunks);
        
        console.log(`Processing ${windows.length} windows for ${chunks.length} chunks`);
        
        for (let i = 0; i < windows.length; i++) {
            const { window, startIdx } = windows[i];
            
            if (onProgress) {
                onProgress(i + 1, windows.length, `Processing window ${i + 1}/${windows.length}`);
            }
            
            console.log(`Summarizing window ${i + 1}/${windows.length} (${window.length} chunks)`);
            
            const prompt = this.buildPrompt(window);
            
            try {
                const modelResponse = await this.generateResponse(prompt);
                const { summary, memory } = modelResponse;
                
                this.memory = memory;
                
                // Check if we have summaries for all chunks
                const expectedKeys = [];
                for (let j = 1; j <= window.length; j++) {
                    expectedKeys.push(String(j));
                }

                const receivedKeys = Object.keys(summary);
                const missingKeys = expectedKeys.filter(key => !receivedKeys.includes(key));

                if (missingKeys.length > 0) {
                    console.warn(`⚠️ Missing summaries for chunks: ${missingKeys.join(', ')} in window ${i + 1}`);
                    console.warn(`Expected keys: ${expectedKeys.join(', ')}`);
                    console.warn(`Received keys: ${receivedKeys.join(', ')}`);

                    // Try to create summaries for missing chunks by using original text
                    for (const missingKey of missingKeys) {
                        const chunkIndex = parseInt(missingKey) - 1;
                        if (chunkIndex < window.length) {
                            const originalText = window[chunkIndex];
                            // Create a simple summary as fallback
                            summary[missingKey] = `[Tóm tắt tự động] ${originalText.substring(0, Math.min(100, originalText.length))}...`;
                            console.log(`📝 Created fallback summary for chunk ${missingKey}`);
                        }
                    }
                }

                // Convert numbered dictionary to ordered list
                const windowSummary = [];
                for (let j = 1; j <= window.length; j++) {
                    if (summary[String(j)]) {
                        windowSummary.push(summary[String(j)]);
                    } else {
                        console.warn(`Missing summary for chunk ${j} in window ${i + 1}`);
                        // Use the fallback summary that was created earlier
                        windowSummary.push(summary[String(j)] || `[MISSING SUMMARY ${j}]`);
                    }
                }
                
                // Extract all summaries (no overlap)
                const relevantSummaries = this.extractRelevantSummaries(windowSummary);
                summarizedChunks.push(...relevantSummaries);
                
                console.log(`Completed window ${i + 1}/${windows.length}`);
                console.log(`Memory length: ${this.memory.length} characters`);
                console.log(`Summarized chunks in this window: ${relevantSummaries.length}`);
                
            } catch (error) {
                console.error(`Error processing window ${i + 1}:`, error);
                
                // Add placeholder summaries for this window to continue processing
                const windowSummary = window.map((_, idx) => `[ERROR SUMMARIZING CHUNK ${startIdx + idx + 1}]`);
                const relevantSummaries = this.extractRelevantSummaries(windowSummary);
                summarizedChunks.push(...relevantSummaries);
                
                console.log(`Added ${relevantSummaries.length} error placeholders for window ${i + 1}`);
            }
        }
        
        return summarizedChunks;
    }

    // Utility method to split text into sentences/paragraphs
    static splitTextIntoChunks(text, chunkType = 'paragraph') {
        if (chunkType === 'paragraph') {
            return text.split(/\n\s*\n/).filter(chunk => chunk.trim().length > 0);
        } else if (chunkType === 'sentence') {
            return text.split(/[.!?]+/).filter(chunk => chunk.trim().length > 0);
        } else {
            throw new Error('chunkType must be either "paragraph" or "sentence"');
        }
    }

    // Utility method to join summarized chunks back into text
    static joinSummarizedChunks(summarizedChunks, chunkType = 'paragraph') {
        if (chunkType === 'paragraph') {
            return summarizedChunks.join('\n\n');
        } else if (chunkType === 'sentence') {
            return summarizedChunks.join('. ');
        } else {
            throw new Error('chunkType must be either "paragraph" or "sentence"');
        }
    }
}
