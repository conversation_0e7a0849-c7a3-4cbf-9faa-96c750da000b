
import { translateWithGemini } from "@/lib/geminiApi";
import { translateWithOpenAI } from "@/lib/openaiApi";
import { translateWithDeepseek } from "@/lib/deepseekApi";
import { getProviderForModel } from "@/lib/modelUtils";


/**
 * Translate text using the appropriate API based on the selected model
 * @param options Translation options
 * @returns Translation results
 */
export async function translateText(options) {
  let provider = getProviderForModel(options.model);
  

  if (!provider) {
    throw new Error(`Unknown model provider for model: ${options.model}`);
  }

  switch (provider) {
    case "gemini":
      return translateWithGemini(options);
    case "openai":
      return translateWithOpenAI(options);
    case "deepseek":
      return translateWithDeepseek(options);
    default:
      throw new Error(`Unsupported provider: ${provider}`);
  }
}
