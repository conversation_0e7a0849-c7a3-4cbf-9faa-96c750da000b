// Test script for Cloudflare bypass and database handling
const path = require('path');
const { testExtraction, testMultiPageExtraction } = require('./puppeteerService');

// Initialize database for testing
async function initializeDatabase() {
    try {
        const Database = require('../db/index');
        const dbPath = path.join(__dirname, '../../data/novels.db');
        
        // Create data directory if it doesn't exist
        const fs = require('fs');
        const dataDir = path.dirname(dbPath);
        if (!fs.existsSync(dataDir)) {
            fs.mkdirSync(dataDir, { recursive: true });
        }
        
        const db = new Database(dbPath);
        await db.initializeTables();
        
        console.log(`✅ Database initialized at: ${dbPath}`);
        return db;
    } catch (error) {
        console.error('❌ Failed to initialize database:', error);
        return null;
    }
}

async function testCloudflareBypass() {
    console.log('🛡️ Testing Cloudflare Bypass and Database Handling\n');
    
    // Initialize database
    const db = await initializeDatabase();
    if (db) {
        // Make database available globally for the test
        global.S = global.S || {};
        global.S.db = db;
        console.log('📊 Database ready for testing\n');
    } else {
        console.log('⚠️ Database initialization failed, continuing without database\n');
    }
    
    try {
        // Test URLs that might have Cloudflare protection
        const testUrls = [
            'https://44xw.com/a/149/148289/',
            'https://44xw.com/a/149/148289/p2.html',
            'https://44xw.com/a/149/148289/p3.html'
        ];
        
        console.log('🔍 Testing single page extraction with Cloudflare bypass...');
        console.log('---');
        
        for (let i = 0; i < testUrls.length; i++) {
            const url = testUrls[i];
            console.log(`\n📄 Test ${i + 1}: ${url}`);
            
            try {
                const result = await testExtraction(null, url);
                
                console.log(`✅ Success: Found ${result.chapters.length} chapters`);
                if (result.bookInfo) {
                    console.log(`📚 Book: ${result.bookInfo.title}`);
                    console.log(`👤 Author: ${result.bookInfo.author || 'N/A'}`);
                    console.log(`📂 Category: ${result.bookInfo.category || 'N/A'}`);
                }
                
                // Test only first URL to avoid too many requests
                break;
                
            } catch (error) {
                console.error(`❌ Failed for ${url}:`, error.message);
                
                // If it's a Cloudflare issue, continue to next URL
                if (error.message.includes('Cloudflare') || error.message.includes('verification')) {
                    console.log('🔄 Trying next URL...');
                    continue;
                }
                
                // For other errors, break
                break;
            }
        }
        
        console.log('\n' + '='.repeat(60) + '\n');
        
        // Test multi-page extraction with database save
        console.log('📚 Testing multi-page extraction with database save...');
        console.log('Starting URL: https://44xw.com/a/149/148289/');
        console.log('Pages to extract: 2 (for testing)');
        console.log('---');
        
        const multiPageResult = await testMultiPageExtraction(
            null, 
            'https://44xw.com/a/149/148289/', 
            2,    // Only 2 pages for testing
            true  // Save to database
        );
        
        console.log('\n📊 Multi-Page Results Summary:');
        console.log(`📖 Total chapters: ${multiPageResult.totalChapters}`);
        console.log(`📄 Pages processed: ${multiPageResult.totalPages}`);
        
        if (multiPageResult.bookInfo) {
            console.log(`📚 Book: ${multiPageResult.bookInfo.title}`);
            console.log(`👤 Author: ${multiPageResult.bookInfo.author || 'N/A'}`);
            console.log(`📂 Category: ${multiPageResult.bookInfo.category || 'N/A'}`);
            console.log(`📅 Status: ${multiPageResult.bookInfo.status || 'N/A'}`);
        }
        
        // Display database results if available
        if (multiPageResult.databaseResult) {
            console.log('\n💾 Database Save Results:');
            console.log(`🆔 Session ID: ${multiPageResult.databaseResult.sessionId}`);
            console.log(`📚 Book ID: ${multiPageResult.databaseResult.bookId}`);
            console.log(`📖 Chapters saved: ${multiPageResult.databaseResult.chaptersInserted}`);
            console.log(`✅ Success: ${multiPageResult.databaseResult.success}`);
        }
        
        if (multiPageResult.databaseStats) {
            console.log('\n📈 Database Statistics:');
            console.log(`📚 Total books in DB: ${multiPageResult.databaseStats.totalBooks}`);
            console.log(`📖 Total chapters in DB: ${multiPageResult.databaseStats.totalChapters}`);
        }
        
        if (multiPageResult.databaseError) {
            console.log('\n⚠️ Database Error (but extraction succeeded):');
            console.log(`❌ Error: ${multiPageResult.databaseError}`);
        }
        
        console.log('\n🎉 All tests completed successfully!');
        
        // Show next steps
        console.log('\n📋 Next Steps:');
        console.log('1. Check database: node electron/db/db-manager.js books');
        console.log('2. View chapters: node electron/db/db-manager.js chapters 1');
        console.log('3. Database stats: node electron/db/db-manager.js stats');
        
    } catch (error) {
        console.error('💥 Test failed:', error.message);
        console.error('Full error:', error);
        
        // Provide troubleshooting tips
        console.log('\n🔧 Troubleshooting Tips:');
        console.log('1. If Cloudflare error: Wait and try again, or use different URL');
        console.log('2. If database error: Run "node electron/db/fix-database.js"');
        console.log('3. If browser error: Close all browser windows and try again');
        
    } finally {
        // Close database connection
        if (db) {
            await db.close();
            console.log('\n🔌 Database connection closed.');
        }
    }
}

// Helper function to test just Cloudflare bypass without extraction
async function testCloudflareOnly() {
    console.log('🛡️ Testing Cloudflare Bypass Only\n');
    
    try {
        const { initWebService, performExtraction } = require('./playwrightLogic');
        
        // Initialize browser
        await initWebService();
        console.log('🌐 Browser initialized');
        
        // Test navigation to Cloudflare-protected site
        const testUrl = 'https://44xw.com/a/149/148289/';
        console.log(`🔗 Navigating to: ${testUrl}`);
        
        // This will trigger Cloudflare handling
        const result = await performExtraction(null, testUrl);
        
        console.log('✅ Successfully bypassed Cloudflare protection');
        console.log(`📖 Found ${result.chapters.length} chapters`);
        
        if (result.bookInfo) {
            console.log(`📚 Book: ${result.bookInfo.title}`);
        }
        
    } catch (error) {
        console.error('❌ Cloudflare bypass test failed:', error.message);
    }
}

// Command line interface
async function runTests() {
    const args = process.argv.slice(2);
    const testType = args[0] || 'full';
    
    switch (testType) {
        case 'cloudflare':
            await testCloudflareOnly();
            break;
        case 'full':
        default:
            await testCloudflareBypass();
            break;
    }
}

// Run tests if this file is executed directly
if (require.main === module) {
    runTests().then(() => {
        console.log('\n✨ Test script finished.');
        process.exit(0);
    }).catch(error => {
        console.error('\n💥 Test script failed:', error);
        process.exit(1);
    });
}

module.exports = { testCloudflareBypass, testCloudflareOnly };
