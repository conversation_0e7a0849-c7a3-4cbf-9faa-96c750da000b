// tts/streamlabsPolly.js
const fs = require('fs');
const axios = require('axios');
const  config  = {
  settings: {
    tts: {
      streamlabs_polly_voice: 'Emma' // hoặc null nếu muốn ép người dùng chọn
    }
  }
}
function checkRateLimit(response) {
  // Dựa vào status code hoặc headers. Đơn giản:
  if (response.status === 429) return false;
  return true;
}
const voices = [
  "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
  "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>",
  "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"
];

class StreamlabsPolly {
  constructor() {
    this.url = 'https://streamlabs.com/polly/speak';
    this.max_chars = 550;
    this.voices = voices;
  }

  getRandomVoice() {
    return this.voices[Math.floor(Math.random() * this.voices.length)];
  }

  async run(text, filepath, randomVoice = false) {
    const voice = randomVoice
      ? this.getRandomVoice()
      : config.settings.tts.streamlabs_polly_voice;

    if (!voice || !voices.includes(voice)) {
      throw new Error(`Invalid voice. Please set a valid voice from: ${voices.join(', ')}`);
    }

    const body = new URLSearchParams({
      voice,
      text,
      service: 'polly'
    });

    try {
      const response = await axios.post(this.url, body, {
        headers: {
          'Referer': 'https://streamlabs.com/',
          'Content-Type': 'application/x-www-form-urlencoded',
        }
      });

      if (!checkRateLimit(response)) {
        console.log('Rate limited, retrying...');
        return this.run(text, filepath, randomVoice);
      }

      const speakUrl = response.data?.speak_url;
      if (!speakUrl) throw new Error('No speak_url in response');

      const voiceData = await axios.get(speakUrl, { responseType: 'arraybuffer' });
      fs.writeFileSync(filepath, voiceData.data);
      console.log(`✅ Saved to: ${filepath}`);

    } catch (err) {
      const msg = err.response?.data?.error;
      if (msg === 'No text specified!') {
        throw new Error('Please specify a text to convert to speech.');
      }

      console.error('❌ Error occurred calling Streamlabs Polly:', err.message);
    }
  }
}

module.exports = StreamlabsPolly;

const tts = new StreamlabsPolly();

tts.run("Hello, this is a test message from Streamlabs Polly!", "_test-tts/cache/tts/output.wav", false)
  .then(() => console.log("Done"))
  .catch(err => console.error(err));