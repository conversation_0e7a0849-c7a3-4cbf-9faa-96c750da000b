// Quick fix script for existing database schema issues
const path = require('path');
const knex = require('knex');

async function fixDatabase() {
    const dbPath = path.join(__dirname, '../../data/novels.db');
    
    console.log('🔧 Fixing database schema...');
    console.log(`Database path: ${dbPath}`);
    
    const db = knex({
        client: 'sqlite3',
        connection: {
            filename: dbPath,
        },
        useNullAsDefault: true,
    });

    try {
        // Check if books table exists
        const booksExists = await db.schema.hasTable('books');
        if (!booksExists) {
            console.log('❌ Books table does not exist. Please run the full initialization first.');
            return;
        }

        console.log('✅ Books table found');

        // Check and add missing columns
        let changesMade = false;

        // Check for category column
        const hasCategory = await db.schema.hasColumn('books', 'category');
        if (!hasCategory) {
            console.log('➕ Adding category column...');
            await db.schema.alterTable('books', (table) => {
                table.string('category', 200).nullable();
            });
            console.log('✅ Category column added');
            changesMade = true;
        } else {
            console.log('✅ Category column already exists');
        }

        // Check for last_update_info column
        const hasLastUpdateInfo = await db.schema.hasColumn('books', 'last_update_info');
        if (!hasLastUpdateInfo) {
            console.log('➕ Adding last_update_info column...');
            await db.schema.alterTable('books', (table) => {
                table.string('last_update_info', 200).nullable();
            });
            console.log('✅ Last_update_info column added');
            changesMade = true;
        } else {
            console.log('✅ Last_update_info column already exists');
        }

        if (changesMade) {
            console.log('\n🎉 Database schema fixed successfully!');
            console.log('You can now run the extraction tests again.');
        } else {
            console.log('\n✅ Database schema is already up to date.');
        }

        // Show current table structure
        console.log('\n📋 Current books table structure:');
        const columns = await db('books').columnInfo();
        Object.keys(columns).forEach(col => {
            const info = columns[col];
            console.log(`   ${col}: ${info.type}${info.nullable ? ' (nullable)' : ' (not null)'}`);
        });

        // Show record count
        const count = await db('books').count('id as count').first();
        console.log(`\n📊 Books table contains ${count.count} records`);

    } catch (error) {
        console.error('❌ Error fixing database:', error);
        throw error;
    } finally {
        await db.destroy();
        console.log('\n🔌 Database connection closed');
    }
}

// Run the fix
if (require.main === module) {
    fixDatabase().then(() => {
        console.log('\n✨ Fix completed successfully!');
        process.exit(0);
    }).catch(error => {
        console.error('\n💥 Fix failed:', error);
        process.exit(1);
    });
}

module.exports = fixDatabase;
