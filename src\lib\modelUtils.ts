export const AVAILABLE_MODELS = [
  // Gemini models
  {
    id: "gemini-2.5-pro-exp-03-25",
    name: "Gemini 2.5 Pro Experimental",
    provider: "gemini",
    description: {
      vi: "<PERSON><PERSON> hình tiên tiến nhất của Google, phù hợp cho dịch thuật chất lượng cao",
      en: "Google's most advanced model, suitable for high-quality translations"
    }
  },
  {
    id: "gemini-2.0-flash",
    name: "Gemini 2.0 Flash",
    provider: "gemini",
    description: {
      vi: "<PERSON>ệu suất nhanh và cân bằng, phù hợp cho dịch thuật thông thường",
      en: "Fast and balanced performance, suitable for routine translations"
    }
  },
  {
    id: "gemini-2.5-flash-preview-05-20",
    name: "Gemini 2.5 Flash Preview",
    provider: "gemini",
    description: {
      vi: "Mô hình tiên tiến nhất của Google, phù hợp cho dịch thuật chất lượng cao",
      en: "Google's most advanced model, suitable for high-quality translations"
    }
  },

  // OpenAI models
  {
    id: "gpt-4o",
    name: "GPT-4o (OpenAI)",
    provider: "openai",
    description: {
      vi: "Mô hình mạnh mẽ nhất của OpenAI, chất lượng dịch thuật cao",
      en: "OpenAI's most powerful model, high-quality translations"
    }
  },
  {
    id: "gpt-3.5-turbo",
    name: "GPT-3.5 Turbo (OpenAI)",
    provider: "openai",
    description: {
      vi: "Mô hình cân bằng giữa tốc độ và chất lượng, chi phí thấp",
      en: "Balanced model between speed and quality, lower cost"
    }
  },

  // DeepSeek models
  {
    id: "deepseek-chat",
    name: "DeepSeek Chat",
    provider: "deepseek",
    description: {
      vi: "Mô hình chat của DeepSeek, hỗ trợ dịch thuật đa ngôn ngữ",
      en: "DeepSeek's chat model, supports multilingual translation"
    }
  }
];

/**
 * Get the provider for a given model ID
 * @param modelId The model ID to look up
 * @returns The provider name ("gemini", "openai", "deepseek") or undefined if not found
 */
export function getProviderForModel(modelId: string) {
  // console.log(`getProviderForModel called with modelId: ${modelId}`);
  
  // First, try to find the exact model in our list
  const model = AVAILABLE_MODELS.find(m => m.id === modelId);
  if (model) {
    return model.provider;
  }

  // If not found, try to identify by pattern matching
  const modelIdLower = modelId.toLowerCase();

  // Check for Gemini models
  if (modelIdLower.includes('gemini')) {
    return "gemini";
  }

  // Check for OpenAI models
  if (modelIdLower.includes('gpt') || modelIdLower.includes('openai')) {
    return "openai";
  }

  // Check for DeepSeek models
  if (modelIdLower.includes('deepseek')) {
    return "deepseek";
  }

  // If no pattern matches, return undefined
  return undefined;
}