const vi = {
  common: {
    appTitle: "SubtitleAI",
    appDescription: "Dịch phụ đề từ tệp SRT sử dụng Gemini AI",
    loading: "<PERSON>ang tải...",
    save: "<PERSON><PERSON><PERSON>",
    cancel: "Hủy",
    retry: "Th<PERSON> lại",
    retryAll: "Thử lại tất cả",
    retrying: "Đang thử lại...",
    export: "Xuất",
    edit: "Sửa",
    pause: "Tạm dừng",
    resume: "Tiếp tục",
    stop: "Dừng",
    expand: "Mở rộng",
    collapse: "Thu gọn",
    error: "Lỗi",
    success: "Thành công",
    close: "Đóng",
    clear: "Xóa",
    delete: "Xóa",
    play: "<PERSON><PERSON><PERSON>",
    apply: 'Áp dụng'
  },
  preview: {
    title: "Xem trước video",
    description: "Tải lên video để xem trước với phụ đề",
    uploadVideo: "Tải lên video để xem trước với phụ đề",
    hideSubtitles: "Ẩn phụ đề",
    showSubtitles: "Hiện phụ đề",
    translatedOnly: "Bản dịch",
    bilingual: "<PERSON> ngữ",
    originalOnly: "Nguyên bản",
    loadingVideo: "Đang tải video...",
    videoName: "Tên video:",
    videoDuration: "Thời lượng:",
    selectVideo: "Chọn video",
    dropVideoHere: "Thả video của bạn vào đây...",
    invalidVideoType: "Loại tệp không hợp lệ. Vui lòng chọn tệp video.",
    dragAndDropVideo: "Kéo và thả tệp video vào đây",
    fullScreenSubtitleTip: "Phụ đề vẫn hiển thị trong chế độ toàn màn hình."
  },
    subtitleTable: {
    id: "ID",
    time: "Thời gian",
    originalText: "Văn bản gốc",
    translation: "Bản dịch",
    status: "Trạng thái",
    statusTranslation: "Trạng thái dịch",
    action: "Hành động",
    batch: "Nhóm",
    showing: "Đang hiển thị",
    subtitles: "phụ đề",
    translated: "đã dịch",
    errors: "lỗi",
    expandTable: "Mở rộng bảng",
    collapseTable: "Thu gọn bảng",
    waitingToTranslate: "Đang chờ dịch...",
    translating: "Đang dịch...",
    aiSuggestions: "Gợi ý từ AI",
    chooseSuggestion: "Chọn một bản dịch phù hợp hơn",
    clickToApply: "Nhấn để áp dụng",
    noSuggestions: "Không có gợi ý nào",
    suggestBetterTranslation: "Gợi ý bản dịch tốt hơn"
  },
  fileUpload: {
    orClickToSelect: "hoặc nhấp để chọn tệp"
  },
    batchErrorDisplay: {
    failedBatches: "{count} nhóm {batches} thất bại",
    batch: "nhóm",
    batches: "nhóm",
    description: "Mỗi nhóm chứa tối đa 10 phụ đề. Thử lại các nhóm thất bại để hoàn thành việc dịch.",
    retryBatch: "Thử lại nhóm"
  },
  errors: {
    apiKeyRequired: "Cần có khóa API. Vui lòng cung cấp khóa API hợp lệ.",
    fileRequired: "Vui lòng chọn tệp SRT để dịch.",
    translationError: "Lỗi dịch",
    translationErrorDescription: "Đã xảy ra lỗi trong quá trình dịch:",
    rateLimit: "Đã vượt quá giới hạn tần suất. Vui lòng đợi một lát trước khi thử lại.",
    forbidden: "Truy cập API bị cấm. Vui lòng kiểm tra khóa API của bạn.",
    unauthorized: "Truy cập API không được ủy quyền. Khóa API của bạn có thể không hợp lệ.",
    serverError: "Lỗi máy chủ từ API Gemini. Vui lòng thử lại sau.",
    translationSuggestionFailed: "Không thể tạo gợi ý từ AI. Vui lòng thử lại.",
    searchTermRequired: "Vui lòng nhập từ cần tìm."
  },

  batchReplace: {
    title: "Thay thế hàng loạt",
    searchTerm: "Từ cần tìm",
    replaceTerm: "Từ thay thế",
    searchPlaceholder: "Nhập từ cần tìm...",
    replacePlaceholder: "Nhập từ thay thế...",
    replace: "Thay thế",
    replacementComplete: "Đã thay thế trong {count} phụ đề"
  },

  terminologyDictionary: {
    title: "Từ điển thuật ngữ",
    entries: "{count} thuật ngữ",
    addNew: "Thêm thuật ngữ",
    clear: "Xóa tất cả",
    search: "Tìm kiếm thuật ngữ...",
    originalTerm: "Thuật ngữ gốc",
    translation: "Bản dịch",
    noEntries: "Chưa có thuật ngữ nào",
    termRequired: "Thuật ngữ gốc không được để trống",
    saved: "Đã lưu từ điển thuật ngữ",
    edit: "Chỉnh sửa từ điển",
    buildingDictionary: "Đang xây dựng từ điển thuật ngữ...",
    dictionaryBuilt: "Đã xây dựng xong từ điển thuật ngữ",
    continueTranslation: "Dịch tiếp",
    startNewTranslation: "Dịch từ đầu"
  },

  translationSettings: {
    translationInProgress: "Đang dịch...",
    translationPaused: "Đã tạm dừng dịch",
    contextPrompt: "Dựa vào các phụ đề đã dịch trước đó:"
  },

  voiceConfig: {
    title: "Cấu hình giọng đọc",
    language: "Ngôn ngữ lồng tiếng",
    voice1: "Giọng lồng tiếng 1",
    voice2: "Giọng lồng tiếng 2",
    voice3: "Giọng lồng tiếng 3",
    voice4: "Giọng lồng tiếng 4",
    voice5: "Giọng lồng tiếng 5",
    voiceType: "Loại giọng",
    speed: "Tốc độ",
    volume: "Âm lượng",
    pitch: "Cao độ",
    rate: "Rate",
    trim: "Trim",
    reverb: "Độ vang",
    masterVolume: "Âm lượng tổng",
    configApplied: "Đã áp dụng cấu hình giọng đọc",
    selectAll: "Chọn giọng cho tất cả",
    generateAudio: "Tạo âm thanh",
    generateForOriginal: "Tạo âm thanh cho văn bản gốc",
    generateForTranslated: "Tạo âm thanh cho văn bản đã dịch",
    generateForTranslatedNotGenerated: "Tạo âm thanh cho bản dịch chưa có âm thanh",
    generateForOriginalNotGenerated: "Tạo âm thanh cho bản gốc chưa có âm thanh",
    joinAudios: "Nối các file âm thanh",
    audioGenerated: "Đã tạo âm thanh thành công",
    audioJoined: "Đã nối các file âm thanh thành công",
    noAudioToJoin: "Không có file âm thanh để nối",
    processing: "Đang xử lý...",
    selectVoice: "Chọn giọng đọc",
    enabledForAll: "đã được bật cho tất cả phụ đề",
    disabledForAll: "đã được tắt cho tất cả phụ đề"
  },
};
export default vi;
