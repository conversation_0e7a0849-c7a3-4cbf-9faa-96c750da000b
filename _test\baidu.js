const axios = require('axios');
const { parse: urlParse } = require('url');

class Tse {
  constructor() {
    this.author = 'Ulion.Tse';
    this.beginTime = Date.now();
    this.defaultSessionFreq = 1000;
    this.defaultSessionSeconds = 1500 * 1000; // in ms
    this.transformEnTranslatorPool = ['Itranslate', 'Lingvanex', 'MyMemory'];
    this.autoPool = ['auto', 'detect', 'auto-detect'];
    this.zhPool = ['zh', 'zh-CN', 'zh-CHS', 'zh-Hans', 'zh-Hans_CN', 'cn', 'chi'];
  }

  static getHeaders(hostUrl, options = {}) {
    const {
      ifApi = false,
      ifRefererForHost = true,
      ifAjaxForApi = true,
      ifJsonForApi = false,
      ifMultipartForApi = false,
      ifHttpOverrideForApi = false,
    } = options;

    const userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";
    const urlPath = urlParse(hostUrl).path;
    const hostHeaders = {
      [ifRefererForHost ? 'Referer' : 'Host']: hostUrl,
      "User-Agent": userAgent,
    };

    const apiHeaders = {
      'Origin': hostUrl.split(urlPath)[0] || hostUrl,
      'Referer': hostUrl,
      'X-Requested-With': 'XMLHttpRequest',
      'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
      "User-Agent": userAgent,
    };

    if (ifApi && !ifAjaxForApi) {
      delete apiHeaders['X-Requested-With'];
      apiHeaders['Content-Type'] = 'text/plain';
    }
    if (ifApi && ifJsonForApi) {
      apiHeaders['Content-Type'] = 'application/json';
    }
    if (ifApi && ifMultipartForApi) {
      delete apiHeaders['Content-Type'];
    }
    if (ifApi && ifHttpOverrideForApi) {
      apiHeaders['X-HTTP-Method-Override'] = 'GET';
    }

    return ifApi ? apiHeaders : hostHeaders;
  }
}

class BaiduV1 extends Tse {
  constructor() {
    super();
    this.hostUrl = 'https://fanyi.baidu.com';
    this.apiUrl = 'https://fanyi.baidu.com/transapi';
    this.getLangUrl = null;
    this.getLangUrlPattern = /https:\/\/fanyi-cdn\.cdn\.bcebos\.com\/webStatic\/translation\/js\/index\.(.*?)\.js/;
    this.hostHeaders = this.constructor.getHeaders(this.hostUrl);
    this.apiHeaders = this.constructor.getHeaders(this.hostUrl, { ifApi: true });
    this.languageMap = null;
    this.session = null;
    this.queryCount = 0;
    this.outputZh = 'zh';
    this.inputLimit = 5000;
  }

  async baiduApi(queryText, fromLanguage = 'auto', toLanguage = 'en', options = {}) {
    const {
      timeout = 10000,
      proxies = null,
      sleepSeconds = 0,
      isDetailResult = false,
      updateSessionAfterFreq = this.defaultSessionFreq,
      updateSessionAfterSeconds = this.defaultSessionSeconds
    } = options;

    const now = Date.now();
    const shouldUpdateSession = !(
      this.session &&
      this.languageMap &&
      this.queryCount < updateSessionAfterFreq &&
      (now - this.beginTime) < updateSessionAfterSeconds
    );

    if (shouldUpdateSession) {
      this.session = axios.create({ timeout, proxy: proxies || false });

      await this.session.get(this.hostUrl, { headers: this.hostHeaders });
      const html = await this.session.get(this.hostUrl, { headers: this.hostHeaders }).then(res => res.data);

      const match = html.match(this.getLangUrlPattern);
      if (match) {
        this.getLangUrl = match[0];
      }
    }

    const formData = new URLSearchParams();
    formData.append('from', fromLanguage);
    formData.append('to', toLanguage);
    formData.append('query', queryText);
    formData.append('source', 'txt');

    const response = await this.session.post(this.apiUrl, formData.toString(), {
      headers: this.apiHeaders,
    });

    this.queryCount += 1;
    if (sleepSeconds > 0) {
      await new Promise(resolve => setTimeout(resolve, sleepSeconds * 1000));
    }

    const data = response.data;
    return isDetailResult ? data : data.data.map(item => item.dst).join('\n');
  }
}

class TS {
  constructor(srclang = 'zh', tgtlang = 'en', proxy = null) {
    this.srclang = srclang;
    this.tgtlang = tgtlang;
    this.proxy = proxy;
    this.engine = new BaiduV1();
  }

  langmap() {
    return {
      es: "spa", ko: "kor", fr: "fra", ja: "jp", cht: "cht", vi: "vie", uk: "ukr"
    };
  }

  async translate(query) {
    return this.engine.baiduApi(query, this.srclang, this.tgtlang, { proxies: this.proxy });
  }
}

(async () => {
  const translator = new TS('zh', 'en');
  const result = await translator.translate('你好，世界！');
  console.log(result);
})();



module.exports = TS;
