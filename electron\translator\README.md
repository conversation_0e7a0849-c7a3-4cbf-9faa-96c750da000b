# Novel Translator & Summarizer

A powerful AI-powered tool for translating and summarizing Chinese text files to Vietnamese with memory context for consistency.

## Features

- **Translation**: Translate Chinese text to Vietnamese with context memory
- **Summarization**: Generate Vietnamese summaries of Chinese text
- **Memory System**: Maintains character names, terminology, and plot consistency across chunks
- **Resume Support**: Continue from where you left off
- **Sliding Window**: Process large texts efficiently with overlapping windows
- **Progress Tracking**: Save and restore progress automatically

## Installation

1. Make sure you have Node.js installed
2. Install dependencies:
```bash
npm install
```

3. Set up your OpenAI API key:
```bash
export OPENAI_API_KEY="your-api-key-here"
```

Or create a `.env` file:
```
OPENAI_API_KEY=your-api-key-here
```

## Usage

### Translation Commands

#### Basic Translation
```bash
node index.mjs translate -i input.txt -o translated.txt
```

#### Translation with Options
```bash
node index.mjs translate -i input.txt -o translated.txt -m gpt-4o-mini --max-chunks 50 -t paragraph
```

#### Resume Translation
```bash
node index.mjs resume -i input.txt -o translated.txt
```

### Summarization Commands

#### Basic Summarization
```bash
node index.mjs summarize -i input.txt -o summary.txt
```

#### Summarization with Options
```bash
node index.mjs summarize -i input.txt -o summary.txt -m gpt-4o-mini --max-chunks 30 -t paragraph
```

#### Resume Summarization
```bash
node index.mjs resume-summary -i input.txt -o summary.txt
```

## Command Options

### Common Options
- `-i, --input <file>`: Input text file path (required)
- `-o, --output <file>`: Output file path (default: translated.txt or summary.txt)
- `-m, --model <model>`: OpenAI model name (default: gpt-4o-mini)
- `--max-chunks <number>`: Maximum number of chunks to process (-1 for all)
- `-r, --resume`: Resume from where it stopped
- `-t, --chunk-type <type>`: How to split text: paragraph or sentence (default: paragraph)

### Available Commands
- `translate`: Translate Chinese text to Vietnamese
- `resume`: Resume previous translation
- `summarize`: Summarize Chinese text to Vietnamese
- `resume-summary`: Resume previous summarization

## How It Works

### Memory System
Both translation and summarization use a sophisticated memory system that tracks:

**For Translation:**
- Character names and their Vietnamese translations
- Relationships and forms of address
- Recurring terminology
- Narrative style and tone

**For Summarization:**
- Character names and developments
- Plot progression and key events
- Important themes and story elements
- Story context and setting

### Sliding Window Processing
Large texts are processed using sliding windows with overlap to maintain context:
- Default window size: 30 chunks
- Default overlap: 10 chunks
- Configurable via constructor parameters

### Progress Tracking
- Translation progress saved to `translation_progress.json`
- Summarization progress saved to `summary_progress.json`
- Automatic resume capability
- Memory state preserved between sessions

## Examples

### Translate a novel
```bash
# Start translation
node index.mjs translate -i novel.txt -o novel_vietnamese.txt

# Resume if interrupted
node index.mjs resume -i novel.txt -o novel_vietnamese.txt
```

### Summarize a long story
```bash
# Create summary
node index.mjs summarize -i story.txt -o story_summary.txt

# Resume if interrupted
node index.mjs resume-summary -i story.txt -o story_summary.txt
```

### Process only first 20 chunks
```bash
node index.mjs translate -i input.txt --max-chunks 20
```

### Use sentence-level chunking
```bash
node index.mjs summarize -i input.txt -t sentence
```

## File Formats

### Input Files
- Plain text files (.txt)
- UTF-8 encoding
- Chinese text content

### Output Files
- Plain text files (.txt)
- UTF-8 encoding
- Vietnamese content (translation or summary)

### Progress Files
- JSON format
- Contains processed chunks and memory state
- Automatically created and updated

## Tips

1. **For long texts**: Use paragraph chunking for better context
2. **For precise control**: Use sentence chunking for more granular processing
3. **Memory management**: The tool automatically maintains consistency across chunks
4. **Interruption handling**: You can safely interrupt and resume processing
5. **Model selection**: Use gpt-4o-mini for cost-effectiveness or gpt-4 for higher quality

## Troubleshooting

### Common Issues

1. **API Key Error**: Make sure OPENAI_API_KEY is set correctly
2. **File Not Found**: Check input file path and permissions
3. **Memory Issues**: For very large files, consider processing in smaller chunks
4. **JSON Parse Error**: Delete progress files to start fresh if corrupted

### Error Recovery
If processing fails, you can:
1. Use the resume command to continue from the last successful chunk
2. Delete progress files to start over
3. Reduce chunk size or window size for problematic texts

## License

MIT License
