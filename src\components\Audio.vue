<template>
  <div>
    <audio ref="audio" :src="item.audioUrl" @ended="isPlaying = false" v-if="item?.audioUrl"></audio>
    <audio ref="audio" :src="src" @ended="isPlaying = false" v-if="src"></audio>
    <button @click="toggleAudio">
      {{ isPlaying ? '⏸️' : '▶️' }}
    </button>
  </div>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      required: false
    },
    src: {
      type: String,
      required: false
    }
  },
  data() {
    return {
      isPlaying: false
    };
  },
  methods: {
    toggleAudio() {
      const audio = this.$refs.audio;
      if (!audio) return;

      if (this.isPlaying) {
        audio.pause();
        this.isPlaying = false;
      } else {
        audio.play();
        this.isPlaying = true;
      }
    }
  }
};
</script>

<style scoped>
button {
  /* padding: 6px 12px; */
  font-size: 16px;
  cursor: pointer;
}
</style>
