const axios = require('axios');
const fs = require('fs');
const path = require('path');

class YoudaoTTS {
  constructor() {
    this.outputDir = path.join(__dirname, 'cache', 'tts');
    if (!fs.existsSync(this.outputDir)) {
      fs.mkdirSync(this.outputDir, { recursive: true });
    }
  }

  getVoiceList() {
    return ['ja', 'zh', 'en'];
  }

  voiceShowMap(voice) {
    return {
      ja: 'Japanese',
      zh: 'Chinese',
      en: 'English'
    }[voice];
  }

  async speak(content, voice = 'zh', rate = 1.0, voiceIdx = 0) {
    const headers = {
      'Accept': '*/*',
      'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
      'Referer': 'https://fanyi.youdao.com/',
      'Range': 'bytes=0-',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 Chrome/113.0.0.0 Safari/537.36',
    };

    const params = {
      audio: content,
      le: voice
    };

    const url = 'https://dict.youdao.com/dictvoice';

    const response = await axios.get(url, {
      headers,
      params,
      responseType: 'arraybuffer', // ensure binary content
    });

    const filename = `${Date.now()}.mp3`;
    const filepath = path.join(this.outputDir, filename);

    fs.writeFileSync(filepath, response.data);
    return filepath;
  }
}

module.exports = YoudaoTTS;
(async () => {
  const tts = new YoudaoTTS();
  const mp3Path = await tts.speak('Xin chào bạn', 'vi');
  console.log('Saved to:', mp3Path);
})();
