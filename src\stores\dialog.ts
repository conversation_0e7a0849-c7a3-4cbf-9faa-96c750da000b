import { defineStore } from 'pinia'
import { Modal } from 'ant-design-vue'

export interface MessageBoxOptions {
  title?: string
  message: string
  buttons?: string[]
  defaultId?: number
  type?: 'info' | 'warning' | 'error' | 'question'
}

export interface FileFilter {
  name: string
  extensions: string[]
}

export const useDialogStore = defineStore('dialog', () => {
  // Actions
  const showMessageBox = async (options: MessageBoxOptions): Promise<number> => {
    return new Promise((resolve) => {
      const { title = 'Confirmation', message, buttons = ['OK'], defaultId = 0, type = 'info' } = options
      
      let modalType: 'info' | 'success' | 'error' | 'warning' | 'confirm' = 'info'
      
      switch (type) {
        case 'warning':
          modalType = 'warning'
          break
        case 'error':
          modalType = 'error'
          break
        case 'question':
          modalType = 'confirm'
          break
        default:
          modalType = 'info'
      }

      if (buttons.length === 1) {
        Modal[modalType]({
          title,
          content: message,
          onOk: () => resolve(0)
        })
      } else {
        Modal.confirm({
          title,
          content: message,
          okText: buttons[1] || 'OK',
          cancelText: buttons[0] || 'Cancel',
          onOk: () => resolve(1),
          onCancel: () => resolve(0)
        })
      }
    })
  }

  const showOpenFiles = async (extensions: string[]): Promise<string[] | null> => {
    // This would integrate with Electron's dialog API
    // For now, return mock data
    if (window.electronAPI?.invoke) {
      return await window.electronAPI.invoke('showOpenDialogSync', {
        properties: ['openFile', 'multiSelections'],
        filters: [
          { name: 'Video Files', extensions }
        ]
      })
    }
    
    // Fallback for web environment
    return new Promise((resolve) => {
      const input = document.createElement('input')
      input.type = 'file'
      input.multiple = true
      input.accept = extensions.map(ext => `.${ext}`).join(',')
      
      input.onchange = (e) => {
        const files = (e.target as HTMLInputElement).files
        if (files) {
          const filePaths = Array.from(files).map(file => file.path || file.name)
          resolve(filePaths)
        } else {
          resolve(null)
        }
      }
      
      input.click()
    })
  }

  const showOpenFolder = async (): Promise<string | null> => {
    // This would integrate with Electron's dialog API
      const result = await window.electronAPI.invoke('showOpenDialogSync',{
        properties: ['openDirectory']
      })
      return result?.[0] || null

  }

  const showSaveFile = async (defaultName: string, filter: FileFilter): Promise<string | null> => {
    // This would integrate with Electron's dialog API
      return await window.electronAPI.invoke('showSaveDialogSync', {
        defaultPath: defaultName,
        filters: [filter]
      })
  }

  return {
    // Actions
    showMessageBox,
    showOpenFiles,
    showOpenFolder,
    showSaveFile
  }
})
