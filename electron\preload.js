const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');
const fs = require('fs');
const path = require('path');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // Listen for events
  on: (channel, callback) => {
    const subscription = (_event, ...args) => callback(...args);
    ipcRenderer.on(channel, subscription);

    return () => {
      ipcRenderer.removeListener(channel, subscription);
    };
  },
  off(...e) {
    const [t, ...n] = e;
    return ipcRenderer.off(t, ...n);
  },
  send(...e) {
    const [t, ...n] = e;
    return ipcRenderer.send(t, ...n);
  },
  removeListener: (channel, callback) => {
    ipcRenderer.removeListener(channel, callback);
  },
  invoke(...e) {
    const [t, ...n] = e;
    return ipcRenderer.invoke(t, ...n);
  },
  // invoke: (cmd, ...args) => {
  //   return ipcRenderer.invoke(cmd, ...args);
  // },
  request: (cmd, ...args) => {
    return ipcRenderer.invoke('request-any', { cmd, args });
  },
  database: (cmd, ...args) => {
    return ipcRenderer.invoke('database', cmd, ...args);
  },
  // TTS API
  generateTTS: async (data) => {
    return ipcRenderer.invoke('generate-tts', data);
  },

  // Get speakers list
  getSpeakers: (ttsEngine) => {
    return ipcRenderer.invoke('get-speakers', ttsEngine);
  },

  // File system operations
  saveFile: (filePath, content) => {
    return ipcRenderer.invoke('save-file', { filePath, content });
  },

  readFile: ({filePath}) => {
    return ipcRenderer.invoke('read-file', { filePath });
  },

  // Dialog operations
  openFileDialog: (options) => {
    return ipcRenderer.invoke('open-file-dialog', options);
  },

  saveFileDialog: (options) => {
    return ipcRenderer.invoke('save-file-dialog', options);
  },


  // Concatenate audio files
  concatenateAudio: (inputFiles, outputFile, timings) => {
    return ipcRenderer.invoke('concatenate-audio', { inputFiles, outputFile, timings });
  },

  // Run FFmpeg command
  runFFmpeg: (fileListPath, outputPath) => {
    return ipcRenderer.invoke('run-ffmpeg', fileListPath, outputPath);
  },
  mergeAudioFiles: (audioFiles, outputPath, subs, pathFile) => {
    return ipcRenderer.invoke('merge-audio-files', { audioFiles, outputPath, subs,pathFile });
  },

  // Create temporary directory
  createTempDir: (dirName) => {
    return ipcRenderer.invoke('create-temp-dir', dirName);
  },

  // Download file from URL
  downloadFile: (url, filePath) => {
    return ipcRenderer.invoke('download-file', url, filePath);
  },

  // Write file
  writeFile: (filePath, content) => {
    return ipcRenderer.invoke('write-file', filePath, content);
  },

  // Open file with default application
  openFile: (filePath) => {
    return ipcRenderer.invoke('open-file', filePath);
  },

  // Get app paths
  getAppPath: (name) => {
    return ipcRenderer.invoke('get-app-path', name);
  },
  //
  openFolder: (folderPath) => {
    return ipcRenderer.invoke('open-folder', folderPath);
  },
  setSession: (session) => {
    return ipcRenderer.invoke('set-session', session);
  },
  getSession: () => {
    return ipcRenderer.invoke('get-session');
  },

  setCurrentDir: (dirPath) => {
    return ipcRenderer.invoke('set-current-dir', dirPath);
  },
  getCurrentDir: () => {
    return ipcRenderer.invoke('get-current-dir');
  },

  // Video speed adjustment
  adjustSpeed: (options) => {
    return ipcRenderer.invoke('adjust-speed', options);
  },

  // Process management
  stopProcess: (processId) => {
    return ipcRenderer.invoke('stop-process', processId);
  },

  getActiveProcesses: () => {
    return ipcRenderer.invoke('get-active-processes');
  },

  // Video to WAV conversion
  convertVideoToWav: (options) => {
    return ipcRenderer.invoke('convert-video-to-wav', options);
  },

  // Whisper audio processing
  processAudioWhisper: (options) => {
    return ipcRenderer.invoke('process-audio-whisper', options);
  },

  // Video OCR processing
  processVideoOcr: (options) => {
    return ipcRenderer.invoke('process-video-ocr', options);
  },

  // Run OCR batch script
  runOcrBatch: (options) => {
    return ipcRenderer.invoke('run-ocr-batch', options);
  },

  // Video cutting functions
  getVideoDuration: (videoPath) => {
    return ipcRenderer.invoke('get-video-duration', videoPath);
  },

  cutVideoSegment: (options) => {
    return ipcRenderer.invoke('cut-video-segment', options);
  },

  splitVideoIntoParts: (options) => {
    return ipcRenderer.invoke('split-video-into-parts', options);
  },

  // Video rendering with SRT
  renderVideoWithSrt: (options) => {
    return ipcRenderer.invoke('render-video-with-srt', options);
  },

  // Event listeners for video rendering progress
  onRenderVideoProgress: (callback) => {
    return ipcRenderer.on('render-video-progress', callback);
  },

  removeRenderVideoProgress: (callback) => {
    return ipcRenderer.removeListener('render-video-progress', callback);
  },
  processSrtAndVideo: (options) => {
    return ipcRenderer.invoke('process-srt-and-video', options);
  },
  processSRTAndRender: (srtArray, videoPath) => {
    return ipcRenderer.invoke('process-srt-and-render', srtArray, videoPath);
  },
  processVideoSimplified: (srtArray, videoPath, options={}) => {
    return ipcRenderer.invoke('process-video-simplified', srtArray, videoPath,options);
  },
  demucs: (options) => {
    return ipcRenderer.invoke('demucs', options);
  },
  processVideoWithOptions: (options) => {
    return ipcRenderer.invoke('process-video-with-options', options);
  },
  getTextFromFrameVideo: (data) => {
    return ipcRenderer.invoke('get-text-from-frame-video', data);
  },
  getVideoInfo: (filePath) => {
    return ipcRenderer.invoke('get-video-info', filePath);
  },
  convertVideoToVideo: (options) => {
    return ipcRenderer.invoke('convert-video-to-video', options);
  },
  // check-file-htdemucs-file-exists
  checkHtdemucsFileExists: (filePath) => {
    return ipcRenderer.invoke('check-file-htdemucs-file-exists', {fileInput: filePath});
  },


  // Font management
  getFonts: () => {
    return ipcRenderer.invoke('get-fonts');
  }
});

// Wait for the document to be ready before executing any code
function d(e = ["complete", "interactive"]) {
  return new Promise((t) => {
    e.includes(document.readyState)
      ? t(!0)
      : document.addEventListener("readystatechange", () => {
          e.includes(document.readyState) && t(!0);
        });
  });
}
const i = {
  append(e, t) {
    if (!Array.from(e.children).find((n) => n === t)) return e.appendChild(t);
  },
  remove(e, t) {
    if (Array.from(e.children).find((n) => n === t)) return e.removeChild(t);
  },
};
function c() {
  const e = "sk-folding-cube",
    t = `
        .color {
            color: #9810fa;
            user-select: none;
        }
        .sk-folding-cube {
            margin: 20px auto;
            width: 40px;
            height: 40px;
            position: relative;
            -webkit-transform: rotateZ(45deg);
            transform: rotateZ(45deg);
        }
        .sk-folding-cube .sk-cube {
            float: left;
            width: 50%;
            height: 50%;
            position: relative;
            -webkit-transform: scale(1.1);
            -ms-transform: scale(1.1);
            transform: scale(1.1); 
        }
        .sk-folding-cube .sk-cube:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #9810fa;
            -webkit-animation: sk-foldCubeAngle 2.4s infinite linear both;
            animation: sk-foldCubeAngle 2.4s infinite linear both;
            -webkit-transform-origin: 100% 100%;
            -ms-transform-origin: 100% 100%;
            transform-origin: 100% 100%;
        }
        .sk-folding-cube .sk-cube2 {
            -webkit-transform: scale(1.1) rotateZ(90deg);
            transform: scale(1.1) rotateZ(90deg);
        }
        .sk-folding-cube .sk-cube3 {
            -webkit-transform: scale(1.1) rotateZ(180deg);
            transform: scale(1.1) rotateZ(180deg);
        }
        .sk-folding-cube .sk-cube4 {
            -webkit-transform: scale(1.1) rotateZ(270deg);
            transform: scale(1.1) rotateZ(270deg);
        }
        .sk-folding-cube .sk-cube2:before {
            -webkit-animation-delay: 0.3s;
            animation-delay: 0.3s;
        }
        .sk-folding-cube .sk-cube3:before {
            -webkit-animation-delay: 0.6s;
            animation-delay: 0.6s; 
        }
        .sk-folding-cube .sk-cube4:before {
            -webkit-animation-delay: 0.9s;
            animation-delay: 0.9s;
        }
        @-webkit-keyframes sk-foldCubeAngle {
            0%, 10% {
                -webkit-transform: perspective(140px) rotateX(-180deg);
                transform: perspective(140px) rotateX(-180deg);
                opacity: 0; 
            } 25%, 75% {
                -webkit-transform: perspective(140px) rotateX(0deg);
                transform: perspective(140px) rotateX(0deg);
                opacity: 1; 
            } 90%, 100% {
                -webkit-transform: perspective(140px) rotateY(180deg);
                transform: perspective(140px) rotateY(180deg);
                opacity: 0; 
            } 
        }
  
        @keyframes sk-foldCubeAngle {
            0%, 10% {
                -webkit-transform: perspective(140px) rotateX(-180deg);
                transform: perspective(140px) rotateX(-180deg);
                opacity: 0; 
            } 25%, 75% {
                -webkit-transform: perspective(140px) rotateX(0deg);
                transform: perspective(140px) rotateX(0deg);
                opacity: 1; 
            } 90%, 100% {
                -webkit-transform: perspective(140px) rotateY(180deg);
                transform: perspective(140px) rotateY(180deg);
                opacity: 0; 
            }
        }
        .app-loading-wrap {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
            z-index: 99;
        }
    `,
    n = document.createElement("style"),
    o = document.createElement("div");
  return (
    (n.id = "app-loading-style"),
    (n.innerHTML = t),
    (o.className = "app-loading-wrap"),
    (o.innerHTML = `
        <div>
            <div class="${e}">
                <div class="sk-cube1 sk-cube"></div>
                <div class="sk-cube2 sk-cube"></div>
                <div class="sk-cube4 sk-cube"></div>
                <div class="sk-cube3 sk-cube"></div>
            </div>
            <div class="color">Loading...</div>
        </div>
    `),
    {
      appendLoading() {
        i.append(document.head, n), i.append(document.body, o);
      },
      removeLoading() {
        i.remove(document.head, n), i.remove(document.body, o);
      },
    }
  );
}
const { appendLoading: l, removeLoading: a } = c();
d().then(l);
window.onmessage = (e) => {
  e.data.payload === "removeLoading" && a();
};
setTimeout(a, 2999);