const { app } = require('electron');
const path = require("path");
const fs = require("fs");
const { execWithLog, slugifyText, audioTTSDir } = require("./utils");
const { spawn } = require("child_process");
const { getFrameVideo } = require("./ffmpegHandler");


function getVenvPythonPath(basePath = 'scripts/venv') {
  // app.getAppPath()
  basePath = path.join(basePath, 'venv');
  return process.platform === 'win32'
    ? path.join(basePath, 'Scripts', 'python.exe')
    : path.join(basePath, 'bin', 'python');
}

/**
 * Process video with OCR to extract text and generate SRT file
 * @param {Object} options - OCR options
 * @param {string} options.videoPath - Path to the video file
 * @param {string} options.outputPath - Path to save the SRT file (optional)
 * @param {string} options.lang - Language for OCR (default: 'zh')
 * @param {number} options.frameRate - Frame rate for OCR (default: 3)
 * @param {string} options.crop - Crop coordinates in format "x1,y1,x2,y2" (optional)
 * @param {string} options.pythonPath - Path to Python executable (optional)
 * @param {string} options.scriptPath - Path to OCR script (optional)
 * @param {string} options.processId - Process ID for tracking (optional)
 * @returns {Promise<Object>} - Result object
 */
async function processVideoOcr(event,{
  videoPath,
  outputPath = null,
  lang = 'zh',
  frameRate = 3,
  crop = null,
  pythonPath = null,
  scriptPath = null,
  processId = null
}) {
  if (!videoPath || !fs.existsSync(videoPath)) {
    return { 
      success: false, 
      error: "Input video file does not exist" 
    };
  }
  // rename file if exist slugifyText videoPath
  const { dir, name, ext } = path.parse(videoPath);
  const newName = slugifyText(name);
  const originalVideoPath = path.join(dir, `${newName}${ext}`);
  const newVideoPath = path.join(audioTTSDir, `${newName}${ext}`);
  // await fs.copyFileSync(videoPath, newVideoPath);
  console.log('originalVideoPath: ', originalVideoPath);
  console.log('newVideoPath: ', newVideoPath);
  const dbPath = await S.db.Config.getValueByName('ocr_path')
  console.log('db ocr_path: ', dbPath);



  try {
    // Determine output path if not provided
    if (!outputPath) {
      const { dir, name } = path.parse(originalVideoPath);
      outputPath = path.join(dir, `${name}-ocr.srt`);
    }

    // Prepare command arguments
    const args = [];
    
    // Add Python script path
    if (scriptPath?.includes(".bat")) {
      // args.push(scriptPath);
    } else {
      // run venv
      const ocr_video = path.join(dbPath, 'ocr_video.py');
      args.push(ocr_video);
    }
    if(!scriptPath?.includes(".bat")){
      // Add video path
      args.push("--video", videoPath);
      // Add output path
      args.push("--output", outputPath);
      // Add frame rate
      args.push("--frame_rate", frameRate.toString());
      // Add language
      args.push("--lang", lang);
      // Add crop if provided
      if (crop) {
        args.push("--crop", crop);
      }
      // Determine Python executable
      const pythonExecutable = pythonPath || getVenvPythonPath(dbPath);
      
      console.log("Running OCR with command:", pythonExecutable, args.join(" "));
      
      // Execute the command
      const cb = ()=>{
        // fs.unlinkSync(newVideoPath);
      }
      const result = await execWithLog.bind({cb})(event, pythonExecutable, args, processId);

      // remove file
      // fs.unlinkSync(newVideoPath);
      
      return {
        success: true,
        processId: result.processId,
        outputPath: outputPath,
        status: 'started'
      };
    }
    args.push('/c', scriptPath,newVideoPath,outputPath, frameRate.toString(), lang, `${crop}` || "");

      console.log("Running OCR with command:", scriptPath, args.join(" "));
      const cb = ()=>{
        // fs.unlinkSync(newVideoPath);
      }
      // Execute the command
      const result = await execWithLog.bind({cb})(event, 'cmd.exe', args, processId);

      // remove file
      
      return {
        success: true,
        processId: result.processId,
        outputPath: outputPath,
        status: 'started'
      };

  } catch (err) {
    console.error("Error processing video with OCR:", err.message);
    return { 
      success: false, 
      error: err.message || "Unknown error occurred"
    };
  }
}

/**
 * Run OCR using the batch script
 * @param {Object} options - OCR options
 * @param {string} options.videoPath - Path to the video file
 * @param {string} options.batchPath - Path to the batch script
 * @param {string} options.processId - Process ID for tracking (optional)
 * @returns {Promise<Object>} - Result object
 */
async function runOcrBatchScript({
  videoPath,
  batchPath,
  processId = null
}) {
  if (!videoPath || !fs.existsSync(videoPath)) {
    return { 
      success: false, 
      error: "Input video file does not exist" 
    };
  }

  if (!batchPath || !fs.existsSync(batchPath)) {
    return { 
      success: false, 
      error: "Batch script does not exist" 
    };
  }

  try {
    // Determine output path
    const { dir, name } = path.parse(videoPath);
    const outputPath = path.join(dir, `${name}-ocr.srt`);
    
    // Execute the batch script with the video path as argument
    const result = await execWithLog(null, 'cmd.exe', ['/c', batchPath, videoPath], processId);
    
    return {
      success: true,
      processId: result.processId,
      outputPath: outputPath,
      status: 'started'
    };
  } catch (err) {
    console.error("Error running OCR batch script:", err);
    return { 
      success: false, 
      error: err.message || "Unknown error occurred"
    };
  }
}

async function imageScanOcr(event, imagePath, cropData = null) {
  const dbPath = await S.db.Config.getValueByName('ocr_path')
  return await new Promise((resolve, reject) => {
    const pythonPath = getVenvPythonPath(dbPath);
    const scriptPath = path.join(dbPath, 'image_scan.py');
    
    const args = [scriptPath, imagePath];
    if(cropData){
      args.push('--crop', cropData);
    }
  
    const child = spawn(pythonPath, args, {
      env: {
        ...process.env,
        PYTHONIOENCODING: 'utf-8'  // Ép stdout/stderr dùng UTF-8
      }
    });

  
    let output = '';
  
    child.stdout.on('data', (data) => {
      output += data.toString();
    });
  
    child.stderr.on('data', (data) => {
      console.error('stderr', data.toString());
    });
  
    child.on('close', (code) => {
      if (code !== 0) {
        reject(new Error(`child process exited with code ${code}`));
        return;
      }

      const lines = output.trim().split('\n');
      const lastLine = lines.reverse().find(line => line.trim().length > 0);

      resolve(lastLine || '');
    });
  
    child.on('error', (error) => {
      reject(error);
    });
  });
}

async function getTextFromFrameVideo(event, {videoPath, fromSecond, cropData}) {
  if (!videoPath || !fs.existsSync(videoPath)) {
    return { 
      success: false, 
      error: "Input video file does not exist" 
    };
  }
  const { dir, name } = path.parse(videoPath);
  let imagePath = path.join(dir, `${name}-ocr.png`);
  console.log('getFrameVideo',cropData);
  imagePath = await getFrameVideo(event, videoPath, fromSecond, imagePath, cropData);
  
  const crop = cropData ? null : '0,0.85,1,1'
  console.log('imageScanOcr');
  const text = await imageScanOcr(event, imagePath, crop);
  // if(imagePath)fs.unlinkSync(imagePath);
  return {
    success: true,
    text
  }
}



module.exports = {
  processVideoOcr,
  runOcrBatchScript,
  imageScanOcr,
  getTextFromFrameVideo
};
