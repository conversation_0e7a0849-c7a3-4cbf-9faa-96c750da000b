<template>
  <div>
    <a-button type="primary" @click="showModal" :disabled="isLoading">
      {{ buttonText }}
    </a-button>

    <a-modal v-model:open="visible" title="Select SRT File" :width="700" :footer="null" @cancel="handleCancel">
      <div class="srt-list-container">
        <!-- Empty state -->
        <div v-if="srtLists.length === 0" class="empty-state">
          <div class="text-center py-8 text-gray-500">
            <p class="mb-4">No SRT files have been imported yet.</p>
            <a-button @click="handleImportNew">Import New SRT File</a-button>
          </div>
        </div>

        <!-- SRT List -->
        <div v-else>
          <div class="mb-4 flex justify-between items-center">
            <h3 class="text-lg font-medium">Previously Imported SRT Files</h3>
            <a-button type="primary" @click="handleImportNew">Import New</a-button>
          </div>

          <a-table :dataSource="srtLists" :columns="columns" :pagination="{ pageSize: 10 }" :loading="isLoading"
            rowKey="name">
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'name'">
                {{ formatSrtName(record.name) }}
              </template>
              <template v-if="column.key === 'count'">
                {{ record.items.length }} items
              </template>
              <template v-if="column.key === 'date'">
                {{ formatDate(record.name) }}
              </template>
              <template v-if="column.key === 'action'">
                <div class="flex gap-2">
                  <a-button type="primary" size="small" @click="selectSrt(record)">
                    Select
                  </a-button>
                  <a-button type="default" size="small" @click="editPath(record)">
                    Edit path
                  </a-button>
                  <a-button danger size="small" @click="deleteSrt(record)">
                    Delete
                  </a-button>
                </div>
              </template>
            </template>
          </a-table>
        </div>
      </div>
    </a-modal>
    <!-- edit path modal -->
    <a-modal v-model:open="showEditPath" title="Edit SRT Path" :width="500" @cancel="showEditPath = false"
      @ok="updateSrtPath">
      <div>
        <label for="srt-path">SRT Path:</label>
        <a-input id="srt-path" v-model:value="editPathValue.path" />
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useTTSStore } from '../stores/ttsStore';
import { message, Modal } from 'ant-design-vue';

const props = defineProps({
  buttonText: {
    type: String,
    default: 'Select SRT File'
  }
});

const emit = defineEmits(['select', 'import-new']);

const ttsStore = useTTSStore();
const visible = ref(false);
const isLoading = ref(false);

const showEditPath = ref(false);
const editPathValue = ref({});
const editPath = (record) => {
  showEditPath.value = true;
  editPathValue.value = { ...record };
};

const updateSrtPath = () => {
  const index = ttsStore.srtLists.findIndex(item => item.name === editPathValue.value.name);
  if (index !== -1) {
    ttsStore.srtLists[index] = { ...editPathValue.value };
    message.success('SRT path updated successfully');
  }
  showEditPath.value = false;
};

// Get SRT lists from store
const srtLists = computed(() => {
  return ttsStore.srtLists.slice().reverse() || [];
});

// Table columns
const columns = [
  {
    title: 'File Name',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: 'Items',
    key: 'count',
  },
  {
    title: 'Import Date',
    key: 'date',
  },
  {
    title: 'Action',
    key: 'action',
  }
];

// Format SRT name (remove timestamp)
function formatSrtName(name) {
  // Extract the file name part before the timestamp
  const parts = name.split('_');
  if (parts.length > 1) {
    // Remove the last part (timestamp)
    parts.pop();
    return parts.join('_');
  }
  return name;
}

// Format date from timestamp in filename
function formatDate(name) {
  try {
    // Extract timestamp from the end of the name
    const timestamp = name.split('_').pop();
    if (timestamp && !isNaN(Number(timestamp))) {
      const date = new Date(Number(timestamp));
      return date.toLocaleString();
    }
    return 'Unknown date';
  } catch (error) {
    return 'Unknown date';
  }
}

// Show modal
function showModal() {
  visible.value = true;
}

// Handle cancel
function handleCancel() {
  visible.value = false;
}

// Handle import new
function handleImportNew() {
  visible.value = false;
  emit('import-new');
}

// Select SRT
async function selectSrt(record) {
  // Initialize layers array if not present
  if (!record.layers) {
    record.layers = [];
  }

  ttsStore.currentSrtList = record;
  emit('select', record);
  await electronAPI.setCurrentDir(record.path)
  visible.value = false;
  message.success(`Selected SRT file: ${formatSrtName(record.name)}`);
}

// Delete SRT
async function deleteSrt(record) {
  const confirm = await new Promise((resolve) => {
    Modal.confirm({
      title: 'Xác nhận',
      content: 'Bạn có chắc chắn muốn xóa subtitle này?',
      okText: 'Có',
      cancelText: 'Không',
      onOk: () => resolve(true),
      onCancel: () => resolve(false),
    });
  });
  if(confirm) {
    const index = ttsStore.srtLists.findIndex(item => item.name === record.name);
    if (index !== -1) {
      ttsStore.srtLists.splice(index, 1);
      message.success(`Deleted SRT file: ${formatSrtName(record.name)}`);
    }
  }

}
</script>

<style scoped>
.srt-list-container {
  max-height: 800px;
  overflow-y: auto;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
}
</style>
