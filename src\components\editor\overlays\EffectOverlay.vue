<template>
  <div class="effect-overlay absolute inset-0">
    <DraggableEffect
      v-if="isVisible"
      :effect="effect"
      :video-dimensions="videoDimensions"
      :is-selected="isSelected"
      @update="handleUpdate"
      @select="handleSelect"
      @context-menu="handleContextMenu"
    />
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useVideoLayersStore } from '@/stores/video-layers-store'
import DraggableEffect from './DraggableEffect.vue'

const props = defineProps({
  effect: {
    type: Object,
    required: true
  },
  currentTime: {
    type: Number,
    required: true
  },
  videoDimensions: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['effect-click'])

const layersStore = useVideoLayersStore()

// Check if effect should be visible at current time
const isVisible = computed(() => {
  if (!props.effect.timeStart || !props.effect.timeEnd) return true

  return props.currentTime >= props.effect.timeStart &&
         props.currentTime <= props.effect.timeEnd
})

// Check if effect is selected
const isSelected = computed(() => {
  return layersStore.selectedEffectId === props.effect.id
})

// Event handlers
const handleUpdate = (updatedEffect) => {
  layersStore.updateEffect(props.effect.type, props.effect.id, updatedEffect)
}

const handleSelect = (effectId) => {
  layersStore.selectEffect(effectId)
  emit('effect-click', props.effect)
}

const handleContextMenu = (event, effect) => {
  // TODO: Show context menu for effect options
  console.log('Effect context menu:', effect)
}
</script>

<style scoped>
.effect-overlay {
  z-index: 30;
}

.blur-effect,
.delogo-effect,
.color-effect,
.crop-effect {
  transition: all 0.2s ease;
}

.blur-effect:hover,
.delogo-effect:hover,
.color-effect:hover,
.crop-effect:hover {
  border-width: 3px;
}
</style>
