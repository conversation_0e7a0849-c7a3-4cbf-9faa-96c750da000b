import { defineStore } from 'pinia'

export const usePromptStore = defineStore('prompt', {
  state: () => ({
    promptTemplates: [
      {
        id: 1,
        name: '<PERSON><PERSON> đề Video Chuyên nghiệp',
        description: 'Template mặc định cho dịch phụ đề video với tự động nhận diện phong cách',
        isDefault: true,
        template: `Bạn là dịch giả phụ đề video chuyên nghiệp. Dịch từ {sourceLang} sang {targetLang}.

{dictionaryText}
YÊU CẦU:
1. Dịch tự nhiên như người Việt nói, ưu tiên từ thuần Việt
2. Giữ nguyên số thứ tự. Chỉ trả về phần dịch, không bao gồm câu gốc
3. <PERSON><PERSON><PERSON> cảm xúc, ngữ điệu người nói
4. <PERSON><PERSON> hợp phụ đề (ngắ<PERSON> gọn, <PERSON><PERSON> đọc)
5. Tự động nhận diện loại video và điều chỉnh phong cách
6. KHÔNG giữ từ ngôn ngữ gốc, KHÔNG thêm ghi chú
{dictionaryRule}
{duplicateRule} Câu giống nhau → dịch đồng nhất

PHONG CÁCH TỰ ĐỘNG:
- Phim/Drama: Giữ tính văn học
- Vlog/YouTube: Thân thiện, đời thường  
- Tin tức: Trang trọng, chính xác
- Gaming: Sôi động, năng lượng

Dịch văn bản sau:`
      },
      {
        id: 2,
        name: 'Dịch Chuẩn Mực',
        description: 'Template dịch chuẩn mực với từ điển thuật ngữ',
        isDefault: false,
        template: `Bạn là một dịch giả chuyên nghiệp. Nhiệm vụ của bạn là dịch văn bản từ {sourceLang} sang {targetLang}.

{dictionaryText}Hướng dẫn:
1. Dịch toàn bộ văn bản được cung cấp, không bỏ sót nội dung. Giữ ngắn gọn để phù hợp với thời gian phụ đề.
2. Giữ nguyên số thứ tự. Chỉ trả về phần dịch, không bao gồm câu gốc.
3. KHÔNG giữ lại bất kỳ từ nào bằng ngôn ngữ gốc.
4. KHÔNG thêm bất kỳ ghi chú hay giải thích nào.
{dictionaryRule}
{duplicateRule} Nếu phát hiện nhiều câu giống hệt nhau, hãy đảm bảo dịch chúng một cách đồng nhất.

Hãy dịch văn bản sau đây thành {targetLang} một cách tự nhiên và chính xác:`
      },
      {
        id: 3,
        name: 'Dịch Anime/Manga',
        description: 'Template chuyên dụng cho dịch anime, manga với thuật ngữ Nhật Bản',
        isDefault: false,
        template: `Bạn là dịch giả chuyên về anime/manga. Dịch từ {sourceLang} sang {targetLang}.

{dictionaryText}
YÊU CẦU CHUYÊN BIỆT:
1. Giữ nguyên tên nhân vật, địa danh Nhật Bản (trừ khi có trong từ điển)
2. Dịch thuật ngữ anime/manga phù hợp với fan Việt
3. Giữ nguyên các từ như: -san, -kun, -chan, -sama, -senpai, -kouhai
4. Dịch cảm thán, thán từ tự nhiên: "Eh?", "Ara ara", "Sugoi"
5. Giữ nguyên số thứ tự, chỉ trả về phần dịch
6. KHÔNG thêm ghi chú giải thích
{dictionaryRule}
{duplicateRule} Câu giống nhau → dịch đồng nhất

PHONG CÁCH:
- Giữ tính cách nhân vật qua cách nói
- Dịch tự nhiên nhưng không mất đi "hương vị" anime
- Ưu tiên sự dễ hiểu cho khán giả Việt

Dịch văn bản sau:`
      }
    ],
    selectedTemplateId: 1
  }),

  getters: {
    selectedTemplate: (state) => {
      return state.promptTemplates.find(t => t.id === state.selectedTemplateId) || state.promptTemplates[0]
    },
    
    defaultTemplate: (state) => {
      return state.promptTemplates.find(t => t.isDefault) || state.promptTemplates[0]
    }
  },

  actions: {
    // Thêm template mới
    addTemplate(template) {
      const newId = Math.max(...this.promptTemplates.map(t => t.id)) + 1
      const newTemplate = {
        id: newId,
        name: template.name || `Template ${newId}`,
        description: template.description || '',
        isDefault: false,
        template: template.template || ''
      }
      this.promptTemplates.push(newTemplate)
      return newTemplate
    },

    // Cập nhật template
    updateTemplate(id, updates) {
      const index = this.promptTemplates.findIndex(t => t.id === id)
      if (index !== -1) {
        // Không cho phép thay đổi isDefault nếu đang cập nhật template khác
        if (updates.isDefault && this.promptTemplates[index].id !== this.defaultTemplate.id) {
          // Bỏ default của template cũ
          const oldDefaultIndex = this.promptTemplates.findIndex(t => t.isDefault)
          if (oldDefaultIndex !== -1) {
            this.promptTemplates[oldDefaultIndex].isDefault = false
          }
        }
        
        this.promptTemplates[index] = { ...this.promptTemplates[index], ...updates }
        return this.promptTemplates[index]
      }
      return null
    },

    // Xóa template
    deleteTemplate(id) {
      const index = this.promptTemplates.findIndex(t => t.id === id)
      if (index !== -1) {
        const template = this.promptTemplates[index]
        
        // Không cho phép xóa template default
        if (template.isDefault) {
          throw new Error('Không thể xóa template mặc định')
        }
        
        // Nếu đang chọn template này thì chuyển về default
        if (this.selectedTemplateId === id) {
          this.selectedTemplateId = this.defaultTemplate.id
        }
        
        this.promptTemplates.splice(index, 1)
        return true
      }
      return false
    },

    // Chọn template
    selectTemplate(id) {
      const template = this.promptTemplates.find(t => t.id === id)
      if (template) {
        this.selectedTemplateId = id
        return template
      }
      return null
    },

    // Tạo prompt từ template đã chọn
    generatePrompt(sourceLang = 'auto', targetLang = 'Vietnamese', dictionary = {}) {
      const template = this.selectedTemplate
      return this.generatePromptFromTemplate(template, sourceLang, targetLang, dictionary)
    },

    // Tạo prompt từ template cụ thể
    generatePromptFromTemplate(template, sourceLang = 'auto', targetLang = 'Vietnamese', dictionary = {}) {
      let dictionaryText = ''
      let dictionaryRule = ''
      let duplicateRule = ''

      // Xử lý từ điển thuật ngữ
      if (Object.keys(dictionary).length > 0) {
        dictionaryText = 'Từ điển thuật ngữ (sử dụng đồng nhất):\n'
        for (const [term, translation] of Object.entries(dictionary)) {
          dictionaryText += `- ${term}: ${translation}\n`
        }
        dictionaryText += '\n'
        dictionaryRule = '7. Sử dụng từ điển thuật ngữ để đảm bảo tính nhất quán\n'
        duplicateRule = '8.'
      } else {
        duplicateRule = '7.'
      }

      // Xử lý sourceLang
      const processedSourceLang = sourceLang === 'auto' ? 'ngôn ngữ được phát hiện' : sourceLang

      // Thay thế các placeholder trong template
      return template.template
        .replace(/\{sourceLang\}/g, processedSourceLang)
        .replace(/\{targetLang\}/g, targetLang)
        .replace(/\{dictionaryText\}/g, dictionaryText)
        .replace(/\{dictionaryRule\}/g, dictionaryRule)
        .replace(/\{duplicateRule\}/g, duplicateRule)
    },

    // Sao chép template
    duplicateTemplate(id) {
      const template = this.promptTemplates.find(t => t.id === id)
      if (template) {
        const newTemplate = {
          ...template,
          id: Math.max(...this.promptTemplates.map(t => t.id)) + 1,
          name: `${template.name} (Copy)`,
          isDefault: false
        }
        this.promptTemplates.push(newTemplate)
        return newTemplate
      }
      return null
    },

    // Đặt làm template mặc định
    setAsDefault(id) {
      // Bỏ default của template cũ
      const oldDefaultIndex = this.promptTemplates.findIndex(t => t.isDefault)
      if (oldDefaultIndex !== -1) {
        this.promptTemplates[oldDefaultIndex].isDefault = false
      }
      
      // Đặt template mới làm default
      const newDefaultIndex = this.promptTemplates.findIndex(t => t.id === id)
      if (newDefaultIndex !== -1) {
        this.promptTemplates[newDefaultIndex].isDefault = true
        return this.promptTemplates[newDefaultIndex]
      }
      return null
    },

    // Import templates từ JSON
    importTemplates(templates) {
      const imported = []
      templates.forEach(template => {
        try {
          const newTemplate = this.addTemplate(template)
          imported.push(newTemplate)
        } catch (error) {
          console.error('Error importing template:', template.name, error)
        }
      })
      return imported
    },

    // Export templates ra JSON
    exportTemplates() {
      return JSON.stringify(this.promptTemplates, null, 2)
    }
  },

  persist: {
    key: 'prompt-store',
    storage: localStorage
  }
})
