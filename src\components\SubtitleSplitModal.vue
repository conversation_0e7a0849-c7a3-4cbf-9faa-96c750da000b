<template>
  <a-modal
    v-model:open="visible"
    title="Tách subtitle"
    :ok-text="'Tách'"
    :cancel-text="'Hủy'"
    @ok="handleOk"
    @cancel="handleCancel"
    :width="800"
  >
    <div v-if="subtitle">
      <a-alert
        message="Thông tin subtitle hiện tại"
        :description="`ID: ${subtitle.id} | Thời gian: ${subtitle.start} → ${subtitle.end}`"
        type="info"
        show-icon
        class="mb-4"
      />
      
      <a-form :model="form" layout="vertical">
        <a-form-item label="Điểm tách thời gian">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-input 
                v-model:value="form.splitTime" 
                placeholder="00:00:00,000"
                @change="updatePreview"
              />
            </a-col>
            <a-col :span="12">
              <a-slider
                v-model:value="splitTimeSlider"
                :min="0"
                :max="100"
                @change="onSliderChange"
                :tooltip-formatter="formatSliderTooltip"
              />
            </a-col>
          </a-row>
          <div class="text-xs text-gray-500 mt-1">
            Kéo thanh trượt hoặc nhập thời gian để chọn điểm tách
          </div>
        </a-form-item>
        
        <a-divider>Phần 1 (Trước điểm tách)</a-divider>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="Thời gian">
              <a-input :value="`${subtitle.start} → ${form.splitTime}`" disabled />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="Thời lượng">
              <a-input :value="formatDuration(part1Duration)" disabled />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-form-item label="Văn bản gốc (Phần 1)">
          <a-textarea 
            v-model:value="form.text1" 
            :rows="3" 
            placeholder="Văn bản phần 1..."
          />
        </a-form-item>
        
        <a-form-item label="Văn bản dịch (Phần 1)">
          <a-textarea 
            v-model:value="form.translatedText1" 
            :rows="3" 
            placeholder="Văn bản dịch phần 1..."
          />
        </a-form-item>
        
        <a-divider>Phần 2 (Sau điểm tách)</a-divider>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="Thời gian">
              <a-input :value="`${form.splitTime} → ${subtitle.end}`" disabled />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="Thời lượng">
              <a-input :value="formatDuration(part2Duration)" disabled />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-form-item label="Văn bản gốc (Phần 2)">
          <a-textarea 
            v-model:value="form.text2" 
            :rows="3" 
            placeholder="Văn bản phần 2..."
          />
        </a-form-item>
        
        <a-form-item label="Văn bản dịch (Phần 2)">
          <a-textarea 
            v-model:value="form.translatedText2" 
            :rows="3" 
            placeholder="Văn bản dịch phần 2..."
          />
        </a-form-item>
        
        <a-form-item>
          <a-checkbox v-model:checked="form.autoSplit">
            Tự động chia văn bản theo tỷ lệ thời gian
          </a-checkbox>
        </a-form-item>
      </a-form>
    </div>
    
    <a-alert
      v-if="validationError"
      :message="validationError"
      type="error"
      show-icon
      class="mt-4"
    />
  </a-modal>
</template>

<script>
import { defineComponent, ref, computed, watch } from 'vue';
import { parseTimeToSeconds } from '@/lib/utils';
import { secondsToSRTTime } from '@/lib/subtitleUtils';

export default defineComponent({
  name: 'SubtitleSplitModal',
  props: {
    open: {
      type: Boolean,
      default: false
    },
    subtitle: {
      type: Object,
      default: null
    }
  },
  emits: ['update:open', 'confirm'],
  setup(props, { emit }) {
    const visible = ref(false);
    const validationError = ref('');
    const splitTimeSlider = ref(50);
    
    const form = ref({
      splitTime: '',
      text1: '',
      text2: '',
      translatedText1: '',
      translatedText2: '',
      autoSplit: true
    });
    
    // Computed properties
    const part1Duration = computed(() => {
      if (!props.subtitle || !form.value.splitTime) return 0;
      try {
        const start = parseTimeToSeconds(props.subtitle.start);
        const split = parseTimeToSeconds(form.value.splitTime);
        return Math.max(0, split - start);
      } catch {
        return 0;
      }
    });
    
    const part2Duration = computed(() => {
      if (!props.subtitle || !form.value.splitTime) return 0;
      try {
        const split = parseTimeToSeconds(form.value.splitTime);
        const end = parseTimeToSeconds(props.subtitle.end);
        return Math.max(0, end - split);
      } catch {
        return 0;
      }
    });
    
    // Watch for prop changes
    watch(() => props.open, (newVal) => {
      visible.value = newVal;
      if (newVal && props.subtitle) {
        resetForm();
        calculateDefaultSplit();
      }
    });
    
    watch(visible, (newVal) => {
      emit('update:open', newVal);
    });
    
    // Watch for auto split changes
    watch(() => form.value.autoSplit, (newVal) => {
      if (newVal) {
        autoSplitText();
      }
    });
    
    const resetForm = () => {
      form.value = {
        splitTime: '',
        text1: '',
        text2: '',
        translatedText1: '',
        translatedText2: '',
        autoSplit: true
      };
      validationError.value = '';
      splitTimeSlider.value = 50;
    };
    
    const calculateDefaultSplit = () => {
      if (!props.subtitle) return;
      
      const start = parseTimeToSeconds(props.subtitle.start);
      const end = parseTimeToSeconds(props.subtitle.end);
      const mid = (start + end) / 2;
      
      form.value.splitTime = secondsToSRTTime(mid);
      autoSplitText();
    };
    
    const autoSplitText = () => {
      if (!props.subtitle || !form.value.autoSplit) return;
      
      const text = props.subtitle.text || '';
      const translatedText = props.subtitle.translatedText || '';
      
      // Calculate split ratio based on time
      const totalDuration = part1Duration.value + part2Duration.value;
      const ratio1 = totalDuration > 0 ? part1Duration.value / totalDuration : 0.5;
      
      // Split text
      const textSplitPoint = Math.floor(text.length * ratio1);
      const translatedSplitPoint = Math.floor(translatedText.length * ratio1);
      
      // Find better split points (spaces, punctuation)
      let finalTextSplit = textSplitPoint;
      let finalTranslatedSplit = translatedSplitPoint;
      
      // Look for space or punctuation near the calculated point
      for (let i = Math.max(0, textSplitPoint - 10); i < Math.min(text.length, textSplitPoint + 10); i++) {
        if (/[\s.,!?;:]/.test(text[i])) {
          finalTextSplit = i;
          break;
        }
      }
      
      for (let i = Math.max(0, translatedSplitPoint - 10); i < Math.min(translatedText.length, translatedSplitPoint + 10); i++) {
        if (/[\s.,!?;:]/.test(translatedText[i])) {
          finalTranslatedSplit = i;
          break;
        }
      }
      
      form.value.text1 = text.substring(0, finalTextSplit).trim();
      form.value.text2 = text.substring(finalTextSplit).trim();
      form.value.translatedText1 = translatedText.substring(0, finalTranslatedSplit).trim();
      form.value.translatedText2 = translatedText.substring(finalTranslatedSplit).trim();
    };
    
    const onSliderChange = (value) => {
      if (!props.subtitle) return;
      
      const start = parseTimeToSeconds(props.subtitle.start);
      const end = parseTimeToSeconds(props.subtitle.end);
      const splitTime = start + (end - start) * (value / 100);
      
      form.value.splitTime = secondsToSRTTime(splitTime);
      updatePreview();
    };
    
    const updatePreview = () => {
      if (form.value.autoSplit) {
        autoSplitText();
      }
    };
    
    const formatSliderTooltip = (value) => {
      return `${value}%`;
    };
    
    const formatDuration = (seconds) => {
      return `${seconds.toFixed(2)}s`;
    };
    
    const validateForm = () => {
      validationError.value = '';
      
      if (!form.value.text1.trim() && !form.value.text2.trim()) {
        validationError.value = 'Ít nhất một phần phải có văn bản';
        return false;
      }
      
      try {
        const start = parseTimeToSeconds(props.subtitle.start);
        const end = parseTimeToSeconds(props.subtitle.end);
        const split = parseTimeToSeconds(form.value.splitTime);
        
        if (split <= start || split >= end) {
          validationError.value = 'Điểm tách phải nằm giữa thời gian bắt đầu và kết thúc';
          return false;
        }
        
        if (part1Duration.value < 0.5 || part2Duration.value < 0.5) {
          validationError.value = 'Mỗi phần phải có thời lượng ít nhất 0.5 giây';
          return false;
        }
        
      } catch (error) {
        validationError.value = 'Định dạng thời gian không hợp lệ';
        return false;
      }
      
      return true;
    };
    
    const handleOk = () => {
      if (!validateForm()) return;
      
      const splitData = {
        splitTime: form.value.splitTime,
        part1: {
          text: form.value.text1.trim(),
          translatedText: form.value.translatedText1.trim()
        },
        part2: {
          text: form.value.text2.trim(),
          translatedText: form.value.translatedText2.trim()
        }
      };
      
      emit('confirm', splitData);
      visible.value = false;
    };
    
    const handleCancel = () => {
      visible.value = false;
    };
    
    return {
      visible,
      form,
      validationError,
      splitTimeSlider,
      part1Duration,
      part2Duration,
      onSliderChange,
      updatePreview,
      formatSliderTooltip,
      formatDuration,
      handleOk,
      handleCancel
    };
  }
});
</script>
