const fs = require('fs');
const path = require('path');
const axios = require('axios');
const { execSync } = require('child_process');
const { randomInt } = require('crypto');

const settings = {
    config: {
        settings: {
            tts: {
                tiktok_sessionid: 'f99b95b348771c84efde83a0d457cc4d',
                tiktok_voice: 'BV074_streaming'
            }
        }
    }
}
const ENDPOINT_DATA = [
  { url: 'https://tiktok-tts.weilnet.workers.dev/api/generation', response: 'data' },
//   { url: 'https://countik.com/api/text/speech', response: 'v_data' },
  { url: 'https://gesserit.co/api/tiktok-tts', response: 'base64' },
  { url: 'https://api16-normal-v6.tiktokv.com/media/api/text/speech/invoke', response: 'data.v_str', type: 'tiktok' }
];

const VOICES = [
  'en_us_ghostface', 'en_us_chewbacca', 'en_us_c3po', 'en_us_stitch', 'en_us_stormtrooper', 'en_us_rocket',
  'en_au_001', 'en_au_002', 'en_uk_001', 'en_uk_003', 'en_us_001', 'en_us_002',
  'en_us_006', 'en_us_007', 'en_us_009', 'en_us_010', 'fr_001', 'fr_002',
  'de_001', 'de_002', 'es_002', 'es_mx_002', 'br_001', 'br_003', 'br_004', 'br_005',
  'id_001', 'jp_001', 'jp_003', 'jp_005', 'jp_006', 'kr_002', 'kr_003', 'kr_004',
  'en_female_f08_salut_damour', 'en_male_m03_lobby', 'en_female_f08_warmy_breeze',
  'en_male_m03_sunshine_soon', 'en_male_narration', 'en_male_funny', 'en_female_emotional'
];
class TikTokTTS {
  constructor() {
    this.maxChars = 300; // giới hạn ký tự mỗi chunk
    this.uriBase = 'https://api16-normal-c-useast1a.tiktokv.com/media/api/text/speech/invoke/';
    this.headers = {
      'User-Agent': 'com.zhiliaoapp.musically/2022600030 (Linux; U; Android 7.1.2; es_ES; SM-G988N; Build/NRD90M;tt-ok/*********)',
      'Cookie': `sessionid=${settings.config.settings.tts.tiktok_sessionid}`
    };
    this.session = axios.create({ headers: this.headers });
  }

  randomEndpoint() {
    return ENDPOINT_DATA[randomInt(ENDPOINT_DATA.length)];
  }

  async run(text, filepath, randomVoice = false, playSound = false, randomEndpoint = false) {
    const voice = randomVoice
      ? this.randomVoice()
      : settings.config.settings.tts.tiktok_voice;

    const chunks = this._splitText(text);

    // Use either a random endpoint or iterate through all endpoints
    const endpoints = randomEndpoint
      ? [this.randomEndpoint()]
      : ENDPOINT_DATA;

    for (const entry of endpoints) {
      let endpointValid = true;
      const tempFiles = [];
      console.log('Using endpoint: ', entry);

      for (let i = 0; i < chunks.length; i++) {
        if (!endpointValid) break;
        const chunk = chunks[i];
        if(!chunk) continue;
        try {
          const res = entry.type === 'tiktok' ? await this.session.post(`${entry.url}/?text_speaker=${voice}&req_text=${chunk}&speaker_map_type=0&aid=1233`) : await axios.post(entry.url, {
            text: chunk,
            voice: voice
          });
          if (res.status === 200 && res.data[entry.response]) {
            const audioBase64 = res.data[entry.response];
            const buffer = Buffer.from(audioBase64, 'base64');

            // Lưu từng file tạm
            const tempFile = filepath + `_part_${i}.mp3`;
            fs.writeFileSync(tempFile, buffer);
            tempFiles.push(tempFile);
          }  else if (res.status === 200 && entry.type === 'tiktok') {
            const status_code = res.data.status_code;
            if (status_code !== 0) return this.handleStatusError(status_code);
            const audioBase64 = res.data.data.v_str;
            const buffer = Buffer.from(audioBase64, 'base64');

            // Lưu từng file tạm
            const tempFile = filepath + `_part_${i}.mp3`;
            fs.writeFileSync(tempFile, buffer);
            tempFiles.push(tempFile);
          }

          else {
            endpointValid = false;
          }
        } catch (error) {
          console.error('Error:', error.message);
          endpointValid = false;
        }
      }

      if (!endpointValid) {
        // Xóa các file tạm nếu có
        for (const f of tempFiles) {
          if (fs.existsSync(f)) fs.unlinkSync(f);
        }
        continue;
      }

      if (tempFiles.length === 1) {
        // Nếu chỉ 1 chunk thì rename thẳng
        fs.renameSync(tempFiles[0], filepath);
      } else {
        // Tạo file danh sách để ffmpeg nối
        const listFile = filepath + '_list.txt';
        const fileContent = tempFiles.map(f => `file '${path.resolve(f)}'`).join('\n');
        fs.writeFileSync(listFile, fileContent);

        // Nối file với ffmpeg
        try {
          execSync(`ffmpeg -y -f concat -safe 0 -i "${listFile}" -c copy "${filepath}"`);
        } catch (e) {
          console.error('FFmpeg concat error:', e);
          // Xóa tạm rồi return lỗi
          tempFiles.forEach(f => fs.unlinkSync(f));
          fs.unlinkSync(listFile);
          throw e;
        }

        // Xóa file tạm và file danh sách
        tempFiles.forEach(f => fs.unlinkSync(f));
        fs.unlinkSync(listFile);
      }

      console.log(`File '${filepath}' has been generated successfully.`);

      if (playSound) {
        const player = require('play-sound')();
        player.play(filepath);
      }

      break;
    }
    return filepath;
  }
    handleStatusError(status_code) {
    switch (status_code) {
        case 1:
            throw new Error(`Your TikTok session id might be invalid or expired. Try getting a new one. status_code: ${status_code}`);
        case 2:
            throw new Error(`The provided text is too long. status_code: ${status_code}`);
        case 4:
            throw new Error(`Invalid speaker, please check the list of valid speaker values. status_code: ${status_code}`);
        case 5:
            throw new Error(`No session id found. status_code: ${status_code}`);
    }
    }
  _splitText(text) {
    // Tách đoạn dựa trên dấu câu và khoảng trắng, giữ giới hạn 300 ký tự
    const separatedChunks = [...text.matchAll(/.*?[.,!?:;\n]|.+/g)].map(m => m[0]);
    const finalChunks = [];

    for (let chunk of separatedChunks) {
      if (chunk.length > this.maxChars) {
        // tách nhỏ theo khoảng trắng nếu quá dài
        const words = chunk.split(' ');
        let temp = '';
        for (const word of words) {
          if ((temp + word).length > this.maxChars) {
            if (temp) finalChunks.push(temp.trim());
            temp = word + ' ';
          } else {
            temp += word + ' ';
          }
        }
        if (temp) finalChunks.push(temp.trim());
      } else {
        finalChunks.push(chunk.trim());
      }
    }

    return finalChunks;
  }

  randomVoice() {
    return VOICES[randomInt(VOICES.length)];
  }
}


const filePath = path.join(__dirname, 'cache', 'tts', 'output2.mp3');


const tts = new TikTokTTS();

// Example 1: Using the default behavior (try all endpoints in sequence)
// tts.run(`Tôi chỉ là một con yêu rắn nhỏ bé, không mang trong mình linh khí hay sức mạnh huyền bí nào.`, filePath, false, true);

// Example 2: Using a random endpoint (set the 5th parameter to true)
tts.run(`Tôi chỉ là một con yêu rắn nhỏ bé, không mang trong mình linh khí hay sức mạnh huyền bí nào.`, filePath, false, true, true);

// Example 3: Using both random voice and random endpoint
// tts.run(`Tôi chỉ là một con yêu rắn nhỏ bé, không mang trong mình linh khí hay sức mạnh huyền bí nào.`, filePath, true, true, true);