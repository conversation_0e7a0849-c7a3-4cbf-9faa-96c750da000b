import { defineStore } from 'pinia';
import { message } from 'ant-design-vue';
import axios from 'axios';

export const useElevenlabsStore = defineStore('elevenlabs', {
  state: () => ({
    // API Keys management
    apiKeys: [],
    currentApiKeyIndex: 0,
    
    // Voice data
    voices: [],
    searchResults: [],
    lastSearchQuery: '',
    
    // Settings
    selectedVoice: '',
    selectedModel: 'eleven_multilingual_v2',
    speed: 1.0,
    stability: 0.5,
    clarityBoost: 0.75,
    outputFormat: 'mp3_44100_128',
    
    // Proxy settings
    useProxy: false,
    proxyList: [],
    currentProxyIndex: 0,
    
    // Processing settings
    concurrency: 1,
    
    // UI state
    loadingVoices: false,
    loadingSearch: false,
  }),

  persist: {
    storage: localStorage,
    pick: [
      'apiKeys',
      'currentApiKeyIndex',
      'selectedVoice',
      'selectedModel',
      'speed',
      'stability',
      'clarityBoost',
      'outputFormat',
      'useProxy',
      'proxyList',
      'currentProxyIndex',
      'concurrency'
    ]
  },

  getters: {
    // Get current API key
    currentApiKey: (state) => {
      if (state.apiKeys.length === 0) return '';
      return state.apiKeys[state.currentApiKeyIndex] || '';
    },

    // Check if we have valid API key
    hasValidApiKey: (state) => {
      return state.apiKeys.length > 0 && state.apiKeys.some(key => key.trim().length > 0);
    },

    // Get current proxy
    currentProxy: (state) => {
      if (!state.useProxy || state.proxyList.length === 0) return null;
      return state.proxyList[state.currentProxyIndex] || null;
    },

    // Get voice options for select
    voiceOptions: (state) => {
      const voicesToUse = state.searchResults.length > 0 ? state.searchResults : state.voices;
      return voicesToUse.map(voice => ({
        value: voice.voice_id,
        label: `${voice.name} (${voice.voice_id})`,
        voice: voice
      }));
    },

    // Get API key count
    apiKeyCount: (state) => state.apiKeys.length,
  },

  actions: {
    // API Key management
    addApiKey(apiKey) {
      const trimmedKey = apiKey.trim();
      if (!trimmedKey) {
        message.error('API key cannot be empty');
        return false;
      }

      // Basic validation for ElevenLabs API key format
      if (trimmedKey.length < 20) {
        message.error('API key seems too short. ElevenLabs API keys are typically longer.');
        return false;
      }

      if (this.apiKeys.includes(trimmedKey)) {
        message.warning('API key already exists');
        return false;
      }

      this.apiKeys.push(trimmedKey);

      // Set as current if it's the first key
      if (this.apiKeys.length === 1) {
        this.currentApiKeyIndex = 0;
      }

      message.success(`Added API key. Total: ${this.apiKeys.length}`);
      console.log('API key added:', {
        keyPreview: `${trimmedKey.slice(0, 8)}...${trimmedKey.slice(-8)}`,
        totalKeys: this.apiKeys.length,
        currentIndex: this.currentApiKeyIndex
      });

      return true;
    },

    // Test API key validity
    async testApiKey(apiKey) {
      try {
        const response = await axios.get('https://api.elevenlabs.io/v1/user', {
          headers: {
            'xi-api-key': apiKey
          }
        });
        return { valid: true, data: response.data };
      } catch (error) {
        return {
          valid: false,
          error: error.response?.data?.detail || error.message,
          status: error.response?.status
        };
      }
    },

    removeApiKey(index) {
      if (index >= 0 && index < this.apiKeys.length) {
        this.apiKeys.splice(index, 1);
        
        // Adjust current index if needed
        if (this.currentApiKeyIndex >= this.apiKeys.length) {
          this.currentApiKeyIndex = Math.max(0, this.apiKeys.length - 1);
        }
        
        message.success('API key removed');
      }
    },

    // Rotate to next API key (for load balancing)
    rotateApiKey() {
      if (this.apiKeys.length > 1) {
        this.currentApiKeyIndex = (this.currentApiKeyIndex + 1) % this.apiKeys.length;
      }
    },

    // Get random API key
    getRandomApiKey() {
      if (this.apiKeys.length === 0) return '';
      const randomIndex = Math.floor(Math.random() * this.apiKeys.length);
      return this.apiKeys[randomIndex];
    },

    // Load all voices (original endpoint)
    async loadAllVoices(apiKey = null) {
      const keyToUse = apiKey || this.currentApiKey;
      if (!keyToUse) {
        message.error('No API key available');
        return false;
      }

      this.loadingVoices = true;
      try {
        const response = await axios.get('https://api.elevenlabs.io/v1/voices', {
          headers: {
            'xi-api-key': keyToUse
          }
        });

        this.voices = response.data.voices || [];
        message.success(`Loaded ${this.voices.length} voices`);
        return true;
      } catch (error) {
        console.error('Error loading voices:', error);
        message.error('Failed to load voices: ' + (error.response?.data?.detail || error.message));
        return false;
      } finally {
        this.loadingVoices = false;
      }
    },

    // Search voices with new API
    async searchVoices(query, apiKey = null) {
      const keyToUse = apiKey || this.currentApiKey;
      if (!keyToUse) {
        message.error('No API key available');
        return false;
      }

      this.loadingSearch = true;
      this.lastSearchQuery = query;
      
      try {
        const params = new URLSearchParams();
        if (query) params.append('search', query);
        params.append('sort', 'name');
        params.append('sort_direction', 'asc');

        const response = await axios.get(
          `https://api.us.elevenlabs.io/v2/voices?${params.toString()}`,
          {
            headers: {
              'xi-api-key': keyToUse
            }
          }
        );

        this.searchResults = response.data.voices || [];
        
        if (query) {
          message.success(`Found ${this.searchResults.length} voices for "${query}"`);
        } else {
          message.success(`Loaded ${this.searchResults.length} voices`);
        }
        
        return true;
      } catch (error) {
        console.error('Error searching voices:', error);
        message.error('Failed to search voices: ' + (error.response?.data?.detail || error.message));
        return false;
      } finally {
        this.loadingSearch = false;
      }
    },

    // Clear search results
    clearSearch() {
      this.searchResults = [];
      this.lastSearchQuery = '';
    },

    // Proxy management
    addProxy(proxy) {
      const trimmedProxy = proxy.trim();
      if (!trimmedProxy) return false;

      if (!this.proxyList.includes(trimmedProxy)) {
        this.proxyList.push(trimmedProxy);
        message.success(`Added proxy. Total: ${this.proxyList.length}`);
        return true;
      }
      return false;
    },

    removeProxy(index) {
      if (index >= 0 && index < this.proxyList.length) {
        this.proxyList.splice(index, 1);
        
        // Adjust current index if needed
        if (this.currentProxyIndex >= this.proxyList.length) {
          this.currentProxyIndex = Math.max(0, this.proxyList.length - 1);
        }
        
        message.success('Proxy removed');
      }
    },

    // Rotate to next proxy
    rotateProxy() {
      if (this.proxyList.length > 1) {
        this.currentProxyIndex = (this.currentProxyIndex + 1) % this.proxyList.length;
      }
    },

    // Get random proxy
    getRandomProxy() {
      if (!this.useProxy || this.proxyList.length === 0) return null;
      const randomIndex = Math.floor(Math.random() * this.proxyList.length);
      return this.proxyList[randomIndex];
    },

    // Load proxy file
    async loadProxyFile(file) {
      try {
        const text = await file.text();
        let proxies = [];

        try {
          // Try to parse as JSON first
          const parsed = JSON.parse(text);
          proxies = Array.isArray(parsed) ? parsed : [parsed];
        } catch {
          // If not JSON, treat as text file with one proxy per line
          proxies = text.split('\n')
            .map(line => line.trim())
            .filter(line => line.length > 0);
        }

        // Add all valid proxies
        let addedCount = 0;
        proxies.forEach(proxy => {
          if (this.addProxy(proxy)) {
            addedCount++;
          }
        });

        if (addedCount > 0) {
          message.success(`Added ${addedCount} new proxies`);
          return true;
        } else {
          message.warning('No new proxies added');
          return false;
        }
      } catch (error) {
        message.error('Failed to load proxy file: ' + error.message);
        return false;
      }
    },

    // Reset all data
    resetAll() {
      this.voices = [];
      this.searchResults = [];
      this.lastSearchQuery = '';
      this.selectedVoice = '';
      this.currentApiKeyIndex = 0;
      this.currentProxyIndex = 0;
      
      message.success('ElevenLabs data reset');
    },

    // Make TTS request with automatic API key rotation
    async generateTTS(text, voiceId = null, options = {}) {
      const voice = voiceId || this.selectedVoice;
      if (!voice) {
        throw new Error('No voice selected');
      }

      if (!this.hasValidApiKey) {
        throw new Error('No valid API key available');
      }

      const requestOptions = {
        text,
        model_id: options.model || this.selectedModel,
        voice_settings: {
          stability: options.stability ?? this.stability,
          similarity_boost: options.clarityBoost ?? this.clarityBoost,
          speed: options.speed ?? this.speed
        }
      };

      // Try with current API key first, then rotate if failed
      let lastError = null;
      const maxRetries = Math.min(this.apiKeys.length, 3); // Try max 3 API keys

      for (let attempt = 0; attempt < maxRetries; attempt++) {
        const apiKey = this.currentApiKey;

        // Debug logging
        console.log(`Attempt ${attempt + 1}:`, {
          apiKey: apiKey ? `${apiKey.slice(0, 8)}...${apiKey.slice(-8)}` : 'null',
          voice,
          hasApiKey: !!apiKey,
          apiKeyLength: apiKey?.length
        });

        if (!apiKey) {
          throw new Error('No API key available');
        }

        try {
          const response = await axios.post(
            `https://api.elevenlabs.io/v1/text-to-speech/${voice}`,
            requestOptions,
            {
              headers: {
                'xi-api-key': apiKey,
                'Content-Type': 'application/json'
              },
              responseType: 'arraybuffer'
            }
          );

          console.log('TTS request successful');
          return response.data;
        } catch (error) {
          lastError = error;
          console.error(`API key ${attempt + 1} failed:`, {
            status: error.response?.status,
            statusText: error.response?.statusText,
            detail: error.response?.data?.detail || error.message,
            apiKey: apiKey ? `${apiKey.slice(0, 8)}...${apiKey.slice(-8)}` : 'null'
          });

          // Rotate to next API key for next attempt
          if (attempt < maxRetries - 1) {
            this.rotateApiKey();
          }
        }
      }

      // If all API keys failed, throw the last error
      throw lastError;
    }
  }
});
