<!-- GptTrain.vue -->
<template>
    <div class="flex-1 absolute w-full p-4 h-full overflow-y-auto">
        <div class="flex justify-center">
            <div class="w-full">
                <a-button type="primary" danger class="mb-1 mr-2" @click="S.$router.replace('/')">Back</a-button>
                <a-button type="primary" class="mb-2" @click="openShowModal">Add training words</a-button>

                <div class="table-container">
                    <a-table :columns="columns" :data-source="store.trainList" :pagination="false">
                        <template #bodyCell="{ column, record, index }">
                            <template v-if="column.key === 'action'">
                                <a-space>
                                    <a-button type="primary" @click="changeInfo(record, index)">Edit</a-button>
                                    <a-button
                                        type="danger"
                                        :disabled="!!record.status"
                                        @click="openDeleteModal(record, index)"
                                        >Delete</a-button
                                    >
                                </a-space>
                            </template>
                        </template>
                    </a-table>
                </div>

                <!-- Add/Edit Modal -->
                <a-modal :open="store.showModal" title="Case Setup" @cancel="handleCloseModal" :footer="null">
                    <div class="h-1/2 overflow-y-auto">
                        <div class="flex items-center mb-4">
                            <span class="mr-2">Name:</span>
                            <a-input
                                v-model:value="store.trainName"
                                placeholder="Please fill in the name of the training word"
                                class="w-2/3"
                            />
                        </div>

                        <div class="flex justify-between items-center mb-4">
                            <a-select v-model:value="store.gpt_model_id" class="w-48" @change="changeGptModel">
                                <a-select-option
                                    v-for="model in gptModelList"
                                    :key="model.gpt_model_id"
                                    :value="model.gpt_model_id"
                                >
                                    {{ model.gpt_model_text }}
                                </a-select-option>
                            </a-select>
                        </div>

                        <div class="mb-4">
                            <div class="mb-2">System Defaults:</div>
                            <a-textarea v-model:value="store.presets" :rows="3" placeholder="Input content" />
                        </div>

                        <div v-for="(item, index) in store.trainInfoList" :key="index" class="mb-4">
                            <div class="flex justify-between items-center mb-2">
                                <span>{{ item.type === 0 ? 'Case Input' : 'Case Output' }}:</span>
                                <delete-outlined v-if="item.type === 0" @click="delPrompt(item, index)" />
                            </div>
                            <a-textarea v-model:value="item.text" :rows="3" placeholder="Input content" />
                        </div>

                        <div class="flex justify-end gap-2 mt-4">
                            <a-button
                                v-if="canShowReasoning"
                                type="primary"
                                :loading="store.isLoading"
                                @click="reasoning"
                            >
                                Intelligent Reasoning
                            </a-button>
                            <a-button type="primary" @click="addCase">
                                {{ getAddCaseButtonText }}
                            </a-button>
                        </div>
                    </div>

                    <div class="flex justify-end gap-2 mt-4">
                        <a-button @click="handleCloseModal">Cancel</a-button>
                        <a-button type="primary" @click="addTrainInfo">Add</a-button>
                    </div>
                </a-modal>

                <!-- Delete Confirmation Modal -->
                <a-modal
                    :open="store.delModal"
                    title="Confirm Delete"
                    @ok="delectInfo"
                    @cancel="store.delModal = false"
                >
                    <p>This operation will permanently delete the training word. Are you sure you want to delete it?</p>
                </a-modal>
            </div>
        </div>
    </div>
</template>

<script setup>
import { defineStore } from 'pinia';
import { message } from 'ant-design-vue';
import { DeleteOutlined } from '@ant-design/icons-vue';
import AIClient from '@/lib/AIClient';
import { useTTSStore } from '@/stores/ttsStore';



// Store definition
const useGptStore = defineStore('gpt', {
    state: () => ({
        trainList: [],
        trainInfoList: [],
        showModal: false,
        delModal: false,
        isLoading: false,
        trainName: '',
        presets: '',
        nowType: 0,
        nowIndex: 0,
        gpt_model_id: 6,
    }),
    actions: {
        async getTrainList() {
            // await gptInitData();
            const data = await electronAPI.invoke('database', 'GptRoles.getData');
            this.trainList = data || [];
        },
    },
});

const store = useGptStore();
const ttsStore = useTTSStore();


const gptModelList = ttsStore.aiServices.openai.models.map((model, index) => ({
    gpt_model_id: index + 1,
    gpt_model_text: model,
}));


// Table columns
const columns = [
    { title: 'ID', dataIndex: 'id', key: 'id', width: 50 },
    { title: 'Training word name', dataIndex: 'name', key: 'name' },
    {
        title: 'Preset word',
        dataIndex: 'prompt',
        key: 'prompt',
        customRender: ({ text }) => truncateString(text, 40),
    },
    { title: 'Action', key: 'action', width: 200 },
];

// Computed properties
const canShowReasoning = computed(() => {
    return store.trainInfoList.length > 0 && store.trainInfoList[store.trainInfoList.length - 1].type === 1;
});

const getAddCaseButtonText = computed(() => {
    return store.trainInfoList.length === 0 || store.trainInfoList[store.trainInfoList.length - 1].type === 1
        ? 'Add case input'
        : 'Add case output';
});

// Methods
const changeInfo = async (item, index) => {
    try {
        store.trainName = item.name;
        store.presets = item.prompt;
        store.nowIndex = index;
        store.nowType = 1;

        // Get training info list data
        const trainInfoData = await electronAPI.invoke('database', 'GptRolesAnli.getDataById', item.id);
        store.trainInfoList = trainInfoData;

        // Get and set GPT model
        const roleData = await electronAPI.invoke('database', 'GptRoles.getDataById', item.id);
        const modelItem = gptModelList.find((model) => model.gpt_model_text === roleData.model);
        if (modelItem) {
            store.gpt_model_id = modelItem.gpt_model_id;
        }

        store.showModal = true;
    } catch (error) {
        message.error('Failed to load training data');
        console.error('Error in changeInfo:', error);
    }
};
const initInfo = () => {
    store.trainName = '';
    store.presets = '';
    store.trainInfoList = [];
};
const openShowModal = () => {
    store.nowType = 0;
    store.gpt_model_id = 1;
    store.showModal = true;
    initInfo();
};
const openDeleteModal = (item, index) => {
    F.l(item, index);
    store.nowIndex = index;
    store.delModal = true;
};
const handleCloseModal = () => {
    store.showModal = false;
};

const delectInfo = async () => {
    try {
        const itemToDelete = store.trainList[store.nowIndex];

        // Delete associated training examples first
        await electronAPI.invoke('database', 'GptRolesAnli.deleteImagesByRoleId', itemToDelete.id);

        // Then delete the main training record
        await electronAPI.invoke('database', 'GptRoles.deleteImage', itemToDelete.id);

        // Refresh the list and close modal
        await store.getTrainList();
        store.delModal = false;
        message.success('Training data deleted successfully');
    } catch (error) {
        message.error('Failed to delete training data');
        console.error('Error in delectInfo:', error);
    }
};

const reasoning = async () => {
    let _trainInfoList = [...store.trainInfoList];
    let _prompt_item = '';
    try {
        store.isLoading = true;
        F.l(_trainInfoList, '_trainInfoList');
        let gpt_model = 'gpt-3.5-turbo';
        gptModelList.forEach((item, index) => {
            if (store.gpt_model_id == item.gpt_model_id) {
                gpt_model = gptModelList[index].gpt_model_text;
            }
        });
        let proxy_address = await electronAPI.invoke('database', 'Config.getValueByName', 'proxy_address');
        let openAIKey = (await electronAPI.invoke('database', 'Config.getValueByName', 'openai_key')) || ttsStore.aiServices.openai.apiKey;
        F.l(openAIKey, gpt_model, proxy_address, 'openAIKeyopenAIKey');
        F.l(
            store.trainList[store.nowIndex]?.prompt,
            store.presets,
            'store.trainList[store.nowIndex].prompt',
            _trainInfoList,
            ', _trainInfoList',
        );
        const aIClient = new AIClient({
            provider: 'gemini',
            apiKey: 'AIzaSyBOF2B5g63mmt1Vlng4BsXAiq4F6NZMCfA',
            model: 'gemini-1.5-flash'
        })
        _prompt_item = await aIClient.train(store.presets, _trainInfoList);
        F.l(_prompt_item, '_prompt_item');
    } catch (error) {
        F.l(error);
        store.isLoading = false;
        message.error('Gpt is invalid, please make sure gpt is connected and tested');
        return;
    }
    _trainInfoList[_trainInfoList.length - 1].text = _prompt_item.text;
    store.isLoading = false;
    store.trainInfoList = _trainInfoList;
};
const addCase = () => {
    const lastCase = store.trainInfoList[store.trainInfoList.length - 1];
    if (store.trainInfoList.length !== 0 && lastCase?.text.trim().length === 0) {
        message.error('Case content cannot be empty');
        return;
    }

    const type = store.trainInfoList.length > 0 ? (lastCase.type === 0 ? 1 : 0) : 0;

    store.trainInfoList.push({ type, text: '' });
};

const addTrainInfo = async () => {
    const trimmedPresets = store.presets.trim();
    const trimmedTrainName = store.trainName.trim();

    if (!trimmedTrainName) {
        message.error('Please enter a name');
        return;
    }

    if (!trimmedPresets) {
        message.error('Please enter the system default');
        return;
    }

    const lastCase = store.trainInfoList[store.trainInfoList.length - 1];
    if (store.trainInfoList.length !== 0) {
        if (lastCase.type === 0) {
            message.error('Please add output example');
            return;
        }
        if (lastCase.text.trim() === '') {
            message.error('Case content cannot be empty');
            return;
        }
    }

    try {
        const gptModel = gptModelList.find((m) => m.gpt_model_id === store.gpt_model_id)?.gpt_model_text;

        if (store.nowType === 0) {
            // Add new training
            const trainInfoData = {
                name: store.trainName,
                prompt: store.presets,
                model: gptModel,
            };

            const [trainInfo] = await electronAPI.invoke('database', 'GptRoles.insert', trainInfoData);

            for (const item of store.trainInfoList) {
                await electronAPI.invoke('database', 'GptRolesAnli.insert', {
                    ...item,
                    role_id: trainInfo.id,
                });
            }
        } else {
            // Update existing training
            await electronAPI.invoke('database', 'GptRoles.update', store.trainList[store.nowIndex].id, {
                name: store.trainName,
                prompt: store.presets,
            });

            for (const item of store.trainInfoList) {
                if (item.id) {
                    await electronAPI.invoke('database', 'GptRolesAnli.update', item.id, {
                        ...item,
                        role_id: store.trainList[store.nowIndex].id,
                    });
                } else {
                    await electronAPI.invoke('database', 'GptRolesAnli.insert', {
                        ...item,
                        role_id: store.trainList[store.nowIndex].id,
                    });
                }
            }
        }

        await store.getTrainList();
        handleCloseModal();
        message.success('Operation successful');
    } catch (error) {
        message.error('Operation failed');
        console.error(error);
    }
};

// Utility functions
const truncateString = (str, num) => {
    if (!str || str.length <= num) return str;
    return str.slice(0, num - 3) + '...';
};

const _delPrompt = (item, index) => {
    if (store.trainInfoList.length <= 1) {
        message.error('At least one case input is required');
        return;
    }
    store.trainInfoList.splice(index, 1);
    if (index === store.trainInfoList.length) {
        store.trainInfoList.push({ type: 0, text: '' });
    }
};


  const delPrompt = async (item, index) => {
    let _trainInfoList = [...store.trainInfoList]
    if (item.id) {
      await electronAPI.invoke('database', 'GptRolesAnli.deleteImagesById', item.id)
      if (_trainInfoList[index + 1]) {
        await electronAPI.invoke('database', 'GptRolesAnli.deleteImagesById', _trainInfoList[index + 1].id)
        console.log(store.trainList[store.nowIndex], store.nowIndex, 'trainList[nowIndex], nowIndex');
        changeInfo(store.trainList[store.nowIndex], store.nowIndex)
      }
    } else {
      if (_trainInfoList[index + 1]) {
        _trainInfoList.splice(index, 2)
      } else {
        _trainInfoList.splice(index, 1)
      }
      store.trainInfoList = _trainInfoList;
    }
  }


// Lifecycle hooks
onMounted(async () => {
    await store.getTrainList();
});
</script>

<style>
.container {
    height: 100%;
    overflow: hidden auto;
}
</style>
