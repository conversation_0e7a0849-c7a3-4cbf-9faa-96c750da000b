@url = http://localhost:8082

###
GET {{url}}/api/get-speakers?type=volcengine

###
GET {{url}}/api/get-speakers



###
POST {{url}}/api/generate-tts
Content-Type: application/json

{
    "text":"Tạo chuỗi ký và sinh hash", 
    "speaker":"BV421_vivn_streaming",
    "audio_config":{},
    "language": "vi"
}

###
POST {{url}}/api/tiktok-tts
Content-Type: application/json

{
    "voice":"BV074_streaming",
    "text":"Tạo chuỗi ký và sinh hash",
    "sessionId":"f99b95b348771c84efde83a0d457cc4d"
}


###
POST https://gesserit.co/api/tiktok-tts
Content-Type: application/json

{
    "voice":"BV074_streaming",
    "text":"Tạo chuỗi ký và sinh hash"
}

###
GET https://gesserit.co/api/voices

###
GET https://api.streamelements.com/kappa/v2/speech?voice=vi-VN-Wavenet-C&text=Tạo chuỗi ký và sinh has<PERSON>##
[<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, Amy, Russell, Nicole, Vicki, Marlene, Hans, Naja, Mads, Gwyneth, Zhiyu, es-ES-Standard-A, it-IT-Standard-A, it-IT-Wavenet-A, ja-JP-Standard-A, ja-JP-Wavenet-A, ko-KR-Standard-A, ko-KR-Wavenet-A, pt-BR-Standard-A, tr-TR-Standard-A, sv-SE-Standard-A, nl-NL-Standard-A, nl-NL-Wavenet-A, en-US-Wavenet-A, en-US-Wavenet-B, en-US-Wavenet-C, en-US-Wavenet-D, en-US-Wavenet-E, en-US-Wavenet-F, en-GB-Standard-A, en-GB-Standard-B, en-GB-Standard-C, en-GB-Standard-D, en-GB-Wavenet-A, en-GB-Wavenet-B, en-GB-Wavenet-C, en-GB-Wavenet-D, en-US-Standard-B, en-US-Standard-C, en-US-Standard-D, en-US-Standard-E, de-DE-Standard-A, de-DE-Standard-B, de-DE-Wavenet-A, de-DE-Wavenet-B, de-DE-Wavenet-C, de-DE-Wavenet-D, en-AU-Standard-A, en-AU-Standard-B, en-AU-Wavenet-A, en-AU-Wavenet-B, en-AU-Wavenet-C, en-AU-Wavenet-D, en-AU-Standard-C, en-AU-Standard-D, fr-CA-Standard-A, fr-CA-Standard-B, fr-CA-Standard-C, fr-CA-Standard-D, fr-FR-Standard-C, fr-FR-Standard-D, fr-FR-Wavenet-A, fr-FR-Wavenet-B, fr-FR-Wavenet-C, fr-FR-Wavenet-D, da-DK-Wavenet-A, pl-PL-Wavenet-A, pl-PL-Wavenet-B, pl-PL-Wavenet-C, pl-PL-Wavenet-D, pt-PT-Wavenet-A, pt-PT-Wavenet-B, pt-PT-Wavenet-C, pt-PT-Wavenet-D, ru-RU-Wavenet-A, ru-RU-Wavenet-B, ru-RU-Wavenet-C, ru-RU-Wavenet-D, sk-SK-Wavenet-A, tr-TR-Wavenet-A, tr-TR-Wavenet-B, tr-TR-Wavenet-C, tr-TR-Wavenet-D, tr-TR-Wavenet-E, uk-UA-Wavenet-A, ar-XA-Wavenet-A, ar-XA-Wavenet-B, ar-XA-Wavenet-C, cs-CZ-Wavenet-A, nl-NL-Wavenet-B, nl-NL-Wavenet-C, nl-NL-Wavenet-D, nl-NL-Wavenet-E, en-IN-Wavenet-A, en-IN-Wavenet-B, en-IN-Wavenet-C, fil-PH-Wavenet-A, fi-FI-Wavenet-A, el-GR-Wavenet-A, hi-IN-Wavenet-A, hi-IN-Wavenet-B, hi-IN-Wavenet-C, hu-HU-Wavenet-A, id-ID-Wavenet-A, id-ID-Wavenet-B, id-ID-Wavenet-C, it-IT-Wavenet-B, it-IT-Wavenet-C, it-IT-Wavenet-D, ja-JP-Wavenet-B, ja-JP-Wavenet-C, ja-JP-Wavenet-D, cmn-CN-Wavenet-A, cmn-CN-Wavenet-B, cmn-CN-Wavenet-C, cmn-CN-Wavenet-D, nb-no-Wavenet-E, nb-no-Wavenet-A, nb-no-Wavenet-B, nb-no-Wavenet-C, nb-no-Wavenet-D, vi-VN-Wavenet-A, vi-VN-Wavenet-B, vi-VN-Wavenet-C, vi-VN-Wavenet-D, sr-rs-Standard-A, lv-lv-Standard-A, is-is-Standard-A, bg-bg-Standard-A, af-ZA-Standard-A, Tracy, Danny, Huihui, Yaoyao, Kangkang, HanHan, Zhiwei, Asaf, An, Stefanos, Filip, Ivan, Heidi, Herena, Kalpana, Hemant, Matej, Andika, Rizwan, Lado, Valluvar, Linda, Heather, Sean, Michael, Karsten, Guillaume, Pattara, Jakub, Szabolcs, Hoda, Naayf]

####

Tôi chỉ là một con yêu rắn nhỏ không có linh chất
Nhưng lại bắt chước loài người dâng lễ vật lên Miếu Sơn Thần
Món đồ cúng tôi bày biện
Lại chính là ba người vừa bị cắn chết

####
GET https://api.lolhuman.xyz/api/translate/auto/ja?apikey=gataDios&text=Lại chính là ba người vừa bị cắn chết


###
POST https://tiktok-tts.weilnet.workers.dev/api/generation
Content-Type: application/json

{
    "text":"Tạo chuỗi ký và sinh hash",
    "voice":"BV075_streaming"
}

### 
POST https://tiktok-tts.printmechanicalbeltpumpkingutter.workers.dev/api/generation
Content-Type: application/json

{
    "text":"Tạo chuỗi ký và sinh hash",
    "voice":"BV562_streaming"
}

### openai models
GET https://generativelanguage.googleapis.com/v1beta/models?key=AIzaSyBOF2B5g63mmt1Vlng4BsXAiq4F6NZMCfA

### openrouter
GET https://openrouter.ai/api/v1/models
Authorization: Bearer 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