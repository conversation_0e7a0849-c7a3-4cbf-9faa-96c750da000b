// Initialize summary tables in database
const path = require('path');
const Database = require('./index');

async function initSummaryTables() {
    console.log('🗄️ Initializing Summary Tables...\n');
    
    try {
        // Initialize database
        const dbPath = path.join(__dirname, '../../data/novels.db');
        console.log(`Database path: ${dbPath}`);
        
        const db = new Database(dbPath);
        
        // Create all tables including the new summary table
        await db.initializeTables();
        
        console.log('✅ All tables initialized successfully');
        
        // Check table structure
        console.log('\n📊 Checking table structure...');
        
        // Check books table
        const booksInfo = await db.knex.raw("PRAGMA table_info(books)");
        console.log(`📚 Books table: ${booksInfo.length} columns`);
        
        // Check chapters table
        const chaptersInfo = await db.knex.raw("PRAGMA table_info(chapters)");
        console.log(`📖 Chapters table: ${chaptersInfo.length} columns`);
        
        // Check chapter_summaries table
        const summariesInfo = await db.knex.raw("PRAGMA table_info(chapter_summaries)");
        console.log(`📝 Chapter summaries table: ${summariesInfo.length} columns`);
        
        console.log('\n📋 Chapter summaries table structure:');
        summariesInfo.forEach(column => {
            console.log(`  ${column.name}: ${column.type} ${column.notnull ? 'NOT NULL' : ''} ${column.pk ? 'PRIMARY KEY' : ''}`);
        });
        
        // Check foreign key constraints
        console.log('\n🔗 Checking foreign key constraints...');
        const fkInfo = await db.knex.raw("PRAGMA foreign_key_list(chapter_summaries)");
        console.log(`Found ${fkInfo.length} foreign key constraints:`);
        fkInfo.forEach(fk => {
            console.log(`  ${fk.from} -> ${fk.table}.${fk.to}`);
        });
        
        // Check indexes
        console.log('\n📇 Checking indexes...');
        const indexInfo = await db.knex.raw("PRAGMA index_list(chapter_summaries)");
        console.log(`Found ${indexInfo.length} indexes:`);
        indexInfo.forEach(index => {
            console.log(`  ${index.name}: ${index.unique ? 'UNIQUE' : 'INDEX'}`);
        });
        
        // Get current data counts
        console.log('\n📈 Current data counts:');
        
        const bookCount = await db.knex('books').count('id as count').first();
        console.log(`  Books: ${bookCount.count}`);
        
        const chapterCount = await db.knex('chapters').count('id as count').first();
        console.log(`  Chapters: ${chapterCount.count}`);
        
        const summaryCount = await db.knex('chapter_summaries').count('id as count').first();
        console.log(`  Summaries: ${summaryCount.count}`);
        
        // Test basic operations
        console.log('\n🧪 Testing basic operations...');
        
        if (bookCount.count > 0) {
            // Get a sample book
            const sampleBook = await db.books.getAllBooks(1);
            if (sampleBook.length > 0) {
                const book = sampleBook[0];
                console.log(`Sample book: ${book.title} (ID: ${book.id})`);
                
                // Get chapters for this book
                const chapters = await db.chapters.getChaptersByBookId(book.id);
                console.log(`  Chapters: ${chapters.length}`);
                
                if (chapters.length > 0) {
                    // Test summary operations
                    const testChapter = chapters[0];
                    console.log(`  Testing with chapter: ${testChapter.title}`);
                    
                    // Test upsert summary
                    const summaryId = await db.chapterSummaries.upsertSummary(
                        testChapter.id,
                        book.id,
                        'Test original content',
                        null
                    );
                    console.log(`  ✅ Summary upserted with ID: ${summaryId}`);
                    
                    // Test update summary
                    await db.chapterSummaries.upsertSummary(
                        testChapter.id,
                        book.id,
                        null,
                        'Test summary content'
                    );
                    console.log(`  ✅ Summary updated`);
                    
                    // Test get summary
                    const summary = await db.chapterSummaries.getSummaryByChapter(testChapter.id);
                    if (summary) {
                        console.log(`  ✅ Summary retrieved: ${summary.status}`);
                    }
                    
                    // Test get stats
                    const stats = await db.chapterSummaries.getSummaryStats(book.id);
                    console.log(`  ✅ Stats retrieved: ${stats.total} total, ${stats.completed} completed`);
                }
            }
        } else {
            console.log('  No books found - skipping operation tests');
            console.log('  Run extraction first: node electron/playwright/test-extraction.js');
        }
        
        console.log('\n🎉 Summary tables initialization completed successfully!');
        console.log('\n📋 Next steps:');
        console.log('1. Run extraction if no books: node electron/playwright/test-extraction.js');
        console.log('2. Test summary system: node electron/test-summary-system.js');
        console.log('3. Start the app: npm run electron:dev');
        console.log('4. Go to Summary page and test the UI');
        
        await db.close();
        
    } catch (error) {
        console.error('❌ Error initializing summary tables:', error);
        console.error('Stack trace:', error.stack);
        throw error;
    }
}

// Command line interface
if (require.main === module) {
    initSummaryTables().then(() => {
        console.log('\n✨ Initialization completed');
        process.exit(0);
    }).catch(error => {
        console.error('\n💥 Initialization failed:', error);
        process.exit(1);
    });
}

module.exports = { initSummaryTables };
