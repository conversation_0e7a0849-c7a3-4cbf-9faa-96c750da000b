// Database migration script for novel extraction database
const path = require('path');
const Database = require('./index');

class DatabaseMigrator {
    constructor(dbPath = null) {
        this.dbPath = dbPath || path.join(__dirname, '../../data/novels.db');
        this.db = null;
    }

    async initialize() {
        try {
            this.db = new Database(this.dbPath);
            console.log(`Connected to database: ${this.dbPath}`);
            return true;
        } catch (error) {
            console.error('Failed to connect to database:', error);
            return false;
        }
    }

    async runMigrations() {
        try {
            console.log('Starting database migrations...\n');

            // Migration 1: Add category and last_update_info columns to books table
            await this.migrateBooksTable();

            // Migration 2: Update chapters table if needed (future migrations)
            await this.migrateChaptersTable();

            console.log('\n✅ All migrations completed successfully!');
            return true;
        } catch (error) {
            console.error('❌ Migration failed:', error);
            return false;
        }
    }

    async migrateBooksTable() {
        try {
            console.log('📋 Migrating books table...');

            // Check if books table exists
            const tableExists = await this.db.knex.schema.hasTable('books');
            if (!tableExists) {
                console.log('   Books table does not exist, will be created on next initialization.');
                return;
            }

            let changesMade = false;

            // Check and add category column
            const hasCategory = await this.db.knex.schema.hasColumn('books', 'category');
            if (!hasCategory) {
                console.log('   Adding category column...');
                await this.db.knex.schema.alterTable('books', (table) => {
                    table.string('category', 200).nullable();
                });
                changesMade = true;
            } else {
                console.log('   ✓ Category column already exists');
            }

            // Check and add last_update_info column
            const hasLastUpdateInfo = await this.db.knex.schema.hasColumn('books', 'last_update_info');
            if (!hasLastUpdateInfo) {
                console.log('   Adding last_update_info column...');
                await this.db.knex.schema.alterTable('books', (table) => {
                    table.string('last_update_info', 200).nullable();
                });
                changesMade = true;
            } else {
                console.log('   ✓ Last_update_info column already exists');
            }

            if (changesMade) {
                console.log('   ✅ Books table migration completed');
            } else {
                console.log('   ✓ Books table is up to date');
            }

        } catch (error) {
            console.error('   ❌ Books table migration failed:', error);
            throw error;
        }
    }

    async migrateChaptersTable() {
        try {
            console.log('📋 Checking chapters table...');

            // Check if chapters table exists
            const tableExists = await this.db.knex.schema.hasTable('chapters');
            if (!tableExists) {
                console.log('   Chapters table does not exist, will be created on next initialization.');
                return;
            }

            // Future migrations for chapters table can be added here
            console.log('   ✓ Chapters table is up to date');

        } catch (error) {
            console.error('   ❌ Chapters table check failed:', error);
            throw error;
        }
    }

    async showTableInfo() {
        try {
            console.log('\n📊 Database Table Information:');

            // Books table info
            const booksExists = await this.db.knex.schema.hasTable('books');
            if (booksExists) {
                const booksCount = await this.db.books.getBooksCount();
                console.log(`   📚 Books table: ${booksCount} records`);

                // Show column info
                const booksColumns = await this.db.knex('books').columnInfo();
                console.log('   Columns:', Object.keys(booksColumns).join(', '));
            } else {
                console.log('   📚 Books table: Not created yet');
            }

            // Chapters table info
            const chaptersExists = await this.db.knex.schema.hasTable('chapters');
            if (chaptersExists) {
                const chaptersCount = await this.db.chapters.getChaptersCount();
                console.log(`   📖 Chapters table: ${chaptersCount} records`);

                // Show column info
                const chaptersColumns = await this.db.knex('chapters').columnInfo();
                console.log('   Columns:', Object.keys(chaptersColumns).join(', '));
            } else {
                console.log('   📖 Chapters table: Not created yet');
            }

        } catch (error) {
            console.error('Error getting table info:', error);
        }
    }

    async close() {
        if (this.db) {
            await this.db.close();
            console.log('Database connection closed.');
        }
    }
}

// Command line interface
async function runCLI() {
    const args = process.argv.slice(2);
    const command = args[0] || 'migrate';
    const dbPath = args[1]; // Optional custom database path

    const migrator = new DatabaseMigrator(dbPath);
    const initialized = await migrator.initialize();

    if (!initialized) {
        console.error('Failed to initialize database migrator.');
        process.exit(1);
    }

    try {
        switch (command) {
            case 'migrate':
                await migrator.runMigrations();
                break;
            case 'info':
                await migrator.showTableInfo();
                break;
            case 'books-only':
                await migrator.migrateBooksTable();
                break;
            case 'chapters-only':
                await migrator.migrateChaptersTable();
                break;
            default:
                console.log('Available commands:');
                console.log('  migrate       - Run all migrations (default)');
                console.log('  info          - Show database table information');
                console.log('  books-only    - Migrate only books table');
                console.log('  chapters-only - Migrate only chapters table');
                console.log('');
                console.log('Usage: node migrate.js [command] [database_path]');
                console.log('Example: node migrate.js migrate ./custom/path/novels.db');
        }
    } catch (error) {
        console.error('Command failed:', error);
        process.exit(1);
    } finally {
        await migrator.close();
    }
}

// Run CLI if this file is executed directly
if (require.main === module) {
    runCLI();
}

module.exports = DatabaseMigrator;
