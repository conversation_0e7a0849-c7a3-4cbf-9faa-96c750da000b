<script setup>
import { ref, computed } from 'vue';
import { useTTSStore } from '../stores/ttsStore';

const ttsStore = useTTSStore();
const text = ref(ttsStore.text);
// const selectedSpeaker = ref(ttsStore.selectedSpeaker);

const speakers = computed(() => ttsStore.speakers);
const isLoading = computed(() => ttsStore.isLoading);
const error = computed(() => ttsStore.error);
const audioUrl = computed(() => ttsStore.audioUrl);

async function generateTTS() {
  if (!text.value) return;
  ttsStore.setText(text.value);
  ttsStore.setSelectedSpeaker(ttsStore.selectedSpeaker);
  await ttsStore.generateTTS();
}

function formatDuration(seconds) {
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins}:${secs.toString().padStart(2, '0')}`;
}

function downloadAudio(){
  const a = document.createElement('a');
  a.href = audioUrl.value;
  a.download = `tts-audio-${Date.now()}.mp3`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
}
</script>

<template>
  <!-- Container chiếm full height, có scroll riêng -->
  <div class="flex-1 flex flex-col bg-gray-900 text-white">
    <!-- Header - cố định không scroll -->
    <div class="flex-shrink-0 p-4 border-b border-gray-700">
      <h2 class="text-lg font-medium text-white">Text to Speech</h2>
    </div>

    <!-- Content area - có thể scroll -->
    <div class="flex-1 min-h-0 overflow-auto p-4">
      <div class="space-y-4 max-w-2xl">
        <div>
          <label for="speaker" class="block text-sm font-medium text-gray-300 mb-2">
            Voice
          </label>
          <a-select
            id="speaker"
            v-model:value="ttsStore.selectedSpeaker"
            class="w-full"
            placeholder="Select a voice"
          >
            <a-select-option v-for="(speaker, index) in speakers" :key="index" :value="speaker.id">
              {{ speaker.name }}
            </a-select-option>
          </a-select>
        </div>

        <div>
          <label for="text" class="block text-sm font-medium text-gray-300 mb-2">
            Text
          </label>
          <a-textarea
            id="text"
            v-model:value="text"
            placeholder="Enter text to convert to speech"
            :rows="6"
            class="w-full"
          />
        </div>

        <div class="flex justify-end">
          <a-button type="primary" @click="generateTTS" :loading="isLoading">
            Generate Speech
          </a-button>
        </div>

        <div v-if="error" class="p-3 bg-red-900/50 text-red-300 rounded border border-red-700">
          {{ error }}
        </div>

        <div v-if="audioUrl" class="bg-gray-800 p-4 rounded-lg border border-gray-700">
          <h3 class="text-md font-medium text-white mb-3">Generated Audio</h3>
          <audio controls class="w-full mb-3" :src="audioUrl"></audio>
          <div class="text-sm text-gray-400 mb-3">
            Duration: {{ formatDuration(ttsStore.audioDuration) }}
          </div>
          <div class="flex space-x-2">
            <a-button type="default" size="small" :href="audioUrl" target="_blank">
              Open in New Tab
            </a-button>
            <a-button type="default" size="small" @click="downloadAudio">
              Download
            </a-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
