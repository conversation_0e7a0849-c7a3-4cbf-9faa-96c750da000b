<template>
  <a-modal
    v-model:open="visible"
    title="Thay thế hàng loạt"
    width="800px"
    @ok="handleReplace"
    @cancel="handleCancel"
    :confirmLoading="processing"
  >
    <div class="batch-replace-content">
      <!-- Search and Replace Form -->
      <a-form layout="vertical">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="Từ cần thay thế" required>
              <a-input 
                v-model:value="searchText" 
                placeholder="Nhập từ hoặc cụm từ cần thay thế"
                @pressEnter="addReplaceRule"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="Thay thế bằng" required>
              <a-input 
                v-model:value="replaceText" 
                placeholder="Nhập từ hoặc cụm từ thay thế"
                @pressEnter="addReplaceRule"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item>
              <a-checkbox v-model:checked="caseSensitive">
                <PERSON><PERSON> biệt hoa thường
              </a-checkbox>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item>
              <a-checkbox v-model:checked="wholeWord">
                Chỉ thay thế từ nguyên vẹn
              </a-checkbox>
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item>
          <a-button type="primary" @click="addReplaceRule" :disabled="!searchText.trim()">
            <template #icon><PlusOutlined /></template>
            Thêm quy tắc
          </a-button>
        </a-form-item>
      </a-form>

      <!-- Replace Rules List -->
      <div v-if="replaceRules.length > 0" class="replace-rules mb-4">
        <h4 class="text-sm font-medium mb-2">Quy tắc thay thế ({{ replaceRules.length }})</h4>
        <div class="rules-list max-h-40 overflow-y-auto border rounded p-2 bg-gray-800">
          <div 
            v-for="(rule, index) in replaceRules" 
            :key="index"
            class="rule-item flex items-center justify-between p-2 mb-1 bg-gray-700 rounded border"
          >
            <div class="rule-content flex-1 text-gray-600">
              <span class="search-text font-mono bg-red-200 px-2 py-1 rounded text-sm">
                "{{ rule.search }}"
              </span>
              <span class="mx-2">→</span>
              <span class="replace-text font-mono bg-green-200 px-2 py-1 rounded text-sm">
                "{{ rule.replace }}"
              </span>
              <div class="rule-options text-xs text-gray-500 mt-1">
                <span v-if="rule.caseSensitive" class="mr-2">🔤 Phân biệt hoa thường</span>
                <span v-if="rule.wholeWord">📝 Từ nguyên vẹn</span>
              </div>
            </div>
            <a-button 
              type="text" 
              danger 
              size="small" 
              @click="removeRule(index)"
              :icon="h(DeleteOutlined)"
            />
          </div>
        </div>
      </div>

      <!-- Preview Section -->
      <div v-if="replaceRules.length > 0" class="preview-section">
        <div class="flex items-center justify-between mb-2">
          <h4 class="text-sm font-medium">Xem trước kết quả</h4>
          <a-button size="small" @click="generatePreview">
            <template #icon><EyeOutlined /></template>
            Tạo xem trước
          </a-button>
        </div>

        <div v-if="previewResults.length > 0" class="preview-results max-h-60 overflow-y-auto border rounded p-3 bg-gray-800">
          <div class="preview-stats mb-3 text-sm text-gray-600">
            Tìm thấy {{ previewResults.length }} subtitle có thay đổi
          </div>
          
          <div 
            v-for="result in previewResults.slice(0, 10)" 
            :key="result.id"
            class="preview-item mb-3 p-2 bg-gray-900 rounded border"
          >
            <div class="subtitle-info text-xs text-gray-500 mb-1">
              Subtitle #{{ result.id }}
            </div>
            <div class="before-after">
              <div class="before mb-1">
                <span class="label text-xs text-red-600 font-medium">Trước:</span>
                <div class="text font-mono text-sm" v-html="result.beforeHighlight"></div>
              </div>
              <div class="after">
                <span class="label text-xs text-green-600 font-medium">Sau:</span>
                <div class="text font-mono text-sm" v-html="result.afterHighlight"></div>
              </div>
            </div>
          </div>

          <div v-if="previewResults.length > 10" class="text-center text-sm text-gray-500 mt-2">
            ... và {{ previewResults.length - 10 }} subtitle khác
          </div>
        </div>
      </div>

      <!-- Options -->
      <div class="options-section mt-4">
        <a-form-item label="Tùy chọn">
          <a-checkbox v-model:checked="onlyTranslated">
            Chỉ thay thế trong subtitle đã dịch
          </a-checkbox>
        </a-form-item>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-between items-center">
        <div class="text-sm text-gray-500">
          <span v-if="previewResults.length > 0">
            Sẽ thay đổi {{ previewResults.length }} subtitle
          </span>
        </div>
        <div class="flex gap-2">
          <a-button @click="handleCancel">Hủy</a-button>
          <a-button 
            type="primary" 
            @click="handleReplace" 
            :disabled="replaceRules.length === 0"
            :loading="processing"
          >
            Thay thế
          </a-button>
        </div>
      </div>
    </template>
  </a-modal>
</template>

<script setup>
import { ref, computed, h } from 'vue'
import { message } from 'ant-design-vue'
import { 
  PlusOutlined, 
  DeleteOutlined, 
  EyeOutlined 
} from '@ant-design/icons-vue'

// Props
const props = defineProps({
  open: {
    type: Boolean,
    default: false
  },
  subtitles: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:open', 'replace', 'close'])

// Reactive data
const visible = ref(props.open)
const processing = ref(false)

// Form data
const searchText = ref('')
const replaceText = ref('')
const caseSensitive = ref(false)
const wholeWord = ref(false)
const onlyTranslated = ref(true)

// Replace rules
const replaceRules = ref([])
const previewResults = ref([])

// Watch for prop changes
import { watch } from 'vue'
watch(() => props.open, (newVal) => {
  visible.value = newVal
  if (newVal) {
    resetForm()
  }
})

watch(visible, (newVal) => {
  emit('update:open', newVal)
})

// Methods
const resetForm = () => {
  searchText.value = ''
  replaceText.value = ''
  caseSensitive.value = false
  wholeWord.value = false
  onlyTranslated.value = true
  replaceRules.value = []
  previewResults.value = []
}

const addReplaceRule = () => {
  if (!searchText.value.trim()) {
    message.warning('Vui lòng nhập từ cần thay thế')
    return
  }

  const rule = {
    search: searchText.value.trim(),
    replace: replaceText.value.trim(),
    caseSensitive: caseSensitive.value,
    wholeWord: wholeWord.value
  }

  // Check for duplicate rules
  const exists = replaceRules.value.some(r => 
    r.search === rule.search && 
    r.caseSensitive === rule.caseSensitive && 
    r.wholeWord === rule.wholeWord
  )

  if (exists) {
    message.warning('Quy tắc này đã tồn tại')
    return
  }

  replaceRules.value.push(rule)
  searchText.value = ''
  replaceText.value = ''
  message.success('Đã thêm quy tắc thay thế')

  // Auto generate preview
  generatePreview()
}

const removeRule = (index) => {
  replaceRules.value.splice(index, 1)
  generatePreview()
}

const applyRules = (text, rules) => {
  let result = text
  const changes = []

  rules.forEach(rule => {
    let flags = 'g'
    if (!rule.caseSensitive) flags += 'i'

    let searchPattern = rule.search
    if (rule.wholeWord) {
      searchPattern = `\\b${searchPattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`
    } else {
      searchPattern = searchPattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
    }

    const regex = new RegExp(searchPattern, flags)
    const matches = [...result.matchAll(regex)]
    
    if (matches.length > 0) {
      result = result.replace(regex, rule.replace)
      changes.push({
        rule,
        matches: matches.length
      })
    }
  })

  return { result, changes }
}

const highlightChanges = (original, modified, rules) => {
  let highlighted = original

  rules.forEach(rule => {
    let flags = 'g'
    if (!rule.caseSensitive) flags += 'i'

    let searchPattern = rule.search
    if (rule.wholeWord) {
      searchPattern = `\\b${searchPattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`
    } else {
      searchPattern = searchPattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
    }

    const regex = new RegExp(searchPattern, flags)
    highlighted = highlighted.replace(regex, `<mark class="bg-red-200">$&</mark>`)
  })

  return highlighted
}

const generatePreview = () => {
  if (replaceRules.value.length === 0) {
    previewResults.value = []
    return
  }

  const results = []
  const subtitlesToProcess = onlyTranslated.value 
    ? props.subtitles.filter(sub => sub.translatedText && sub.translatedText.trim())
    : props.subtitles.filter(sub => sub.translatedText)

  subtitlesToProcess.forEach(subtitle => {
    const originalText = subtitle.translatedText || ''
    const { result: modifiedText, changes } = applyRules(originalText, replaceRules.value)

    if (originalText !== modifiedText) {
      results.push({
        id: subtitle.id,
        index: subtitle.index,
        original: originalText,
        modified: modifiedText,
        beforeHighlight: highlightChanges(originalText, modifiedText, replaceRules.value),
        afterHighlight: modifiedText.replace(
          new RegExp(`(${replaceRules.value.map(r => r.replace).join('|')})`, 'gi'),
          '<mark class="bg-green-200">$1</mark>'
        ),
        changes
      })
    }
  })

  previewResults.value = results
  
  if (results.length === 0) {
    message.info('Không tìm thấy subtitle nào cần thay đổi')
  } else {
    message.success(`Tìm thấy ${results.length} subtitle sẽ được thay đổi`)
  }
}

const handleReplace = async () => {
  if (replaceRules.value.length === 0) {
    message.warning('Vui lòng thêm ít nhất một quy tắc thay thế')
    return
  }

  if (previewResults.value.length === 0) {
    message.warning('Không có subtitle nào cần thay đổi')
    return
  }

  processing.value = true

  try {
    const replacements = previewResults.value.map(result => ({
      id: result.id,
      newText: result.modified
    }))

    emit('replace', replacements)
    
    message.success(`Đã thay thế thành công ${replacements.length} subtitle`)
    handleCancel()
  } catch (error) {
    message.error('Lỗi khi thay thế: ' + error.message)
  } finally {
    processing.value = false
  }
}

const handleCancel = () => {
  visible.value = false
  emit('close')
}
</script>

<style scoped>
.batch-replace-content {
  max-height: 70vh;
  overflow-y: auto;
}

.rule-item {
  transition: all 0.2s ease;
}

.rule-item:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.preview-item {
  transition: all 0.2s ease;
}

.preview-item:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(mark) {
  padding: 1px 2px;
  border-radius: 2px;
}
</style>
