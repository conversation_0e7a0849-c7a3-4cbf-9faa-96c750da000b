import { ref } from 'vue'

// Font service để quản lý fonts từ static/fonts
class FontService {
  constructor() {
    this.fonts = ref([])
    this.isLoaded = ref(false)
  }

  // Load danh sách fonts từ static/fonts
  async loadFonts() {
    try {
      if (window.electronAPI && window.electronAPI.getFonts) {
        const fontList = await window.electronAPI.getFonts()
        this.fonts.value = fontList
        this.isLoaded.value = true
        return fontList
      } else {
        // Fallback cho development mode
        const defaultFonts = [
          { name: 'Arial', path: 'Arial', isSystem: true },
          { name: 'Helve<PERSON>', path: 'Helvetica', isSystem: true },
          { name: 'Times New Roman', path: 'Times New Roman', isSystem: true },
          { name: 'Courier New', path: 'Courier New', isSystem: true },
          { name: 'Verd<PERSON>', path: 'Verdana', isSystem: true }
        ]
        this.fonts.value = defaultFonts
        this.isLoaded.value = true
        return defaultFonts
      }
    } catch (error) {
      console.error('Error loading fonts:', error)
      // Fallback fonts
      const fallbackFonts = [
        { name: '<PERSON><PERSON>', path: 'Arial', isSystem: true },
        { name: 'Helvetica', path: 'Helvetica', isSystem: true }
      ]
      this.fonts.value = fallbackFonts
      this.isLoaded.value = true
      return fallbackFonts
    }
  }

  // Lấy danh sách fonts
  getFonts() {
    return this.fonts.value
  }

  // Kiểm tra xem fonts đã được load chưa
  isReady() {
    return this.isLoaded.value
  }

  // Lấy font path cho FFmpeg (tương đối hoặc tuyệt đối)
  getFontPath(fontName) {
    const font = this.fonts.value.find(f => f.name === fontName)
    if (!font) return 'Arial' // Default fallback
    
    if (font.isSystem) {
      return font.name // System fonts chỉ cần tên
    } else {
      // Custom fonts cần đường dẫn tương đối
      return `static/fonts/${font.path}`
    }
  }

  // Lấy font name cho ASS subtitle
  getAssFontName(fontName) {
    const font = this.fonts.value.find(f => f.name === fontName)
    if (!font) return 'Arial'
    
    // ASS subtitle chỉ cần tên font
    return font.name
  }
}

// Singleton instance
const fontService = new FontService()

export default fontService
