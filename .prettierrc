{"singleQuote": true, "arrowParens": "always", "bracketSameLine": false, "bracketSpacing": true, "embeddedLanguageFormatting": "auto", "htmlWhitespaceSensitivity": "css", "insertPragma": false, "jsxSingleQuote": false, "printWidth": 120, "proseWrap": "preserve", "quoteProps": "as-needed", "requirePragma": false, "semi": true, "tabWidth": 2, "trailingComma": "all", "useTabs": false, "vueIndentScriptAndStyle": false}