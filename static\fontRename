#!/bin/bash

FONT_DIR="static/fonts"

find "$FONT_DIR" -type f \( -iname "*.ttf" -o -iname "*.otf" \) | while read -r font; do
  # Trích family name
  family=$(fc-scan "$font" | grep "family:" | head -n1 | sed 's/.*"\(.*\)".*/\1/')
  # Trích style (subfamily) — ví dụ: Bold, Medium, Regular
  style=$(fc-scan "$font" | grep "style:" | head -n1 | sed 's/.*"\(.*\)".*/\1/')

  if [[ -z "$family" ]]; then
    echo "⚠️  Không lấy được family từ: $font"
    continue
  fi

  # Làm sạch tên
  base_name=$(echo "$family" | tr ' ' '_')
  style_clean=$(echo "$style" | tr ' ' '_')

  # Ghép tên mới nếu style không phải Regular (hoặc có tên trùng)
  if [[ "$style_clean" != "Regular" ]]; then
    full_name="${base_name}_${style_clean}"
  else
    full_name="${base_name}"
  fi

  # Lấy phần mở rộng
  ext="${font##*.}"
  new_path="$FONT_DIR/$full_name.$ext"

  if [[ "$font" != "$new_path" ]]; then
    if [[ -e "$new_path" ]]; then
      echo "⚠️  Bỏ qua (tên trùng): $new_path"
    else
      echo "📝 Đổi tên: $(basename "$font") → $(basename "$new_path")"
      mv "$font" "$new_path"
    fi
  else
    echo "✅ Đã đúng tên: $(basename "$font")"
  fi
done
