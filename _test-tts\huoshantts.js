const fs = require("fs");
const axios = require("axios");
const path = require("path");


    function getvoicelist_EN(self){
        return [
            'jp_male_satoshi',
            'jp_female_mai',
            'zh_male_rap',
            'zh_female_sichuan','zh_male_xiaoming','zh_male_zhubo','zh_female_zhubo','zh_female_qingxin','zh_female_story','en_male_adam','en_male_bob','en_female_sarah']
        }
    function getvoicelist_CN(self){
        return ['jp_male_satoshi','jp_female_mai','zh_male_rap','zh_female_sichuan','zh_male_xiaoming','zh_male_zhubo','zh_female_zhubo','zh_female_qingxin','zh_female_story','en_male_adam','en_male_bob','en_female_sarah']
        }

    function getvoicelist(self){
        return ['jp_male_satoshi','jp_female_mai','zh_male_rap','zh_female_sichuan','zh_male_xiaoming','zh_male_zhubo','zh_female_zhubo','zh_female_qingxin','zh_female_story','en_male_adam','en_male_bob','en_female_sarah']
        }

async function speak(content, voice = "zh_male_zhubo") {
    try {
        const headers = {
            "authority": "translate.volcengine.com",
            "accept": "application/json, text/plain, */*",
            "accept-language": "zh-CN,zh;q=0.9",
            "origin": "chrome-extension://klgfhbdadaspgppeadghjjemk",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "none",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64)",
        };

        const data = {
            text: content,
            speaker: voice
        };

        const res = await axios.post(
            "https://translate.volcengine.com/crx/tts/v1/",
            data,
            { headers }
        );

        const audioData = res.data?.audio?.data;
        if (!audioData) throw new Error("No audio data returned.");

        const audioBuffer = Buffer.from(audioData, "base64");

        const dir = path.join(__dirname, "cache", "tts");
        if (!fs.existsSync(dir)) fs.mkdirSync(dir, { recursive: true });

        const filename = `${Date.now()}.mp3`;
        const filepath = path.join(dir, filename);

        fs.writeFileSync(filepath, audioBuffer);
        console.log("Audio saved to:", filepath);
        return filepath;

    } catch (err) {
        console.error("TTS error:", err.message);
        return null;
    }
}

// Example usage
speak("你好，这是测试语音", "zh_female_zhubo");
