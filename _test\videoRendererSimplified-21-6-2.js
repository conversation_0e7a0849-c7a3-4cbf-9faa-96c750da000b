const { exec } = require('child_process');
const path = require('path');
const fs = require('fs');
const util = require('util');
const { getAudioDuration, getEncoder,getVideoInfo } = require('../ffmpegHandler');
const { cssToASSColor, generateASSSubtitle, clampAtTempo } = require('./assBuild');
const {addAudioToVideo, upVolume } = require('./addAudioToVideo');
// const execPromise = util.promisify(exec);
const execPromise = (cmd) =>
  new Promise((resolve, reject) => {
    exec(cmd, (err, stdout, stderr) => {
      if (err) {
        console.error('❌ FFmpeg error:', err);
        console.error(stderr);
        return reject(err);
      }
      resolve();
    });
  });

// Custom execPromise with larger buffer for long videos
const execPromiseWithLargeBuffer = (command) => {
  return new Promise((resolve, reject) => {
    exec(command, {
      maxBuffer: 50 * 1024 * 1024, // 50MB buffer instead of default 1MB
      timeout: 30 * 60 * 1000 // 30 minutes timeout
    }, (error, stdout, stderr) => {
      if (error) {
        reject(error);
      } else {
        resolve({ stdout, stderr });
      }
    });
  });
};

const BATCH_SIZE = 5; // Giảm batch size để test và tránh memory overflow với nhiều audio inputs

// Helper function để xử lý batch với retry mechanism
async function processBatchWithRetry(event, batch, batchIndex, videoPath, outputDir, options, totalBatches, totalVideoDuration, totalProcessedTime) {
  const maxRetries = 2;
  const retrySizes = [Math.ceil(batch.length / 2), Math.ceil(batch.length / 3)]; // Chia nhỏ batch khi retry

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      if (attempt === 0) {
        // Lần đầu xử lý batch bình thường
        return await processSingleBatch(event, batch, batchIndex, videoPath, outputDir, options, totalBatches, totalVideoDuration, totalProcessedTime);
      } else {
        // Retry với batch size nhỏ hơn
        const smallerBatchSize = retrySizes[attempt - 1];
        console.log(`🔄 Retry ${attempt}: Splitting batch ${batchIndex + 1} into smaller chunks of ${smallerBatchSize} items`);

        const subBatches = [];
        for (let i = 0; i < batch.length; i += smallerBatchSize) {
          subBatches.push(batch.slice(i, i + smallerBatchSize));
        }

        const subBatchPaths = [];
        for (let subIndex = 0; subIndex < subBatches.length; subIndex++) {
          const subBatch = subBatches[subIndex];
          const subBatchIndex = `${batchIndex}_${subIndex}`;
          const subBatchPath = await processSingleBatch(event, subBatch, subBatchIndex, videoPath, outputDir, options, totalBatches, totalVideoDuration, totalProcessedTime);
          subBatchPaths.push(subBatchPath);
        }

        // Concatenate sub-batches
        if (subBatchPaths.length > 1) {
          const concatListPath = path.join(outputDir, `concat_batch_${batchIndex}.txt`);
          const concatListContent = subBatchPaths.map((p) => `file '${p}'`).join('\n');
          fs.writeFileSync(concatListPath, concatListContent);

          const finalBatchOutput = path.join(outputDir, `batch_${batchIndex}.mp4`);
          const encoder = await getEncoder();
          const concatCmd = `ffmpeg -f concat -safe 0 -i "${concatListPath}" -c:v ${encoder} -preset fast -crf 23 -c:a aac -b:a 192k -y "${finalBatchOutput}"`;

          await execPromiseWithLargeBuffer(concatCmd);

          // Cleanup sub-batches
          subBatchPaths.forEach(p => {
            if (fs.existsSync(p)) fs.unlinkSync(p);
          });
          if (fs.existsSync(concatListPath)) fs.unlinkSync(concatListPath);

          return finalBatchOutput;
        } else {
          return subBatchPaths[0];
        }
      }
    } catch (error) {
      const isMemoryError = error.message.includes('Cannot allocate memory') ||
                           error.message.includes('out of memory') ||
                           error.message.includes('memory allocation');

      if (isMemoryError && attempt < maxRetries) {
        console.log(`⚠️ Memory error in batch ${batchIndex + 1}, attempt ${attempt + 1}. Retrying with smaller batch size...`);
        event?.sender?.send('video-task', {
          data: `⚠️ Memory error in batch ${batchIndex + 1}, retrying with smaller chunks...`,
          code: 0,
        });
        continue;
      } else {
        console.error(`❌ Batch ${batchIndex + 1} failed after ${attempt + 1} attempts:`, error.message);
        throw error;
      }
    }
  }
}

const resolutionMap = {
  '144p': 144,
  '240p': 240,
  '360p': 360,
  '480p': 480,
  '720p': 720,
  '1080p': 1080,
  '1440p': 1440,
  '2160p': 2160,
  '4k': 2160,
};

function getResolution(options) {
  const quality = options?.output?.quality || 'custom';
  const resolution = options?.videoInfo
  const defaultResolution = {width: resolution.width, height: resolution.height}
  if (quality === 'custom') return defaultResolution
  if (!quality) return defaultResolution

  const [qualityRaw, aspectRaw = '16:9'] = quality.toLowerCase().split('/');
  const qualityKey = /^\d+$/.test(qualityRaw) ? qualityRaw + 'p' : qualityRaw;

  const size = resolutionMap[qualityKey];
  if (!size) return null;

  const [wRatio, hRatio] = aspectRaw.split(':').map(Number);
  if (!wRatio || !hRatio) return null;

  let width, height;

  if (wRatio > hRatio) {
    // Landscape → height = size
    height = size;
    width = Math.round((height * wRatio) / hRatio);
  } else {
    // Portrait → width = size
    width = size;
    height = Math.round((width * hRatio) / wRatio);
  }

  return { width, height };
}


function buildBatchAndSrt(event, srtArray, BATCH_SIZE) {
  console.log(`🔍 buildBatchAndSrt called with:`);
  console.log(`- srtArray.length: ${srtArray.length}`);
  console.log(`- BATCH_SIZE parameter: ${BATCH_SIZE}`);

  let adjustedCurrentTime = 0; // Thời gian tích luỹ sau khi điều chỉnh tốc độ

  for (let i = 0; i < srtArray.length; i++) {
    const currentSrt = srtArray[i];
    const nextSrt = srtArray[i + 1];

    // Thời lượng video segment gốc
    const originalVideoSegmentDuration = nextSrt
      ? nextSrt.startTime - currentSrt.startTime
      : currentSrt.endTime - currentSrt.startTime;

    // Ensure originalVideoSegmentDuration is valid and positive
    if (isNaN(originalVideoSegmentDuration) || originalVideoSegmentDuration <= 0) {
      console.warn(`⚠️ Invalid originalVideoSegmentDuration for segment ${i}: ${originalVideoSegmentDuration}, setting to 0.1s`);
      currentSrt.originalVideoSegmentDuration = 0.1;
    } else {
      currentSrt.originalVideoSegmentDuration = originalVideoSegmentDuration;
    }

    // Tính speed ratio using the corrected originalVideoSegmentDuration
    if (currentSrt.duration > currentSrt.originalVideoSegmentDuration) {
      currentSrt.speedRatio = currentSrt.originalVideoSegmentDuration / currentSrt.duration; // < 1 = chậm lại
    } else {
      currentSrt.speedRatio = 1; // không thay đổi tốc độ
    }

    // Thời lượng thực tế sau khi điều chỉnh tốc độ
    const adjustedVideoDuration = currentSrt.originalVideoSegmentDuration / currentSrt.speedRatio;

    // Thời lượng cuối cùng của đoạn này (lấy max giữa video đã điều chỉnh và audio)
    // Handle edge cases where durations might be 0 or invalid
    const validAdjustedVideoDuration = isNaN(adjustedVideoDuration) || adjustedVideoDuration <= 0 ? currentSrt.duration : adjustedVideoDuration;
    const validAudioDuration = isNaN(currentSrt.duration) || currentSrt.duration <= 0 ? 0.1 : currentSrt.duration; // Minimum 0.1s

    currentSrt.finalDuration = Math.max(validAdjustedVideoDuration, validAudioDuration);

    // Ensure finalDuration is never NaN or 0
    if (isNaN(currentSrt.finalDuration) || currentSrt.finalDuration <= 0) {
      console.warn(`⚠️ Invalid finalDuration for segment ${i}, setting to 0.1s`);
      currentSrt.finalDuration = 0.1;
    }

    // Cập nhật thời gian start/end mới cho SRT
    currentSrt.adjustedStartTime = adjustedCurrentTime;
    currentSrt.adjustedEndTime = adjustedCurrentTime + currentSrt.finalDuration;

    // Cập nhật thời gian tích luỹ
    adjustedCurrentTime += currentSrt.finalDuration;

    // const logs = `📊 Segment ${i}: original=${originalVideoSegmentDuration.toFixed(
    //   2,
    // )}s, audio=${currentSrt.duration.toFixed(2)}s, speed=${currentSrt.speedRatio.toFixed(
    //   3,
    // )}, adjusted=${adjustedVideoDuration.toFixed(2)}s, final=${currentSrt.finalDuration.toFixed(
    //   2,
    // )}s, newTime=${currentSrt.adjustedStartTime.toFixed(2)}-${currentSrt.adjustedEndTime.toFixed(2)}s`;
    // console.log(logs);
    // event?.sender?.send('video-task', {
    //   data: logs,
    //   code: 0,
    // });
  }

  const batches = [];
  for (let i = 0; i < srtArray.length; i += BATCH_SIZE) {
    batches.push(srtArray.slice(i, i + BATCH_SIZE));
  }

  console.log(`📦 Batch creation debug:`);
  console.log(`- srtArray.length: ${srtArray.length}`);
  console.log(`- BATCH_SIZE: ${BATCH_SIZE}`);
  console.log(`- Number of batches created: ${batches.length}`);
  // console.log(`- Batch sizes: ${batches.map(b => b.length).join(', ')}`);

  return batches;
}

// Tách phần xử lý batch thành hàm riêng để có thể retry
async function processSingleBatch(event, batch, batchIndex, videoPath, outputDir, options, totalBatches, totalVideoDuration, totalProcessedTime) {
  const type = 'video-task';
  const holdOriginalAudio = options?.audio?.holdOriginalAudio || options?.audio?.holdMusicOnly || false;
  const addVoiceAudio = true;

  const inputs = [`-i "${videoPath}"`];
  let segmentFilters = [];
  let currentTime = 0;
  let videoSegments = [];
  let originalAudioSegments = [];
  let voiceAudioSegments = [];

  // Add audio files as inputs
  if (addVoiceAudio) {
    batch.forEach((srt) => {
      if (srt.audioUrl) {
        const audioPath = srt.audioUrl.replace('file://', '');
        if (fs.existsSync(audioPath)) {
          inputs.push(`-i "${audioPath}"`);
        }
      }
    });
  }

  batch.forEach((srt, index) => {
    const vStart = srt.startTime;
    const vDur = srt.originalVideoSegmentDuration;
    const speedRatio = srt.speedRatio || 1;
    const finalDur = srt.finalDuration;

    // === VIDEO PROCESSING ===
    let videoFilter = `[0:v]trim=start=${vStart.toFixed(6)}:duration=${vDur.toFixed(6)},setpts=PTS-STARTPTS`;
    if (speedRatio < 1) {
      videoFilter += `,setpts=PTS/${speedRatio.toFixed(6)}`;
    }
    if (options.output?.quality) {
      const resolution = getResolution(options);
      options.output.resolution = resolution;
      videoFilter += `,scale=${resolution.width || 1920}:${resolution.height || 1080}`;
    }
    videoFilter += `[v${index}]`;
    segmentFilters.push(videoFilter);
    videoSegments.push(`[v${index}]`);

    // === ENHANCED ORIGINAL AUDIO PROCESSING ===
    if (holdOriginalAudio) {
      let originalAudioFilter = `[0:a]atrim=start=${vStart.toFixed(6)}:duration=${vDur.toFixed(6)},asetpts=PTS-STARTPTS`;

      // Professional tempo adjustment with quality preservation
      if (speedRatio < 1) {
        let currentRatio = speedRatio;
        let tempoFilters = [];

        // Break down large tempo changes into smaller steps for better quality
        while (currentRatio < 0.5) {
          tempoFilters.push('atempo=0.5');
          currentRatio /= 0.5;
        }
        while (currentRatio > 2.0) {
          tempoFilters.push('atempo=2.0');
          currentRatio /= 2.0;
        }
        if (Math.abs(currentRatio - 1.0) > 0.001) {
          tempoFilters.push(`atempo=${currentRatio.toFixed(6)}`);
        }

        if (tempoFilters.length > 0) {
          originalAudioFilter += `,${tempoFilters.join(',')}`;
        }
      }

      // Professional audio cleanup and enhancement
      originalAudioFilter += `,aresample=48000:resampler=soxr:precision=33:cheby=1:dither_method=triangular`; // High-quality resampling
      originalAudioFilter += `,agate=threshold=-60dB:ratio=10:attack=1:release=8`; // Noise gate
      originalAudioFilter += `,highpass=f=60:poles=2,lowpass=f=12000:poles=2`; // Clean frequency range
      originalAudioFilter += `,acompressor=threshold=-24dB:ratio=2:attack=5:release=50:makeup=1dB`; // Gentle compression
      originalAudioFilter += `,adelay=${currentTime * 1000}|${currentTime * 1000}[oa${index}]`;

      segmentFilters.push(originalAudioFilter);
      originalAudioSegments.push(`[oa${index}]`);
    }

    // === ENHANCED VOICE AUDIO PROCESSING FOR BETTER QUALITY ===
    if (addVoiceAudio) {
      const voiceAudioIndex = index + 1; // +1 because input[0] is video
      const volume = srt.mixVolume ?? 1;
      const boostDb = options.audioBoost || 2; // Reduced to 2dB for cleaner sound
      const volumeMultiplier = Math.pow(10, boostDb / 20) * volume;

      // Simplified voice processing for speed
      let voiceAudioFilter = `[${voiceAudioIndex}:a]`;

      // Basic processing only
      voiceAudioFilter += `aresample=48000,`;
      voiceAudioFilter += `volume=${volumeMultiplier.toFixed(6)}`;

      // Duration adjustment with professional crossfading
      const durationDiff = Math.abs(srt.duration - finalDur);
      if (srt.duration > finalDur && durationDiff > 0.001) {
        // Professional fade out before trimming
        const fadeStart = Math.max(0, finalDur - 0.05); // 50ms fade
        if (fadeStart > 0) {
          voiceAudioFilter += `,afade=t=out:st=${fadeStart.toFixed(6)}:d=0.05:curve=qsin`;
        }
        voiceAudioFilter += `,atrim=duration=${finalDur.toFixed(6)}`;
      } else if (srt.duration < finalDur && durationDiff > 0.001) {
        const padDuration = finalDur - srt.duration;
        voiceAudioFilter += `,apad=pad_dur=${padDuration.toFixed(6)}`;
      }

      // Professional crossfade between segments for seamless transitions
      if (index > 0) {
        voiceAudioFilter += `,afade=t=in:st=0:d=0.02:curve=qsin`; // 20ms smooth fade in
      }
      if (index < batch.length - 1) {
        const fadeOutStart = Math.max(0, finalDur - 0.02);
        voiceAudioFilter += `,afade=t=out:st=${fadeOutStart.toFixed(6)}:d=0.02:curve=qsin`; // 20ms smooth fade out
      }

      voiceAudioFilter += `,adelay=${currentTime * 1000}|${currentTime * 1000}[va${index}]`;
      segmentFilters.push(voiceAudioFilter);
      voiceAudioSegments.push(`[va${index}]`);
    }

    currentTime += finalDur;
  });

  // ====== FIX: Only add remaining video in the LAST batch ======
  const isLastBatch = batchIndex === totalBatches - 1;

  if (isLastBatch && totalProcessedTime < totalVideoDuration) {
    const remainingStart = totalProcessedTime;
    const remainingDuration = totalVideoDuration - totalProcessedTime;

    console.log(`📺 Adding remaining video: ${remainingStart.toFixed(2)}s to ${totalVideoDuration.toFixed(2)}s (${remainingDuration.toFixed(2)}s)`);

    // Add remaining video segment
    const remainingIndex = batch.length;
    let remainingVideoFilter = `[0:v]trim=start=${remainingStart.toFixed(6)}:duration=${remainingDuration.toFixed(6)},setpts=PTS-STARTPTS`;

    if (options.output?.quality) {
      const resolution = getResolution(options);
      remainingVideoFilter += `,scale=${resolution.width || 1920}:${resolution.height || 1080}`;
    }
    remainingVideoFilter += `[v${remainingIndex}]`;
    segmentFilters.push(remainingVideoFilter);
    videoSegments.push(`[v${remainingIndex}]`);

    // Add professional audio processing for remaining video
    if (holdOriginalAudio) {
      let remainingAudioFilter = `[0:a]atrim=start=${remainingStart.toFixed(6)}:duration=${remainingDuration.toFixed(6)},asetpts=PTS-STARTPTS`;
      remainingAudioFilter += `,aresample=48000:resampler=soxr:precision=33:cheby=1:dither_method=triangular`;
      remainingAudioFilter += `,agate=threshold=-60dB:ratio=10:attack=1:release=8`;
      remainingAudioFilter += `,highpass=f=60:poles=2,lowpass=f=12000:poles=2`;
      remainingAudioFilter += `,acompressor=threshold=-24dB:ratio=2:attack=5:release=50:makeup=1dB`;
      remainingAudioFilter += `,adelay=${currentTime * 1000}|${currentTime * 1000}[oa${remainingIndex}]`;
      segmentFilters.push(remainingAudioFilter);
      originalAudioSegments.push(`[oa${remainingIndex}]`);
    }

    if (addVoiceAudio) {
      // Generate professional-quality silence for remaining duration
      let silentAudioFilter = `aevalsrc=0:duration=${remainingDuration.toFixed(6)}:sample_rate=48000:channel_layout=stereo`;
      silentAudioFilter += `,adelay=${currentTime * 1000}|${currentTime * 1000}[va${remainingIndex}]`;
      segmentFilters.push(silentAudioFilter);
      voiceAudioSegments.push(`[va${remainingIndex}]`);
    }

    currentTime += remainingDuration;
  }

  // === CONCAT VIDEO ===
  const totalSegments = videoSegments.length;
  const videoConcat = `${videoSegments.join('')}concat=n=${totalSegments}:v=1:a=0[vout]`;
  segmentFilters.push(videoConcat);

  // === PROFESSIONAL AUDIO MIXING FOR BROADCAST QUALITY ===
  let audioMix = '';
  if (holdOriginalAudio && addVoiceAudio) {
    const totalAudioSegments = Math.min(originalAudioSegments.length, voiceAudioSegments.length);
    const allAudioTags = [];
    const weights = [];

    for (let i = 0; i < totalAudioSegments; i++) {
      allAudioTags.push(`[oa${i}][va${i}]`);
      weights.push('0.3 1.0'); // Even lower original audio, prioritize voice clarity
    }

    // Professional mixing with advanced ducking and dynamics
    audioMix = `${allAudioTags.join('')}amix=inputs=${totalAudioSegments * 2}:duration=longest:weights=${weights.join(' ')},`;

    // Advanced multiband dynamics processing
    audioMix += `acompressor=threshold=-16dB:ratio=3:attack=2:release=20:makeup=1dB:knee=2,`;

    // Professional broadcast limiter with lookahead
    audioMix += `alimiter=limit=0.9:attack=0.5:release=3:asc=1,`;

    // Broadcast-standard EQ curve
    audioMix += `equalizer=f=100:width_type=h:width=1:g=0.5,`; // Slight low boost for warmth
    audioMix += `equalizer=f=3000:width_type=h:width=1:g=1,`; // Presence boost for clarity
    audioMix += `equalizer=f=8000:width_type=h:width=2:g=-0.5,`; // Gentle high cut to reduce harshness

    // Final high-quality resampling and cleanup
    audioMix += `aresample=48000:resampler=soxr:precision=33:cheby=1,`;
    audioMix += `highpass=f=60:poles=2,lowpass=f=15000:poles=2`; // Professional frequency range
    audioMix += `[aout]`;

  } else if (holdOriginalAudio) {
    const originalAudioTags = originalAudioSegments.join('');
    audioMix = `${originalAudioTags}amix=inputs=${originalAudioSegments.length}:duration=longest,`;

    // Professional processing for original audio only
    audioMix += `acompressor=threshold=-18dB:ratio=2:attack=3:release=30:makeup=1dB,`;
    audioMix += `aresample=48000:resampler=soxr:precision=33:cheby=1,`;
    audioMix += `alimiter=limit=0.95:attack=0.5:release=3[aout]`;

  } else if (addVoiceAudio) {
    const voiceAudioTags = voiceAudioSegments.join('');
    audioMix = `${voiceAudioTags}amix=inputs=${voiceAudioSegments.length}:duration=longest,`;

    // Professional processing for voice-only audio
    audioMix += `acompressor=threshold=-16dB:ratio=2.5:attack=2:release=25:makeup=1dB,`;
    audioMix += `alimiter=limit=0.95:attack=0.5:release=3,`;
    audioMix += `aresample=48000:resampler=soxr:precision=33:cheby=1[aout]`;
  }

  if (audioMix) segmentFilters.push(audioMix);

  // Rest of the code remains the same...
  const filterComplex = segmentFilters.join('; ');
  const batchOutput = path.join(outputDir, `batch_${batchIndex}.mp4`);

  const filterFile = path.join(outputDir, `filter_${batchIndex}.txt`);
  fs.writeFileSync(filterFile, filterComplex);

  // === PROFESSIONAL FFMPEG ENCODING FOR BROADCAST QUALITY ===
  let ffmpegCmd = `ffmpeg -v verbose ${inputs.join(' ')} -filter_complex_script "${filterFile}" -map "[vout]"`;
  if (audioMix) {
    // Professional audio encoding with highest quality settings
    ffmpegCmd += ` -map "[aout]" -c:a aac -profile:a aac_low -ar 48000 -ac 2 -b:a 256k -aac_coder twoloop -movflags +faststart`;
  } else {
    ffmpegCmd += ` -an`;
  }
  // Fast video encoding settings for speed
  const encoder = await getEncoder();
  if (encoder.includes('nvenc')) {
    ffmpegCmd += ` -c:v ${encoder} -preset fast -cq 25 -pix_fmt yuv420p -movflags +faststart -y "${batchOutput}"`;
  } else {
    ffmpegCmd += ` -c:v ${encoder} -preset fast -crf 25 -pix_fmt yuv420p -movflags +faststart -y "${batchOutput}"`;
  }

  console.log(`🧩 Running FFmpeg for batch ${batchIndex + 1}/${totalBatches}`);
  console.log(`📊 Batch ${batchIndex + 1} expected duration: ${currentTime.toFixed(2)}s`);

  event?.sender?.send('video-task', {
    data: `🧩 Running FFmpeg for batch ${batchIndex + 1}/${totalBatches} (${currentTime.toFixed(2)}s)`,
    code: 0,
  });

  try {
    console.log('ffmpegCmd',ffmpegCmd);
    await execPromiseWithLargeBuffer(ffmpegCmd);
    fs.unlinkSync(filterFile);
    return batchOutput;
  } catch (error) {
    console.error(`❌ Batch ${batchIndex + 1} processing failed:`, error.message);
    throw error;
  }
}

const processVideoSimplified = async (event, videoPath, srtArray, outputDir, finalOutput, options = {}) => {
  const workDirTemp = path.join(outputDir, '_temp');
  if (!fs.existsSync(outputDir)) fs.mkdirSync(outputDir, { recursive: true });
  const type = 'video-task';
  const totalVideoDuration = options?.videoInfo?.duration || 0;

  // console.log(`🔍 INITIAL DEBUG:`);
  // console.log(`- Original srtArray.length: ${srtArray.length}`);
  // console.log(`- First 3 items:`, srtArray.slice(0, 3).map(s => ({ index: s.index, text: s.text?.substring(0, 50), audioUrl: !!s.audioUrl })));

  // 1. Calculate audio duration for each segment
  let processedCount = 0;
  for (const srt of srtArray) {
    if (srt.audioUrl && fs.existsSync(srt.audioUrl.replace('file://', ''))) {
      srt.duration = await getAudioDuration(srt.audioUrl.replace('file://', ''));
      processedCount++;
      event?.sender?.send(type, {
        data: `🕐 Audio duration for segment ${srt.index}: ${srt.duration} seconds`,
        code: 0,
      });
    }
  }

  const batches = buildBatchAndSrt(event, srtArray, BATCH_SIZE);
  const batchVideoPaths = [];



  // ====== FIX: Calculate total processed time across all batches ======
  let totalProcessedTime = 0;
  for (const batch of batches) {
    const lastSrt = batch[batch.length - 1];
    const batchEndTime = lastSrt.startTime + lastSrt.originalVideoSegmentDuration;
    if (batchEndTime > totalProcessedTime) {
      totalProcessedTime = batchEndTime;
    }
  }

  for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
    const batch = batches[batchIndex];
    try {
      const batchOutput = await processBatchWithRetry(event, batch, batchIndex, videoPath, outputDir, options, batches.length, totalVideoDuration, totalProcessedTime);
      batchVideoPaths.push(batchOutput);
    } catch (error) {
      console.error(`❌ Failed to process batch ${batchIndex + 1}:`, error.message);
      throw error;
    }
  }

  // 4. Concatenate batches
  const concatListPath = path.join(outputDir, 'concat_list.txt');
  const concatListContent = batchVideoPaths.map((p) => `file '${p}'`).join('\n');
  fs.writeFileSync(concatListPath, concatListContent);
  const concatOutput = path.join(outputDir, 'concatenated.mp4');
  const encoder = await getEncoder();

  // Professional concatenation with audio quality preservation
  let concatCmd = `ffmpeg -f concat -safe 0 -i "${concatListPath}" `;
  if (encoder.includes('qsv')) {
    concatCmd += `-c:v ${encoder} -preset fast -global_quality 21 -c:a aac -profile:a aac_low -ar 48000 -b:a 256k -aac_coder twoloop -movflags +faststart -y "${concatOutput}"`;
  } else if (encoder.includes('nvenc')) {
    concatCmd += `-c:v ${encoder} -preset fast -cq 21 -c:a aac -profile:a aac_low -ar 48000 -b:a 256k -aac_coder twoloop -movflags +faststart -y "${concatOutput}"`;
  } else {
    concatCmd += `-c:v ${encoder} -preset fast -crf 21 -c:a aac -profile:a aac_low -ar 48000 -b:a 256k -aac_coder twoloop -movflags +faststart -y "${concatOutput}"`;
  }

  console.log('📦 Running final concat...');
  event?.sender?.send(type, {
    data: '📦 Running final concat...',
    code: 0,
  });
  await execPromise(concatCmd);

  // backgroundMusic
  const backgroundMusic = options.audio?.backgroundMusic;
  if (backgroundMusic?.enabled && backgroundMusic?.file) {
    const musicPath = options.audio?.backgroundMusic?.file;
    const musicVolume = options.audio?.backgroundMusic?.volume;
    const outputWithMusic = path.join(outputDir, 'final_with_music.mp4');
    await addAudioToVideo(event, concatOutput, musicPath, outputWithMusic, {
      audioBitrate: '192k',
      volume: musicVolume,
    });
    fs.renameSync(outputWithMusic, concatOutput);
  } else {
    // up volume 1.5x
    const outputWithMusic = path.join(outputDir, 'final_with_music.mp4');
    await upVolume(event, concatOutput, outputWithMusic, 10);
    fs.renameSync(outputWithMusic, concatOutput);
  }

  // 5. Apply subtitles if needed
  const adjustedSrtPath = 'subtitles.ass'
  if (options.textSubtitle?.enabled) {
    const textSubtitle = options.textSubtitle;
    const subtitleOptions = {
      fontSize: textSubtitle.fontSize || 48,
      // textColor: cssToASSColor(textSubtitle.color || '#000000'),
      textColor: textSubtitle.assColors?.text || '&H000000',
      backgroundColor: textSubtitle.assColors?.background || '&H000000',
      outlineColor: textSubtitle.assColors?.border || '&H000000',
      // backgroundColor: cssToASSColor(textSubtitle.backgroundColor || '#fff700', '00'),
      borderStyle: 4,
      bold: textSubtitle.bold || true,
      addPadding: true,
      alignment: 2,
      marginVertical: 50,
      resolution: options.output.resolution,
      assOptions: textSubtitle.assOptions
    };
    console.log('subtitleOptions', subtitleOptions);
    console.log('textSubtitle', textSubtitle);
    const assContent = generateASSSubtitle(srtArray, subtitleOptions);
    fs.writeFileSync(adjustedSrtPath, assContent, 'utf8');
    const encoder = await getEncoder();
    const command = `ffmpeg -i "${concatOutput}" -vf "ass=${adjustedSrtPath}" -c:a copy -c:v ${encoder} -preset fast -crf 23 -y "${finalOutput}"`;
    await execPromise(command);
    console.log(`✅ Subtitle applied: ${finalOutput}`);
    event?.sender?.send(type, {
      data: `✅ Subtitle applied: ${finalOutput}`,
      code: 0,
    });
  } else {
    fs.renameSync(concatOutput, finalOutput);
  }

  // 6. Cleanup
  batchVideoPaths.forEach((p) => {
    if (fs.existsSync(p)) fs.unlinkSync(p);
  });
  if (fs.existsSync(concatListPath)) fs.unlinkSync(concatListPath);
  if (fs.existsSync(concatOutput)) fs.unlinkSync(concatOutput);
  // if (fs.existsSync(adjustedSrtPath)) fs.unlinkSync(adjustedSrtPath);

  console.log(`✅ Final video generated: ${finalOutput}`);
  event?.sender?.send(type, {
    data: `✅ Final video generated: ${finalOutput}`,
    code: 0,
    success: true,
  });

  return {
    videoPath: finalOutput,
    adjustedSrtArray: srtArray,
  };
};

module.exports = {
  processVideoSimplified,
  processBatchWithRetry,
  buildBatchAndSrt,
  getResolution,
  processSingleBatch
};