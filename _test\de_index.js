var B = Object.defineProperty;
var K = (_0x4b6277, _0x1025e9, _0x29e7bc) => _0x1025e9 in _0x4b6277 ? B(_0x4b6277, _0x1025e9, {
  'enumerable': true,
  'configurable': true,
  'writable': true,
  'value': _0x29e7bc
}) : _0x4b6277[_0x1025e9] = _0x29e7bc;
var h = (_0x355b56, _0x29209b, _0x32615c) => K(_0x355b56, typeof _0x29209b != "symbol" ? _0x29209b + '' : _0x29209b, _0x32615c);
import { ipcMain as _0x106b59, dialog as _0x3137a2, app as _0x2cfc6e, session as _0x528d91, clipboard as _0x56630d, Browser<PERSON><PERSON>ow as _0x476f93, shell as _0x5dc2f1, protocol as _0x3b8f90, screen as _0x438436 } from 'electron';
import { createRequire as _0x596093 } from 'node:module';
import { fileURLToPath as _0x2cbb6b } from 'node:url';
import _0x179b4f from 'node:path';
import _0x5dab6e from 'node:os';
import _0x294b7b, { basename as _0x4b14b1, join as _0x2198a1 } from 'path';
import _0x1a4335 from 'fluent-ffmpeg';
import { exec as _0xb68efa, spawn as _0x2f94a9 } from 'child_process';
import _0x3ea0f7 from 'request';
import _0x54032b from 'fs';
import _0x825b2, { writeFileSync as _0x3605a1, mkdirSync as _0x2cbf9a, existsSync as _0x223dbb, createReadStream as _0x2593a2, readFileSync as _0x563963, unlinkSync as _0x35bcf2, lstatSync as _0x28d9e1, rmdirSync as _0x488075, readdirSync as _0x20a24e } from 'node:fs';
import _0x213aa7 from 'crypto';
import _0x26af74 from 'node-machine-id';
import _0x239a19 from 'dns';
import _0x4f52ed from 'winreg';
import _0x5dcdf6 from 'asar';
function oe(_0x595695) {
  _0x106b59.handle("showOpenDialogSync", async (_0x3120f9, _0x5217bf) => _0x3137a2.showOpenDialogSync(_0x595695, _0x5217bf));
  _0x106b59.handle("showMessageBoxSync", async (_0x834b84, _0x514196) => _0x3137a2.showMessageBoxSync(_0x595695, _0x514196));
  _0x106b59.handle("showSaveDialogSync", async (_0x4cee21, _0x20c3d4) => _0x3137a2.showSaveDialogSync(_0x595695, _0x20c3d4));
}
class ie {
  constructor() {
    K(this, "_ffmpeg", undefined);
    K(this, "ffmpegPath", undefined);
    K(this, typeof _0x22cb(0xb5) != "symbol" ? _0x22cb(0xb5) + '' : "ffprobePath", undefined);
    this._ffmpeg = _0x1a4335;
    if (_0x2cfc6e.isPackaged) {
      this.ffmpegPath = _0x294b7b.join(process.resourcesPath, "ffmpeg", 'ffmpeg');
      this.ffprobePath = _0x294b7b.join(process.resourcesPath, "ffmpeg", "ffprobe");
    } else {
      const _0x25cfa9 = _0x2cfc6e.getAppPath();
      this.ffmpegPath = _0x294b7b.join(_0x25cfa9, "ffmpeg", "ffmpeg");
      this.ffprobePath = _0x294b7b.join(_0x25cfa9, "ffmpeg", 'ffprobe');
    }
    this._ffmpeg.setFfmpegPath(this.ffmpegPath);
    this._ffmpeg.setFfprobePath(this.ffprobePath);
  }
  get ["ffmpeg"]() {
    return this._ffmpeg;
  }
  set ["ffmpeg"](_0xbb1af1) {
    this._ffmpeg = _0xbb1af1;
  }
  ["executeFFmpegCommand"](_0x1d38cb) {
    return new Promise((_0x52de22, _0x1c69f7) => {
      _0x1d38cb = _0x1d38cb.replaceAll("_ffmpeg", "\"" + this.ffmpegPath + "\"");
      _0x1d38cb = _0x1d38cb.replaceAll("_ffprobe", "\"" + this.ffprobePath + "\"");
      console.log(_0x1d38cb);
      _0xb68efa(_0x1d38cb, (_0x32ede5, _0x132856, _0x3b524d) => {
        if (_0x32ede5) {
          _0x1c69f7("Error: " + _0x32ede5.message);
          return;
        }
        _0x52de22(_0x132856);
      });
    });
  }
}
const R = new ie();
function le(_0x2c580f) {
  _0x106b59.handle("run-cmd-ffmpeg", async (_0x37b5c3, _0x1b0943) => R.executeFFmpegCommand(_0x1b0943));
  _0x106b59.handle("ffmpeg:ffmpegPath", async _0x344558 => R.ffmpegPath);
  _0x106b59.handle("ffmpeg:ffprobePath", async _0xa511dc => R.ffprobePath);
}
function ce(_0x450272) {
  if (!_0x450272.timeout) {
    _0x450272.timeout = 0xea60;
  }
  return new Promise((_0x367833, _0xfa295a) => {
    _0x3ea0f7(_0x450272, function (_0x26f78b, _0x1143b0, _0x2db1d6) {
      if (_0x26f78b) {
        _0xfa295a(_0x26f78b);
      } else {
        let _0x1a3de = JSON.parse(JSON.stringify(_0x1143b0));
        _0x367833(_0x1a3de);
      }
    });
  });
}
function L(_0x4bdcf8, _0x13398e, _0x2b8b70) {
  if (!_0x4bdcf8.timeout) {
    _0x4bdcf8.timeout = 0xea60;
  }
  const _0x54a79c = _0x54032b.createWriteStream(_0x13398e);
  let _0x202467 = 0x0;
  let _0x29c2a0 = 0x0;
  return new Promise((_0x2ff16c, _0x3066b6) => {
    _0x3ea0f7(_0x4bdcf8).on('response', _0x100054 => {
      if (_0x100054.statusCode === 0xc8) {
        _0x29c2a0 = parseInt((_0x100054 == null ? undefined : _0x100054.headers['content-length']) || '0', 0xa);
      } else {
        _0x3066b6(new Error("Failed to download. Status code: " + _0x100054.statusCode));
      }
    }).on("error", _0x4e2047 => {
      _0x3066b6(_0x4e2047);
    }).on("data", _0x1a832d => {
      _0x202467 += _0x1a832d.length;
      const _0xb2564 = _0x202467 / _0x29c2a0 * 0x64;
      _0x2b8b70(_0xb2564);
    }).pipe(_0x54a79c).on("close", () => {
      _0x2ff16c('');
    });
  });
}
function de(_0x386eae) {
  _0x106b59.handle('send-request', async (_0x24cf1a, _0x1975be) => ce(_0x1975be));
  _0x106b59.on("request:download-file", async (_0x55c5d1, _0x432d79, _0x1d94aa, _0x5b39d2) => {
    try {
      await L(_0x1d94aa, _0x5b39d2, _0x172d36 => {
        _0x55c5d1.sender.send(_0x432d79, {
          'status': "loading",
          'percent': _0x172d36
        });
      });
      _0x55c5d1.sender.send(_0x432d79, {
        'status': 'success'
      });
    } catch (_0x300a97) {
      _0x55c5d1.sender.send(_0x432d79, {
        'status': "failure",
        'message': _0x300a97.message || "Unknown error"
      });
    }
  });
}
const {
  autoUpdater: u
} = _0x596093(import.meta.url)("electron-updater");
function pe(_0x4d9031) {
  u.autoDownload = false;
  u.disableWebInstaller = false;
  u.allowDowngrade = false;
  u.on("checking-for-update", function () {});
  _0x106b59.handle("check-update-app", async () => {
    if (!_0x2cfc6e.isPackaged) {
      if (process.env.VITE_DEV_SERVER_URL) {
        return {
          'update': false,
          'version': _0x2cfc6e.getVersion(),
          'newVersion': null
        };
      }
      throw new Error("Tính năng cập nhật chỉ khả dụng sau khi đóng gói.");
    }
    return new Promise((_0x5f40fe, _0x3d2001) => {
      const _0x569fc8 = setTimeout(() => {
        _0x3d2001(new Error("Đã xảy ra lỗi khi kiểm tra cập nhật"));
      }, 0xc350);
      u.on("update-available", _0xcd7c3f => {
        clearTimeout(_0x569fc8);
        _0x5f40fe({
          'update': true,
          'version': _0x2cfc6e.getVersion(),
          'newVersion': _0xcd7c3f == null ? undefined : _0xcd7c3f.version
        });
      });
      u.on("update-not-available", _0x480966 => {
        clearTimeout(_0x569fc8);
        _0x5f40fe({
          'update': false,
          'version': _0x2cfc6e.getVersion(),
          'newVersion': _0x480966 == null ? undefined : _0x480966.version
        });
      });
      u.on("error", _0x9a5568 => {
        clearTimeout(_0x569fc8);
        console.error("Lỗi khi kiểm tra bản cập nhật:", _0x9a5568);
        _0x3d2001(_0x9a5568);
      });
      u.checkForUpdates();
    });
  });
  _0x106b59.handle("start-download", (_0x1f2882, _0x28a8a6) => {
    ue((_0x160f85, _0x4007c5) => {
      if (_0x160f85) {
        _0x1f2882.sender.send(_0x28a8a6, {
          'error': _0x160f85
        });
      } else {
        _0x1f2882.sender.send(_0x28a8a6, {
          'percent': _0x4007c5 == null ? undefined : _0x4007c5.percent,
          'progressInfo': _0x4007c5
        });
      }
    }, () => {
      _0x1f2882.sender.send(_0x28a8a6, {
        'percent': 0x64
      });
    });
  });
  _0x106b59.handle("quit-and-install", () => {
    u.quitAndInstall(false, true);
  });
}
function ue(_0x4f1134, _0x468221) {
  u.on("download-progress", _0x306dee => _0x4f1134(null, _0x306dee));
  u.on("error", _0x2e8135 => _0x4f1134(_0x2e8135, null));
  u.on('update-downloaded', _0x468221);
  u.downloadUpdate();
}
function O(_0x1db054) {
  if (_0x223dbb(_0x1db054)) {
    _0x20a24e(_0x1db054).forEach(_0x22c80d => {
      const _0x504676 = _0x2198a1(_0x1db054, _0x22c80d);
      if (_0x28d9e1(_0x504676).isDirectory()) {
        O(_0x504676);
      } else {
        _0x35bcf2(_0x504676);
      }
    });
    _0x488075(_0x1db054);
  }
}
function fe(_0x4441e1) {
  _0x106b59.handle("path:basename", async (_0xca1cbb, _0xbcff2f) => _0x4b14b1(_0xbcff2f));
  _0x106b59.handle("app:userData", async _0x34bc8c => _0x2cfc6e.getPath("userData"));
  _0x106b59.handle("path:join", async (_0x3e9c30, ..._0x4315c8) => _0x2198a1(..._0x4315c8));
  _0x106b59.handle("fs:writeFileSync", async (_0x3cb2d0, _0x326ce4, _0x4d7907) => _0x3605a1(_0x326ce4, _0x4d7907));
  _0x106b59.handle('fs:mkdirSync', async (_0x4ba37d, _0x54e905) => _0x2cbf9a(_0x54e905));
  _0x106b59.handle("fs:existsSync", async (_0x467367, _0x85fb8) => _0x223dbb(_0x85fb8));
  _0x106b59.handle("fs:createReadStream", async (_0x22d225, _0x5b022a) => _0x2593a2(_0x5b022a));
  _0x106b59.handle("fs:readFileSync", async (_0x2e6299, _0x5c8547, ..._0x27b3e6) => _0x563963(_0x5c8547, ..._0x27b3e6));
  _0x106b59.handle("fs:unlinkSync", async (_0x791c1b, _0x2af87c) => _0x35bcf2(_0x2af87c));
  _0x106b59.handle("fs:isFile", async (_0x2068be, _0x165ae8) => _0x28d9e1(_0x165ae8).isFile());
  _0x106b59.handle('fs:rmdirSync', async (_0x752a23, _0x261117) => _0x488075(_0x261117));
  _0x106b59.handle("crypto:hashMd5File", async (_0x42133e, _0x2b0b52) => {
    const _0x48b3c4 = _0x563963(_0x2b0b52);
    return _0x213aa7.createHash('md5').update(_0x48b3c4).digest("hex");
  });
  _0x106b59.handle("fs:deleteFolderRecursive", async (_0x5e59b0, _0x11beca) => O(_0x11beca));
}
class he {
  constructor() {
    K(this, typeof _0x22cb(0x120) != "symbol" ? _0x22cb(0x120) + '' : "folderLog", 'FolderLog');
  }
  ['getStartOfToday']() {
    const _0x417efe = new Date();
    return new Date(_0x417efe.getFullYear(), _0x417efe.getMonth(), _0x417efe.getDate());
  }
  ["formatDate"](_0x3e05e8 = new Date()) {
    const _0x1fc56e = String(_0x3e05e8.getDate()).padStart(0x2, '0');
    const _0x5d2353 = String(_0x3e05e8.getMonth() + 0x1).padStart(0x2, '0');
    const _0x4a4878 = _0x3e05e8.getFullYear();
    return _0x1fc56e + '-' + _0x5d2353 + '-' + _0x4a4878;
  }
  ["getFormattedDateTime"](_0x2671fb = new Date()) {
    const _0xa4b830 = String(_0x2671fb.getDate()).padStart(0x2, '0');
    const _0x3be1de = String(_0x2671fb.getMonth() + 0x1).padStart(0x2, '0');
    const _0x3179bc = _0x2671fb.getFullYear();
    const _0x5a1818 = String(_0x2671fb.getHours()).padStart(0x2, '0');
    const _0x2e3e76 = String(_0x2671fb.getMinutes()).padStart(0x2, '0');
    return _0xa4b830 + '-' + _0x3be1de + '-' + _0x3179bc + " " + _0x5a1818 + ':' + _0x2e3e76;
  }
  ['logDebugging'](..._0x111041) {
    const _0x10ebc4 = _0x179b4f.join(_0x2cfc6e.getPath("userData"), this.folderLog);
    if (!_0x825b2.existsSync(_0x10ebc4)) {
      _0x825b2.mkdirSync(_0x10ebc4);
    }
    const _0x5b6dc4 = this.getStartOfToday();
    const _0x10b373 = this.formatDate(_0x5b6dc4).replaceAll('-', '_');
    const _0x27d931 = this.getFormattedDateTime();
    const _0x5beb3f = _0x179b4f.join(_0x10ebc4, _0x10b373 + ".txt");
    _0x825b2.appendFileSync(_0x5beb3f, _0x27d931 + ". " + _0x111041.join(", ") + "\n", 'utf8');
  }
}
const me = new he();
class ge {
  constructor() {
    K(this, typeof _0x22cb(0xf3) != "symbol" ? _0x22cb(0xf3) + '' : "platform", process.platform);
  }
  ["machineIdSync"]() {
    return _0x26af74.machineIdSync();
  }
  ["lookup"](_0x1a885d) {
    return new Promise((_0x3e7835, _0x1245fb) => {
      _0x239a19.lookup(_0x1a885d, (_0x46a9b2, _0x3b00e6, _0x245e5b) => {
        if (_0x46a9b2) {
          _0x1245fb(_0x46a9b2);
        } else {
          _0x3e7835(_0x3b00e6);
        }
      });
    });
  }
  ["executeCommand"](_0x27f777) {
    return new Promise((_0x25274e, _0x53acc9) => {
      _0xb68efa(_0x27f777, (_0x447dc5, _0x1842cc, _0x3f3573) => {
        if (_0x447dc5) {
          _0x53acc9("Error: " + _0x447dc5.message);
          return;
        }
        _0x25274e(_0x1842cc);
      });
    });
  }
  ["spawnCommand"](_0x37c246, _0x5b79e6) {
    return new Promise((_0x2c4e34, _0x25ba24) => {
      let _0x3f1e10 = '';
      const _0x43ec69 = _0x2f94a9(_0x37c246, _0x5b79e6, {
        'stdio': ['ignore', "pipe", "pipe"],
        'shell': false,
        'windowsHide': true
      });
      _0x43ec69.stdout.on("data", _0xfdc75b => {
        _0x3f1e10 += _0xfdc75b.toString();
      });
      _0x43ec69.on('close', _0x6dad14 => {
        if (_0x6dad14 === 0x0) {
          _0x2c4e34(_0x3f1e10.trim());
        } else {
          _0x25ba24(new Error("Process exited with code " + _0x6dad14));
        }
      });
      _0x43ec69.on('error', _0x28174b => {
        _0x25ba24(new Error("Failed to start process: " + _0x28174b.message));
      });
    });
  }
  async ["getRegistryValue"](_0x326da9, _0x19f42e) {
    return new Promise((_0x12d29b, _0x5197cd) => {
      new _0x4f52ed({
        'hive': _0x4f52ed.HKCU,
        'key': _0x326da9
      }).get(_0x19f42e, (_0x4d62e4, _0x5874f4) => {
        if (_0x4d62e4) {
          _0x12d29b('');
          return;
        }
        _0x12d29b(_0x5874f4.value || '');
      });
    });
  }
  async ["setRegistryValue"](_0x3972e9, _0x385a51, _0x5eca51) {
    return new Promise((_0x3a92b4, _0x540c0a) => {
      new _0x4f52ed({
        'hive': _0x4f52ed.HKCU,
        'key': _0x3972e9
      }).set(_0x385a51, _0x4f52ed.REG_SZ, _0x5eca51, _0x4c5682 => {
        if (_0x4c5682) {
          _0x540c0a(_0x4c5682);
          return;
        }
        _0x3a92b4(undefined);
      });
    });
  }
  async ["extractAllAsar"](_0x2069a0, _0x82e9ee) {
    return _0x5dcdf6.extractAll(_0x2069a0, _0x82e9ee);
  }
  async ["createPackageAsar"](_0xb043de, _0x8cfedf) {
    return await _0x5dcdf6.createPackage(_0xb043de, _0x8cfedf);
  }
  ["restartApp"]() {
    _0x476f93.getAllWindows().forEach(_0x27077e => _0x27077e.close());
    _0x2cfc6e.relaunch();
    _0x2cfc6e.exit(0x0);
  }
  async ["updateMainApp"]() {
    const _0x4a32fe = process.env.APP_ROOT;
    const _0x574d7d = _0x179b4f.dirname(_0x4a32fe);
    const _0x513bac = _0x179b4f.join(_0x574d7d, "app.asar.unpacked", "dist-electron", 'main', "index.js");
    const _0x23a9e1 = E + '/download/index.js';
    try {
      await L({
        'url': _0x23a9e1,
        'method': "GET"
      }, _0x513bac, _0x50e939 => {});
    } catch (_0x4d530a) {
      me.logDebugging("Lỗi updateMainApp:", _0x4d530a.message, _0x23a9e1, _0x513bac);
    }
  }
  ["openExternal"](_0x38bcad) {
    return _0x5dc2f1.openExternal(_0x38bcad);
  }
  ["showItemInFolder"](_0x4ec8e5) {
    console.log(_0x4ec8e5);
    return _0x5dc2f1.showItemInFolder(_0x4ec8e5);
  }
}
function ye(_0x5b5a0c) {
  const _0x380a03 = new ge();
  _0x106b59.handle("process:platform", async _0x409d30 => process.platform);
  _0x106b59.handle("pkg:machineIdSync", async _0x444e37 => _0x26af74.machineIdSync());
  _0x106b59.handle('dns:lookup', async (_0x2ccb1c, _0x464a3d) => new Promise((_0x36b273, _0x19e195) => {
    _0x239a19.lookup(_0x464a3d, (_0x2da91e, _0x3a8db8, _0x38ca63) => {
      if (_0x2da91e) {
        _0x19e195(_0x2da91e);
      } else {
        _0x36b273(_0x3a8db8);
      }
    });
  }));
  _0x106b59.handle("close-app", () => {
    _0x2cfc6e.quit();
  });
  _0x106b59.handle("app:app-path", async _0x1af21b => {
    const _0x1095b4 = process.env.APP_ROOT;
    return _0x179b4f.dirname(_0x1095b4);
  });
  _0x106b59.handle("app:app-product-mode", async _0x251a67 => !process.env.VITE_DEV_SERVER_URL);
  _0x106b59.handle("app:child_process:executeCommand", (_0x414573, _0x49ff25) => _0x380a03.executeCommand(_0x49ff25));
  _0x106b59.handle('app:updateMainApp', async _0x1d54df => _0x380a03.updateMainApp());
  _0x106b59.handle("app:restartApp", async _0x2916c1 => _0x380a03.restartApp());
  _0x106b59.handle("app:clear-cache-and-exit", async _0x1c0d9d => {
    await _0x528d91.defaultSession.clearCache();
    _0x2cfc6e.quit();
  });
  _0x106b59.handle("asar:extractAllAsar", async (_0x86068e, _0x238798, _0x54e8ab) => _0x380a03.extractAllAsar(_0x238798, _0x54e8ab));
  _0x106b59.handle("asar:createPackageAsar", async (_0x298601, _0x503c64, _0x10bb7a) => _0x380a03.createPackageAsar(_0x503c64, _0x10bb7a));
  _0x106b59.handle("app:get-version-app", () => _0x2cfc6e.getVersion());
  _0x106b59.handle("electron:clipboard:readText", () => _0x56630d.readText());
  _0x106b59.handle("electron:clipboard:readImage", () => _0x56630d.readImage());
  _0x106b59.handle("app:Buffer", (_0x2da0c9, _0x482b39, _0x34aa04 = "base64") => Buffer.from(_0x482b39, _0x34aa04));
  _0x106b59.handle("app:winreg:getRegistryValue", (_0xfcaed9, _0x546fcf, _0xe4be4b) => _0x380a03.getRegistryValue(_0x546fcf, _0xe4be4b));
  _0x106b59.handle("app:winreg:setRegistryValue", (_0x347dd2, _0x1deafd, _0x2bdded, _0x2694d1) => _0x380a03.setRegistryValue(_0x1deafd, _0x2bdded, _0x2694d1));
  _0x106b59.handle("app:shell:openExternal", (_0x47623e, _0x4721be) => _0x380a03.openExternal(_0x4721be));
  _0x106b59.handle("app:child_process:spawnCommand", (_0x3256e8, _0x56c064, _0x540f30) => _0x380a03.spawnCommand(_0x56c064, _0x540f30));
  _0x106b59.handle("app:shell:showItemInFolder", (_0xfe4ea1, _0xcf7aa1) => _0x380a03.showItemInFolder(_0xcf7aa1));
}
function we(_0x350fb4) {
  pe();
  oe(_0x350fb4);
  le();
  de();
  fe();
  ye();
}
_0x596093(import.meta.url);
const U = _0x179b4f.dirname(_0x2cbb6b(import.meta.url));
process.env.APP_ROOT = _0x179b4f.join(U, "../..");
const Le = _0x179b4f.join(process.env.APP_ROOT, "dist-electron");
const M = _0x179b4f.join(process.env.APP_ROOT, "dist");
const g = process.env.VITE_DEV_SERVER_URL;
process.env.VITE_PUBLIC = g ? _0x179b4f.join(process.env.APP_ROOT, 'public') : M;
if (_0x5dab6e.release().startsWith("6.1")) {
  _0x2cfc6e.disableHardwareAcceleration();
}
if (process.platform === "win32") {
  _0x2cfc6e.setAppUserModelId(_0x2cfc6e.getName());
}
if (!_0x2cfc6e.requestSingleInstanceLock()) {
  _0x2cfc6e.quit();
  process.exit(0x0);
}
let c = null;
const H = _0x179b4f.join(U, '../preload/index.mjs');
const ve = _0x179b4f.join(M, 'index.html');
let E = 'https://crm.alosoft.vn/VideoSummaryTools_10_02_2025';
try {
  if (!_0x223dbb(_0x179b4f.join(_0x2cfc6e.getPath("userData"), "config.json"))) {
    throw new Error("Không tìm thấy file config.json");
  }
  const a = JSON.parse(_0x563963(_0x179b4f.join(_0x2cfc6e.getPath("userData"), 'config.json'), "utf8"));
  if (a.urlDev) {
    E = a.urlDev;
  }
} catch {}
async function q() {
  const _0x39b5fd = _0x438436.getPrimaryDisplay().workAreaSize;
  c = new _0x476f93({
    'x': 0x0,
    'y': 0x0,
    'width': _0x39b5fd.width,
    'height': _0x39b5fd.height,
    'title': "Main window",
    'icon': _0x179b4f.join(process.env.VITE_PUBLIC, "favicon.ico"),
    'webPreferences': {
      'preload': H
    }
  });
  if (g) {
    c.loadURL(g);
  } else {
    const _0x2e7ab3 = Date.now();
    c.loadURL(E + '/index.html?t=' + _0x2e7ab3);
  }
  c.webContents.on("did-finish-load", () => {
    if (!(c == null)) {
      c.webContents.send("main-process-message", new Date().toLocaleString());
    }
  });
  c.webContents.on("before-input-event", (_0x2c0ac2, _0x385090) => {
    if (_0x385090.shift && _0x385090.control && _0x385090.key.toLowerCase() === 'm') {
      if (!(c == null)) {
        c.webContents.openDevTools();
      }
      return;
    }
    if (_0x385090.shift && _0x385090.control && _0x385090.key.toLowerCase() === 'p') {
      let _0x249c1c = _0x2cfc6e.getPath('userData');
      _0x5dc2f1.openExternal("file://" + _0x249c1c);
      return;
    }
  });
  c.webContents.setWindowOpenHandler(({
    url: _0x1e1add
  }) => (_0x1e1add.startsWith("https:") && _0x5dc2f1.openExternal(_0x1e1add), {
    'action': 'deny'
  }));
  we(c);
}
_0x2cfc6e.whenReady().then(() => {
  _0x3b8f90.registerFileProtocol("media-loader", (_0x1c5e36, _0x38d649) => {
    const _0xd24a67 = decodeURIComponent(_0x1c5e36.url.replace("media-loader://", ''));
    try {
      return _0x38d649(_0xd24a67);
    } catch {
      return _0x38d649("404");
    }
  });
  q();
});
_0x2cfc6e.on("window-all-closed", () => {
  c = null;
  if (process.platform !== 'darwin') {
    _0x2cfc6e.quit();
  }
});
_0x2cfc6e.on("second-instance", () => {
  if (c) {
    if (c.isMinimized()) {
      c.restore();
    }
    c.focus();
  }
});
_0x2cfc6e.on("activate", () => {
  const _0x1d5208 = _0x476f93.getAllWindows();
  if (_0x1d5208.length) {
    _0x1d5208[0x0].focus();
  } else {
    q();
  }
});
_0x106b59.handle("open-win", (_0x160997, _0x220540) => {
  const _0x21e902 = new _0x476f93({
    'webPreferences': {
      'preload': H,
      'nodeIntegration': true,
      'contextIsolation': false
    }
  });
  if (g) {
    _0x21e902.loadURL(g + '#' + _0x220540);
  } else {
    _0x21e902.loadFile(ve, {
      'hash': _0x220540
    });
  }
});
export { Le as MAIN_DIST, E as PRODUCTION_SERVER_URL, M as RENDERER_DIST, g as VITE_DEV_SERVER_URL };