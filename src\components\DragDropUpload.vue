
<template>
  <div
    :class="`border-2 rounded-md transition-colors relative ${
      isDragOver ? dragOverClass : normalClass
    } ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`"
    @dragenter="handleDragEnter"
    @dragleave="handleDragLeave"
    @dragover="handleDragOver"
    @drop="handleDrop"
    @click="!disabled && triggerFileInput()"
  >
    <div :class="`p-${padding} text-center`">
      <input
        ref="fileInput"
        type="file"
        :accept="accept"
        :multiple="multiple"
        @change="handleFileChange"
        class="hidden"
        :disabled="disabled"
      />
      
      <!-- Custom slot for content -->
      <slot v-if="$slots.default" :isDragOver="isDragOver" :files="selectedFiles" />
      
      <!-- Default content -->
      <div v-else class="flex flex-col items-center justify-center py-4">
        <component 
          :is="iconComponent" 
          :class="`h-${iconSize} w-${iconSize} mb-2 text-gray-400`" 
        />
        <p class="text-sm text-gray-600 font-medium">
          {{ isDragOver ? dropText : dragText }}
        </p>
        <p class="text-xs text-gray-400 mt-1">
          {{ clickText }}
        </p>
        <div v-if="maxSize" class="text-xs text-gray-400 mt-1">
          {{ `Max size: ${formatFileSize(maxSize)}` }}
        </div>
      </div>
      
      <!-- Preview section -->
      <div v-if="showPreview && selectedFiles.length > 0" class="mt-4 border-t pt-4">
        <slot name="preview" :files="selectedFiles">
          <div class="space-y-2">
            <div 
              v-for="(file, index) in selectedFiles" 
              :key="index"
              class="flex items-center justify-between bg-gray-50 rounded p-2"
            >
              <div class="flex items-center space-x-2">
                <component :is="getFileIcon(file)" class="h-4 w-4 text-gray-500" />
                <span class="text-sm text-gray-700 truncate">{{ file.name }}</span>
                <span class="text-xs text-gray-500">({{ formatFileSize(file.size) }})</span>
              </div>
              <button
                @click.stop="removeFile(index)"
                class="text-red-500 hover:text-red-700 p-1"
                type="button"
              >
                <X class="h-3 w-3" />
              </button>
            </div>
          </div>
        </slot>
      </div>
      
      <!-- Error messages -->
      <div v-if="errors.length > 0" class="mt-2">
        <div 
          v-for="(error, index) in errors" 
          :key="index"
          class="text-xs text-red-500 bg-red-50 rounded p-1 mt-1"
        >
          {{ error }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, defineEmits, defineProps } from 'vue'
import { Upload, File, Image, Video, Music, X } from 'lucide-vue-next'

// Props
const props = defineProps({
  // File constraints
  accept: {
    type: String,
    default: '*/*'
  },
  multiple: {
    type: Boolean,
    default: false
  },
  maxSize: {
    type: Number,
    default: null // in bytes
  },
  maxFiles: {
    type: Number,
    default: null
  },
  
  // Styling
  dragOverClass: {
    type: String,
    default: 'border-blue-400 bg-blue-50'
  },
  normalClass: {
    type: String,
    default: 'border-gray-300 border-dashed hover:border-gray-400'
  },
  padding: {
    type: [String, Number],
    default: 4
  },
  iconSize: {
    type: [String, Number],
    default: 8
  },
  
  // Text content
  dragText: {
    type: String,
    default: 'Drag and drop files here'
  },
  dropText: {
    type: String,
    default: 'Drop files here'
  },
  clickText: {
    type: String,
    default: 'or click to select files'
  },
  
  // Features
  showPreview: {
    type: Boolean,
    default: true
  },
  disabled: {
    type: Boolean,
    default: false
  },
  
  // Initial files
  modelValue: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'files-selected', 'files-removed', 'error'])

// Reactive data
const isDragOver = ref(false)
const dragCounter = ref(0)
const selectedFiles = ref([...props.modelValue])
const errors = ref([])
const fileInput = ref(null)

// Computed
const iconComponent = computed(() => {
  if (props.accept.includes('image/')) return Image
  if (props.accept.includes('video/')) return Video
  if (props.accept.includes('audio/')) return Music
  return Upload
})

// Methods
const handleDragEnter = (e) => {
  if (props.disabled) return
  e.preventDefault()
  e.stopPropagation()
  dragCounter.value++
  isDragOver.value = true
}

const handleDragLeave = (e) => {
  if (props.disabled) return
  e.preventDefault()
  e.stopPropagation()
  dragCounter.value--
  if (dragCounter.value === 0) {
    isDragOver.value = false
  }
}

const handleDragOver = (e) => {
  if (props.disabled) return
  e.preventDefault()
  e.stopPropagation()
}

const handleDrop = (e) => {
  if (props.disabled) return
  e.preventDefault()
  e.stopPropagation()
  isDragOver.value = false
  dragCounter.value = 0
  
  const files = Array.from(e.dataTransfer.files)
  processFiles(files)
}

const handleFileChange = (e) => {
  if (props.disabled) return
  const files = Array.from(e.target.files)
  processFiles(files)
}

const processFiles = (files) => {
  errors.value = []
  const validFiles = []
  
  // Check file constraints
  files.forEach(file => {
    // Check file type
    if (props.accept !== '*/*' && !isFileTypeAccepted(file)) {
      errors.value.push(`File type not accepted: ${file.name}`)
      return
    }
    
    // Check file size
    if (props.maxSize && file.size > props.maxSize) {
      errors.value.push(`File too large: ${file.name} (${formatFileSize(file.size)})`)
      return
    }
    
    validFiles.push(file)
  })
  
  // Check max files limit
  const totalFiles = props.multiple ? selectedFiles.value.length + validFiles.length : validFiles.length
  if (props.maxFiles && totalFiles > props.maxFiles) {
    errors.value.push(`Maximum ${props.maxFiles} files allowed`)
    return
  }
  
  // Add files
  if (props.multiple) {
    selectedFiles.value = [...selectedFiles.value, ...validFiles]
  } else {
    selectedFiles.value = validFiles.slice(0, 1)
  }
  
  // Emit events
  emit('update:modelValue', selectedFiles.value)
  emit('files-selected', validFiles)
  console.log(selectedFiles.value);
  console.log(validFiles);
  
  
  if (errors.value.length > 0) {
    emit('error', errors.value)
  }
  
  // Clear input
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

const isFileTypeAccepted = (file) => {
  const acceptedTypes = props.accept.split(',').map(type => type.trim())
  return acceptedTypes.some(type => {
    if (type === '*/*') return true
    if (type.endsWith('/*')) {
      const category = type.split('/')[0]
      return file.type.startsWith(category + '/')
    }
    return file.type === type || file.name.toLowerCase().endsWith(type.replace('*', ''))
  })
}

const triggerFileInput = () => {
  if (fileInput.value) {
    fileInput.value.click()
  }
}

const removeFile = (index) => {
  const removedFile = selectedFiles.value.splice(index, 1)[0]
  emit('update:modelValue', selectedFiles.value)
  emit('files-removed', [removedFile])
}

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const getFileIcon = (file) => {
  if (file.type.startsWith('image/')) return Image
  if (file.type.startsWith('video/')) return Video
  if (file.type.startsWith('audio/')) return Music
  return File
}

// Watch for external changes to modelValue
import { watch } from 'vue'
watch(() => props.modelValue, (newValue) => {
  selectedFiles.value = [...newValue]
}, { deep: true })
</script>