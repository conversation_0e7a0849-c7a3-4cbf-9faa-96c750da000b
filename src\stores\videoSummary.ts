import { defineStore } from 'pinia'
import { ref, reactive } from 'vue'

export interface Video {
  id: string
  name: string
  path: string
  pathOutput: string[]
  textAudio?: string
  textSummary?: string
}

export const useVideoSummaryStore = defineStore('videoSummary', () => {
  // State
  const videoList = ref<Video[]>([])
  const videoMapChecked = reactive<Record<string, boolean>>({})

  // Actions
  const addVideos = (videos: Video[]) => {
    videoList.value.push(...videos)
  }

  const removeVideo = (id: string) => {
    videoList.value = videoList.value.filter(video => video.id !== id)
    delete videoMapChecked[id]
  }

  const removeVideos = (ids: string[]) => {
    videoList.value = videoList.value.filter(video => !ids.includes(video.id))
    ids.forEach(id => delete videoMapChecked[id])
  }

  const updateVideoField = (id: string, field: keyof Video, value: any) => {
    const video = videoList.value.find(v => v.id === id)
    if (video) {
      ;(video as any)[field] = value
    }
  }

  const addPathOutput = (id: string, path: string) => {
    const video = videoList.value.find(v => v.id === id)
    if (video) {
      video.pathOutput.push(path)
    }
  }

  const toggleVideoCheck = (id: string) => {
    videoMapChecked[id] = !videoMapChecked[id]
  }

  const checkAllVideos = (checked: boolean) => {
    videoList.value.forEach(video => {
      videoMapChecked[video.id] = checked
    })
  }

  const updateVideosFromImport = (videoData: Record<string, any>) => {
    videoList.value = videoList.value.map(video => {
      if (videoData[video.name]) {
        return {
          ...video,
          textAudio: videoData[video.name].transcription,
          textSummary: videoData[video.name].summary
        }
      }
      return video
    })
  }

  const clearAll = () => {
    videoList.value = []
    Object.keys(videoMapChecked).forEach(key => {
      delete videoMapChecked[key]
    })
  }

  return {
    // State
    videoList,
    videoMapChecked,
    
    // Actions
    addVideos,
    removeVideo,
    removeVideos,
    updateVideoField,
    addPathOutput,
    toggleVideoCheck,
    checkAllVideos,
    updateVideosFromImport,
    clearAll
  }
})
