#!/usr/bin/env node

const axios = require("axios");
const cheerio = require("cheerio");
const fs = require("fs");
const { spawn } = require("child_process");
const { program } = require("commander");
const { table } = require("table");
require("dotenv").config();

const API_KEY = process.env.ELEVENLABS_API_KEY || '***************************************************'
const DEFAULT_VOICE_ID = "EXAVITQu4vr4xnSDxMaL";

if (!API_KEY) {
    console.error("Error: ELEVENLABS_API_KEY not set in environment.");
    process.exit(1);
}

function isLinuxToolAvailable(tool) {
    return spawn("which", [tool]).stdout;
}

async function getVoices() {
    const res = await axios.get("https://api.elevenlabs.io/v1/voices", {
        headers: { "xi-api-key": API_KEY }
    });

    const rows = res.data.voices.map(v => [v.voice_id, v.name, v.category]);
    console.log(table([["Voice ID", "Name", "Category"], ...rows]));
}

async function urlToText(url) {
    const res = await axios.get(url);
    const $ = cheerio.load(res.data);

    const article = $("article");
    article.find("aside, header, footer, figure, figcaption, nav, script, a, img").remove();

    let text = article.text();
    text = text.replace(/\n+/g, "\n")
               .replace(/[/;:]/g, "")
               .replace(/\d{4}/g, "")
               .replace(/\n*\w*Comments\n*/g, "")
               .replace(/(\n\s*)+\n+/g, "\n\n")
               .replace(/ +/g, " ")
               .replace(/ ?([.,?!])/g, "$1")
               .replace(/Listen\s+\d+\s+min.*Share/g, "")
               .replace(/Most Popular/g, "")
               .replace(/From our sponsor/g, "");

    return text.trim();
}

async function getNewsByCategory(category) {
    const categories = {
        ai: "https://www.wired.com/feed/tag/ai/latest/rss",
        gear: "https://www.wired.com/feed/category/gear/latest/rss",
        business: "https://www.wired.com/feed/category/business/latest/rss",
        culture: "https://www.wired.com/feed/category/culture/latest/rss",
        science: "https://www.wired.com/feed/category/science/latest/rss",
        security: "https://www.wired.com/feed/category/security/latest/rss"
    };

    const url = categories[category];
    if (!url) return "";

    const res = await axios.get(url);
    const matches = res.data.match(/<item>[\s\S]*?<\/item>/g);
    let text = "";

    matches.forEach(item => {
        const title = item.match(/<title>(.*?)<\/title>/)?.[1];
        const desc = item.match(/<description>(.*?)<\/description>/)?.[1];
        text += `${title}\n${desc}\n\n`;
    });

    return text;
}

async function playAudio(voiceId, text, endpoint, outputFileName) {
    const url = `https://api.elevenlabs.io/v1/text-to-speech/${voiceId}` + (endpoint === "stream" ? "/stream" : "");

    const response = await axios.post(url, { text }, {
        headers: { "xi-api-key": API_KEY },
        responseType: "arraybuffer"
    });

    if (endpoint === "stream") {
        // Save temp file and play
        const tmpFile = "temp_audio.wav";
        fs.writeFileSync(tmpFile, response.data);
        const player = process.platform === "darwin" ? "afplay"
            : process.platform === "win32" ? "wmplayer"
            : "mpv";

        spawn(player, [tmpFile], { stdio: "inherit" });
    } else {
        fs.writeFileSync(outputFileName, response.data);
        console.log(`Audio saved to ${outputFileName}`);
    }
}

// CLI definition
program
    .option("-a, --audio", "Use /v1/text-to-speech")
    .option("-s, --stream", "Use /v1/text-to-speech/{voice_id}/stream")
    .option("--get-voices", "List all available voices")
    .option("-v, --voice-id <id>", "Voice ID to use", DEFAULT_VOICE_ID)
    .option("-t, --text <text>", "Text to convert")
    .option("-f, --file <file>", "File containing text")
    .option("-u, --url <url>", "URL to article")
    .option("--ai", "Get AI news")
    .option("--gear", "Get Gear news")
    .option("--business", "Get Business news")
    .option("--culture", "Get Culture news")
    .option("--science", "Get Science news")
    .option("--security", "Get Security news")
    .option("-o, --output <filename>", "Output file name", "output.wav")
    .parse(process.argv);

(async () => {
    const options = program.opts();

    if (options.getVoices) {
        await getVoices();
        return;
    }

    const endpoint = options.audio ? "audio" : options.stream ? "stream" : null;
    if (!endpoint) {
        console.error("You must specify --audio or --stream");
        process.exit(1);
    }

    let text = "This is a sample text to speech conversion.";
    if (options.text) {
        text = options.text;
    } else if (options.file) {
        text = fs.readFileSync(options.file, "utf-8");
    } else if (options.url) {
        text = await urlToText(options.url);
    } else {
        const categories = ["ai", "gear", "business", "culture", "science", "security"];
        const category = categories.find(c => options[c]);
        if (category) text = await getNewsByCategory(category);
    }

    await playAudio(options.voiceId, text, endpoint, options.output);
})();
