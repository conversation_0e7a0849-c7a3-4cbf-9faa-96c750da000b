const fs = require('fs-extra');
const { v4: uuidv4 } = require('uuid');

/**
 * JavaScript Interpreter for executing code within agent context
 */
class JavaScriptInterpreter {
    constructor(agent = null, serializePath = null) {
        this.agent = agent;
        this.serializePath = serializePath;
        this.globals = {};
        this.executionHistory = [];
        
        // Load from file if path exists
        if (this.serializePath && fs.existsSync(this.serializePath)) {
            this.load();
        }
    }

    /**
     * Execute JavaScript code
     * @param {string} code
     * @returns {any}
     */
    execute(code) {
        try {
            // Create execution context with globals
            const context = {
                ...this.globals,
                console: console,
                process: process,
                setTimeout: setTimeout,
                setInterval: setInterval,
                clearTimeout: clearTimeout,
                clearInterval: clearInterval
            };

            // Try to evaluate as expression first
            try {
                const func = new Function(...Object.keys(context), `return (${code})`);
                const result = func(...Object.values(context));

                // Store execution in history
                this.executionHistory.push({
                    id: uuidv4(),
                    code: code,
                    result: result,
                    timestamp: new Date().toISOString(),
                    success: true
                });

                this.save();
                return result;
            } catch (expressionError) {
                // If expression fails, try as statement
                const func = new Function(...Object.keys(context), code);
                const result = func(...Object.values(context));

                // Store execution in history
                this.executionHistory.push({
                    id: uuidv4(),
                    code: code,
                    result: result,
                    timestamp: new Date().toISOString(),
                    success: true
                });

                this.save();
                return result;
            }
        } catch (error) {
            // Store error in history
            this.executionHistory.push({
                id: uuidv4(),
                code: code,
                error: error.message,
                timestamp: new Date().toISOString(),
                success: false
            });

            this.save();
            throw error;
        }
    }

    /**
     * Set global variable
     * @param {string} name 
     * @param {any} value 
     */
    setGlobal(name, value) {
        this.globals[name] = value;
        this.save();
    }

    /**
     * Get global variable
     * @param {string} name 
     * @returns {any}
     */
    getGlobal(name) {
        return this.globals[name];
    }

    /**
     * Clear all globals
     */
    clearGlobals() {
        this.globals = {};
        this.save();
    }

    /**
     * Get execution history
     * @param {number} limit 
     * @returns {Array}
     */
    getHistory(limit = null) {
        if (limit) {
            return this.executionHistory.slice(-limit);
        }
        return [...this.executionHistory];
    }

    /**
     * Clear execution history
     */
    clearHistory() {
        this.executionHistory = [];
        this.save();
    }

    /**
     * Save interpreter state to file
     */
    save() {
        if (!this.serializePath) return;
        
        try {
            const data = {
                globals: this.globals,
                executionHistory: this.executionHistory
            };
            fs.writeJsonSync(this.serializePath, data);
        } catch (error) {
            console.error('Error saving interpreter state:', error);
        }
    }

    /**
     * Load interpreter state from file
     */
    load() {
        if (!this.serializePath || !fs.existsSync(this.serializePath)) return;
        
        try {
            const data = fs.readJsonSync(this.serializePath);
            this.globals = data.globals || {};
            this.executionHistory = data.executionHistory || [];
        } catch (error) {
            console.error('Error loading interpreter state:', error);
            this.globals = {};
            this.executionHistory = [];
        }
    }

    /**
     * Check if code contains potentially dangerous operations
     * @param {string} code 
     * @returns {boolean}
     */
    isSafeCode(code) {
        const dangerousPatterns = [
            /require\s*\(\s*['"]fs['"]/, // File system access
            /require\s*\(\s*['"]child_process['"]/, // Process execution
            /require\s*\(\s*['"]os['"]/, // OS access
            /process\.exit/, // Process termination
            /eval\s*\(/, // Dynamic code evaluation
            /Function\s*\(/, // Dynamic function creation
        ];

        return !dangerousPatterns.some(pattern => pattern.test(code));
    }

    /**
     * Execute code with safety checks
     * @param {string} code 
     * @param {boolean} safeMode 
     * @returns {any}
     */
    safeExecute(code, safeMode = true) {
        if (safeMode && !this.isSafeCode(code)) {
            throw new Error('Code contains potentially dangerous operations');
        }
        
        return this.execute(code);
    }
}

module.exports = { JavaScriptInterpreter };