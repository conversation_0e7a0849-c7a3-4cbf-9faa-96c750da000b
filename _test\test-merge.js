const { execSync, spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// The original command that's failing
const originalCommand = `G:\\CODE\\Capcut-TTS-app\\capcut-tts-app\\static\\merge_audio_v2.exe "F:\\ReviewDao\\11-05\\ran-quy\\test_audio\\temp\\audio_files.json" "F:\\ReviewDao\\11-05\\ran-quy\\test_audio\\temp\\subs.json" "F:\\ReviewDao\\11-05\\ran-quy/test_audio/combined-audio.mp3"`;

// Normalize paths to use consistent separators
const executablePath = path.normalize('G:/CODE/Capcut-TTS-app/capcut-tts-app/static/merge_audio_v2.exe');
const audioFilesJsonPath = path.normalize('F:/ReviewDao/11-05/ran-quy/test_audio/temp/audio_files.json');
const subsJsonPath = path.normalize('F:/ReviewDao/11-05/ran-quy/test_audio/temp/subs.json');
const outputPath = path.normalize('F:/ReviewDao/11-05/ran-quy/test_audio/combined-audio.mp3');

// Check if files exist
console.log('Checking if files exist:');
console.log(`Executable: ${executablePath} - ${fs.existsSync(executablePath) ? 'Exists' : 'Not found'}`);
console.log(`Audio files JSON: ${audioFilesJsonPath} - ${fs.existsSync(audioFilesJsonPath) ? 'Exists' : 'Not found'}`);
console.log(`Subs JSON: ${subsJsonPath} - ${fs.existsSync(subsJsonPath) ? 'Exists' : 'Not found'}`);
console.log(`Output directory: ${path.dirname(outputPath)} - ${fs.existsSync(path.dirname(outputPath)) ? 'Exists' : 'Not found'}`);

// Test 1: Original command with execSync
console.log('\n--- Test 1: Original command with execSync ---');
console.log('Command:', originalCommand);
try {
    execSync(originalCommand, { stdio: 'inherit' });
    console.log('Success!');
} catch (error) {
    console.error('Error:', error.message);
}

// Test 2: Normalized paths with execSync
console.log('\n--- Test 2: Normalized paths with execSync ---');
const normalizedCommand = `"${executablePath}" "${audioFilesJsonPath}" "${subsJsonPath}" "${outputPath}"`;
console.log('Command:', normalizedCommand);
try {
    execSync(normalizedCommand, { stdio: 'inherit' });
    console.log('Success!');
} catch (error) {
    console.error('Error:', error.message);
}

// Test 3: Using spawn
console.log('\n--- Test 3: Using spawn instead of execSync ---');
console.log('Command:', normalizedCommand);
try {
    const process = spawn(executablePath, [
        audioFilesJsonPath,
        subsJsonPath,
        outputPath
    ], {
        stdio: 'inherit',
        shell: true
    });

    process.on('error', (error) => {
        console.error('Spawn error:', error);
    });

    process.on('exit', (code) => {
        if (code === 0) {
            console.log('Success! Process exited with code 0');
        } else {
            console.error(`Process exited with code ${code}`);
        }
    });
} catch (error) {
    console.error('Error:', error.message);
}
