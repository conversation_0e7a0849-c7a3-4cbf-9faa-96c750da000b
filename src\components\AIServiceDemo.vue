<template>
  <div class="p-4 max-w-4xl mx-auto">
    <h1 class="text-2xl font-bold mb-4">AI Services Demo</h1>
    
    <!-- Quick Test Panel -->
    <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 mb-6">
      <h2 class="text-lg font-semibold mb-3">Quick Test</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium mb-1">Service</label>
          <a-select v-model:value="testService" class="w-full">
            <a-select-option value="openai">OpenAI</a-select-option>
            <a-select-option value="deepseek">DeepSeek</a-select-option>
            <a-select-option value="gemini">Gemini</a-select-option>
            <a-select-option value="claude">Claude</a-select-option>
            <a-select-option value="openrouter">OpenRouter</a-select-option>
          </a-select>
        </div>
        <div>
          <label class="block text-sm font-medium mb-1">API Key</label>
          <a-input 
            v-model:value="testApiKey" 
            type="password" 
            placeholder="Enter API key to test"
          />
        </div>
      </div>
      <div class="mt-4 flex gap-2">
        <a-button @click="testLoadModels" :loading="testing" type="primary">
          Test Load Models
        </a-button>
        <a-button @click="clearResults" type="default">
          Clear Results
        </a-button>
      </div>
    </div>

    <!-- Results -->
    <div v-if="testResults.length > 0" class="space-y-4">
      <h2 class="text-lg font-semibold">Test Results</h2>
      <div 
        v-for="(result, index) in testResults" 
        :key="index"
        class="border rounded-lg p-4"
        :class="{
          'border-green-200 bg-green-50': result.success,
          'border-red-200 bg-red-50': !result.success
        }"
      >
        <div class="flex items-center justify-between mb-2">
          <h3 class="font-medium">{{ result.service }} - {{ result.timestamp }}</h3>
          <a-tag :color="result.success ? 'green' : 'red'">
            {{ result.success ? 'Success' : 'Failed' }}
          </a-tag>
        </div>
        <div class="text-sm text-gray-600">
          <div><strong>Endpoint:</strong> {{ result.endpoint }}</div>
          <div><strong>Duration:</strong> {{ result.duration }}ms</div>
          <div v-if="result.success">
            <strong>Models loaded:</strong> {{ result.models.length }}
            <div class="mt-2 flex flex-wrap gap-1">
              <a-tag v-for="model in result.models.slice(0, 10)" :key="model" size="small">
                {{ model }}
              </a-tag>
              <a-tag v-if="result.models.length > 10" size="small" color="blue">
                +{{ result.models.length - 10 }} more
              </a-tag>
            </div>
          </div>
          <div v-else class="text-red-600">
            <strong>Error:</strong> {{ result.error }}
          </div>
        </div>
      </div>
    </div>

    <!-- Service Status Overview -->
    <div class="mt-8">
      <h2 class="text-lg font-semibold mb-4">Current Service Status</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div 
          v-for="(service, key) in ttsStore.aiServices" 
          :key="key"
          class="border rounded-lg p-4"
        >
          <div class="flex items-center justify-between mb-2">
            <h3 class="font-medium">{{ service.name }}</h3>
            <a-switch :checked="service.enabled" size="small" disabled />
          </div>
          <div class="space-y-1 text-sm">
            <div>API Key: {{ service.apiKey ? '✓' : '✗' }}</div>
            <div>Models: {{ service.models.length || service.defaultModels.length }}</div>
            <div>Status: 
              <a-tag v-if="service.loading" color="blue" size="small">Loading</a-tag>
              <a-tag v-else-if="!service.enabled" color="red" size="small">Disabled</a-tag>
              <a-tag v-else-if="!service.apiKey" color="orange" size="small">No Key</a-tag>
              <a-tag v-else color="green" size="small">Ready</a-tag>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { message } from 'ant-design-vue';
import { useTTSStore } from '../stores/ttsStore';
import { getOpenAIModels, getGeminiModels } from '../lib/apiUtils';

const ttsStore = useTTSStore();

const testService = ref('openai');
const testApiKey = ref('');
const testing = ref(false);
const testResults = ref([]);

const serviceEndpoints = {
  openai: 'https://api.openai.com/v1',
  deepseek: 'https://api.deepseek.com/v1',
  gemini: 'https://generativelanguage.googleapis.com/v1beta',
  claude: 'https://api.anthropic.com/v1',
  openrouter: 'https://openrouter.ai/api/v1'
};

async function testLoadModels() {
  if (!testApiKey.value.trim()) {
    message.warning('Please enter an API key');
    return;
  }

  testing.value = true;
  const startTime = Date.now();
  
  try {
    let models = [];
    const endpoint = serviceEndpoints[testService.value];
    
    switch (testService.value) {
      case 'openai':
      case 'deepseek':
      case 'claude':
        models = await getOpenAIModels(endpoint, testApiKey.value);
        break;
      case 'openrouter':
        const openrouterData = await getOpenAIModels(endpoint, testApiKey.value);
        // Extract model IDs and categorize by :free suffix
        models = openrouterData.map(model => {
          const isFree = model.id.endsWith(':free');
          return `${model.id}${isFree ? ' (FREE)' : ''}`;
        });
        break;
      case 'gemini':
        models = await getGeminiModels(endpoint, testApiKey.value);
        break;
    }
    
    const duration = Date.now() - startTime;
    
    testResults.value.unshift({
      service: testService.value.toUpperCase(),
      endpoint,
      success: true,
      models,
      duration,
      timestamp: new Date().toLocaleTimeString()
    });
    
    message.success(`Loaded ${models.length} models in ${duration}ms`);
    
  } catch (error) {
    const duration = Date.now() - startTime;
    
    testResults.value.unshift({
      service: testService.value.toUpperCase(),
      endpoint: serviceEndpoints[testService.value],
      success: false,
      error: error.message,
      duration,
      timestamp: new Date().toLocaleTimeString()
    });
    
    message.error(`Failed to load models: ${error.message}`);
  } finally {
    testing.value = false;
  }
}

function clearResults() {
  testResults.value = [];
}
</script>
