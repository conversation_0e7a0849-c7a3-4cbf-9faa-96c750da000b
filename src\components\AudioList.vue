<script setup>
import { computed, ref, watch } from 'vue';
import { useTTSStore } from '../stores/ttsStore';

const ttsStore = useTTSStore();
const allAudios = computed(() => ttsStore.generatedAudios.slice().reverse());

// Pagination
const currentPage = ref(1);
const pageSize = ref(5);
const pageInput = ref('1');
const generatedAudios = computed(() => {
  const startIndex = (currentPage.value - 1) * pageSize.value;
  const endIndex = startIndex + pageSize.value;
  return allAudios.value.slice(startIndex, endIndex);
});
const totalPages = computed(() => Math.ceil(allAudios.value.length / pageSize.value));

// Compute the pages to show around the current page
const middlePages = computed(() => {
  const pages = [];
  for (let i = -1; i <= 1; i++) {
    const page = currentPage.value + i;
    if (page > 1 && page < totalPages.value) {
      pages.push(page);
    }
  }
  return pages;
});

// Watch for changes to currentPage and update pageInput
watch(currentPage, (newPage) => {
  pageInput.value = newPage.toString();
});

function formatDate(dateString) {
  const date = new Date(dateString);
  return date.toLocaleString();
}

function formatDuration(ms) {
  const seconds = ms / 1000;
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins}:${secs.toString().padStart(2, '0')}`;
}

function removeAudio(id) {
  ttsStore.removeGeneratedAudio(id);
}

function clearAllAudios() {
  ttsStore.clearGeneratedAudios();
}

function downloadAudio(audio){
  const a = document.createElement('a');
  a.href = audio.url;
  a.download = `tts-audio-${audio.id}.mp3`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
}

function changePageSize(newSize) {
  // Calculate the first item index of the current page
  const firstItemIndex = (currentPage.value - 1) * pageSize.value;

  // Update page size
  pageSize.value = newSize;

  // Calculate the new page number to keep the view around the same items
  currentPage.value = Math.floor(firstItemIndex / newSize) + 1;

  // Make sure the current page is valid
  if (currentPage.value > totalPages.value) {
    currentPage.value = Math.max(1, totalPages.value);
  }

  // Update page input to match current page
  pageInput.value = currentPage.value.toString();
}

function goToPage() {
  // Parse the input as a number
  const page = parseInt(pageInput.value);

  // Validate the page number
  if (!isNaN(page) && page >= 1 && page <= totalPages.value) {
    currentPage.value = page;
  } else {
    // Reset the input to the current page if invalid
    pageInput.value = currentPage.value.toString();
  }
}
</script>

<template>
  <!-- Container chiếm full height, có scroll riêng -->
  <div class="flex-1 flex flex-col bg-gray-900 text-white">
    <!-- Header - cố định không scroll -->
    <div class="flex-shrink-0 p-4 border-b border-gray-700">
      <div class="flex justify-between items-center">
        <h2 class="text-lg font-medium text-white">Generated Audio History</h2>

        <div class="flex space-x-2">
          <span class="text-sm text-gray-400 self-center" v-if="allAudios.length > 0">
            Total: {{ allAudios.length }} items
          </span>
          <a-button type="danger" @click="clearAllAudios" :disabled="allAudios.length === 0">
            Clear All
          </a-button>
        </div>
      </div>
    </div>
    <!-- Content area - có thể scroll -->
    <div class="flex-1"></div>
      <div class="p-4 h-[700px] min-h-0 overflow-auto">
        <div v-if="allAudios.length === 0" class="text-center py-8 text-gray-400">
          No audio files have been generated yet.
        </div>

        <div v-else class="space-y-4">
          <div v-for="audio in generatedAudios" :key="audio.id" class="bg-gray-800 p-4 rounded-lg border border-gray-700">
            <div class="flex justify-between mb-2">
              <span class="text-sm font-medium text-gray-300">
                {{ formatDate(audio.timestamp) }}
              </span>
              <span class="text-sm text-gray-400">
                Duration: {{ formatDuration(audio.duration) }} | Voice: {{ audio.voice }}
              </span>
            </div>

            <p class="text-gray-200 mb-2 line-clamp-2">{{ audio.text }}</p>

            <audio controls class="w-full mb-2" :src="audio.url"></audio>

            <div class="flex justify-between">
              <div class="flex space-x-2">
                <a-button type="default" size="small" :href="audio.url" target="_blank">
                  Open in New Tab
                </a-button>
                <a-button type="default" size="small" @click="downloadAudio(audio)">
                  Download
                </a-button>
              </div>

              <a-button type="danger" size="small" @click="removeAudio(audio.id)">
                Remove
              </a-button>
            </div>
          </div>
        </div>
      </div>
    <!-- </div> -->

    <!-- Pagination - cố định ở bottom -->
    <div v-if="allAudios.length > 0" class="flex-shrink-0 p-4 border-t border-gray-700 bg-gray-800">
      <div class="flex flex-col space-y-3">
        <div class="flex justify-between items-center">
          <div class="text-sm text-gray-300">
            Showing {{ (currentPage - 1) * pageSize + 1 }} to {{ Math.min(currentPage * pageSize, allAudios.length) }} of {{ allAudios.length }} items
          </div>

          <div class="flex items-center space-x-2">
            <span class="text-sm text-gray-300">Items per page:</span>
            <a-select v-model:value="pageSize" style="width: 80px" size="small" @change="changePageSize">
              <a-select-option :value="5">5</a-select-option>
              <a-select-option :value="10">10</a-select-option>
              <a-select-option :value="20">20</a-select-option>
              <a-select-option :value="50">50</a-select-option>
            </a-select>
          </div>
        </div>

        <div v-if="totalPages > 1" class="flex justify-center">
          <div class="flex items-center space-x-2">
            <a-button
              size="small"
              @click="currentPage = Math.max(1, currentPage - 1)"
              :disabled="currentPage === 1"
            >
              Previous
            </a-button>

            <!-- Page buttons - show up to 5 pages -->
            <template v-if="totalPages <= 7">
              <a-button
                v-for="page in totalPages"
                :key="page"
                size="small"
                :type="page === currentPage ? 'primary' : 'default'"
                @click="currentPage = page"
              >
                {{ page }}
              </a-button>
            </template>

            <!-- For many pages, show ellipsis -->
            <template v-else>
              <!-- First page -->
              <a-button
                size="small"
                :type="currentPage === 1 ? 'primary' : 'default'"
                @click="currentPage = 1"
              >
                1
              </a-button>

              <!-- Ellipsis if needed -->
              <span v-if="currentPage > 3" class="text-gray-400">...</span>

              <!-- Pages around current -->
              <a-button
                v-for="page in middlePages"
                :key="page"
                size="small"
                :type="page === currentPage ? 'primary' : 'default'"
                @click="currentPage = page"
              >
                {{ page }}
              </a-button>

              <!-- Ellipsis if needed -->
              <span v-if="currentPage < totalPages - 2" class="text-gray-400">...</span>

              <!-- Last page -->
              <a-button
                size="small"
                :type="currentPage === totalPages ? 'primary' : 'default'"
                @click="currentPage = totalPages"
              >
                {{ totalPages }}
              </a-button>
            </template>

            <a-button
              size="small"
              @click="currentPage = Math.min(totalPages, currentPage + 1)"
              :disabled="currentPage === totalPages"
            >
              Next
            </a-button>

            <!-- Go to page input -->
            <div class="flex items-center ml-4">
              <span class="text-sm text-gray-300 mr-2">Go to:</span>
              <a-input-number
                v-model:value="pageInput"
                size="small"
                style="width: 60px"
                :min="1"
                :max="totalPages"
                @pressEnter="goToPage"
              />
              <a-button size="small" class="ml-1" @click="goToPage">Go</a-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Custom scrollbar styling để match với dark theme */
.overflow-auto::-webkit-scrollbar {
  width: 8px;
}

.overflow-auto::-webkit-scrollbar-track {
  background: #374151;
  border-radius: 4px;
}

.overflow-auto::-webkit-scrollbar-thumb {
  background: #6B7280;
  border-radius: 4px;
}

.overflow-auto::-webkit-scrollbar-thumb:hover {
  background: #9CA3AF;
}
</style>