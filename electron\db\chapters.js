const moment = require('moment');
const BatchProcessor = require('./batch-utils');

class Chapters {
    constructor(knex) {
        this.knex = knex;
        this.batchProcessor = new BatchProcessor(knex);
    }

    async createTable() {
        return this.knex.schema.hasTable('chapters').then((exists) => {
            if (!exists) {
                return this.knex.schema.createTable('chapters', (table) => {
                    table.increments('id').primary();
                    table.integer('book_id').nullable();
                    table.string('title', 500).notNullable();
                    table.string('href', 500).notNullable();
                    table.text('text').nullable();
                    table.string('full_url', 1000).notNullable();
                    table.integer('chapter_index').notNullable();
                    table.integer('page_number').nullable();
                    table.string('source_url', 1000).nullable();
                    table.string('book_title', 500).nullable();
                    table.string('extraction_session', 100).nullable(); // To group extractions
                    table.timestamp('created_at').defaultTo(this.knex.fn.now());
                    table.timestamp('updated_at').defaultTo(this.knex.fn.now());

                    // Indexes for better performance
                    table.index(['book_id']);
                    table.index(['chapter_index']);
                    table.index(['page_number']);
                    table.index(['extraction_session']);
                    table.unique(['full_url']); // Prevent duplicate chapters
                });
            }
        });
    }

    // Insert a single chapter
    async insertChapter(chapterData) {
        try {
            const [id] = await this.knex('chapters').insert({
                book_id: chapterData.book_id || null,
                title: chapterData.title,
                href: chapterData.href,
                text: chapterData.text || chapterData.title,
                full_url: chapterData.fullUrl,
                chapter_index: chapterData.index,
                page_number: chapterData.pageNumber || null,
                source_url: chapterData.sourceUrl || null,
                book_title: chapterData.bookTitle || null,
                extraction_session: chapterData.extractionSession || null,
                created_at: moment().format('YYYY-MM-DD HH:mm:ss'),
                updated_at: moment().format('YYYY-MM-DD HH:mm:ss')
            });
            return id;
        } catch (error) {
            if (error.code === 'SQLITE_CONSTRAINT_UNIQUE') {
                // Chapter already exists, update it instead
                return await this.updateChapter(chapterData.fullUrl, chapterData);
            }
            throw error;
        }
    }

    // Insert multiple chapters in batch with chunking
    async insertChaptersBatch(chaptersArray, extractionSession = null, chunkSize = 50) {
        if (!chaptersArray || chaptersArray.length === 0) {
            return [];
        }

        console.log(`Inserting ${chaptersArray.length} chapters in batches of ${chunkSize}...`);

        const results = [];
        let successCount = 0;
        let errorCount = 0;

        // Process in chunks to avoid SQLite compound SELECT limit
        for (let i = 0; i < chaptersArray.length; i += chunkSize) {
            const chunk = chaptersArray.slice(i, i + chunkSize);
            console.log(`Processing chunk ${Math.floor(i / chunkSize) + 1}/${Math.ceil(chaptersArray.length / chunkSize)} (${chunk.length} chapters)`);

            const batchData = chunk.map(chapter => ({
                book_id: chapter.book_id || null,
                title: chapter.title,
                href: chapter.href,
                text: chapter.text || chapter.title,
                full_url: chapter.fullUrl,
                chapter_index: chapter.index,
                page_number: chapter.pageNumber || null,
                source_url: chapter.sourceUrl || null,
                book_title: chapter.bookTitle || null,
                extraction_session: extractionSession || chapter.extractionSession || null,
                created_at: moment().format('YYYY-MM-DD HH:mm:ss'),
                updated_at: moment().format('YYYY-MM-DD HH:mm:ss')
            }));

            try {
                // Try batch insert for this chunk
                const chunkResults = await this.knex('chapters').insert(batchData);
                results.push(...chunkResults);
                successCount += chunk.length;
                console.log(`✅ Chunk inserted successfully (${chunk.length} chapters)`);
            } catch (error) {
                console.warn(`⚠️ Batch insert failed for chunk, trying individual inserts:`, error.message);

                // If batch insert fails, insert one by one for this chunk
                for (const chapter of chunk) {
                    try {
                        const id = await this.insertChapter({
                            ...chapter,
                            extractionSession: extractionSession || chapter.extractionSession
                        });
                        results.push(id);
                        successCount++;
                    } catch (err) {
                        console.warn(`❌ Failed to insert chapter: ${chapter.title}`, err.message);
                        errorCount++;
                    }
                }
            }

            // Small delay between chunks to be gentle on the database
            if (i + chunkSize < chaptersArray.length) {
                await new Promise(resolve => setTimeout(resolve, 100));
            }
        }

        console.log(`📊 Batch insert completed: ${successCount} successful, ${errorCount} failed`);
        return results;
    }

    // Alternative batch insert using the advanced batch processor
    async insertChaptersBatchAdvanced(chaptersArray, extractionSession = null, options = {}) {
        if (!chaptersArray || chaptersArray.length === 0) {
            return [];
        }

        const {
            batchSize = null, // Auto-determine if not specified
            logProgress = true
        } = options;

        // Use the advanced batch processor with automatic fallback
        return await this.batchProcessor.insertChaptersWithFallback(
            chaptersArray,
            extractionSession,
            {
                batchSize: batchSize || this.batchProcessor.getOptimalBatchSize(chaptersArray.length, 'medium'),
                logProgress
            }
        );
    }

    // Update existing chapter
    async updateChapter(fullUrl, updateData) {
        return await this.knex('chapters')
            .where('full_url', fullUrl)
            .update({
                ...updateData,
                updated_at: moment().format('YYYY-MM-DD HH:mm:ss')
            });
    }

    // Get chapters by book_id
    async getChaptersByBookId(bookId) {
        return await this.knex('chapters')
            .where('book_id', bookId)
            .orderBy('chapter_index', 'asc');
    }

    // Get chapters by extraction session
    async getChaptersBySession(sessionId) {
        return await this.knex('chapters')
            .where('extraction_session', sessionId)
            .orderBy('chapter_index', 'asc');
    }

    // Get all chapters with pagination
    async getAllChapters(limit = 100, offset = 0) {
        return await this.knex('chapters')
            .limit(limit)
            .offset(offset)
            .orderBy('created_at', 'desc');
    }

    // Count total chapters
    async getChaptersCount() {
        const result = await this.knex('chapters').count('id as count').first();
        return result.count;
    }

    // Delete chapters by session
    async deleteChaptersBySession(sessionId) {
        return await this.knex('chapters')
            .where('extraction_session', sessionId)
            .del();
    }

    // Get latest extraction sessions
    async getExtractionSessions(limit = 10) {
        return await this.knex('chapters')
            .select('extraction_session', 'book_title', 'source_url')
            .count('id as chapter_count')
            .max('created_at as last_updated')
            .whereNotNull('extraction_session')
            .groupBy('extraction_session', 'book_title', 'source_url')
            .orderBy('last_updated', 'desc')
            .limit(limit);
    }
}

module.exports = Chapters;
