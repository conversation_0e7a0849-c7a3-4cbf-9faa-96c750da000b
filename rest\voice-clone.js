const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

// === Cấu hình ===
const groupId = '1909853211993314096';
const apiKey = 'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************.B6HbWRa_5cxuVnz1tN5x0Z-P7AGRXSpzkGDDD7lY480iPMEX9SY1_vvw-Dy5ehRVNSWvaKPvVJGAO7DENb4bucDJCKdCF749zvEYVM-mmbnF0tKoO2M77BqEFfoaM-jmH27SF7lUnx0-VVtY9jFyMTDBAebY62lK2TiiDgw2DWSdRkuVX9ThLXEaKrQiYIkTfqAhBmIUaHeYxvrhpbdCNrRmOYO55wmR76KEadYu_BJnKSVwEQaRyDC0O1PYPTNQt8h_SB5sm2HL75i0dm3qSgixIQ6z2mG1qfRzx5vwoTOgEFoWOpJTxkYJkbltpvgawu9FtgRVB3fmdpTez_tk1w';
const filePath = './rest/nu-tm.wav'; // Đường dẫn file audio thực tế

// === Chuẩn bị form data ===
const form = new FormData();
form.append('purpose', 'voice_clone');
form.append('file', fs.createReadStream(filePath));

// === Gửi yêu cầu ===
// axios.post(`https://api.minimax.io/v1/files/upload?GroupId=${groupId}`, form, {
//   headers: {
//     ...form.getHeaders(),
//     Authorization: `Bearer ${apiKey}`
//   }
// })
// .then(res => {
//   console.log('✅ Upload thành công:', res.data);
// })
// .catch(err => {
//   console.error('❌ Lỗi upload:', err.response?.data || err.message);
// });
/*
✅ Upload thành công: {
  file: {
    file_id: 283090847965361,
    bytes: 4551940,
    created_at: 1750668821,
    filename: 'nu-tm.wav',
    purpose: 'voice_clone'
  },
  base_resp: { status_code: 0, status_msg: 'success' }
}
*/


// === Gọi API voice_clone ===
const url = `https://api.minimax.io/v1/voice_clone?GroupId=${groupId}`;

const headers = {
  'Authorization': `Bearer ${apiKey}`,
  'Content-Type': 'application/json'
};

const payload = {
  file_id: 283090847965361,
  voice_id: 'voice_nutm_283090847965361'
};

axios.post(url, payload, { headers })
  .then(response => {
    console.log('✅ Voice clone thành công:', response.data);
  })
  .catch(error => {
    console.error('❌ Lỗi gọi voice_clone:', error.response?.data || error.message);
  });

/*
✅ Voice clone thành công: {
  input_sensitive: false,
  input_sensitive_type: 0,
  demo_audio: '',
  base_resp: { status_code: 0, status_msg: 'success' }
}
*/