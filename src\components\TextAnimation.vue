<template>
        <a-form-item label="Text Animation/watermark">
          <a-checkbox v-model:checked="renderOptions.showText">
            Show text Animation
          </a-checkbox>
          <div v-if="renderOptions.showText" class="mt-2">



            <a-row :gutter="16">
              <a-col :span="6">
                <a-form-item label="Text">
                  <a-input
                    v-model:value="renderOptions.textValue"
                    type="text"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="Font Size">
                  <a-input-number
                    v-model:value="renderOptions.fontSize"
                    :min="12"
                    :max="72"
                    addon-after="px"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="Text Color">
                  <a-input
                    v-model:value="renderOptions.textColor"
                    type="color"
                  />
                </a-form-item>
              </a-col>
              <!-- opacity -->
              <a-col :span="6">
                <a-form-item label="Opacity">
                  <a-slider
                    v-model:value="renderOptions.textOpacity"
                    :min="0"
                    :max="100"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <!-- Font Selection -->
            <a-row :gutter="16" class="mb-4">
              <!-- direction -->
              <a-col :span="6">
                <a-form-item label="Hướng di chuyển">
                  <a-select v-model:value="renderOptions.textDirection">
                    <a-select-option value="random">Random</a-select-option>
                    <a-select-option value="updown">Up & Down</a-select-option>
                    <a-select-option value="leftright">Left & Right</a-select-option>
                    <a-select-option value="diagonal">Diagonal</a-select-option>
                    <a-select-option value="all">All</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <!-- text speed -->
              <a-col :span="6">
                <a-form-item label="Text Speed">
                  <a-slider
                    v-model:value="renderOptions.textSpeed"
                    :min="30"
                    :max="120"
                  />
                </a-form-item>
              </a-col>
                <a-col :span="12">
                    <a-form-item label="Font Family">
                        <a-select
                            v-model:value="renderOptions.textFontFamily"
                            placeholder="Select font"
                            :loading="!fontService.isReady()"
                        >
                            <a-select-option
                                v-for="font in fontService.getFonts()"
                                :key="font.name"
                                :value="font.name"
                            >
                                <span :style="{ fontFamily: font.isSystem ? font.name : (loadedFonts.has(font.path) ? font.name : 'inherit') }">
                                    {{ font.name }}
                                    <span v-if="!font.isSystem" class="text-gray-400 text-xs ml-2">(Custom)</span>
                                </span>
                            </a-select-option>
                        </a-select>
                    </a-form-item>
                </a-col>
            </a-row>
          </div>
        </a-form-item>
</template>
<script setup>
import { ref, onMounted } from 'vue';
import { useSubtitleStore } from '@/stores/subtitle-store';
import fontService from '@/services/fontService';

const subtitleStore = useSubtitleStore();
const renderOptions = subtitleStore.renderOptions;

// Font loading state
const loadedFonts = ref(new Set());

const loadFontForPreview = (font) => {
  if (!font || font.isSystem || loadedFonts.value.has(font.path)) return;

  const fontName = font.name;
  const fontUrl = `http://localhost:8082/fonts/${font.path}`;
  const fontFace = new FontFace(fontName, `url('${fontUrl}')`);

  fontFace.load().then(() => {
    document.fonts.add(fontFace);
    loadedFonts.value.add(font.path);
    console.log(`Font loaded: ${fontName}`);
  }).catch(error => {
    console.error(`Error loading font ${font.path}:`, error);
  });
};

// Load fonts on component mount
onMounted(async () => {
    await fontService.loadFonts();

    // Preload all fonts for preview
    fontService.getFonts().forEach(font => {
      loadFontForPreview(font);
    });
});
</script>



