const fs = require('fs-extra');
const path = require('path');

/**
 * Normal Memory implementation for storing conversation history
 */
class NormalMemory {
    constructor(serializePath = null) {
        this.messages = [];
        this.serializePath = serializePath;
        this.enterIndex = null;
        
        // Load from file if path exists
        if (this.serializePath && fs.existsSync(this.serializePath)) {
            this.load();
        }
    }

    /**
     * Add a message to memory
     * @param {string} role - 'user', 'assistant', or 'system'
     * @param {string|Array} content - Message content
     */
    addMessage(role, content) {
        const message = {
            role: role,
            content: content,
            timestamp: new Date().toISOString()
        };
        
        this.messages.push(message);
        this.save();
    }

    /**
     * Get all messages
     * @returns {Array}
     */
    getMessages() {
        return [...this.messages];
    }

    /**
     * Get recent messages
     * @param {number} count 
     * @returns {Array}
     */
    getRecentMessages(count) {
        return this.messages.slice(-count);
    }

    /**
     * Clear all messages
     */
    clear() {
        this.messages = [];
        this.save();
    }

    /**
     * Save memory to file
     */
    save() {
        if (!this.serializePath) return;
        
        try {
            const dir = path.dirname(this.serializePath);
            fs.ensureDirSync(dir);
            fs.writeJsonSync(this.serializePath, {
                messages: this.messages,
                enterIndex: this.enterIndex
            });
        } catch (error) {
            console.error('Error saving memory:', error);
        }
    }

    /**
     * Load memory from file
     */
    load() {
        if (!this.serializePath || !fs.existsSync(this.serializePath)) return;
        
        try {
            const data = fs.readJsonSync(this.serializePath);
            this.messages = data.messages || [];
            this.enterIndex = data.enterIndex || null;
        } catch (error) {
            console.error('Error loading memory:', error);
            this.messages = [];
        }
    }

    /**
     * Create a checkpoint for temporary context
     */
    enter() {
        this.enterIndex = this.messages.length;
    }

    /**
     * Recover to checkpoint, removing temporary messages
     */
    recover() {
        if (this.enterIndex !== null) {
            this.messages = this.messages.slice(0, this.enterIndex);
            this.enterIndex = null;
            this.save();
        }
    }

    /**
     * Get memory size (number of messages)
     * @returns {number}
     */
    size() {
        return this.messages.length;
    }

    /**
     * Remove last N messages
     * @param {number} count 
     */
    removeLast(count = 1) {
        this.messages = this.messages.slice(0, -count);
        this.save();
    }
}

module.exports = { NormalMemory };