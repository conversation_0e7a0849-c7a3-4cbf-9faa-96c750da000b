// Service for extracting chapter content and generating summaries
const { initWebService } = require('../playwright/playwrightLogic');
const { runAgent } = require('../agent/summary');

class ChapterContentService {
    constructor(database) {
        this.db = database;
        this.isProcessing = false;
        this.currentProgress = {
            current: 0,
            total: 0,
            status: 'idle',
            currentChapter: null,
            errors: []
        };
    }

    // Extract content from a single chapter with anti-Cloudflare measures
    async extractChapterContent(chapterUrl, previousUrl = null) {
        try {
            // Initialize browser if needed
            if (!global.S || !global.S.page) {
                await initWebService();
            }

            console.log(`📖 Extracting content from: ${chapterUrl}`);

            // Set referer header to avoid Cloudflare detection
            const headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
                'Accept-Encoding': 'gzip, deflate, br',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            };

            // Add referer if we have previous URL
            if (previousUrl) {
                headers['Referer'] = previousUrl;
                console.log(`🔗 Using referer: ${previousUrl}`);
            }

            await global.S.page.setExtraHTTPHeaders(headers);

            // Navigate to chapter page with referer
            await global.S.page.goto(chapterUrl, {
                waitUntil: 'networkidle',
                timeout: 30000,
                referer: previousUrl || undefined
            });

            // Handle Cloudflare protection if detected
            await this.handleCloudflareIfNeeded();

            // Wait for content to load
            await global.S.page.waitForTimeout(2000);

            // Extract content using the provided selector logic
            const content = await global.S.page.evaluate(() => {
                const article = document.querySelector('article.txtBody');
                if (!article) {
                    console.error('Không tìm thấy phần tử article.txtBody');
                    return null;
                }

                // Remove unwanted elements
                const selectorsToRemove = ['h1', 'div.wenzidaxiao', 'div#article_tip'];
                selectorsToRemove.forEach(selector => {
                    const el = article.querySelector(selector);
                    if (el) el.remove();
                });

                const text = article.innerText.trim();
                return text;
            });

            if (!content) {
                throw new Error('No content found in article.txtBody');
            }

            console.log(`✅ Content extracted: ${content.length} characters`);
            return content;

        } catch (error) {
            console.error(`❌ Error extracting content from ${chapterUrl}:`, error.message);
            throw error;
        }
    }

    // Handle Cloudflare protection detection
    async handleCloudflareIfNeeded() {
        try {
            await global.S.page.waitForTimeout(1000);

            const isCloudflare = await global.S.page.evaluate(() => {
                const indicators = [
                    'Verifying you are human',
                    'Checking your browser',
                    'DDoS protection by Cloudflare',
                    'Please wait while we check your browser',
                    'This process is automatic',
                    'cf-browser-verification',
                    'cf-challenge-running',
                    'cloudflare'
                ];

                const pageText = document.body.innerText.toLowerCase();
                const pageHTML = document.documentElement.innerHTML.toLowerCase();

                return indicators.some(indicator =>
                    pageText.includes(indicator.toLowerCase()) ||
                    pageHTML.includes(indicator.toLowerCase())
                );
            });

            if (isCloudflare) {
                console.log('🛡️ Cloudflare protection detected, waiting for bypass...');

                // Wait for Cloudflare to complete (up to 30 seconds)
                let attempts = 0;
                const maxAttempts = 30;

                while (attempts < maxAttempts) {
                    await global.S.page.waitForTimeout(1000);
                    attempts++;

                    const stillCloudflare = await global.S.page.evaluate(() => {
                        const indicators = [
                            'Verifying you are human',
                            'Checking your browser',
                            'cf-browser-verification',
                            'cf-challenge-running'
                        ];

                        const pageText = document.body.innerText.toLowerCase();
                        return indicators.some(indicator =>
                            pageText.includes(indicator.toLowerCase())
                        );
                    });

                    if (!stillCloudflare) {
                        console.log('✅ Cloudflare protection bypassed successfully');
                        break;
                    }

                    if (attempts % 5 === 0) {
                        console.log(`⏳ Still waiting for Cloudflare bypass... (${attempts}/${maxAttempts}s)`);
                    }
                }

                if (attempts >= maxAttempts) {
                    console.log('⚠️ Cloudflare bypass timeout - continuing anyway');
                }

                // Wait a bit more for page to stabilize
                await global.S.page.waitForTimeout(2000);
            }

        } catch (error) {
            console.log('Error handling Cloudflare protection:', error.message);
            // Continue anyway
        }
    }

    // Generate summary for text content
    async generateSummary(text) {
        try {
            console.log(`🤖 Generating summary for text (${text.length} characters)...`);
            
            const summary = await runAgent(text);
            
            console.log(`✅ Summary generated: ${summary.length} characters`);
            return summary;
            
        } catch (error) {
            console.error('❌ Error generating summary:', error.message);
            throw error;
        }
    }

    // Extract chapter number from title (e.g., "第576章 执掌仙神禁区" -> 576)
    extractChapterNumber(title) {
        try {
            // Match patterns like "第576章", "Chapter 576", "576章", etc.
            const patterns = [
                /第(\d+)章/,           // 第576章
                /Chapter\s*(\d+)/i,    // Chapter 576
                /(\d+)章/,             // 576章
                /第(\d+)回/,           // 第576回
                /(\d+)回/,             // 576回
                /^(\d+)[\.\s]/,        // 576. or 576
                /第(\d+)节/,           // 第576节
                /(\d+)节/              // 576节
            ];

            for (const pattern of patterns) {
                const match = title.match(pattern);
                if (match) {
                    return parseInt(match[1], 10);
                }
            }

            // Fallback: try to find any number in the title
            const numberMatch = title.match(/(\d+)/);
            if (numberMatch) {
                return parseInt(numberMatch[1], 10);
            }

            return null;
        } catch (error) {
            console.warn(`Failed to extract chapter number from: ${title}`);
            return null;
        }
    }

    // Process chapters in range with content extraction and summarization
    async processChaptersRange(bookId, startIndex, endIndex, options = {}) {
        const {
            extractContent = true,
            generateSummaries = true,
            delay = 10000, // 10 seconds between chapters
            onProgress = null
        } = options;

        if (this.isProcessing) {
            throw new Error('Another processing operation is already running');
        }

        this.isProcessing = true;
        this.currentProgress = {
            current: 0,
            total: 0,
            status: 'starting',
            currentChapter: null,
            errors: []
        };

        try {
            // Get all chapters and extract real chapter numbers
            const allChapters = await this.db.chapters.getChaptersByBookId(bookId);

            // Add real chapter number to each chapter
            const chaptersWithNumbers = allChapters.map(chapter => ({
                ...chapter,
                realChapterNumber: this.extractChapterNumber(chapter.title)
            })).filter(chapter => chapter.realChapterNumber !== null); // Only keep chapters with valid numbers

            // Filter by real chapter numbers and sort
            const filteredChapters = chaptersWithNumbers
                .filter(chapter => chapter.realChapterNumber >= startIndex && chapter.realChapterNumber <= endIndex)
                .sort((a, b) => a.realChapterNumber - b.realChapterNumber); // Sort by real chapter number

            this.currentProgress.total = filteredChapters.length;
            this.currentProgress.status = 'processing';

            console.log(`🚀 Processing ${filteredChapters.length} chapters (${startIndex} to ${endIndex}) in correct order`);
            console.log(`📋 Chapter sequence: ${filteredChapters.map(c => `${c.realChapterNumber}(ID:${c.id})`).join(', ')}`);

            let previousUrl = null; // Track previous URL for referer

            for (let i = 0; i < filteredChapters.length; i++) {
                const chapter = filteredChapters[i];
                this.currentProgress.current = i + 1;
                this.currentProgress.currentChapter = chapter;

                try {
                    console.log(`\n📖 Processing Chapter ${chapter.realChapterNumber} (ID: ${chapter.id}): ${chapter.title}`);
                    console.log(`🔗 URL: ${chapter.full_url}`);
                    if (previousUrl) {
                        console.log(`🔙 Referer: ${previousUrl}`);
                    }

                    let originalText = null;
                    let summaryText = null;

                    // Extract content if requested
                    if (extractContent) {
                        this.currentProgress.status = `extracting_${chapter.realChapterNumber}`;

                        // Use previous URL as referer to avoid Cloudflare detection
                        originalText = await this.extractChapterContent(chapter.full_url, previousUrl);

                        // Save original text to database
                        await this.db.chapterSummaries.upsertSummary(
                            chapter.id,
                            bookId,
                            originalText,
                            null
                        );

                        console.log(`💾 Content saved: ${originalText.length} characters`);
                    }

                    // Generate summary if requested
                    if (generateSummaries && originalText) {
                        this.currentProgress.status = `summarizing_${chapter.realChapterNumber}`;

                        // Update status to processing
                        await this.db.chapterSummaries.updateStatus(chapter.id, 'processing');

                        summaryText = await this.generateSummary(originalText);

                        // Save summary to database
                        await this.db.chapterSummaries.upsertSummary(
                            chapter.id,
                            bookId,
                            null,
                            summaryText
                        );

                        console.log(`📝 Summary saved: ${summaryText.length} characters`);
                    }

                    console.log(`✅ Chapter ${chapter.realChapterNumber} completed successfully`);

                    // Update previousUrl for next chapter's referer
                    previousUrl = chapter.full_url;

                    // Call progress callback if provided
                    if (onProgress) {
                        onProgress({
                            ...this.currentProgress,
                            chapter: chapter,
                            originalText: originalText,
                            summaryText: summaryText
                        });
                    }

                    // Delay between chapters (except for the last one)
                    if (i < filteredChapters.length - 1) {
                        console.log(`⏳ Waiting ${delay/1000} seconds before next chapter...`);
                        await new Promise(resolve => setTimeout(resolve, delay));
                    }

                } catch (error) {
                    console.error(`❌ Error processing chapter ${chapter.realChapterNumber} (ID: ${chapter.id}):`, error.message);

                    // Update status to failed
                    await this.db.chapterSummaries.updateStatus(
                        chapter.id,
                        'failed',
                        error.message
                    );

                    this.currentProgress.errors.push({
                        chapter: chapter.realChapterNumber,
                        chapterId: chapter.id,
                        title: chapter.title,
                        error: error.message
                    });

                    // Continue with next chapter
                    continue;
                }
            }

            this.currentProgress.status = 'completed';
            console.log(`\n🎉 Processing completed! ${filteredChapters.length - this.currentProgress.errors.length}/${filteredChapters.length} chapters successful`);

            if (this.currentProgress.errors.length > 0) {
                console.log(`⚠️ Errors encountered:`);
                this.currentProgress.errors.forEach(error => {
                    console.log(`   Chapter ${error.chapter}: ${error.error}`);
                });
            }

            return {
                success: true,
                processed: filteredChapters.length - this.currentProgress.errors.length,
                total: filteredChapters.length,
                errors: this.currentProgress.errors
            };

        } catch (error) {
            this.currentProgress.status = 'failed';
            console.error('❌ Processing failed:', error.message);
            throw error;
        } finally {
            this.isProcessing = false;
        }
    }

    // Get current processing progress
    getProgress() {
        return { ...this.currentProgress };
    }

    // Stop current processing
    async stopProcessing() {
        if (this.isProcessing) {
            this.currentProgress.status = 'stopping';
            this.isProcessing = false;
            console.log('🛑 Processing stopped by user');
        }
    }

    // Get chapters without content for a book
    async getChaptersWithoutContent(bookId, startIndex = null, endIndex = null) {
        return await this.db.chapterSummaries.getChaptersWithoutSummaries(bookId, startIndex, endIndex);
    }

    // Get summary statistics for a book
    async getSummaryStats(bookId) {
        return await this.db.chapterSummaries.getSummaryStats(bookId);
    }

    // Export summaries
    async exportSummaries(bookId, format = 'json') {
        return await this.db.chapterSummaries.getCompletedSummaries(bookId, format);
    }
}

module.exports = ChapterContentService;
