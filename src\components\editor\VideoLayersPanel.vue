<template>
  <div class="video-layers-panel bg-gray-800 text-white p-3 h-full flex flex-col">
    <!-- Header -->
    <div class="flex items-center justify-between mb-3">
      <h3 class="text-sm font-medium">Layers & Effects</h3>
      <div class="flex items-center gap-1">
        <a-button size="small" @click="addTextLayer" title="Add Text">
          <template #icon>
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
              <polyline points="14,2 14,8 20,8"/>
              <line x1="16" y1="13" x2="8" y2="13"/>
              <line x1="16" y1="17" x2="8" y2="17"/>
            </svg>
          </template>
        </a-button>
        
        <a-button size="small" @click="addImageLayer" title="Add Image">
          <template #icon>
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
              <circle cx="8.5" cy="8.5" r="1.5"/>
              <polyline points="21,15 16,10 5,21"/>
            </svg>
          </template>
        </a-button>
        
        <a-button size="small" @click="addEffectLayer" title="Add Effect">
          <template #icon>
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="3"/>
              <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>
            </svg>
          </template>
        </a-button>
      </div>
    </div>
    
    <!-- Layers List -->
    <div class="flex-1 overflow-auto">
      <div class="space-y-1">
        <div
          v-for="(layer, index) in sortedLayers"
          :key="index"
          class="layer-item bg-gray-700 rounded p-2 cursor-pointer hover:bg-gray-600 transition-colors"
          :class="{ 'ring-2 ring-blue-500': selectedLayerId === layer.id }"
          @click="selectLayer(layer.id)"
        >
          <!-- Layer Header -->
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-2 flex-1">
              <!-- Layer Type Icon -->
              <div class="layer-icon">
                <svg v-if="layer.type === 'video'" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <polygon points="23 7 16 12 23 17 23 7"/>
                  <rect x="1" y="5" width="15" height="14" rx="2" ry="2"/>
                </svg>
                <svg v-else-if="layer.type === 'subtitle'" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                </svg>
                <svg v-else-if="layer.type === 'text'" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <polyline points="4,7 4,4 20,4 20,7"/>
                  <line x1="9" y1="20" x2="15" y2="20"/>
                  <line x1="12" y1="4" x2="12" y2="20"/>
                </svg>
                <svg v-else-if="layer.type === 'image'" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                  <circle cx="8.5" cy="8.5" r="1.5"/>
                  <polyline points="21,15 16,10 5,21"/>
                </svg>
                <svg v-else width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                </svg>
              </div>
              
              <!-- Layer Name -->
              <span class="text-sm font-medium truncate">{{ layer.name }}</span>
              
              <!-- Lock Icon -->
              <svg v-if="layer.locked" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="text-yellow-500">
                <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
                <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
              </svg>
            </div>
            
            <!-- Layer Controls -->
            <div class="flex items-center gap-1">
              <!-- Visibility Toggle -->
              <button
                @click.stop="layersStore.toggleLayer(layer.id)"
                class="p-1 hover:bg-gray-500 rounded"
                :class="{ 'text-blue-400': layer.enabled, 'text-gray-500': !layer.enabled }"
              >
                <svg v-if="layer.enabled" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                  <circle cx="12" cy="12" r="3"/>
                </svg>
                <svg v-else width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/>
                  <line x1="1" y1="1" x2="23" y2="23"/>
                </svg>
              </button>
              
              <!-- Delete Button -->
              <button
                v-if="!layer.locked && layer.type !== 'video'"
                @click.stop="deleteLayer(layer.id)"
                class="p-1 hover:bg-red-600 rounded text-red-400"
              >
                <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <polyline points="3,6 5,6 21,6"/>
                  <path d="M19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"/>
                </svg>
              </button>
            </div>
          </div>
          
          <!-- Layer Properties (when selected) -->
          <div v-if="selectedLayerId === layer.id" class="mt-2 pt-2 border-t border-gray-600" @click.stop>
            <LayerProperties :layer="layer" @update="updateLayerProperty" />
          </div>
        </div>
      </div>
    </div>
    
    <!-- Effects Section -->
    <div class="mt-4 pt-3 border-t border-gray-600">
      <h4 class="text-xs font-medium text-gray-400 mb-2">EFFECTS</h4>
      
      <!-- Effect Buttons -->
      <div class="grid grid-cols-2 gap-2">
        <a-button size="small" @click="addDelogoEffect" class="text-xs">
          <template #icon>
            <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="10"/>
              <line x1="15" y1="9" x2="9" y2="15"/>
              <line x1="9" y1="9" x2="15" y2="15"/>
            </svg>
          </template>
          Delogo
        </a-button>
        
        <a-button size="small" @click="addBlurEffect" class="text-xs">
          <template #icon>
            <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="3"/>
              <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>
            </svg>
          </template>
          Blur
        </a-button>
      </div>
      
      <!-- Active Effects List -->
      <div v-if="activeEffects.length > 0" class="mt-2 space-y-1">
        <div
          v-for="effect in activeEffects"
          :key="effect.id"
          class="flex items-center justify-between bg-gray-700 rounded px-2 py-1 text-xs"
        >
          <span>{{ effect.type }} {{ effect.id.split('-')[1] }}</span>
          <button
            @click="removeEffect(effect.type, effect.id)"
            class="text-red-400 hover:text-red-300"
          >
            <svg width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <line x1="18" y1="6" x2="6" y2="18"/>
              <line x1="6" y1="6" x2="18" y2="18"/>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useVideoLayersStore } from '@/stores/video-layers-store'
import LayerProperties from './LayerProperties.vue'

const layersStore = useVideoLayersStore()

// Local state
const selectedLayerId = ref(null)

// Computed
const sortedLayers = computed(() => layersStore.sortedLayers.slice().reverse()) // Reverse for UI display
const activeEffects = computed(() => {
  const effects = layersStore.activeEffects
  return [
    ...effects.delogo.map(e => ({ ...e, type: 'delogo' })),
    ...effects.blur.map(e => ({ ...e, type: 'blur' })),
    ...effects.text.map(e => ({ ...e, type: 'text' })),
    ...effects.image.map(e => ({ ...e, type: 'image' }))
  ]
})

// Methods
const selectLayer = (id) => {
  selectedLayerId.value = selectedLayerId.value === id ? null : id
}

const updateLayerProperty = (property, value) => {
  if (selectedLayerId.value) {
    layersStore.updateLayerProperty(selectedLayerId.value, property, value)
  }
}

const deleteLayer = (id) => {
  layersStore.removeLayer(id)
  if (selectedLayerId.value === id) {
    selectedLayerId.value = null
  }
}

const addTextLayer = () => {
  const id = layersStore.addLayer({
    type: 'text',
    name: 'Text Layer',
    properties: {
      text: 'Sample Text',
      fontSize: 32,
      color: '#ffffff',
      fontFamily: 'Arial',
      position: { x: 50, y: 50 },
      alignment: 'center'
    }
  })
  selectedLayerId.value = id
}

const addImageLayer = () => {
  const id = layersStore.addLayer({
    type: 'image',
    name: 'Image Layer',
    properties: {
      src: '',
      position: { x: 50, y: 50 },
      scale: 100,
      rotation: 0,
      opacity: 100
    }
  })
  selectedLayerId.value = id
}

const addEffectLayer = () => {
  const id = layersStore.addLayer({
    type: 'effect',
    name: 'Effect Layer',
    properties: {
      type: 'color-correction',
      brightness: 0,
      contrast: 1,
      saturation: 1,
      hue: 0
    }
  })
  selectedLayerId.value = id
}

const addDelogoEffect = () => {
  layersStore.addEffect('delogo', {
    x: 10,
    y: 10,
    width: 100,
    height: 50
  })
}

const addBlurEffect = () => {
  layersStore.addEffect('blur', {
    x: 10,
    y: 10,
    width: 100,
    height: 50,
    intensity: 10
  })
}

const removeEffect = (type, id) => {
  layersStore.removeEffect(type, id)
}
</script>

<style scoped>
.layer-item {
  transition: all 0.2s ease;
}

.layer-item:hover {
  transform: translateX(2px);
}

.layer-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}
</style>
