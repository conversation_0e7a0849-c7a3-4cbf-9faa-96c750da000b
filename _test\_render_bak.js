const processVideoSimplifiedV1 = async (event, videoPath, srtArray, outputDir, finalOutput, options = {}) => {
  if (!fs.existsSync(outputDir)) fs.mkdirSync(outputDir);
  const type = 'video-task';
  // 1. Tính duration cho audio từng đoạn
  for (const srt of srtArray) {
    srt.duration = await getAudioDuration(srt.audioUrl.replace('file://', ''));
    event?.sender?.send(type, {
      data: `🕐 Audio duration for segment ${srt.index}: ${srt.duration} seconds`,
      code: 0,
    });
  }

  // 2. Tính thời lượng video segment và speed ratio cho từng đoạn
  for (let i = 0; i < srtArray.length; i++) {
    const currentSrt = srtArray[i];
    const nextSrt = srtArray[i + 1];

    // Thời lượng video segment = từ start hiện tại đến start đoạn tiếp theo
    // Nếu là đoạn cuối thì dùng endTime - startTime
    const videoSegmentDuration = nextSrt
      ? nextSrt.startTime - currentSrt.startTime
      : currentSrt.endTime - currentSrt.startTime;

    currentSrt.videoSegmentDuration = videoSegmentDuration;

    // Tính speed ratio: nếu audio dài hơn video segment thì làm chậm
    if (currentSrt.duration > videoSegmentDuration) {
      currentSrt.speedRatio = videoSegmentDuration / currentSrt.duration; // < 1 = chậm lại
    } else {
      currentSrt.speedRatio = 1; // không thay đổi tốc độ
    }

    // Thời lượng cuối cùng của đoạn này
    currentSrt.finalDuration = Math.max(videoSegmentDuration, currentSrt.duration);
    const logs = `📊 Segment ${i}: video=${videoSegmentDuration.toFixed(2)}s, audio=${currentSrt.duration.toFixed(
      2,
    )}s, speed=${currentSrt.speedRatio.toFixed(3)}, final=${currentSrt.finalDuration.toFixed(2)}s`;
    console.log(logs);
    event?.sender?.send(type, {
      data: logs,
      code: 0,
    });
  }

  // 3. Chia thành nhiều batch nếu quá nhiều
  const batches = [];
  for (let i = 0; i < srtArray.length; i += BATCH_SIZE) {
    batches.push(srtArray.slice(i, i + BATCH_SIZE));
  }

  const batchVideoPaths = [];

  for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
    const batch = batches[batchIndex];
    const inputs = [`-i "${videoPath}"`];
    let videoFilters = [];
    let audioFilters = [];
    let currentTime = 0;
    batch.forEach((srt, index) => {
      const audioPath = srt.audioUrl.replace('file://', '');
      inputs.push(`-i "${audioPath}"`);

      const vStart = srt.startTime;
      const vDur = srt.videoSegmentDuration;
      const speedRatio = srt.speedRatio;
      const finalDur = srt.finalDuration;
      const audioIndex = index + 1;

      // Video filter với speed adjustment nếu cần
      if (speedRatio < 1) {
        // Làm chậm video
        videoFilters.push(
          `[0:v]trim=start=${vStart}:duration=${vDur},setpts=PTS-STARTPTS,setpts=PTS/${speedRatio},scale=1920:1080[v${index}]`,
        );
      } else {
        // Không thay đổi tốc độ
        videoFilters.push(`[0:v]trim=start=${vStart}:duration=${vDur},setpts=PTS-STARTPTS,scale=1920:1080[v${index}]`);
      }

      const volume = srt.mixVolume ?? 1;
      const boostDb = 14;
      const volumeMultiplier = Math.pow(10, boostDb / 20);
      // Trim audio gốc cho đoạn tương ứng
      audioFilters.push(
        `[0:a]atrim=start=${vStart}:duration=${vDur},asetpts=PTS-STARTPTS,dynaudnorm=f=150:g=15,adelay=${
          currentTime * 1000
        }|${currentTime * 1000}[ga${index}]`,
      );
      // audioFilters.push(`[${audioIndex}:a]dynaudnorm=f=150:g=15,volume=${volumeMultiplier},adelay=${currentTime * 1000}|${currentTime * 1000}[a${index}]`);
      audioFilters.push(
        `[${audioIndex}:a]dynaudnorm=f=150:g=15,volume=${volumeMultiplier},adelay=${currentTime * 1000}|${
          currentTime * 1000
        }[va${index}]`,
      );

      currentTime += finalDur;
    });

    const videoConcat = `${videoFilters.join('; ')}; ${batch.map((_, i) => `[v${i}]`).join('')}concat=n=${
      batch.length
    }:v=1:a=0[vout]`;
    // const audioMix = `${audioFilters.join('; ')}; ${batch.map((_, i) => `[a${i}]`).join('')}amix=inputs=${batch.length}:duration=longest[aout]`;
    const allAudioTags = batch
      .map((_, i) => `[ga${i}][va${i}]`)
      .flat()
      .join('');
    const audioMix = `${audioFilters.join('; ')}; ${allAudioTags}amix=inputs=${
      batch.length * 2
    }:duration=longest[aout]`;

    const filterComplex = `${videoConcat}; ${audioMix}`;

    const batchOutput = path.join(outputDir, `batch_${batchIndex}.mp4`);
    batchVideoPaths.push(batchOutput);
const encoder = await getEncoder();
    const ffmpegCmd =
      `ffmpeg ${inputs.join(' ')} ` +
      `-filter_complex "${filterComplex}" ` +
      `-map "[vout]" -map "[aout]" ` +
      `-c:v ${encoder} -preset fast -crf 23 ` +
      `-c:a aac -ar 44100 -ac 2 -b:a 192k ` +
      `-y "${batchOutput}"`;

    console.log(`🧩 Running FFmpeg for batch ${batchIndex + 1}/${batches.length}`);
    event?.sender?.send(type, {
      data: `🧩 Running FFmpeg for batch ${batchIndex + 1}/${batches.length}`,
      code: 0,
    });
    await execPromise(ffmpegCmd);
  }

  // 4. Gộp các batch lại bằng concat
  const concatListPath = path.join(outputDir, 'concat_list.txt');
  const concatListContent = batchVideoPaths.map((p) => `file '${p}'`).join('\n');
  fs.writeFileSync(concatListPath, concatListContent);
const encoder = await getEncoder();
  const concatCmd =
    `ffmpeg -f concat -safe 0 -i "${concatListPath}" ` +
    `-c:v ${encoder} -preset fast -crf 23 -c:a aac -b:a 192k -y "${finalOutput}"`;

  console.log('📦 Running final concat...');
  event?.sender?.send(type, {
    data: '📦 Running final concat...',
    code: 0,
  });
  await execPromise(concatCmd);

  console.log(`✅ Final video generated: ${finalOutput}`);
  event?.sender?.send(type, {
    data: `✅ Final video generated: ${finalOutput}`,
    code: 0,
  });
  // cleanup
  batchVideoPaths.forEach((p) => {
    if (fs.existsSync(p)) fs.unlinkSync(p);
  });
  if (fs.existsSync(concatListPath)) fs.unlinkSync(concatListPath);
  return finalOutput;
};