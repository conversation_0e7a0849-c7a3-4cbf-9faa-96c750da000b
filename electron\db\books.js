const moment = require('moment');

class Books {
    constructor(knex) {
        this.knex = knex;
    }

    async createTable() {
        return this.knex.schema.hasTable('books').then(async (exists) => {
            if (!exists) {
                // Create new table with all columns
                return this.knex.schema.createTable('books', (table) => {
                    table.increments('id').primary();
                    table.string('title', 500).notNullable();
                    table.string('author', 200).nullable();
                    table.string('category', 200).nullable(); // 分类
                    table.text('description').nullable();
                    table.string('source_url', 1000).notNullable();
                    table.string('base_url', 1000).nullable(); // Base URL for chapters
                    table.string('cover_image', 1000).nullable();
                    table.string('status', 50).defaultTo('ongoing'); // ongoing, completed, dropped
                    table.string('last_update_info', 200).nullable(); // 更新时间信息
                    table.integer('total_chapters').defaultTo(0);
                    table.integer('extracted_chapters').defaultTo(0);
                    table.string('extraction_session', 100).nullable();
                    table.timestamp('created_at').defaultTo(this.knex.fn.now());
                    table.timestamp('updated_at').defaultTo(this.knex.fn.now());
                    table.timestamp('last_extraction_at').nullable();

                    // Indexes
                    table.index(['title']);
                    table.index(['author']);
                    table.index(['status']);
                    table.unique(['source_url']); // Prevent duplicate books
                });
            } else {
                // Table exists, check if we need to add new columns
                await this.migrateTable();
            }
        });
    }

    async migrateTable() {
        try {
            // Check if category column exists
            const hasCategory = await this.knex.schema.hasColumn('books', 'category');
            if (!hasCategory) {
                console.log('Adding category column to books table...');
                await this.knex.schema.alterTable('books', (table) => {
                    table.string('category', 200).nullable();
                });
            }

            // Check if last_update_info column exists
            const hasLastUpdateInfo = await this.knex.schema.hasColumn('books', 'last_update_info');
            if (!hasLastUpdateInfo) {
                console.log('Adding last_update_info column to books table...');
                await this.knex.schema.alterTable('books', (table) => {
                    table.string('last_update_info', 200).nullable();
                });
            }

            // Check if memory column exists
            const hasMemory = await this.knex.schema.hasColumn('books', 'memory');
            if (!hasMemory) {
                console.log('Adding memory column to books table...');
                await this.knex.schema.alterTable('books', (table) => {
                    table.text('memory').nullable(); // Store novel translation/summary memory
                });
            }

            console.log('Books table migration completed.');
        } catch (error) {
            console.error('Error migrating books table:', error);
            throw error;
        }
    }

    // Insert a new book or get existing one - FIXED VERSION
    async insertBook(bookData) {
        console.log(`🔍 Checking for existing book: ${bookData.sourceUrl}`);

        // ALWAYS check first - no exceptions
        const existingBook = await this.getBookBySourceUrl(bookData.sourceUrl);

        if (existingBook) {
            console.log(`📚 Book already exists: ${existingBook.title} (ID: ${existingBook.id})`);

            // Update existing book with new information
            await this.updateBook(existingBook.id, {
                title: bookData.title || existingBook.title,
                author: bookData.author || existingBook.author,
                category: bookData.category || existingBook.category,
                description: bookData.description || existingBook.description,
                base_url: bookData.baseUrl || existingBook.base_url,
                cover_image: bookData.coverImage || existingBook.cover_image,
                status: bookData.status || existingBook.status,
                last_update_info: bookData.lastUpdate || existingBook.last_update_info,
                total_chapters: bookData.totalChapters || existingBook.total_chapters,
                extracted_chapters: bookData.extractedChapters || existingBook.extracted_chapters,
                extraction_session: bookData.extractionSession || existingBook.extraction_session,
                last_extraction_at: moment().format('YYYY-MM-DD HH:mm:ss')
            });

            console.log(`✅ Book updated: ${bookData.title}`);
            return existingBook.id;
        }

        // Only insert if book doesn't exist
        console.log(`➕ Creating new book: ${bookData.title}`);
        try {
            const [id] = await this.knex('books').insert({
                title: bookData.title,
                author: bookData.author || null,
                category: bookData.category || null,
                description: bookData.description || null,
                source_url: bookData.sourceUrl,
                base_url: bookData.baseUrl || null,
                cover_image: bookData.coverImage || null,
                status: bookData.status || 'ongoing',
                last_update_info: bookData.lastUpdate || null,
                total_chapters: bookData.totalChapters || 0,
                extracted_chapters: bookData.extractedChapters || 0,
                extraction_session: bookData.extractionSession || null,
                created_at: moment().format('YYYY-MM-DD HH:mm:ss'),
                updated_at: moment().format('YYYY-MM-DD HH:mm:ss')
            });

            console.log(`✅ New book created: ${bookData.title} (ID: ${id})`);
            return id;

        } catch (error) {
            // If still get constraint error, it means race condition
            if (error.code === 'SQLITE_CONSTRAINT_UNIQUE' || error.code === 'SQLITE_CONSTRAINT') {
                console.warn('⚠️ Race condition detected, getting existing book...');
                const raceBook = await this.getBookBySourceUrl(bookData.sourceUrl);
                if (raceBook) {
                    console.log(`🔄 Found book from race condition: ${raceBook.title} (ID: ${raceBook.id})`);
                    return raceBook.id;
                }
            }
            console.error('❌ Error inserting book:', error);
            throw error;
        }
    }

    // Update book information
    async updateBook(bookId, updateData) {
        return await this.knex('books')
            .where('id', bookId)
            .update({
                ...updateData,
                updated_at: moment().format('YYYY-MM-DD HH:mm:ss')
            });
    }

    // Update extraction progress
    async updateExtractionProgress(bookId, extractedChapters, totalChapters = null) {
        const updateData = {
            extracted_chapters: extractedChapters,
            last_extraction_at: moment().format('YYYY-MM-DD HH:mm:ss'),
            updated_at: moment().format('YYYY-MM-DD HH:mm:ss')
        };
        
        if (totalChapters !== null) {
            updateData.total_chapters = totalChapters;
        }
        
        return await this.knex('books')
            .where('id', bookId)
            .update(updateData);
    }

    // Get book by ID
    async getBookById(bookId) {
        return await this.knex('books')
            .where('id', bookId)
            .first();
    }

    // Get book by source URL
    async getBookBySourceUrl(sourceUrl) {
        return await this.knex('books')
            .where('source_url', sourceUrl)
            .first();
    }

    // Get all books with pagination
    async getAllBooks(limit = 50, offset = 0) {
        return await this.knex('books')
            .limit(limit)
            .offset(offset)
            .orderBy('updated_at', 'desc');
    }

    // Search books by title
    async searchBooksByTitle(title, limit = 20) {
        return await this.knex('books')
            .where('title', 'like', `%${title}%`)
            .limit(limit)
            .orderBy('title', 'asc');
    }

    // Get books by status
    async getBooksByStatus(status, limit = 50) {
        return await this.knex('books')
            .where('status', status)
            .limit(limit)
            .orderBy('updated_at', 'desc');
    }

    // Count total books
    async getBooksCount() {
        const result = await this.knex('books').count('id as count').first();
        return result.count;
    }

    // Delete book and its chapters
    async deleteBook(bookId) {
        // First delete all chapters of this book
        await this.knex('chapters').where('book_id', bookId).del();
        
        // Then delete the book
        return await this.knex('books').where('id', bookId).del();
    }

    // Get book statistics
    async getBookStats(bookId) {
        const book = await this.getBookById(bookId);
        if (!book) return null;

        const chapterCount = await this.knex('chapters')
            .where('book_id', bookId)
            .count('id as count')
            .first();

        const latestChapter = await this.knex('chapters')
            .where('book_id', bookId)
            .orderBy('chapter_index', 'desc')
            .first();

        return {
            ...book,
            actual_chapter_count: chapterCount.count,
            latest_chapter: latestChapter
        };
    }

    // Get book memory for novel translation/summarization
    async getBookMemory(bookId) {
        try {
            const result = await this.knex('books')
                .select('memory')
                .where('id', bookId)
                .first();

            return result ? { memory: result.memory || '' } : { memory: '' };
        } catch (error) {
            console.error('Error getting book memory:', error);
            throw error;
        }
    }

    // Update book memory for novel translation/summarization
    async updateBookMemory(data) {
        try {
            const { bookId, memory } = data;

            if (!bookId) {
                throw new Error('Book ID is required');
            }

            const updateData = {
                memory: memory || '',
                updated_at: moment().format('YYYY-MM-DD HH:mm:ss')
            };

            const result = await this.knex('books')
                .where('id', bookId)
                .update(updateData);

            if (result === 0) {
                throw new Error(`Book with ID ${bookId} not found`);
            }

            console.log(`✅ Book memory updated for book ID: ${bookId}, memory length: ${memory ? memory.length : 0} characters`);
            return { success: true, bookId, memoryLength: memory ? memory.length : 0 };
        } catch (error) {
            console.error('Error updating book memory:', error);
            throw error;
        }
    }

    // Clear book memory
    async clearBookMemory(bookId) {
        try {
            if (!bookId) {
                throw new Error('Book ID is required');
            }

            const result = await this.knex('books')
                .where('id', bookId)
                .update({
                    memory: '',
                    updated_at: moment().format('YYYY-MM-DD HH:mm:ss')
                });

            if (result === 0) {
                throw new Error(`Book with ID ${bookId} not found`);
            }

            console.log(`✅ Book memory cleared for book ID: ${bookId}`);
            return { success: true, bookId };
        } catch (error) {
            console.error('Error clearing book memory:', error);
            throw error;
        }
    }

    // Get books with memory statistics
    async getBooksWithMemoryStats(limit = 50, offset = 0) {
        try {
            const books = await this.knex('books')
                .select('*')
                .limit(limit)
                .offset(offset)
                .orderBy('updated_at', 'desc');

            // Add memory statistics
            const booksWithStats = books.map(book => ({
                ...book,
                memory_length: book.memory ? book.memory.length : 0,
                has_memory: !!(book.memory && book.memory.trim().length > 0)
            }));

            return booksWithStats;
        } catch (error) {
            console.error('Error getting books with memory stats:', error);
            throw error;
        }
    }
}

module.exports = Books;
