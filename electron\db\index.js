const knex = require('knex');
const moment = require('moment');
const Chapters = require('./chapters');
const Books = require('./books');
const ChapterSummaries = require('./chapter-summaries');
const GptRoles = require('./gpt_roles'); // Assuming you have a GptRoles class for managing GPT roles
const Config = require('./config'); // Assuming you have a Config class for managing configuration settings
const GptRolesAnli = require('./gpt_roles_anli'); // Assuming you have a GptRolesAnli class for managing GPT roles with ANLI

class Database {
    constructor(dbFilePath) {
        this.knex = knex({
            client: 'sqlite3',
            connection: {
                filename: dbFilePath,
            },
            useNullAsDefault: true,
        });

        this.chapters = new Chapters(this.knex);
        this.books = new Books(this.knex);
        this.chapterSummaries = new ChapterSummaries(this.knex);
        this.GptRoles = new GptRoles(this.knex); // Initialize GptRoles
        this.Config = new Config(this.knex); // Initialize Config
        this.GptRolesAnli = new GptRolesAnli(this.knex); // Initialize GptRolesAnli
    }

    async initializeTables() {
        try {
            console.log('Initializing database tables...');

            // Create books table first (chapters reference books)
            // This will also run migrations for existing tables
            await this.books.createTable();
            console.log('Books table initialized');

            // Create chapters table
            await this.chapters.createTable();
            console.log('Chapters table initialized');

            // Create chapter summaries table
            await this.chapterSummaries.createTable();
            console.log('Chapter summaries table initialized');
            this.knex.schema.hasTable('gpt_roles').then((exists) => {
                if (!exists) {
                    return this.knex.schema.createTable('gpt_roles', (table) => {
                        table.increments('id').primary();

                        table.string('name').defaultTo(''); //name
                        table.string('sub_name').defaultTo(''); //Remark
                        table.string('prompt').defaultTo(''); //System Presets
                        table.string('model').defaultTo(''); //GPT model
                        table.integer('status').defaultTo(0); //state
                        table.integer('token').defaultTo(0); // Token amount

                        table.timestamp('inserted_at').defaultTo(moment().format('YYYY-MM-DD HH:mm:ss'));
                        table.index(['name', 'inserted_at'], 'name_index');
                    });
                }
            }),
            this.knex.schema.hasTable('gpt_roles_anli').then((exists) => {
                if (!exists) {
                    return this.knex.schema.createTable('gpt_roles_anli', (table) => {
                        table.increments('id').primary();
                        table.integer('role_id').defaultTo(0); 

                        table.string('text').defaultTo(''); 

                        table.integer('type').defaultTo(0); 

                        table.timestamp('inserted_at').defaultTo(moment().format('YYYY-MM-DD HH:mm:ss'));
                        table.index(['role_id', 'inserted_at'], 'index');
                    });
                }
            }),
            this.knex.schema.hasTable('config').then((exists) => {
                if (!exists) {
                    return this.knex.schema.createTable('config', (table) => {
                        table.increments('id').primary();
                        table.string('name');
                        table.string('value');
                        table.timestamp('inserted_at').defaultTo(moment().format('YYYY-MM-DD HH:mm:ss'));
                    });
                }
            }),

            this.knex.schema.hasTable('model').then((exists) => {
                if (!exists) {
                    return this.knex.schema.createTable('model', (table) => {
                        table.string('hash').primary();
                        table.string('title');
                        table.string('model_name');
                        table.string('sha256');
                        table.string('filename');
                        table.json('config');
                        table.timestamp('inserted_at').defaultTo(this.knex.fn.now());
                    });
                }
            }),

            console.log('Database initialization completed successfully');
            return true;
        } catch (error) {
            console.error('Error initializing database tables:', error);
            throw error;
        }
    }

    // Helper method to save extraction results
    async saveExtractionResults(extractionData, bookInfo = null, options = {}) {
        try {
            const { batchSize = 50, logProgress = true } = options;
            const sessionId = `extraction_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
            let bookId = null;

            if (logProgress) {
                console.log(`💾 Starting database save for ${extractionData.chapters.length} chapters...`);
            }

            // Create or get book record if book info is provided
            if (bookInfo) {
                if (logProgress) {
                    console.log(`📚 Saving book: ${bookInfo.title}`);
                }
                bookId = await this.books.insertBook({
                    ...bookInfo,
                    extractionSession: sessionId,
                    extractedChapters: extractionData.totalChapters || extractionData.chapters.length
                });
                if (logProgress) {
                    console.log(`✅ Book saved with ID: ${bookId}`);
                }
            }

            // Add book_id and session to chapters
            const chaptersWithBookInfo = extractionData.chapters.map(chapter => ({
                ...chapter,
                book_id: bookId,
                extractionSession: sessionId,
                bookTitle: bookInfo?.title || null
            }));

            // Save chapters in batch with specified batch size
            if (logProgress) {
                console.log(`📖 Saving chapters in batches of ${batchSize}...`);
            }

            // Use advanced batch processing for large datasets
            const useAdvancedBatch = extractionData.chapters.length > 200;
            const result = useAdvancedBatch
                ? await this.chapters.insertChaptersBatchAdvanced(chaptersWithBookInfo, sessionId, { batchSize, logProgress })
                : await this.chapters.insertChaptersBatch(chaptersWithBookInfo, sessionId, batchSize);

            // Update book extraction progress if book exists
            if (bookId) {
                await this.books.updateExtractionProgress(
                    bookId,
                    extractionData.chapters.length,
                    extractionData.totalChapters
                );
                if (logProgress) {
                    console.log(`📊 Updated book progress: ${extractionData.chapters.length} chapters`);
                }
            }

            const successCount = Array.isArray(result) ? result.length : extractionData.chapters.length;

            if (logProgress) {
                console.log(`✅ Database save completed: ${successCount} chapters saved`);
            }

            return {
                sessionId,
                bookId,
                chaptersInserted: successCount,
                totalChapters: extractionData.chapters.length,
                success: true
            };

        } catch (error) {
            console.error('❌ Error saving extraction results:', error);
            throw error;
        }
    }

    // Get extraction statistics
    async getExtractionStats() {
        try {
            const totalBooks = await this.books.getBooksCount();
            const totalChapters = await this.chapters.getChaptersCount();
            const recentSessions = await this.chapters.getExtractionSessions(5);

            return {
                totalBooks,
                totalChapters,
                recentSessions
            };
        } catch (error) {
            console.error('Error getting extraction stats:', error);
            throw error;
        }
    }

    // Close database connection
    async close() {
        await this.knex.destroy();
    }
}

module.exports = Database;