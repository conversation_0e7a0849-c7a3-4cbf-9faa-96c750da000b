const { spawn } = require('child_process');

function edgeTTSCmd(cmd, args) {
    return new Promise((resolve, reject) => {
        const child = spawn(cmd, args);
        let stdoutData = '';
        let stderrData = '';
        child.stdout.on('data', (data) => {
            stdoutData += data.toString();
            console.log(data.toString());
        });
        child.stderr.on('data', (data) => {
            stderrData += data.toString();
            console.log(data.toString());
        });
        child.on('exit', (code) => {
            if (code !== 0) {
                console.log(`child process exited with code ${code}: ${stderrData}`);
                reject(new Error(`child process exited with code ${code}: ${stderrData}`));
            } else {
                resolve(stdoutData);
            }
        });
        child.on('error', (error) => {
            console.log(`Spawn error: ${error.message}`);
            reject(error);
        });
    });
}

module.exports = {
    edgeTTSCmd
};