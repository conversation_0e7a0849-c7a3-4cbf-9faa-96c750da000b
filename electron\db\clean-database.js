// Script to clean database and fix issues
const path = require('path');
const Database = require('./index');

async function cleanDatabase() {
    console.log('🧹 Cleaning Database...\n');
    
    const dbPath = path.join(__dirname, '../../data/novels.db');
    const db = new Database(dbPath);
    
    try {
        await db.initializeTables();
        console.log('✅ Database connected\n');
        
        // Show current state
        const stats = await db.getExtractionStats();
        console.log('📊 Current Database State:');
        console.log(`   Books: ${stats.totalBooks}`);
        console.log(`   Chapters: ${stats.totalChapters}`);
        console.log(`   Recent Sessions: ${stats.recentSessions.length}\n`);
        
        // Show all books
        const books = await db.books.getAllBooks(10);
        console.log('📚 Current Books:');
        books.forEach((book, index) => {
            console.log(`   ${index + 1}. ${book.title} (ID: ${book.id})`);
            console.log(`      URL: ${book.source_url}`);
            console.log(`      Chapters: ${book.extracted_chapters}/${book.total_chapters}`);
        });
        
        // Ask user what to do
        console.log('\n🔧 Cleanup Options:');
        console.log('1. Delete all books and chapters (fresh start)');
        console.log('2. Delete specific book by URL');
        console.log('3. Show book details');
        console.log('4. Exit without changes');
        
        // For now, let's provide a function to delete the problematic book
        const problemUrl = 'https://44xw.com/a/149/148289/';
        const problemBook = await db.books.getBookBySourceUrl(problemUrl);
        
        if (problemBook) {
            console.log(`\n🎯 Found problematic book: ${problemBook.title} (ID: ${problemBook.id})`);
            console.log('   This book is causing the constraint error');
            console.log('   Deleting it will allow fresh insertion...');
            
            // Delete the book and its chapters
            await db.books.deleteBook(problemBook.id);
            console.log('✅ Problematic book deleted');
            
            // Verify deletion
            const checkBook = await db.books.getBookBySourceUrl(problemUrl);
            if (!checkBook) {
                console.log('✅ Verification: Book successfully removed');
            } else {
                console.log('❌ Verification: Book still exists');
            }
            
        } else {
            console.log('\n✅ No problematic book found');
            console.log('   The constraint error might be resolved');
        }
        
        // Show final state
        const finalStats = await db.getExtractionStats();
        console.log('\n📊 Final Database State:');
        console.log(`   Books: ${finalStats.totalBooks}`);
        console.log(`   Chapters: ${finalStats.totalChapters}`);
        
    } catch (error) {
        console.error('❌ Error cleaning database:', error);
    } finally {
        await db.close();
        console.log('\n🔌 Database connection closed');
    }
}

async function deleteAllData() {
    console.log('🗑️ Deleting All Data...\n');
    
    const dbPath = path.join(__dirname, '../../data/novels.db');
    const db = new Database(dbPath);
    
    try {
        await db.initializeTables();
        
        // Delete all chapters first
        await db.knex('chapters').del();
        console.log('✅ All chapters deleted');
        
        // Delete all books
        await db.knex('books').del();
        console.log('✅ All books deleted');
        
        // Reset auto-increment
        await db.knex.raw('DELETE FROM sqlite_sequence WHERE name IN ("books", "chapters")');
        console.log('✅ Auto-increment reset');
        
        console.log('\n🎉 Database completely cleaned!');
        console.log('   You can now run extraction without constraint errors');
        
    } catch (error) {
        console.error('❌ Error deleting data:', error);
    } finally {
        await db.close();
    }
}

async function showBookDetails() {
    console.log('📖 Book Details...\n');
    
    const dbPath = path.join(__dirname, '../../data/novels.db');
    const db = new Database(dbPath);
    
    try {
        await db.initializeTables();
        
        const problemUrl = 'https://44xw.com/a/149/148289/';
        const book = await db.books.getBookBySourceUrl(problemUrl);
        
        if (book) {
            console.log('📚 Book Details:');
            Object.keys(book).forEach(key => {
                console.log(`   ${key}: ${book[key]}`);
            });
            
            // Show chapters count
            const chapters = await db.chapters.getChaptersByBookId(book.id);
            console.log(`\n📖 Chapters: ${chapters.length} found`);
            
        } else {
            console.log('❌ Book not found');
        }
        
    } catch (error) {
        console.error('❌ Error showing book details:', error);
    } finally {
        await db.close();
    }
}

// Command line interface
async function runClean() {
    const args = process.argv.slice(2);
    const action = args[0] || 'clean';
    
    switch (action) {
        case 'all':
            await deleteAllData();
            break;
        case 'details':
            await showBookDetails();
            break;
        case 'clean':
        default:
            await cleanDatabase();
            break;
    }
}

if (require.main === module) {
    console.log('🧹 Database Cleaner\n');
    console.log('Usage:');
    console.log('  node clean-database.js        # Clean problematic book');
    console.log('  node clean-database.js all    # Delete all data');
    console.log('  node clean-database.js details # Show book details');
    console.log('');
    
    runClean().then(() => {
        console.log('\n🏁 Cleanup completed');
        process.exit(0);
    }).catch(error => {
        console.error('\n💥 Cleanup failed:', error);
        process.exit(1);
    });
}

module.exports = { cleanDatabase, deleteAllData, showBookDetails };
