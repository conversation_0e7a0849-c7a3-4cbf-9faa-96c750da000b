<template>
  <div v-if="record.status !== 'translating' && editingId !== record.id" class="flex justify-end gap-1" @click.stop>
    <!-- Play button -->
    <a-button type="text" class="h-7 w-7 text-green-600 hover:text-green-700 hover:bg-green-50"
      :title="'Play'" @click.stop="$emit('play', record)" :disabled="translating">
      <template #icon v-if="!record?.isPlayable">
        <PlayCircleOutlined />
      </template>
      <template #icon v-else>
        <PauseOutlined />
      </template>
    </a-button>

    <!-- Edit and AI suggestion buttons for translated subtitles -->
    <template v-if="record.status === 'translated'">
      <!-- <a-button type="text" class="h-7 w-7 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
        :title="'Edit'" @click.stop="$emit('edit', record.id, record.translatedText)"
        :disabled="translating">
        <template #icon>
          <EditOutlined />
        </template>
      </a-button> -->

      <!-- AI suggestion button -->
      <!-- <a-button v-if="onSuggestTranslation" type="text"
        class="h-7 w-7 text-purple-600 hover:text-purple-700 hover:bg-purple-50"
        :title="'Suggest better translation'"
        @click.stop="$emit('suggest', record.id, record.text, record.translatedText)"
        :disabled="translating || loadingSuggestions">
        <template #icon>
          <ThunderboltOutlined />
        </template>
      </a-button> -->
    </template>

    <!-- Retry button for error status -->
    <a-button v-if="record.status === 'error'" type="text"
      class="h-7 w-7 text-rose-600 hover:text-rose-700 hover:bg-rose-50" :title="'Retry'"
      @click.stop="$emit('retry', record.id)"
      :disabled="translating || retryingBatch === Math.floor((record.id - 1) / batchSize)">
      <template #icon>
        <ReloadOutlined />
      </template>
    </a-button>

    <!-- Insert before button -->
    <a-dropdown :trigger="['click']" placement="bottomRight">
      <a-button type="text" class="h-7 w-7 text-cyan-600 hover:text-cyan-700 hover:bg-cyan-50"
        :title="'Insert options'" @click.stop>
        <template #icon>
          <PlusOutlined />
        </template>
      </a-button>
      <template #overlay>
        <a-menu>
          <a-menu-item key="insert-before" @click="$emit('insert-before', record)">
            <template #icon>
              <ArrowUpOutlined />
            </template>
            Chèn trước
          </a-menu-item>
          <a-menu-item key="insert-after" @click="$emit('insert-after', record)">
            <template #icon>
              <ArrowDownOutlined />
            </template>
            Chèn sau
          </a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>

    <!-- Split button -->
    <a-button type="text" class="h-7 w-7 text-orange-600 hover:text-orange-700 hover:bg-orange-50"
      :title="'Split subtitle'" @click.stop="$emit('split', record)">
      <template #icon>
        <ScissorOutlined />
      </template>
    </a-button>

    <!-- Merge button -->
    <a-button type="text" class="h-7 w-7 text-indigo-600 hover:text-indigo-700 hover:bg-indigo-50"
      :title="'Merge with next'" @click.stop="$emit('merge', record)">
      <template #icon>
        <MergeOutlined />
      </template>
    </a-button>

    <!-- Delete button -->
    <a-button type="text" class="h-7 w-7 text-red-600 hover:text-red-700 hover:bg-red-50"
      :title="'Delete'" @click.stop="$emit('delete', record.id)">
      <template #icon>
        <DeleteOutlined />
      </template>
    </a-button>
  </div>
</template>

<script>
import { defineComponent } from 'vue';
import {
  PlayCircleOutlined,
  PauseOutlined,
  EditOutlined,
  ThunderboltOutlined,
  ReloadOutlined,
  PlusOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  ScissorOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue';

// Import MergeOutlined from a different source or create a custom icon
import { MergeCellsOutlined as MergeOutlined } from '@ant-design/icons-vue';

export default defineComponent({
  name: 'SubtitleActions',
  components: {
    PlayCircleOutlined,
    PauseOutlined,
    EditOutlined,
    ThunderboltOutlined,
    ReloadOutlined,
    PlusOutlined,
    ArrowUpOutlined,
    ArrowDownOutlined,
    ScissorOutlined,
    MergeOutlined,
    DeleteOutlined
  },
  props: {
    record: {
      type: Object,
      required: true
    },
    editingId: {
      type: Number,
      default: null
    },
    translating: {
      type: Boolean,
      default: false
    },
    loadingSuggestions: {
      type: Boolean,
      default: false
    },
    retryingBatch: {
      type: Number,
      default: null
    },
    batchSize: {
      type: Number,
      default: 10
    },
    onSuggestTranslation: {
      type: Function,
      default: null
    }
  },
  emits: [
    'play',
    'edit',
    'suggest',
    'retry',
    'insert-before',
    'insert-after',
    'split',
    'merge',
    'delete'
  ]
});
</script>
