import { defineComponent, h } from 'vue'
import { TabsRoot, TabsList as TabsListPrimitive, <PERSON><PERSON><PERSON>rigger as TabsTriggerPrimitive, TabsContent as TabsContentPrimitive } from 'radix-vue'
import { cn } from '@/lib/utils'

// Re-export the TabsRoot as Tabs
export const Tabs = TabsRoot

// TabsList component
export const TabsList = defineComponent({
  name: 'TabsList',
  props: {
    class: String
  },
  setup(props, { slots, attrs }) {
    return () => h(
      TabsListPrimitive,
      {
        class: cn(
          "inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",
          props.class
        ),
        ...attrs
      },
      slots
    )
  }
})

// TabsTrigger component
export const TabsTrigger = defineComponent({
  name: 'TabsTrigger',
  props: {
    class: String
  },
  setup(props, { slots, attrs }) {
    return () => h(
      TabsTriggerPrimitive,
      {
        class: cn(
          "inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",
          props.class
        ),
        ...attrs
      },
      slots
    )
  }
})

// TabsContent component
export const TabsContent = defineComponent({
  name: 'TabsContent',
  props: {
    class: String
  },
  setup(props, { slots, attrs }) {
    return () => h(
      TabsContentPrimitive,
      {
        class: cn(
          "mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
          props.class
        ),
        ...attrs
      },
      slots
    )
  }
})