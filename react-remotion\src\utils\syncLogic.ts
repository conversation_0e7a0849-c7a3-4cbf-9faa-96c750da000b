// src/utils/syncLogic.ts
export interface SrtEntry {
  startTime: number;
  endTime: number;
  audioUrl: string;
  duration: number;
  mixVolume?: number;
}

export interface SyncedSegment extends SrtEntry {
  playbackRate: number;
  outputDuration: number;
}

export const calculateSyncSegments = (srtArray: SrtEntry[]): SyncedSegment[] => {
  return srtArray.map((srt) => {
    const videoDuration = srt.endTime - srt.startTime;
    const audioDuration = srt.duration;

    if (audioDuration > videoDuration) {
      const stretchFactor = audioDuration / videoDuration;
      return {
        ...srt,
        playbackRate: 1 / stretchFactor,
        outputDuration: audioDuration,
      };
    } else {
      return {
        ...srt,
        playbackRate: 1,
        outputDuration: videoDuration,
      };
    }
  });
};
