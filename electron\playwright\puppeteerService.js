const puppeteer = require('puppeteer-extra');
const StealthPlugin = require('puppeteer-extra-plugin-stealth');
const { getRandomImageKeyword, findChromePath } = require('./playwrightLogic');
const { waitForCaptchaResolution } = require('./humanLikeBehavior');
puppeteer.use(StealthPlugin());

async function initializeBrowser() {
    console.log('Initializing Puppeteer browser...');
    
    const baseDir = C.path.join(C.configDirectory || process.cwd(), 'chrome-data/');
    const browser = await puppeteer.launch({
        headless: false, // Set to true in production
        slowMo: 50,
        args: ['--no-sandbox', '--disable-setuid-sandbox', '--disable-dev-shm-usage'],
        userDataDir: baseDir,
        executablePath: findChromePath() || undefined, // Use custom Chrome path if available
    });
    browser.on('disconnected', async () => {
        F.l('Browser was closed by user');

        // Clean up sessions, temporary files, etc.
        await closeBrowser(browser);
    });
    return browser;
}
async function setUserAgent(page) {
    const userAgent =
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) ' +
        'AppleWebKit/537.36 (KHTML, like Gecko) ' +
        'Chrome/89.0.4389.82 Safari/537.36';
    await page.setUserAgent(userAgent);
}
async function handleConsentForm(page) {
    const agreeButtonSelectors = [
        'button#L2AGLb',
        'button[aria-label="Accept all"]',
        'button[aria-label*="consent"]',
        'div[role="none"] button + button',
    ];
    try {
        for (const selector of agreeButtonSelectors) {
            const agreeButton = await page.$(selector);
            if (agreeButton) {
                await agreeButton.click();
                await page.waitForNavigation({ waitUntil: 'networkidle2' });
                break;
            }
        }
    } catch (err) {
        F.l('No consent form found or error in handling consent:', err);
    }
}
async function navigateToPage(page, url = 'https://44xw.com/a/149/148289/') {
    await page.goto(url, { waitUntil: 'networkidle2' });
}
const runWebService = async (event, query = null) => {
    try {
        if (!S.browser) S.browser = await initializeBrowser();
        console.log('Browser initialized successfully.');
        
        S.page = await S.browser.newPage();
        const searchQuery = getRandomImageKeyword();
        await setUserAgent(S.page);
        console.log('User agent set successfully:', await S.page.evaluate(() => navigator.userAgent));
        await waitForCaptchaResolution(S.page);
        await navigateToPage(S.page);
        await handleConsentForm(S.page);
        // await takeScreenshot(page, "after_consent.png");
        // if (!query) await performSearch(null, searchQuery);
        let url = 'https://44xw.com/a/149/148289/';
        const res = await performExtraction(event, url);
        F.l(res);
        // const results = await extractResults(page);

        // return results;
    } catch (error) {
        console.error('Error in puppeteerService:', error);
        throw error;
    } finally {
        // if (browser) {
        //   await browser.close();
        // }
    }
};



async function closeBrowser() {
    if (S.browser) {
        await S.browser.close();
        S.browser = null;
        S.page = null;
    }
}

// Test function for the new extraction functionality using Playwright
const testExtraction = async (event, url = null) => {
    try {
        // Use Playwright's initWebService instead of Puppeteer's initializeBrowser
        const { initWebService, performExtraction } = require('./playwrightLogic');

        // Initialize Playwright web service
        await initWebService();
        console.log('Playwright web service initialized successfully.');

        // Use the new performExtraction function
        const extractedData = await performExtraction(event, url);

        console.log('Extraction Results:');
        console.log(`- Found ${extractedData.chapters.length} chapters`);
        console.log(`- Current page: ${extractedData.pagination.currentPage}`);
        console.log(`- Next pages available: ${extractedData.pagination.nextPages.length}`);
        console.log(`- Page title: ${extractedData.pageTitle}`);

        // Display book information if available
        if (extractedData.bookInfo) {
            console.log('\nBook Information:');
            console.log(`- Title: ${extractedData.bookInfo.title || 'N/A'}`);
            console.log(`- Author: ${extractedData.bookInfo.author || 'N/A'}`);
            console.log(`- Category: ${extractedData.bookInfo.category || 'N/A'}`);
            console.log(`- Status: ${extractedData.bookInfo.status || 'N/A'}`);
            console.log(`- Last Update: ${extractedData.bookInfo.lastUpdate || 'N/A'}`);

            if (extractedData.bookInfo.firstChapter) {
                console.log(`- First Chapter: ${extractedData.bookInfo.firstChapter.title}`);
            }
            if (extractedData.bookInfo.latestChapter) {
                console.log(`- Latest Chapter: ${extractedData.bookInfo.latestChapter.title}`);
            }
        }

        // Log first few chapters as example
        if (extractedData.chapters.length > 0) {
            console.log('\nFirst 5 chapters:');
            extractedData.chapters.slice(0, 5).forEach(chapter => {
                console.log(`${chapter.index}. ${chapter.title} - ${chapter.href}`);
            });
        }

        // Log pagination info
        if (extractedData.pagination.nextPages.length > 0) {
            console.log('\nNext pages:');
            extractedData.pagination.nextPages.forEach(page => {
                console.log(`Page ${page.pageNumber}: ${page.text} - ${page.href}`);
            });
        }

        return extractedData;

    } catch (error) {
        console.error('Error in testExtraction:', error);
        throw error;
    }
};

// Test function for multi-page extraction with database saving
const testMultiPageExtraction = async (event, startUrl = null, maxPages = 3, saveToDb = true) => {
    try {
        const { initWebService, performExtractionMultiplePages } = require('./playwrightLogic');

        // Initialize Playwright web service
        await initWebService();
        console.log('Playwright web service initialized for multi-page extraction.');

        // Use the multi-page extraction function
        const extractedData = await performExtractionMultiplePages(event, startUrl, maxPages);

        console.log('Multi-Page Extraction Results:');
        console.log(`- Total chapters: ${extractedData.totalChapters}`);
        console.log(`- Total pages processed: ${extractedData.totalPages}`);

        // Display book information if available
        if (extractedData.bookInfo) {
            console.log('\nBook Information:');
            console.log(`- Title: ${extractedData.bookInfo.title || 'N/A'}`);
            console.log(`- Author: ${extractedData.bookInfo.author || 'N/A'}`);
            console.log(`- Category: ${extractedData.bookInfo.category || 'N/A'}`);
            console.log(`- Status: ${extractedData.bookInfo.status || 'N/A'}`);
            console.log(`- Last Update: ${extractedData.bookInfo.lastUpdate || 'N/A'}`);
        }

        // Log chapters by page
        const chaptersByPage = {};
        extractedData.chapters.forEach(chapter => {
            if (!chaptersByPage[chapter.pageNumber]) {
                chaptersByPage[chapter.pageNumber] = [];
            }
            chaptersByPage[chapter.pageNumber].push(chapter);
        });

        Object.keys(chaptersByPage).forEach(pageNum => {
            console.log(`\nPage ${pageNum}: ${chaptersByPage[pageNum].length} chapters`);
            chaptersByPage[pageNum].slice(0, 3).forEach(chapter => {
                console.log(`  ${chapter.index}. ${chapter.title}`);
            });
        });

        // Save to database if requested and S.db is available
        if (saveToDb && S.db) {
            try {
                console.log('\n=== Saving to Database ===');

                // Initialize database tables if not already done
                await S.db.initializeTables();

                // Use extracted book information or create default
                const bookInfo = {
                    title: extractedData.bookInfo?.title ||
                           extractedData.pageTitle ||
                           'Unknown Novel',
                    author: extractedData.bookInfo?.author || null,
                    category: extractedData.bookInfo?.category || null,
                    description: null, // Could be extracted from page if available
                    sourceUrl: startUrl || 'https://44xw.com/a/149/148289/',
                    baseUrl: startUrl ? new URL(startUrl).origin : 'https://44xw.com',
                    status: extractedData.bookInfo?.status === '连载' ? 'ongoing' :
                           extractedData.bookInfo?.status === '完结' ? 'completed' : 'ongoing',
                    totalChapters: extractedData.totalChapters,
                    lastUpdate: extractedData.bookInfo?.lastUpdate || null
                };

                // Save extraction results to database with smaller batch size for large datasets
                const batchSize = extractedData.chapters.length > 100 ? 25 : 50;
                const saveResult = await S.db.saveExtractionResults(extractedData, bookInfo, {
                    batchSize: batchSize,
                    logProgress: true
                });

                console.log('Database Save Results:');
                console.log(`- Session ID: ${saveResult.sessionId}`);
                console.log(`- Book ID: ${saveResult.bookId}`);
                console.log(`- Chapters inserted: ${saveResult.chaptersInserted}`);
                console.log(`- Success: ${saveResult.success}`);

                // Get and display database statistics
                const stats = await S.db.getExtractionStats();
                console.log('\nDatabase Statistics:');
                console.log(`- Total books: ${stats.totalBooks}`);
                console.log(`- Total chapters: ${stats.totalChapters}`);
                console.log(`- Recent sessions: ${stats.recentSessions.length}`);

                return {
                    ...extractedData,
                    databaseResult: saveResult,
                    databaseStats: stats
                };

            } catch (dbError) {
                console.error('Error saving to database:', dbError);
                console.log('Continuing without database save...');
                return {
                    ...extractedData,
                    databaseError: dbError.message
                };
            }
        } else {
            if (!S.db) {
                console.log('\nNote: S.db not available, skipping database save');
            } else {
                console.log('\nNote: Database save disabled');
            }
        }

        return extractedData;

    } catch (error) {
        console.error('Error in testMultiPageExtraction:', error);
        throw error;
    }
};

module.exports = {
    runWebService,
    testExtraction,
    testMultiPageExtraction,
    navigateToPage
};