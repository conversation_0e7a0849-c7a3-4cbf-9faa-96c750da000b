class Config {
    constructor(knex) {
        this.knex = knex;
    }

    // 根据 name 查询 value
    async getValueByName(name) {
        const config = await this.knex.select('value').from('config').where('name', '=', name).first();
        if (config) {
            // 如果找到了配置，就返回它的 value
            return config.value;
        } else {
            // 如果没有找到配置，就返回空字符串
            return '';
        }
    }

    // 根据 name 更新 value
    async updateValueByName(name, value) {
        const config = await this.knex('config').where('name', '=', name).first();

        if (config) {
            // 如果找到了配置，就更新它
            return this.knex('config').where('name', '=', name).update({ value });
        } else {
            // 如果没有找到配置，就插入新的配置
            return this.knex('config').insert({ name, value });
        }
    }

    //获取MJ过期时间
    getPowerTime() {
        return this.getValueByName('powerTime');
    }

    //检测MJ是否过期
    async checkMjUserTime() {
        let res = await this.getPowerTime();
        if (res) {
            var expiryDate = new Date(res);
            var currentDate = new Date();
            if (expiryDate > currentDate) {
                return 1;
            }
        }
        return 0;
    }

    //获取小说自动翻译的配置
    async getBookFanyi() {
        let tmp = await this.getValueByName('book_fanyi_cn');
        if (tmp == '1') {
            return 1;
        }
        return 0;
    }
    //获取是都是新人
    async getNewPeople() {
        const config = await this.knex.select('value').from('config').where('name', '=', 'userNew').first();
        console.log(config, 'getNewPeople');
        if (config) {
            // 如果找到了配置，就返回它的 value
            return config.value;
        } else {
            // 如果没有找到配置，就返回空字符串
            return '';
        }
    }

    //获取小说自动翻译的配置 1 自动  0 关闭
    setBookFanyi(auto_type) {
        return this.updateValueByName('book_fanyi_cn', auto_type);
    }

    //获取小说自动翻译的配置
    async getBookGuanlian() {
        let tmp = await this.getValueByName('book_guanlian');
        if (tmp == '1') {
            return 1;
        }
        return 0;
    }

    async set_proxy_mode(mode) {
        return this.updateValueByName('porxy_mode', mode);
    }
    //获取系统代理开关
    async get_proxy_mode() {
        let mode = await this.getValueByName('porxy_mode');
        if (mode) {
            return parseInt(mode);
        }
        return 0;
    }

    //0 普通 1 会员 2 所有
    async set_jianyin_mode(mode) {
        return this.updateValueByName('jianyin_mode', mode);
    }
    //获取剪映模式
    async get_jianyin_mode() {
        let mode = await this.getValueByName('jianyin_mode');
        if (mode) {
            return parseInt(mode);
        }
        return 0;
    }

    async set_proxy_host(host) {
        return this.updateValueByName('porxy_host', host);
    }

    async set_proxy_port(port) {
        return this.updateValueByName('porxy_port', port);
    }

    async get_proxy_host() {
        return await this.getValueByName('porxy_host');
    }

    async get_proxy_port() {
        let port = await this.getValueByName('porxy_port');
        if (port) {
            return parseInt(port);
        }
        return 0;
    }

    //获取小说自动翻译的配置 1 自动  0 关闭
    setBookGuanlian(auto_type) {
        return this.updateValueByName('book_guanlian', auto_type);
    }

    //获取小说高清的方式
    async getSdConfig() {
        return await this.getValueByName('sd_config_book');
    }

    //获取设置高清的方式
    setSdConfig(data) {
        return this.updateValueByName('sd_config_book', data);
    }

    //获取小说高清的方式
    async getBookUpType() {
        return await this.getValueByName('book_upscal_type');
    }

    //获取设置高清的方式
    setBookUpType(auto_type) {
        return this.updateValueByName('book_upscal_type', auto_type);
    }

    //获取小说高清的模型
    async getBookUpModel() {
        return await this.getValueByName('book_upscale_model');
    }

    //获取设置高清模型
    setBookUpModel(model_name) {
        return this.updateValueByName('book_upscale_model', model_name);
    }

    //获取云当前的MJ频道
    async getMjMode() {
        let tmp = await this.getValueByName('mj_local_type');
        if (tmp == '1') {
            return 1;
        }
        return 0;
    }

    //获取是否切分模式
    async getMjImageSplitMode() {
        let tmp = await this.getValueByName('MjImageSplitMode');
        return tmp == '0' ? 0 : 1;
    }

    //打开切分模式
    async openMjImageSplitMode() {
        return await this.updateValueByName('MjImageSplitMode', 1);
    }

    //打开切分模式
    async closeMjImageSplitMode() {
        return await this.updateValueByName('MjImageSplitMode', 0);
    }

    setPikaMyLibraryCookies(cookies) {
        return this.updateValueByName('pika_my_library_cookies', cookies);
    }

    getPikaMyLibraryCookies() {
        return this.getValueByName('pika_my_library_cookies');
    }

    setPixMyLibraryCookies(cookies) {
        return this.updateValueByName('pix_my_library_cookies', cookies);
    }

    getPixMyLibraryCookies() {
        return this.getValueByName('pix_my_library_cookies');
    }

    setPikaSubscriptionsUrl(url) {
        return this.updateValueByName('pika_subscriptions_url', url);
    }

    getPikaSubscriptionsUrl() {
        return this.getValueByName('pika_subscriptions_url');
    }

    setPikaUserInfo(data) {
        return this.updateValueByName('pika_user_info', data);
    }

    getPikaUserInfo() {
        return this.getValueByName('pika_user_info');
    }

    setPikaSubscriptionsCookies(cookies) {
        return this.updateValueByName('pika_subscriptions_cookies', cookies);
    }

    getPikaSubscriptionsCookies() {
        return this.getValueByName('pika_subscriptions_cookies');
    }

    setPixUserPlanDetail(plan_detail) {
        return this.updateValueByName('pix_user_plan_detail', plan_detail);
    }

    async getPixUserPlanDetail() {
        return await this.getValueByName('pix_user_plan_detail');
    }

    //获取水印标志
    async getPixWaterMark() {
        let data = await this.getPixUserPlanDetail();
        if (data) {
            data = JSON.parse(data);
            if (data.price != '0') {
                return 1;
            } else {
                return 0;
            }
        }
        return 0;
    }

    //设置场景文件夹
    setSceneImg(flag) {
        return this.updateValueByName('scene_img_flag', flag);
    }

    async getSceneImgFlag() {
        let tmp = await this.getValueByName('scene_img_flag');
        if (tmp == '') {
            return 0;
        }
        return tmp;
    }

    setPikaGenCookies(cookies) {
        return this.updateValueByName('pika_gen_cookies', cookies);
    }

    getPikaGenCookies() {
        return this.getValueByName('pika_gen_cookies');
    }

    //设置pika的并发和超时
    async getPikaQueue() {
        let tmp = await this.getValueByName('pika_queue_config');
        if (tmp) {
            return JSON.parse(tmp);
        } else {
            return { pika_queue: 6, pika_timeout: 5 };
        }
    }

    //设置pika的并发和超时
    setPikaQueue(data) {
        return this.updateValueByName('pika_queue_config', data);
    }

    //设置云当前的MJ频道
    setMjMode(mode) {
        return this.updateValueByName('mj_local_type', mode);
    }

    //获取云当前的MJ频道
    getMjchannelId() {
        return this.getValueByName('mj_channelId');
    }

    //设置云当前的MJ频道
    setMjchannelId(channelId) {
        return this.updateValueByName('mj_channelId', channelId);
    }

    //获取云当前的MJ频道
    getLocalMjchannelId() {
        return this.getValueByName('local_mj_channelId');
    }

    //设置云当前的MJ频道
    setLocalMjchannelId(channelId) {
        return this.updateValueByName('local_mj_channelId', channelId);
    }

    //设置默认出图方式
    getImgType() {
        return this.getValueByName('mul_img_type');
    }

    //获取出图方式
    setImgType(img_type) {
        return this.updateValueByName('mul_img_type', img_type);
    }

    //默认fenjin_role_id
    get_fenjin_role_id() {
        return this.getValueByName('fenjin_role_id');
    }

    //默认fenjin_role_id
    set_fenjin_role_id(role_id) {
        return this.updateValueByName('fenjin_role_id', role_id);
    }

    //默认tuidao_role_id
    get_tuidao_role_id() {
        return this.getValueByName('tuidao_role_id');
    }

    //默认tuidao_role_id
    set_tuidao_role_id(role_id) {
        return this.updateValueByName('tuidao_role_id', role_id);
    }

    //判断MJ是否压缩图
    async isMjImageJpg() {
        let tmp = await this.getValueByName('mj_image_jpg');
        if (tmp == '') {
            return 0;
        }
        return tmp;
    }

    setMjImageJpg(value) {
        return this.updateValueByName('mj_image_jpg', value);
    }

    setVideoPath(path) {
        return this.updateValueByName('image_path', path);
    }

    //获取视频路径
    getVideoPath() {
        return this.getValueByName('image_path');
    }
    setDraftPath(path) {
        return this.updateValueByName('draft_path', path);
    }
    getDraftPath() {
        return this.getValueByName('draft_path');
    }
    // 保存配音设置
    setGaixieConfig(id) {
        return this.updateValueByName('video_gaixie_role_id', id);
    }
    //获取配音设置
    getGaixieConfig() {
        return this.getValueByName('video_gaixie_role_id');
    }

    // 保存配音设置
    setTtsConfig(data) {
        return this.updateValueByName('tts_config_v3', data);
    }
    //获取配音设置
    getTtsConfig() {
        return this.getValueByName('tts_config_v3');
    }

    getLocalSdurl() {
        return this.getValueByName('api_url');
    }
    //设置地区
    setTtsRegion(region) {
        return this.updateValueByName('region', region);
    }

    getTtsRegion() {
        return this.getValueByName('region');
    }

    setTtsToken(token) {
        return this.updateValueByName('audio_code', token);
    }

    getRoleFlag() {
        return this.getValueByName('role_flag');
    }

    //关闭初始化
    setRoleFlag() {
        return this.updateValueByName('role_flag', '3');
    }

    async getTtsToken() {
        let token = await this.getValueByName('audio_code');
        if (token == '') {
            token = '87f746a45c9f4dcd8c753ec7440a5ebc';
        }
        return token;
    }

    openLocalImg() {
        return this.updateValueByName('local_img_status', '1');
    }

    closeLocalImg() {
        return this.updateValueByName('local_img_status', '0');
    }

    async getLocalImg() {
        let tmp = await this.getValueByName('local_img_status');
        if (tmp == '1') {
            return true;
        }
        return false;
    }

    getbackupNetwork() {
        return this.getValueByName('backupNetwork');
    }

    getuserId() {
        return this.getValueByName('userId');
    }

    //获取用户token
    getUserInfoToken() {
        return this.getValueByName('token');
    }
    //设置用户token
    setUserInfoToken(token) {
        return this.updateValueByName('token', token);
    }

    // 获取所有的配置
    getConfigs(name) {
        return this.knex.select('*').from('config').orderBy('id', 'asc');
    }
    delUrl(name) {
        return this.knex('config').where('name', '=', name).del();
    }
    //删除登录态
    delToken() {
        return this.knex('config').where('name', '=', 'token').del();
    }
    //清空表格
    delConfig() {
        return this.knex('config').del();
    }

    getGptKey() {
        return this.getValueByName('openai_key');
    }

    async get_jianyin_path() {
        return await this.getValueByName('my_jianyin_path');
    }

    async set_jianyin_path(jianyin_path) {
        return await this.updateValueByName('my_jianyin_path', jianyin_path);
    }

    //打开慢速模式
    async openLowModel() {
        return await this.updateValueByName('low_mode', 1);
    }
    //关闭慢速模式
    async closeLowModel() {
        return await this.updateValueByName('low_mode', 0);
    }
    //是否慢速模式
    async isLowModel() {
        let low_mode = await this.getValueByName('low_mode');
        if (low_mode !== '1') {
            return 0;
        } else {
            return 1;
        }
    }
    //获取gpt模型
    async gptModel() {
        let gpt_model = await this.getValueByName('gpt_model');
        if (gpt_model !== '') {
            return gpt_model;
        } else {
            return 'gpt-3.5-turbo';
        }
    }
    //更改gpt模型
    async changeGptModel(res) {
        return await this.updateValueByName('gpt_model', res);
    }
    //获取gpt代理地址
    async proxyAddress() {
        let proxy_address = await this.getValueByName('proxy_address');
        if (proxy_address) {
            return proxy_address;
        } else {
            return 'https://api.openai.com/v1';
        }
    }
    //更改gpt模型
    async changeProxyAddress(res) {
        return await this.updateValueByName('proxy_address', res);
    }
    //更改gpt模型
    async resetProxyAddress(res) {
        return await this.updateValueByName('proxy_address', 'https://api.openai.com/v1');
    }
    //清空model表
    clearModels() {
        return this.knex('model').truncate();
    }
    //清空model表
    clearUpscalers() {
        return this.knex('upscalers').truncate();
    }

    clearSamplers() {
        return this.knex('samplers').truncate();
    }
};
module.exports = Config;