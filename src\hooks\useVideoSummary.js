import { useAiManager } from '@/stores/useAiManager';
import {fileSystem} from '@/logic/fileSystem';
import common from '@/logic/commonHandler';
import { getMediaDuration, createIntroVideo, concatIntroAndMainVideo, extractAndProcessAudio, createIntroVideoWithMusic } from '../logic/ffmpegCommand';
import {summaryConstants} from '@/constants';

export const useVideoSummary = ({ handleTextChange }) => {
  // Lấy các hàm xử lý từ useAiManager()
  const {
    executeSTT,
    summarize,
    executeTTS,
    settings: { optionAPISpeechToText, optionAPITextToSpeech }
  } = useAiManager();


  // 1. Chuyển video thành văn bản (speech-to-text)
  const getTranscriptionVideo = async (videoPath, lang) => {
    const tempFiles = [];
    try {
      // Tạo file âm thanh tạm thời từ video
      const appDataPath = await fileSystem.appData();
      const audioPath = await fileSystem.join(appDataPath, summaryConstants.HANDLE_TRANSCRIPTION_FOLDER, common.generateUniqueId() + ".mp3");
      tempFiles.push(audioPath);

      await extractAndProcessAudio(videoPath, audioPath);

      if (!await fileSystem.existsSync(audioPath)) throw new Error("Lỗi xử lý tách âm thanh video");

      // Chuẩn bị object cho xử lý STT
      const sttInput = {
        type: optionAPISpeechToText,
        path: audioPath,
        lang
      };
      const transcript = await executeSTT(sttInput);

      if (!transcript) throw new Error("Lỗi xử lý âm thanh video");
      return transcript;
    } finally {
      // Xoá các file tạm
      await fileSystem.unlinkSyncs(tempFiles);
    }
  };

  // 2. Tóm tắt văn bản
  const summarizeContent = async (text, lang) => {
    return await summarize(text, lang);
  };

  // 3. Tạo intro video, chuyển text sang speech, ghép lại video
  const createAndAttachVideoIntro = async (text, videoPath, voice, outputFolder, options) => {
    const tempFiles = [];
    try {
      const appDataPath = await fileSystem.appData();
      // 3.1. Tạo file audio từ text
      const audioPath = await fileSystem.join(appDataPath, summaryConstants.HANDLE_TRANSCRIPTION_FOLDER, common.generateUniqueId() + ".mp3");
      const ttsInput = {
        type: optionAPITextToSpeech,
        text,
        voice,
        outputPath: audioPath,
        pitch: options.voicePitch,
        rate: options.speakingRate,
        ...options
      };
      await executeTTS(ttsInput);

      // 3.2. Lấy dữ liệu buffer của audio vừa tạo
      const audioBuffer = await getMediaDuration(audioPath);
      tempFiles.push(audioPath);

      // 3.3. Tạo video mới với intro audio
      const newVideoPath = await fileSystem.join(appDataPath, summaryConstants.HANDLE_TRANSCRIPTION_FOLDER, common.generateUniqueId() + ".mp4");

      // Check if background music is provided
      if (options.backgroundMusic) {
        // Use the new function that handles background music
        await createIntroVideoWithMusic(videoPath, audioPath, options.backgroundMusic, newVideoPath, audioBuffer, options);
      } else {
        // Use original function without background music
        await createIntroVideo(videoPath, newVideoPath, audioBuffer, options);
      }
      tempFiles.push(newVideoPath);

      // 3.4. Tạo đường dẫn output cuối cùng
      const videoName = await fileSystem.basename(videoPath);
      const dirName = outputFolder || await fileSystem.dirname(videoPath);
      let finalPath = await fileSystem.join(dirName, `summary_${videoName}`);
      // 3.5. Đảm bảo đường dẫn không trùng lặp
      finalPath = await common.generatePath(finalPath, "file");

      // 3.5. Gộp lại các file audio, video theo tốc độ gốc
      const hasBackgroundMusic = !!options.backgroundMusic;
      await concatIntroAndMainVideo(newVideoPath, audioPath, videoPath, finalPath, options.speedVideoOriginal, hasBackgroundMusic);

      return finalPath;
    } finally {
      // Xoá file tạm
      await fileSystem.unlinkSyncs(tempFiles);
    }
  };

  // 4. Chu trình xử lý tổng thể: STT → tóm tắt → TTS → ghép lại video
  const processVideoSummary = async (videoObj, lang, voice, outputFolder, options) => {
    // 4.1. Nhận transcript
    const transcript = await getTranscriptionVideo(videoObj.path, lang);
    handleTextChange(videoObj.id, "textAudio", transcript);

    // 4.2. Nhận tóm tắt
    const summary = await summarizeContent(transcript, lang);
    handleTextChange(videoObj.id, "textSummary", summary);

    // 4.3. Tạo video mới với intro tóm tắt
    return await createAndAttachVideoIntro(summary, videoObj.path, voice, outputFolder, options);
  };

  // Trả về các hàm nghiệp vụ
  return {
    getTranscriptionVideo,
    summarizeContent,
    createAndAttachVideoIntro,
    processVideoSummary
  };
};
