<template>
  <div class="safe-area-overlay absolute inset-0 pointer-events-none">
    <!-- Safe area boundaries -->
    <svg class="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
      <!-- Title safe area (90% of screen) -->
      <rect 
        x="5" y="5" 
        width="90" height="90" 
        fill="none" 
        stroke="rgba(0,255,0,0.4)" 
        stroke-width="0.2" 
        stroke-dasharray="2,2"
      />
      
      <!-- Action safe area (80% of screen) -->
      <rect 
        x="10" y="10" 
        width="80" height="80" 
        fill="none" 
        stroke="rgba(255,255,0,0.4)" 
        stroke-width="0.2" 
        stroke-dasharray="1,1"
      />
      
      <!-- Labels -->
      <text x="5" y="3" font-size="2" fill="rgba(0,255,0,0.6)" font-family="Arial">Title Safe</text>
      <text x="10" y="8" font-size="2" fill="rgba(255,255,0,0.6)" font-family="Arial">Action Safe</text>
    </svg>
    
    <!-- Corner indicators -->
    <div class="absolute top-2 left-2 w-4 h-4 border-l-2 border-t-2 border-green-400 opacity-60"></div>
    <div class="absolute top-2 right-2 w-4 h-4 border-r-2 border-t-2 border-green-400 opacity-60"></div>
    <div class="absolute bottom-2 left-2 w-4 h-4 border-l-2 border-b-2 border-green-400 opacity-60"></div>
    <div class="absolute bottom-2 right-2 w-4 h-4 border-r-2 border-b-2 border-green-400 opacity-60"></div>
  </div>
</template>

<script setup>
defineProps({
  videoDimensions: {
    type: Object,
    required: true
  }
})
</script>

<style scoped>
.safe-area-overlay {
  z-index: 5;
}
</style>
