var globalVoiceData = [{
    "voiceId": "CDC794",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Echo",
    "voiceName": "en-US-EchoTurboMultilingualNeural",
    "language": "English (United States)",
    "gender": "Male",
    "description": "Warm, friendly, and engaging",
    "country": "us"
}, {
    "voiceId": "AAD617",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Nova",
    "voiceName": "en-US-NovaTurboMultilingualNeural",
    "language": "English (United States)",
    "gender": "Female",
    "description": "Young, energetic, and engaging",
    "country": "us"
}, {
    "voiceId": "AAB560",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Fable",
    "voiceName": "en-US-FableTurboMultilingualNeural",
    "language": "English (United States)",
    "gender": "Male",
    "description": "Energetic, expressive, and engaging",
    "country": "us"
}, {
    "voiceId": "ADD862",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Shimmer",
    "voiceName": "en-US-ShimmerTurboMultilingualNeural",
    "language": "English (United States)",
    "gender": "Female",
    "description": "Lively, vibrant, and dynamic",
    "country": "us"
}, {
    "voiceId": "CED087",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Amanda",
    "voiceName": "en-US-AmandaMultilingualNeural",
    "language": "English (United States)",
    "gender": "Female",
    "description": "Young, mature",
    "country": "us"
}, {
    "voiceId": "CBD585",
    "server": "deepgram",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Angus",
    "voiceName": "aura-angus-en",
    "language": "English (United States)",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "BCD254",
    "server": "deepgram",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Orpheus",
    "voiceName": "aura-orpheus-en",
    "language": "English (United States)",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "EEB499",
    "server": "deepgram",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Helios",
    "voiceName": "aura-helios-en",
    "language": "English (United States)",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "EEA032",
    "server": "deepgram",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Zeus",
    "voiceName": "aura-zeus-en",
    "language": "English (United States)",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "CEF399",
    "server": "deepgram",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Asteria",
    "voiceName": "aura-asteria-en",
    "language": "English (United States)",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "DEA775",
    "server": "deepgram",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Luna",
    "voiceName": "aura-luna-en",
    "language": "English (United States)",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "AFB139",
    "server": "deepgram",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Stella",
    "voiceName": "aura-stella-en",
    "language": "English (United States)",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "EAA548",
    "server": "deepgram",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Athena",
    "voiceName": "aura-athena-en",
    "language": "English (United States)",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "DEC493",
    "server": "deepgram",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Hera",
    "voiceName": "aura-hera-en",
    "language": "English (United States)",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "DDB171",
    "server": "deepgram",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Orion",
    "voiceName": "aura-orion-en",
    "language": "English (United States)",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "FAA149",
    "server": "deepgram",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Arcas",
    "voiceName": "aura-arcas-en",
    "language": "English (United States)",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "ADC747",
    "server": "deepgram",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Perseus",
    "voiceName": "aura-perseus-en",
    "language": "English (United States)",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "ACA091",
    "server": "openai",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Emily",
    "voiceName": "en_female_emily_us",
    "language": "English (United States)",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "CCE707",
    "server": "openai",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Jessica",
    "voiceName": "en_female_jessica_au",
    "language": "English (United States)",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "BAE065",
    "server": "openai",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Elara",
    "voiceName": "en_female_elara_br",
    "language": "English (United States)",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "EEA717",
    "server": "openai",
    "tab": "All",
    "order": 100,
    "languageCode": "id-ID",
    "displayName": "Meera",
    "voiceName": "en_female_meera_indian",
    "language": "English (United States)",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "EDD300",
    "server": "openai",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Alice",
    "voiceName": "en_female_alice_default",
    "language": "English (United States)",
    "gender": "Female",
    "description": "Young, energetic, and engaging",
    "country": "us"
}, {
    "voiceId": "CCE711",
    "server": "openai",
    "tab": "All",
    "order": 100,
    "languageCode": "zh-CN",
    "displayName": "Liu Ying",
    "voiceName": "zh_female_liu-ying_default",
    "language": "Chinese",
    "gender": "Female",
    "country": "cn"
}, {
    "voiceId": "BEF765",
    "server": "openai",
    "tab": "All",
    "order": 100,
    "languageCode": "ja-JP",
    "displayName": "Yuki",
    "voiceName": "jp_female_yuki_default",
    "language": "Japanese (Japan)",
    "gender": "Female",
    "country": "jp"
}, {
    "voiceId": "EEA717",
    "server": "openai",
    "tab": "All",
    "order": 100,
    "languageCode": "id-ID",
    "displayName": "Meera",
    "voiceName": "en_female_meera_indian",
    "language": "Indian",
    "gender": "Female",
    "country": "in"
}, {
    "voiceId": "DFE601",
    "server": "openai",
    "tab": "All",
    "order": 30,
    "languageCode": "vi-VN",
    "displayName": "Onyx",
    "voiceName": "vi_male_onyx_default",
    "language": "Vietnamese (Vietnam)",
    "gender": "Male",
    "avatar": "onyx.svg",
    "description": "Older, mature, and experienced",
    "country": "vn"
}, {
    "voiceId": "AI2001",
    "server": "openaiv2",
    "tab": "hqVoice",
    "order": 30,
    "languageCode": "multi",
    "displayName": "Onyx (HQ)",
    "voiceName": "onyx__multi",
    "language": "Multilingual",
    "gender": "Male",
    "avatar": "onyx.svg",
    "description": "Older, mature, and experienced",
    "country": "multi"
}, {
    "voiceId": "BCE046",
    "server": "openaiv2",
    "tab": "hqVoice",
    "order": 30,
    "languageCode": "vi-VN",
    "displayName": "Onyx (Saigon)",
    "voiceName": "onyx__sg_vietnamese",
    "language": "Vietnamese (Vietnam)",
    "gender": "Male",
    "avatar": "onyx.svg",
    "description": "Older, mature, and experienced",
    "country": "vn"
}, {
    "voiceId": "ADB071",
    "server": "openai",
    "tab": "All",
    "order": 26,
    "languageCode": "vi-VN",
    "displayName": "Echo",
    "voiceName": "vi_male_echo_default",
    "language": "Vietnamese (Vietnam)",
    "gender": "Male",
    "description": "Warm, professional",
    "country": "vn"
}, {
    "voiceId": "AI2002",
    "server": "openaiv2",
    "tab": "hqVoice",
    "order": 30,
    "languageCode": "multi",
    "displayName": "Echo (HQ)",
    "voiceName": "echo__multi",
    "language": "Multilingual",
    "gender": "Male",
    "description": "Warm, professional",
    "country": "multi"
}, {
    "voiceId": "BBC777",
    "server": "openaiv2",
    "tab": "hqVoice",
    "order": 30,
    "languageCode": "vi-VN",
    "displayName": "Echo (Saigon)",
    "voiceName": "echo__sg_vietnamese",
    "language": "Vietnamese (Vietnam)",
    "gender": "Male",
    "description": "Warm, professional",
    "country": "vn"
}, {
    "voiceId": "FDB078",
    "server": "openai",
    "tab": "All",
    "order": 27,
    "languageCode": "vi-VN",
    "displayName": "Nova",
    "voiceName": "vi_female_nova_default",
    "language": "Vietnamese (Vietnam)",
    "gender": "Female",
    "description": "Young, energetic, and engaging",
    "country": "vn"
}, {
    "voiceId": "CBC380",
    "server": "openaiv2",
    "tab": "hqVoice",
    "order": 30,
    "languageCode": "vi-VN",
    "displayName": "Nova (HaNoi)",
    "voiceName": "nova__hn_vietnamese",
    "language": "Vietnamese (Vietnam)",
    "gender": "Female",
    "description": "Youthful, modern, energetic",
    "country": "vn"
}, {
    "voiceId": "BFD160",
    "server": "openaiv2",
    "tab": "hqVoice",
    "order": 30,
    "languageCode": "vi-VN",
    "displayName": "Nova (Saigon)",
    "voiceName": "nova__sg_vietnamese",
    "language": "Vietnamese (Vietnam)",
    "gender": "Female",
    "description": "Youthful, modern, energetic",
    "country": "vn"
}, {
    "voiceId": "BFD621",
    "server": "openaiv2",
    "tab": "hqVoice",
    "order": 30,
    "languageCode": "vi-VN",
    "displayName": "Alloy (HaNoi)",
    "voiceName": "alloy__hn_vietnamese",
    "language": "Vietnamese (Vietnam)",
    "gender": "Male",
    "description": "Strong, steady, dependable",
    "country": "vn"
}, {
    "voiceId": "DEB104",
    "server": "openaiv2",
    "tab": "hqVoice",
    "order": 30,
    "languageCode": "vi-VN",
    "displayName": "Alloy (Saigon)",
    "voiceName": "alloy__sg_vietnamese",
    "language": "Vietnamese (Vietnam)",
    "gender": "Male",
    "description": "Strong, steady, dependable",
    "country": "vn"
}, {
    "voiceId": "FDD663",
    "server": "openaiv2",
    "tab": "hqVoice",
    "order": 30,
    "languageCode": "vi-VN",
    "displayName": "Ash (HaNoi)",
    "voiceName": "ash__hn_vietnamese",
    "language": "Vietnamese (Vietnam)",
    "gender": "Male",
    "description": "Deep, calm, neutral",
    "country": "vn"
}, {
    "voiceId": "BAA785",
    "server": "openaiv2",
    "tab": "hqVoice",
    "order": 30,
    "languageCode": "vi-VN",
    "displayName": "Ash (Saigon)",
    "voiceName": "ash__sg_vietnamese",
    "language": "Vietnamese (Vietnam)",
    "gender": "Male",
    "description": "Deep, calm, neutral",
    "country": "vn"
}, {
    "voiceId": "ABA632",
    "server": "openaiv2",
    "tab": "hqVoice",
    "order": 30,
    "languageCode": "vi-VN",
    "displayName": "Ballad (HaNoi)",
    "voiceName": "ballad__hn_vietnamese",
    "language": "Vietnamese (Vietnam)",
    "gender": "Male",
    "description": "Smooth, melodic, expressive",
    "country": "vn"
}, {
    "voiceId": "DDD747",
    "server": "openaiv2",
    "tab": "hqVoice",
    "order": 30,
    "languageCode": "vi-VN",
    "displayName": "Ballad (Saigon)",
    "voiceName": "ballad__sg_vietnamese",
    "language": "Vietnamese (Vietnam)",
    "gender": "Male",
    "description": "Smooth, melodic, expressive",
    "country": "vn"
}, {
    "voiceId": "EDB781",
    "server": "openaiv2",
    "tab": "hqVoice",
    "order": 30,
    "languageCode": "vi-VN",
    "displayName": "Coral (HaNoi)",
    "voiceName": "coral__hn_vietnamese",
    "language": "Vietnamese (Vietnam)",
    "gender": "Female",
    "description": "Bright, clear, crisp",
    "country": "vn"
}, {
    "voiceId": "EFE463",
    "server": "openaiv2",
    "tab": "hqVoice",
    "order": 30,
    "languageCode": "vi-VN",
    "displayName": "Coral (Saigon)",
    "voiceName": "coral__sg_vietnamese",
    "language": "Vietnamese (Vietnam)",
    "gender": "Female",
    "description": "Bright, clear, crisp",
    "country": "vn"
}, {
    "voiceId": "AFE647",
    "server": "openaiv2",
    "tab": "hqVoice",
    "order": 30,
    "languageCode": "vi-VN",
    "displayName": "Fable (HaNoi)",
    "voiceName": "fable__hn_vietnamese",
    "language": "Vietnamese (Vietnam)",
    "gender": "Male",
    "description": "Storytelling",
    "country": "vn"
}, {
    "voiceId": "AAB343",
    "server": "openaiv2",
    "tab": "hqVoice",
    "order": 30,
    "languageCode": "vi-VN",
    "displayName": "Fable (Saigon)",
    "voiceName": "fable__sg_vietnamese",
    "language": "Vietnamese (Vietnam)",
    "gender": "Male",
    "description": "Storytelling",
    "country": "vn"
}, {
    "voiceId": "FAD130",
    "server": "openaiv2",
    "tab": "hqVoice",
    "order": 30,
    "languageCode": "vi-VN",
    "displayName": "Sage (HaNoi)",
    "voiceName": "sage__hn_vietnamese",
    "language": "Vietnamese (Vietnam)",
    "gender": "Female",
    "description": "Wise, calm",
    "country": "vn"
}, {
    "voiceId": "CCA082",
    "server": "openaiv2",
    "tab": "hqVoice",
    "order": 30,
    "languageCode": "vi-VN",
    "displayName": "Sage (Saigon)",
    "voiceName": "sage__sg_vietnamese",
    "language": "Vietnamese (Vietnam)",
    "gender": "Female",
    "description": "Wise, calm",
    "country": "vn"
}, {
    "voiceId": "EEB054",
    "server": "openaiv2",
    "tab": "hqVoice",
    "order": 30,
    "languageCode": "vi-VN",
    "displayName": "Shimmer (HaNoi)",
    "voiceName": "shimmer__hn_vietnamese",
    "language": "Vietnamese (Vietnam)",
    "gender": "Female",
    "description": "Soft, friendly",
    "country": "vn"
}, {
    "voiceId": "AAE429",
    "server": "openaiv2",
    "tab": "hqVoice",
    "order": 30,
    "languageCode": "vi-VN",
    "displayName": "Shimmer (Saigon)",
    "voiceName": "shimmer__sg_vietnamese",
    "language": "Vietnamese (Vietnam)",
    "gender": "Female",
    "description": "Soft, friendly",
    "country": "vn"
}, {
    "voiceId": "CAA898",
    "server": "openaiv2",
    "tab": "hqVoice",
    "order": 30,
    "languageCode": "vi-VN",
    "displayName": "Verse (HaNoi)",
    "voiceName": "verse__hn_vietnamese",
    "language": "Vietnamese (Vietnam)",
    "gender": "Male",
    "description": "Poetic, smooth",
    "country": "vn"
}, {
    "voiceId": "BFC040",
    "server": "openaiv2",
    "tab": "hqVoice",
    "order": 30,
    "languageCode": "vi-VN",
    "displayName": "Verse (Saigon)",
    "voiceName": "verse__sg_vietnamese",
    "language": "Vietnamese (Vietnam)",
    "gender": "Male",
    "description": "Poetic, smooth, lyrical",
    "country": "vn"
}, {
    "voiceId": "EED659",
    "server": "openai",
    "tab": "hqVoice",
    "order": 22,
    "languageCode": "vi-VN",
    "displayName": "Ki\u1ec1u Nhi",
    "voiceName": "vi_female_kieunhi_mn",
    "language": "Vietnamese (Vietnam)",
    "gender": "Female",
    "description": "Young, energetic, and southerner",
    "country": "vn"
}, {
    "voiceId": "BCB483",
    "server": "openai",
    "tab": "hqVoice",
    "order": 23,
    "languageCode": "vi-VN",
    "displayName": "Huy\u1ec1n Anh",
    "voiceName": "vi_female_huyenanh_mb",
    "language": "Vietnamese (Vietnam)",
    "gender": "Female",
    "description": "Young, energetic, and northerner",
    "country": "vn"
}, {
    "voiceId": "BDD231",
    "server": "openai",
    "tab": "hqVoice",
    "order": 24,
    "languageCode": "vi-VN",
    "displayName": "H\u00e0 C\u00fac",
    "voiceName": "vi_female_hacuc_mb",
    "language": "Vietnamese (Vietnam)",
    "gender": "Female",
    "description": "Young, energetic, and northerner",
    "country": "vn"
}, {
    "voiceId": "BDC936",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Game On",
    "voiceName": "en_male_jomboy",
    "language": "English",
    "gender": "Male",
    "country": "gb"
}, {
    "voiceId": "BAD997",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Jessie",
    "voiceName": "en_us_002",
    "language": "English",
    "gender": "Female",
    "country": "gb"
}, {
    "voiceId": "CBB101",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Warm",
    "voiceName": "es_mx_002",
    "language": "English",
    "gender": "Female",
    "country": "gb"
}, {
    "voiceId": "EDD879",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Wacky",
    "voiceName": "en_male_funny",
    "language": "English",
    "gender": "Male",
    "country": "gb"
}, {
    "voiceId": "FBF847",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Scream",
    "voiceName": "en_us_ghostface",
    "language": "English",
    "gender": "Male",
    "country": "gb"
}, {
    "voiceId": "BDE721",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Empathetic",
    "voiceName": "en_female_samc",
    "language": "English",
    "gender": "Female",
    "country": "gb"
}, {
    "voiceId": "EBB119",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Serious",
    "voiceName": "en_male_cody",
    "language": "English",
    "gender": "Male",
    "country": "gb"
}, {
    "voiceId": "BDC206",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Beauty Guru",
    "voiceName": "en_female_makeup",
    "language": "English",
    "gender": "Female",
    "country": "gb"
}, {
    "voiceId": "EAE225",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Bestie",
    "voiceName": "en_female_richgirl",
    "language": "English",
    "gender": "Female",
    "country": "gb"
}, {
    "voiceId": "ACC852",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Trickster",
    "voiceName": "en_male_grinch",
    "language": "English",
    "gender": "Male",
    "country": "gb"
}, {
    "voiceId": "CFD373",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Joey",
    "voiceName": "en_us_006",
    "language": "English",
    "gender": "Male",
    "country": "gb"
}, {
    "voiceId": "ECD830",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Story Teller",
    "voiceName": "en_male_narration",
    "language": "English",
    "gender": "Male",
    "country": "gb"
}, {
    "voiceId": "CEA112",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Mr. GoodGuy",
    "voiceName": "en_male_deadpool",
    "language": "English",
    "gender": "Male",
    "country": "gb"
}, {
    "voiceId": "EFD169",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-GB",
    "displayName": "Narrator",
    "voiceName": "en_uk_001",
    "language": "English",
    "gender": "Male",
    "country": "gb"
}, {
    "voiceId": "DFD124",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-GB",
    "displayName": "Male English UK",
    "voiceName": "en_uk_003",
    "language": "English",
    "gender": "Male",
    "country": "gb"
}, {
    "voiceId": "EFB687",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-AU",
    "displayName": "Metro",
    "voiceName": "en_au_001",
    "language": "English",
    "gender": "Male",
    "country": "gb"
}, {
    "voiceId": "CCD332",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Alfred",
    "voiceName": "en_male_jarvis",
    "language": "English",
    "gender": "Male",
    "country": "gb"
}, {
    "voiceId": "BBA667",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "ashmagic",
    "voiceName": "en_male_ashmagic",
    "language": "English",
    "gender": "Male",
    "country": "gb"
}, {
    "voiceId": "EBC096",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "olantekkers",
    "voiceName": "en_male_olantekkers",
    "language": "English",
    "gender": "Male",
    "country": "gb"
}, {
    "voiceId": "EAB700",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-GB",
    "displayName": "Lord Cringe",
    "voiceName": "en_male_ukneighbor",
    "language": "English",
    "gender": "Male",
    "country": "gb"
}, {
    "voiceId": "DFB669",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-GB",
    "displayName": "Mr. Meticulous",
    "voiceName": "en_male_ukbutler",
    "language": "English",
    "gender": "Male",
    "country": "gb"
}, {
    "voiceId": "FFF730",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Debutante",
    "voiceName": "en_female_shenna",
    "language": "English",
    "gender": "Female",
    "country": "gb"
}, {
    "voiceId": "ADC311",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Varsity",
    "voiceName": "en_female_pansino",
    "language": "English",
    "gender": "Female",
    "country": "gb"
}, {
    "voiceId": "AAB643",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Marty",
    "voiceName": "en_male_trevor",
    "language": "English",
    "gender": "Male",
    "country": "gb"
}, {
    "voiceId": "BFA763",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Pop Lullaby",
    "voiceName": "en_female_f08_twinkle",
    "language": "English",
    "gender": "Female",
    "country": "gb"
}, {
    "voiceId": "BFB065",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Classic Electric",
    "voiceName": "en_male_m03_classical",
    "language": "English",
    "gender": "Male",
    "country": "gb"
}, {
    "voiceId": "DDD266",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Bae",
    "voiceName": "en_female_betty",
    "language": "English",
    "gender": "Female",
    "country": "gb"
}, {
    "voiceId": "ECA770",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Cupid",
    "voiceName": "en_male_cupid",
    "language": "English",
    "gender": "Male",
    "country": "gb"
}, {
    "voiceId": "ACE693",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Granny",
    "voiceName": "en_female_grandma",
    "language": "English",
    "gender": "Female",
    "country": "gb"
}, {
    "voiceId": "BAE514",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Cozy",
    "voiceName": "en_male_m2_xhxs_m03_christmas",
    "language": "English",
    "gender": "Male",
    "country": "gb"
}, {
    "voiceId": "EED089",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Author",
    "voiceName": "en_male_santa_narration",
    "language": "English",
    "gender": "Male",
    "country": "gb"
}, {
    "voiceId": "CCB846",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Caroler",
    "voiceName": "en_male_sing_deep_jingle",
    "language": "English",
    "gender": "Male",
    "country": "gb"
}, {
    "voiceId": "FAF184",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Santa",
    "voiceName": "en_male_santa_effect",
    "language": "English",
    "gender": "Male",
    "country": "gb"
}, {
    "voiceId": "DFF450",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "NYE 2023",
    "voiceName": "en_female_ht_f08_newyear",
    "language": "English",
    "gender": "Female",
    "country": "gb"
}, {
    "voiceId": "FFA844",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Magician",
    "voiceName": "en_male_wizard",
    "language": "English",
    "gender": "Male",
    "country": "gb"
}, {
    "voiceId": "DDD053",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Opera",
    "voiceName": "en_female_ht_f08_halloween",
    "language": "English",
    "gender": "Female",
    "country": "gb"
}, {
    "voiceId": "DEB602",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Euphoric",
    "voiceName": "en_female_ht_f08_glorious",
    "language": "English",
    "gender": "Female",
    "country": "gb"
}, {
    "voiceId": "CEB069",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Hypetrain",
    "voiceName": "en_male_sing_funny_it_goes_up",
    "language": "English",
    "gender": "Male",
    "country": "gb"
}, {
    "voiceId": "EDD955",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Melodrama",
    "voiceName": "en_female_ht_f08_wonderful_world",
    "language": "English",
    "gender": "Female",
    "country": "gb"
}, {
    "voiceId": "CEE768",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Quirky Time",
    "voiceName": "en_male_m2_xhxs_m03_silly",
    "language": "English",
    "gender": "Male",
    "country": "gb"
}, {
    "voiceId": "ADB244",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Peaceful",
    "voiceName": "en_female_emotional",
    "language": "English",
    "gender": "Female",
    "country": "gb"
}, {
    "voiceId": "BDC329",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Toon Beat",
    "voiceName": "en_male_m03_sunshine_soon",
    "language": "English",
    "gender": "Male",
    "country": "gb"
}, {
    "voiceId": "ECC654",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Open Mic",
    "voiceName": "en_female_f08_warmy_breeze",
    "language": "English",
    "gender": "Female",
    "country": "gb"
}, {
    "voiceId": "AED856",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Jingle",
    "voiceName": "en_male_m03_lobby",
    "language": "English",
    "gender": "Male",
    "country": "gb"
}, {
    "voiceId": "BDF263",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Thanksgiving",
    "voiceName": "en_male_sing_funny_thanksgiving",
    "language": "English",
    "gender": "Male",
    "country": "gb"
}, {
    "voiceId": "FCE737",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Cottagecore",
    "voiceName": "en_female_f08_salut_damour",
    "language": "English",
    "gender": "Female",
    "country": "gb"
}, {
    "voiceId": "FAF146",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Professor",
    "voiceName": "en_us_007",
    "language": "English",
    "gender": "Male",
    "country": "gb"
}, {
    "voiceId": "BEE585",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Scientist",
    "voiceName": "en_us_009",
    "language": "English",
    "gender": "Male",
    "country": "gb"
}, {
    "voiceId": "FAC591",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Confidence",
    "voiceName": "en_us_010",
    "language": "English",
    "gender": "Male",
    "country": "gb"
}, {
    "voiceId": "EAC971",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-AU",
    "displayName": "Smooth",
    "voiceName": "en_au_002",
    "language": "English",
    "gender": "Male",
    "country": "gb"
}, {
    "voiceId": "BFA903",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Ghost Face",
    "voiceName": "en_us_ghostface",
    "language": "Disney",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "EBE270",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Chewbacca",
    "voiceName": "en_us_chewbacca",
    "language": "Disney",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "FBF763",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "C3PO",
    "voiceName": "en_us_c3po",
    "language": "Disney",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "FBE006",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Stitch",
    "voiceName": "en_us_stitch",
    "language": "Disney",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "ABE897",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Stormtrooper",
    "voiceName": "en_us_stormtrooper",
    "language": "Disney",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "EAF303",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Rocket",
    "voiceName": "en_us_rocket",
    "language": "Disney",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "AAE195",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Madame Leota",
    "voiceName": "en_female_madam_leota",
    "language": "Disney",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "BBC764",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Ghost Host",
    "voiceName": "en_male_ghosthost",
    "language": "Disney",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "DAF788",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Pirate",
    "voiceName": "en_male_pirate",
    "language": "Disney",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "CAD504",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "fr-FR",
    "displayName": "French - Male 1",
    "voiceName": "fr_001",
    "language": "French",
    "gender": "Male",
    "country": "fr"
}, {
    "voiceId": "CFE484",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "fr-FR",
    "displayName": "French - Male 2",
    "voiceName": "fr_002",
    "language": "French",
    "gender": "Male",
    "country": "fr"
}, {
    "voiceId": "FFD000",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "es-ES",
    "displayName": "Spanish (Spain) - Male",
    "voiceName": "es_002",
    "language": "Spanish",
    "gender": "Male",
    "country": "es"
}, {
    "voiceId": "BBB039",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "es-MX",
    "displayName": "Spanish MX - Male",
    "voiceName": "es_mx_002",
    "language": "Spanish",
    "gender": "Male",
    "country": "es"
}, {
    "voiceId": "AFF668",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "pt-BR",
    "displayName": "Portuguese BR - Female 1",
    "voiceName": "br_001",
    "language": "Portuguese",
    "gender": "Female",
    "country": "pt"
}, {
    "voiceId": "FBD635",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "pt-BR",
    "displayName": "Portuguese BR - Female 2",
    "voiceName": "br_003",
    "language": "Portuguese",
    "gender": "Female",
    "country": "pt"
}, {
    "voiceId": "BCD102",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "pt-BR",
    "displayName": "Portuguese BR - Female 3",
    "voiceName": "br_004",
    "language": "Portuguese",
    "gender": "Female",
    "country": "pt"
}, {
    "voiceId": "BFA450",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "pt-BR",
    "displayName": "Portuguese BR - Male",
    "voiceName": "br_005",
    "language": "Portuguese",
    "gender": "Male",
    "country": "pt"
}, {
    "voiceId": "ECD589",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "pt-BR",
    "displayName": "Ivete Sangalo",
    "voiceName": "bp_female_ivete",
    "language": "Portuguese",
    "gender": "Female",
    "country": "pt"
}, {
    "voiceId": "CEA930",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "pt-BR",
    "displayName": "Ludmilla",
    "voiceName": "bp_female_ludmilla",
    "language": "Portuguese",
    "gender": "Female",
    "country": "pt"
}, {
    "voiceId": "FFD378",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "pt-PT",
    "displayName": "Lhays Macedo",
    "voiceName": "pt_female_lhays",
    "language": "Portuguese",
    "gender": "Female",
    "country": "pt"
}, {
    "voiceId": "EDC511",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "pt-PT",
    "displayName": "Laizza",
    "voiceName": "pt_female_laizza",
    "language": "Portuguese",
    "gender": "Female",
    "country": "pt"
}, {
    "voiceId": "BDA018",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "pt-PT",
    "displayName": "Galv\u00e3o Bueno",
    "voiceName": "pt_male_bueno",
    "language": "Portuguese",
    "gender": "Male",
    "country": "pt"
}, {
    "voiceId": "BEA944",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "de-DE",
    "displayName": "German - Female",
    "voiceName": "de_001",
    "language": "German",
    "gender": "Female",
    "country": "de"
}, {
    "voiceId": "ACB483",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "de-DE",
    "displayName": "German - Male",
    "voiceName": "de_002",
    "language": "German",
    "gender": "Male",
    "country": "de"
}, {
    "voiceId": "ACA253",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "id-ID",
    "displayName": "Indonesian - Female",
    "voiceName": "id_001",
    "language": "Indonesian",
    "gender": "Female",
    "country": "id"
}, {
    "voiceId": "EFD524",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "ja-JP",
    "displayName": "Japanese - Female 1",
    "voiceName": "jp_001",
    "language": "Japanese",
    "gender": "Female",
    "country": "jp"
}, {
    "voiceId": "AEA813",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "ja-JP",
    "displayName": "Japanese - Female 2",
    "voiceName": "jp_003",
    "language": "Japanese",
    "gender": "Female",
    "country": "jp"
}, {
    "voiceId": "FBF129",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "ja-JP",
    "displayName": "Japanese - Female 3",
    "voiceName": "jp_005",
    "language": "Japanese",
    "gender": "Female",
    "country": "jp"
}, {
    "voiceId": "AED992",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "ja-JP",
    "displayName": "Japanese - Male",
    "voiceName": "jp_006",
    "language": "Japanese",
    "gender": "Male",
    "country": "jp"
}, {
    "voiceId": "FBF906",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "ja-JP",
    "displayName": "\u308a\u30fc\u3055",
    "voiceName": "jp_female_fujicochan",
    "language": "Japanese",
    "gender": "Female",
    "country": "jp"
}, {
    "voiceId": "BBA154",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "ja-JP",
    "displayName": "\u4e16\u7f85\u9234",
    "voiceName": "jp_female_hasegawariona",
    "language": "Japanese",
    "gender": "Female",
    "country": "jp"
}, {
    "voiceId": "FCE506",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "ja-JP",
    "displayName": "Morio's Kitchen",
    "voiceName": "jp_male_keiichinakano",
    "language": "Japanese",
    "gender": "Male",
    "country": "jp"
}, {
    "voiceId": "AAF412",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "ja-JP",
    "displayName": "\u590f\u7d75\u30b3\u30b3",
    "voiceName": "jp_female_oomaeaika",
    "language": "Japanese",
    "gender": "Female",
    "country": "jp"
}, {
    "voiceId": "DDC010",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "ja-JP",
    "displayName": "\u4f4e\u97f3\u30dc\u30a4\u30b9",
    "voiceName": "jp_male_yujinchigusa",
    "language": "Japanese",
    "gender": "Male",
    "country": "jp"
}, {
    "voiceId": "FCE686",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "ja-JP",
    "displayName": "\u56db\u90ce",
    "voiceName": "jp_female_shirou",
    "language": "Japanese",
    "gender": "Female",
    "country": "jp"
}, {
    "voiceId": "ABC992",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "ja-JP",
    "displayName": "\u7389\u5ddd\u5bff\u7d00",
    "voiceName": "jp_male_tamawakazuki",
    "language": "Japanese",
    "gender": "Male",
    "country": "jp"
}, {
    "voiceId": "ABB876",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "ja-JP",
    "displayName": "\u5e84\u53f8\u679c\u7e54",
    "voiceName": "jp_female_kaorishoji",
    "language": "Japanese",
    "gender": "Female",
    "country": "jp"
}, {
    "voiceId": "EEB483",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "ja-JP",
    "displayName": "\u516b\u6728\u6c99\u5b63",
    "voiceName": "jp_female_yagishaki",
    "language": "Japanese",
    "gender": "Female",
    "country": "jp"
}, {
    "voiceId": "BAA427",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "ja-JP",
    "displayName": "\u30d2\u30ab\u30ad\u30f3",
    "voiceName": "jp_male_hikakin",
    "language": "Japanese",
    "gender": "Male",
    "country": "jp"
}, {
    "voiceId": "BEB775",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "ja-JP",
    "displayName": "\u4e38\u5c71\u793c",
    "voiceName": "jp_female_rei",
    "language": "Japanese",
    "gender": "Female",
    "country": "jp"
}, {
    "voiceId": "DAB645",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "ja-JP",
    "displayName": "\u4fee\u4e00\u6717",
    "voiceName": "jp_male_shuichiro",
    "language": "Japanese",
    "gender": "Male",
    "country": "jp"
}, {
    "voiceId": "CBF656",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "ja-JP",
    "displayName": "\u30de\u30c4\u30c0\u5bb6\u306e\u65e5\u5e38",
    "voiceName": "jp_male_matsudake",
    "language": "Japanese",
    "gender": "Male",
    "country": "jp"
}, {
    "voiceId": "BFA657",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "ja-JP",
    "displayName": "\u307e\u3061\u3053\u308a\u30fc\u305f",
    "voiceName": "jp_female_machikoriiita",
    "language": "Japanese",
    "gender": "Female",
    "country": "jp"
}, {
    "voiceId": "EFF720",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "ja-JP",
    "displayName": "\u30e2\u30b8\u30e3\u30aa",
    "voiceName": "jp_male_matsuo",
    "language": "Japanese",
    "gender": "Male",
    "country": "jp"
}, {
    "voiceId": "FFB457",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "ja-JP",
    "displayName": "\u30e2\u30ea\u30b9\u30b1",
    "voiceName": "jp_male_osada",
    "language": "Japanese",
    "gender": "Male",
    "country": "jp"
}, {
    "voiceId": "DDF054",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "ko-KR",
    "displayName": "Korean - Male 1",
    "voiceName": "kr_002",
    "language": "Korean",
    "gender": "Male",
    "country": "kr"
}, {
    "voiceId": "DDF827",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "ko-KR",
    "displayName": "Korean - Female",
    "voiceName": "kr_003",
    "language": "Korean",
    "gender": "Female",
    "country": "kr"
}, {
    "voiceId": "DEB957",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "ko-KR",
    "displayName": "Korean - Male 2",
    "voiceName": "kr_004",
    "language": "Korean",
    "gender": "Male",
    "country": "kr"
}, {
    "voiceId": "EFC482",
    "server": "tiktok",
    "tab": "hqVoice",
    "order": 25,
    "languageCode": "vi-VN",
    "displayName": "Anh D\u0169ng",
    "voiceName": "BV560_streaming",
    "language": "Vietnamese (Vietnam)",
    "gender": "Male",
    "description": "Youthful, modern, energetic",
    "country": "vn"
}, {
    "voiceId": "AEE454",
    "server": "tiktok",
    "tab": "hqVoice",
    "order": 25,
    "languageCode": "vi-VN",
    "displayName": "Nh\u1eadt Huy",
    "voiceName": "BV075_streaming",
    "language": "Vietnamese (Vietnam)",
    "gender": "Male",
    "description": "Confident, male",
    "country": "vn"
}, {
    "voiceId": "BAD084",
    "server": "tiktok",
    "tab": "hqVoice",
    "order": 25,
    "languageCode": "vi-VN",
    "displayName": "Thu H\u01b0\u01a1ng",
    "voiceName": "vi_female_huong",
    "language": "Vietnamese (Vietnam)",
    "gender": "Female",
    "description": "Older, mature",
    "country": "vn"
}, {
    "voiceId": "ECF913",
    "server": "tiktok",
    "tab": "hqVoice",
    "order": 25,
    "languageCode": "vi-VN",
    "displayName": "B\u00edch Ng\u1ecdc",
    "voiceName": "BV421_vivn_streaming",
    "language": "Vietnamese (Vietnam)",
    "gender": "Female",
    "description": "Young, energetic",
    "country": "vn"
}, {
    "voiceId": "DFD702",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Alto",
    "voiceName": "en_female_f08_salut_damour",
    "language": "Singing",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "AFC997",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Tenor",
    "voiceName": "en_male_m03_lobby",
    "language": "Singing",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "FDC158",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Sunshine Soon",
    "voiceName": "en_male_m03_sunshine_soon",
    "language": "Singing",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "DDC836",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Warmy Breeze",
    "voiceName": "en_female_f08_warmy_breeze",
    "language": "Singing",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "CCE785",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Glorious",
    "voiceName": "en_female_ht_f08_glorious",
    "language": "Singing",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "CBA862",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "It Goes Up",
    "voiceName": "en_male_sing_funny_it_goes_up",
    "language": "Singing",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "BBA808",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Chipmunk",
    "voiceName": "en_male_m2_xhxs_m03_silly",
    "language": "Singing",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "CFB207",
    "server": "tiktok",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Dramatic",
    "voiceName": "en_female_ht_f08_wonderful_world",
    "language": "Singing",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "BFD038",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "af-ZA",
    "displayName": "Adri",
    "voiceName": "af-ZA-AdriNeural",
    "language": "Afrikaans (South Africa)",
    "gender": "Female",
    "country": "za"
}, {
    "voiceId": "FBF132",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "af-ZA",
    "displayName": "Willem",
    "voiceName": "af-ZA-WillemNeural",
    "language": "Afrikaans (South Africa)",
    "gender": "Male",
    "country": "za"
}, {
    "voiceId": "FEC592",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "am-ET",
    "displayName": "\u1218\u1245\u12f0\u1235",
    "voiceName": "am-ET-MekdesNeural",
    "language": "Amharic (Ethiopia)",
    "gender": "Female",
    "country": "et"
}, {
    "voiceId": "DEB790",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "am-ET",
    "displayName": "\u12a0\u121d\u1200",
    "voiceName": "am-ET-AmehaNeural",
    "language": "Amharic (Ethiopia)",
    "gender": "Male",
    "country": "et"
}, {
    "voiceId": "DDC521",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ar-AE",
    "displayName": "\u0641\u0627\u0637\u0645\u0629",
    "voiceName": "ar-AE-FatimaNeural",
    "language": "Arabic",
    "gender": "Female",
    "country": "ae"
}, {
    "voiceId": "EAB327",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ar-AE",
    "displayName": "\u062d\u0645\u062f\u0627\u0646",
    "voiceName": "ar-AE-HamdanNeural",
    "language": "Arabic",
    "gender": "Male",
    "country": "ae"
}, {
    "voiceId": "EAA565",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ar-BH",
    "displayName": "\u0644\u064a\u0644\u0649",
    "voiceName": "ar-BH-LailaNeural",
    "language": "Arabic",
    "gender": "Female",
    "country": "ae"
}, {
    "voiceId": "DEC175",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ar-BH",
    "displayName": "\u0639\u0644\u064a",
    "voiceName": "ar-BH-AliNeural",
    "language": "Arabic",
    "gender": "Male",
    "country": "ae"
}, {
    "voiceId": "CCD535",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ar-DZ",
    "displayName": "\u0623\u0645\u064a\u0646\u0629",
    "voiceName": "ar-DZ-AminaNeural",
    "language": "Arabic",
    "gender": "Female",
    "country": "ae"
}, {
    "voiceId": "AEE486",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ar-DZ",
    "displayName": "\u0625\u0633\u0645\u0627\u0639\u064a\u0644",
    "voiceName": "ar-DZ-IsmaelNeural",
    "language": "Arabic",
    "gender": "Male",
    "country": "ae"
}, {
    "voiceId": "FAE553",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ar-EG",
    "displayName": "\u0633\u0644\u0645\u0649",
    "voiceName": "ar-EG-SalmaNeural",
    "language": "Arabic",
    "gender": "Female",
    "country": "ae"
}, {
    "voiceId": "DDD666",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ar-EG",
    "displayName": "\u0634\u0627\u0643\u0631",
    "voiceName": "ar-EG-ShakirNeural",
    "language": "Arabic",
    "gender": "Male",
    "country": "ae"
}, {
    "voiceId": "FAD332",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ar-IQ",
    "displayName": "\u0631\u0646\u0627",
    "voiceName": "ar-IQ-RanaNeural",
    "language": "Arabic",
    "gender": "Female",
    "country": "ae"
}, {
    "voiceId": "BAE939",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ar-IQ",
    "displayName": "\u0628\u0627\u0633\u0644",
    "voiceName": "ar-IQ-BasselNeural",
    "language": "Arabic",
    "gender": "Male",
    "country": "ae"
}, {
    "voiceId": "FDB802",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ar-JO",
    "displayName": "\u0633\u0646\u0627\u0621",
    "voiceName": "ar-JO-SanaNeural",
    "language": "Arabic",
    "gender": "Female",
    "country": "ae"
}, {
    "voiceId": "ADB784",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ar-JO",
    "displayName": "\u062a\u064a\u0645",
    "voiceName": "ar-JO-TaimNeural",
    "language": "Arabic",
    "gender": "Male",
    "country": "ae"
}, {
    "voiceId": "FCB550",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ar-KW",
    "displayName": "\u0646\u0648\u0631\u0627",
    "voiceName": "ar-KW-NouraNeural",
    "language": "Arabic",
    "gender": "Female",
    "country": "ae"
}, {
    "voiceId": "FEC696",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ar-KW",
    "displayName": "\u0641\u0647\u062f",
    "voiceName": "ar-KW-FahedNeural",
    "language": "Arabic",
    "gender": "Male",
    "country": "ae"
}, {
    "voiceId": "CFF890",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ar-LB",
    "displayName": "\u0644\u064a\u0644\u0649",
    "voiceName": "ar-LB-LaylaNeural",
    "language": "Arabic",
    "gender": "Female",
    "country": "ae"
}, {
    "voiceId": "CCB197",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ar-LB",
    "displayName": "\u0631\u0627\u0645\u064a",
    "voiceName": "ar-LB-RamiNeural",
    "language": "Arabic",
    "gender": "Male",
    "country": "ae"
}, {
    "voiceId": "CDE046",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ar-LY",
    "displayName": "\u0625\u064a\u0645\u0627\u0646",
    "voiceName": "ar-LY-ImanNeural",
    "language": "Arabic",
    "gender": "Female",
    "country": "ae"
}, {
    "voiceId": "ECC750",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ar-LY",
    "displayName": "\u0623\u062d\u0645\u062f",
    "voiceName": "ar-LY-OmarNeural",
    "language": "Arabic",
    "gender": "Male",
    "country": "ae"
}, {
    "voiceId": "EEB358",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ar-MA",
    "displayName": "\u0645\u0646\u0649",
    "voiceName": "ar-MA-MounaNeural",
    "language": "Arabic",
    "gender": "Female",
    "country": "ae"
}, {
    "voiceId": "DBA458",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ar-MA",
    "displayName": "\u062c\u0645\u0627\u0644",
    "voiceName": "ar-MA-JamalNeural",
    "language": "Arabic",
    "gender": "Male",
    "country": "ae"
}, {
    "voiceId": "DAE913",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ar-OM",
    "displayName": "\u0639\u0627\u0626\u0634\u0629",
    "voiceName": "ar-OM-AyshaNeural",
    "language": "Arabic",
    "gender": "Female",
    "country": "ae"
}, {
    "voiceId": "FCE106",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ar-OM",
    "displayName": "\u0639\u0628\u062f\u0627\u0644\u0644\u0647",
    "voiceName": "ar-OM-AbdullahNeural",
    "language": "Arabic",
    "gender": "Male",
    "country": "ae"
}, {
    "voiceId": "AFC752",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ar-QA",
    "displayName": "\u0623\u0645\u0644",
    "voiceName": "ar-QA-AmalNeural",
    "language": "Arabic",
    "gender": "Female",
    "country": "ae"
}, {
    "voiceId": "EFF026",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ar-QA",
    "displayName": "\u0645\u0639\u0627\u0630",
    "voiceName": "ar-QA-MoazNeural",
    "language": "Arabic",
    "gender": "Male",
    "country": "ae"
}, {
    "voiceId": "DDF957",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ar-SA",
    "displayName": "\u0632\u0627\u0631\u064a\u0629",
    "voiceName": "ar-SA-ZariyahNeural",
    "language": "Arabic",
    "gender": "Female",
    "country": "ae"
}, {
    "voiceId": "ADB675",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ar-SA",
    "displayName": "\u062d\u0627\u0645\u062f",
    "voiceName": "ar-SA-HamedNeural",
    "language": "Arabic",
    "gender": "Male",
    "country": "ae"
}, {
    "voiceId": "DEB052",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ar-SY",
    "displayName": "\u0623\u0645\u0627\u0646\u064a",
    "voiceName": "ar-SY-AmanyNeural",
    "language": "Arabic",
    "gender": "Female",
    "country": "ae"
}, {
    "voiceId": "DFB882",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ar-SY",
    "displayName": "\u0644\u064a\u062b",
    "voiceName": "ar-SY-LaithNeural",
    "language": "Arabic",
    "gender": "Male",
    "country": "ae"
}, {
    "voiceId": "AAF662",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ar-TN",
    "displayName": "\u0631\u064a\u0645",
    "voiceName": "ar-TN-ReemNeural",
    "language": "Arabic",
    "gender": "Female",
    "country": "ae"
}, {
    "voiceId": "ABC197",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ar-TN",
    "displayName": "\u0647\u0627\u062f\u064a",
    "voiceName": "ar-TN-HediNeural",
    "language": "Arabic",
    "gender": "Male",
    "country": "ae"
}, {
    "voiceId": "EEF081",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ar-YE",
    "displayName": "\u0645\u0631\u064a\u0645",
    "voiceName": "ar-YE-MaryamNeural",
    "language": "Arabic",
    "gender": "Female",
    "country": "ae"
}, {
    "voiceId": "EDF588",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ar-YE",
    "displayName": "\u0635\u0627\u0644\u062d",
    "voiceName": "ar-YE-SalehNeural",
    "language": "Arabic",
    "gender": "Male",
    "country": "ae"
}, {
    "voiceId": "FCD449",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "as-IN",
    "displayName": "\u09af\u09be\u09b6\u09bf\u0995\u09be",
    "voiceName": "as-IN-YashicaNeural",
    "language": "India",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "FAB322",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "as-IN",
    "displayName": "\u09aa\u09cd\u09f0\u09bf\u09af\u09bc\u09ae",
    "voiceName": "as-IN-PriyomNeural",
    "language": "India",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "EEE008",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "az-AZ",
    "displayName": "Banu",
    "voiceName": "az-AZ-BanuNeural",
    "language": "Azerbaijani (Latin, Azerbaijan)",
    "gender": "Female",
    "country": "az"
}, {
    "voiceId": "AED403",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "az-AZ",
    "displayName": "Bab\u0259k",
    "voiceName": "az-AZ-BabekNeural",
    "language": "Azerbaijani (Latin, Azerbaijan)",
    "gender": "Male",
    "country": "az"
}, {
    "voiceId": "ABA658",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "bg-BG",
    "displayName": "\u041a\u0430\u043b\u0438\u043d\u0430",
    "voiceName": "bg-BG-KalinaNeural",
    "language": "Bulgarian (Bulgaria)",
    "gender": "Female",
    "country": "bg"
}, {
    "voiceId": "FBB631",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "bg-BG",
    "displayName": "\u0411\u043e\u0440\u0438\u0441\u043b\u0430\u0432",
    "voiceName": "bg-BG-BorislavNeural",
    "language": "Bulgarian (Bulgaria)",
    "gender": "Male",
    "country": "bg"
}, {
    "voiceId": "EDB876",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "bn-BD",
    "displayName": "\u09a8\u09ac\u09a8\u09c0\u09a4\u09be",
    "voiceName": "bn-BD-NabanitaNeural",
    "language": "Bangla (Bangladesh)",
    "gender": "Female",
    "country": "bd"
}, {
    "voiceId": "DFE469",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "bn-BD",
    "displayName": "\u09aa\u09cd\u09b0\u09a6\u09cd\u09ac\u09c0\u09aa",
    "voiceName": "bn-BD-PradeepNeural",
    "language": "Bangla (Bangladesh)",
    "gender": "Male",
    "country": "bd"
}, {
    "voiceId": "CDA002",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "bn-IN",
    "displayName": "\u09a4\u09be\u09a8\u09bf\u09b6\u09be",
    "voiceName": "bn-IN-TanishaaNeural",
    "language": "Bengali (India)",
    "gender": "Female",
    "country": "in"
}, {
    "voiceId": "DFA426",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "bn-IN",
    "displayName": "\u09ad\u09be\u09b8\u09cd\u0995\u09b0",
    "voiceName": "bn-IN-BashkarNeural",
    "language": "Bengali (India)",
    "gender": "Male",
    "country": "in"
}, {
    "voiceId": "FBE659",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "bs-BA",
    "displayName": "Vesna",
    "voiceName": "bs-BA-VesnaNeural",
    "language": "Bosnian (Bosnia and Herzegovina)",
    "gender": "Female",
    "country": "ba"
}, {
    "voiceId": "CAD840",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "bs-BA",
    "displayName": "Goran",
    "voiceName": "bs-BA-GoranNeural",
    "language": "Bosnian (Bosnia and Herzegovina)",
    "gender": "Male",
    "country": "ba"
}, {
    "voiceId": "EED760",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ca-ES",
    "displayName": "Joana",
    "voiceName": "ca-ES-JoanaNeural",
    "language": "Catalan",
    "gender": "Female",
    "country": "es"
}, {
    "voiceId": "ACF212",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ca-ES",
    "displayName": "Enric",
    "voiceName": "ca-ES-EnricNeural",
    "language": "Catalan",
    "gender": "Male",
    "country": "es"
}, {
    "voiceId": "FCE277",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ca-ES",
    "displayName": "Alba",
    "voiceName": "ca-ES-AlbaNeural",
    "language": "Catalan",
    "gender": "Female",
    "country": "es"
}, {
    "voiceId": "CDD649",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "cs-CZ",
    "displayName": "Vlasta",
    "voiceName": "cs-CZ-VlastaNeural",
    "language": "Czech",
    "gender": "Female",
    "country": "cz"
}, {
    "voiceId": "CEC867",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "cs-CZ",
    "displayName": "Anton\u00edn",
    "voiceName": "cs-CZ-AntoninNeural",
    "language": "Czech",
    "gender": "Male",
    "country": "cz"
}, {
    "voiceId": "EEA380",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "cy-GB",
    "displayName": "Nia",
    "voiceName": "cy-GB-NiaNeural",
    "language": "Welsh",
    "gender": "Female",
    "country": "gb"
}, {
    "voiceId": "DEC483",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "cy-GB",
    "displayName": "Aled",
    "voiceName": "cy-GB-AledNeural",
    "language": "Welsh",
    "gender": "Male",
    "country": "gb"
}, {
    "voiceId": "FCD954",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "da-DK",
    "displayName": "Christel",
    "voiceName": "da-DK-ChristelNeural",
    "language": "Danish (Denmark)",
    "gender": "Female",
    "country": "dk"
}, {
    "voiceId": "ACA526",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "da-DK",
    "displayName": "Jeppe",
    "voiceName": "da-DK-JeppeNeural",
    "language": "Danish (Denmark)",
    "gender": "Male",
    "country": "dk"
}, {
    "voiceId": "CCD336",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "de-AT",
    "displayName": "Ingrid",
    "voiceName": "de-AT-IngridNeural",
    "language": "German (Austria)",
    "gender": "Female",
    "country": "at"
}, {
    "voiceId": "CDB602",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "de-AT",
    "displayName": "Jonas",
    "voiceName": "de-AT-JonasNeural",
    "language": "German (Austria)",
    "gender": "Male",
    "country": "at"
}, {
    "voiceId": "EAD306",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "de-CH",
    "displayName": "Leni",
    "voiceName": "de-CH-LeniNeural",
    "language": "German (Switzerland)",
    "gender": "Female",
    "country": "ch"
}, {
    "voiceId": "EBE594",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "de-CH",
    "displayName": "Jan",
    "voiceName": "de-CH-JanNeural",
    "language": "German (Switzerland)",
    "gender": "Male",
    "country": "ch"
}, {
    "voiceId": "ADA580",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "de-DE",
    "displayName": "Katja",
    "voiceName": "de-DE-KatjaNeural",
    "language": "German (Germany)",
    "gender": "Female",
    "country": "de"
}, {
    "voiceId": "BFF561",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "de-DE",
    "displayName": "Conrad",
    "voiceName": "de-DE-ConradNeural",
    "language": "German (Germany)",
    "gender": "Male",
    "country": "de"
}, {
    "voiceId": "CCC650",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "de-DE",
    "displayName": "Seraphina Mehrsprachig",
    "voiceName": "de-DE-SeraphinaMultilingualNeural",
    "language": "German (Germany)",
    "gender": "Female",
    "country": "de"
}, {
    "voiceId": "FBB261",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "de-DE",
    "displayName": "Florian Mehrsprachig",
    "voiceName": "de-DE-FlorianMultilingualNeural",
    "language": "German (Germany)",
    "gender": "Male",
    "country": "de"
}, {
    "voiceId": "BBF783",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "de-DE",
    "displayName": "Amala",
    "voiceName": "de-DE-AmalaNeural",
    "language": "German (Germany)",
    "gender": "Female",
    "country": "de"
}, {
    "voiceId": "DCD176",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "de-DE",
    "displayName": "Bernd",
    "voiceName": "de-DE-BerndNeural",
    "language": "German (Germany)",
    "gender": "Male",
    "country": "de"
}, {
    "voiceId": "DCE827",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "de-DE",
    "displayName": "Christoph",
    "voiceName": "de-DE-ChristophNeural",
    "language": "German (Germany)",
    "gender": "Male",
    "country": "de"
}, {
    "voiceId": "EAC015",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "de-DE",
    "displayName": "Elke",
    "voiceName": "de-DE-ElkeNeural",
    "language": "German (Germany)",
    "gender": "Female",
    "country": "de"
}, {
    "voiceId": "EFA305",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "de-DE",
    "displayName": "Gisela",
    "voiceName": "de-DE-GiselaNeural",
    "language": "German (Germany)",
    "gender": "Female",
    "country": "de"
}, {
    "voiceId": "ADC949",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "de-DE",
    "displayName": "Kasper",
    "voiceName": "de-DE-KasperNeural",
    "language": "German (Germany)",
    "gender": "Male",
    "country": "de"
}, {
    "voiceId": "FAC335",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "de-DE",
    "displayName": "Killian",
    "voiceName": "de-DE-KillianNeural",
    "language": "German (Germany)",
    "gender": "Male",
    "country": "de"
}, {
    "voiceId": "EAF311",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "de-DE",
    "displayName": "Klarissa",
    "voiceName": "de-DE-KlarissaNeural",
    "language": "German (Germany)",
    "gender": "Female",
    "country": "de"
}, {
    "voiceId": "BEF377",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "de-DE",
    "displayName": "Klaus",
    "voiceName": "de-DE-KlausNeural",
    "language": "German (Germany)",
    "gender": "Male",
    "country": "de"
}, {
    "voiceId": "BFF042",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "de-DE",
    "displayName": "Louisa",
    "voiceName": "de-DE-LouisaNeural",
    "language": "German (Germany)",
    "gender": "Female",
    "country": "de"
}, {
    "voiceId": "BAF100",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "de-DE",
    "displayName": "Maja",
    "voiceName": "de-DE-MajaNeural",
    "language": "German (Germany)",
    "gender": "Female",
    "country": "de"
}, {
    "voiceId": "BAF618",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "de-DE",
    "displayName": "Ralf",
    "voiceName": "de-DE-RalfNeural",
    "language": "German (Germany)",
    "gender": "Male",
    "country": "de"
}, {
    "voiceId": "BEE179",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "de-DE",
    "displayName": "Tanja",
    "voiceName": "de-DE-TanjaNeural",
    "language": "German (Germany)",
    "gender": "Female",
    "country": "de"
}, {
    "voiceId": "CEF086",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "de-DE",
    "displayName": "Seraphina Dragon HD Latest",
    "voiceName": "de-DE-Seraphina:DragonHDLatestNeural",
    "language": "German (Germany)",
    "gender": "Female",
    "country": "de"
}, {
    "voiceId": "EAA770",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "el-GR",
    "displayName": "\u0391\u03b8\u03b7\u03bd\u03ac",
    "voiceName": "el-GR-AthinaNeural",
    "language": "Greek (Greece)",
    "gender": "Female",
    "country": "gr"
}, {
    "voiceId": "FFE083",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "el-GR",
    "displayName": "\u039d\u03ad\u03c3\u03c4\u03bf\u03c1\u03b1\u03c2",
    "voiceName": "el-GR-NestorasNeural",
    "language": "Greek (Greece)",
    "gender": "Male",
    "country": "gr"
}, {
    "voiceId": "FBA208",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-AU",
    "displayName": "Natasha",
    "voiceName": "en-AU-NatashaNeural",
    "language": "English (Australia)",
    "gender": "Female",
    "country": "au"
}, {
    "voiceId": "BCC821",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-AU",
    "displayName": "William",
    "voiceName": "en-AU-WilliamNeural",
    "language": "English (Australia)",
    "gender": "Male",
    "country": "au"
}, {
    "voiceId": "FDD383",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-AU",
    "displayName": "Annette",
    "voiceName": "en-AU-AnnetteNeural",
    "language": "English (Australia)",
    "gender": "Female",
    "country": "au"
}, {
    "voiceId": "DDF902",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-AU",
    "displayName": "Carly",
    "voiceName": "en-AU-CarlyNeural",
    "language": "English (Australia)",
    "gender": "Female",
    "country": "au"
}, {
    "voiceId": "EDD581",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-AU",
    "displayName": "Darren",
    "voiceName": "en-AU-DarrenNeural",
    "language": "English (Australia)",
    "gender": "Male",
    "country": "au"
}, {
    "voiceId": "EFB406",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-AU",
    "displayName": "Duncan",
    "voiceName": "en-AU-DuncanNeural",
    "language": "English (Australia)",
    "gender": "Male",
    "country": "au"
}, {
    "voiceId": "AFB937",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-AU",
    "displayName": "Elsie",
    "voiceName": "en-AU-ElsieNeural",
    "language": "English (Australia)",
    "gender": "Female",
    "country": "au"
}, {
    "voiceId": "BDA215",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-AU",
    "displayName": "Freya",
    "voiceName": "en-AU-FreyaNeural",
    "language": "English (Australia)",
    "gender": "Female",
    "country": "au"
}, {
    "voiceId": "DAE688",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-AU",
    "displayName": "Joanne",
    "voiceName": "en-AU-JoanneNeural",
    "language": "English (Australia)",
    "gender": "Female",
    "country": "au"
}, {
    "voiceId": "CEE578",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-AU",
    "displayName": "Ken",
    "voiceName": "en-AU-KenNeural",
    "language": "English (Australia)",
    "gender": "Male",
    "country": "au"
}, {
    "voiceId": "FFB745",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-AU",
    "displayName": "Kim",
    "voiceName": "en-AU-KimNeural",
    "language": "English (Australia)",
    "gender": "Female",
    "country": "au"
}, {
    "voiceId": "CAF657",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-AU",
    "displayName": "Neil",
    "voiceName": "en-AU-NeilNeural",
    "language": "English (Australia)",
    "gender": "Male",
    "country": "au"
}, {
    "voiceId": "ADD172",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-AU",
    "displayName": "Tim",
    "voiceName": "en-AU-TimNeural",
    "language": "English (Australia)",
    "gender": "Male",
    "country": "au"
}, {
    "voiceId": "AFC638",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-AU",
    "displayName": "Tina",
    "voiceName": "en-AU-TinaNeural",
    "language": "English (Australia)",
    "gender": "Female",
    "country": "au"
}, {
    "voiceId": "AFB698",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-CA",
    "displayName": "Clara",
    "voiceName": "en-CA-ClaraNeural",
    "language": "English (Canada)",
    "gender": "Female",
    "country": "ca"
}, {
    "voiceId": "EDD003",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-CA",
    "displayName": "Liam",
    "voiceName": "en-CA-LiamNeural",
    "language": "English (Canada)",
    "gender": "Male",
    "country": "ca"
}, {
    "voiceId": "CEE774",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-GB",
    "displayName": "Sonia",
    "voiceName": "en-GB-SoniaNeural",
    "language": "English (United Kingdom)",
    "gender": "Female",
    "country": "gb"
}, {
    "voiceId": "AAB191",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-GB",
    "displayName": "Ryan",
    "voiceName": "en-GB-RyanNeural",
    "language": "English (United Kingdom)",
    "gender": "Male",
    "country": "gb"
}, {
    "voiceId": "ADE857",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-GB",
    "displayName": "Libby",
    "voiceName": "en-GB-LibbyNeural",
    "language": "English (United Kingdom)",
    "gender": "Female",
    "country": "gb"
}, {
    "voiceId": "CCA464",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "en-GB",
    "displayName": "Ada Multilingual",
    "voiceName": "en-GB-AdaMultilingualNeural",
    "language": "English (United Kingdom)",
    "gender": "Female",
    "country": "gb"
}, {
    "voiceId": "EDD253",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "en-GB",
    "displayName": "Ollie Multilingual",
    "voiceName": "en-GB-OllieMultilingualNeural",
    "language": "English (United Kingdom)",
    "gender": "Male",
    "country": "gb"
}, {
    "voiceId": "BBD389",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-GB",
    "displayName": "Abbi",
    "voiceName": "en-GB-AbbiNeural",
    "language": "English (United Kingdom)",
    "gender": "Female",
    "country": "gb"
}, {
    "voiceId": "BDF523",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-GB",
    "displayName": "Alfie",
    "voiceName": "en-GB-AlfieNeural",
    "language": "English (United Kingdom)",
    "gender": "Male",
    "country": "gb"
}, {
    "voiceId": "AAB059",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-GB",
    "displayName": "Bella",
    "voiceName": "en-GB-BellaNeural",
    "language": "English (United Kingdom)",
    "gender": "Female",
    "country": "gb"
}, {
    "voiceId": "DFE374",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-GB",
    "displayName": "Elliot",
    "voiceName": "en-GB-ElliotNeural",
    "language": "English (United Kingdom)",
    "gender": "Male",
    "country": "gb"
}, {
    "voiceId": "CDC935",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-GB",
    "displayName": "Ethan",
    "voiceName": "en-GB-EthanNeural",
    "language": "English (United Kingdom)",
    "gender": "Male",
    "country": "gb"
}, {
    "voiceId": "BCB595",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-GB",
    "displayName": "Hollie",
    "voiceName": "en-GB-HollieNeural",
    "language": "English (United Kingdom)",
    "gender": "Female",
    "country": "gb"
}, {
    "voiceId": "BEA729",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-GB",
    "displayName": "Maisie",
    "voiceName": "en-GB-MaisieNeural",
    "language": "English (United Kingdom)",
    "gender": "Female",
    "country": "gb"
}, {
    "voiceId": "EAF266",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-GB",
    "displayName": "Noah",
    "voiceName": "en-GB-NoahNeural",
    "language": "English (United Kingdom)",
    "gender": "Male",
    "country": "gb"
}, {
    "voiceId": "EEE217",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-GB",
    "displayName": "Oliver",
    "voiceName": "en-GB-OliverNeural",
    "language": "English (United Kingdom)",
    "gender": "Male",
    "country": "gb"
}, {
    "voiceId": "BCE703",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-GB",
    "displayName": "Olivia",
    "voiceName": "en-GB-OliviaNeural",
    "language": "English (United Kingdom)",
    "gender": "Female",
    "country": "gb"
}, {
    "voiceId": "EED708",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-GB",
    "displayName": "Thomas",
    "voiceName": "en-GB-ThomasNeural",
    "language": "English (United Kingdom)",
    "gender": "Male",
    "country": "gb"
}, {
    "voiceId": "AFB908",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-GB",
    "displayName": "Mia",
    "voiceName": "en-GB-MiaNeural",
    "language": "English (United Kingdom)",
    "gender": "Female",
    "country": "gb"
}, {
    "voiceId": "CFA156",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-HK",
    "displayName": "Yan",
    "voiceName": "en-HK-YanNeural",
    "language": "English (Hong Kong SAR)",
    "gender": "Female",
    "country": "hk"
}, {
    "voiceId": "EEB001",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-HK",
    "displayName": "Sam",
    "voiceName": "en-HK-SamNeural",
    "language": "English (Hong Kong SAR)",
    "gender": "Male",
    "country": "hk"
}, {
    "voiceId": "ADA167",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-IE",
    "displayName": "Emily",
    "voiceName": "en-IE-EmilyNeural",
    "language": "English (Ireland)",
    "gender": "Female",
    "country": "ie"
}, {
    "voiceId": "BED968",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-IE",
    "displayName": "Connor",
    "voiceName": "en-IE-ConnorNeural",
    "language": "English (Ireland)",
    "gender": "Male",
    "country": "ie"
}, {
    "voiceId": "ADB264",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-IN",
    "displayName": "Aarav",
    "voiceName": "en-IN-AaravNeural",
    "language": "English (India)",
    "gender": "Male",
    "country": "in"
}, {
    "voiceId": "FCA078",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-IN",
    "displayName": "Aashi",
    "voiceName": "en-IN-AashiNeural",
    "language": "English (India)",
    "gender": "Female",
    "country": "in"
}, {
    "voiceId": "EAB838",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-IN",
    "displayName": "Ananya",
    "voiceName": "en-IN-AnanyaNeural",
    "language": "English (India)",
    "gender": "Female",
    "country": "in"
}, {
    "voiceId": "DFC261",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-IN",
    "displayName": "Kavya",
    "voiceName": "en-IN-KavyaNeural",
    "language": "English (India)",
    "gender": "Female",
    "country": "in"
}, {
    "voiceId": "AED742",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-IN",
    "displayName": "Kunal",
    "voiceName": "en-IN-KunalNeural",
    "language": "English (India)",
    "gender": "Male",
    "country": "in"
}, {
    "voiceId": "DBB338",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-IN",
    "displayName": "Neerja",
    "voiceName": "en-IN-NeerjaNeural",
    "language": "English (India)",
    "gender": "Female",
    "country": "in"
}, {
    "voiceId": "BCA134",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-IN",
    "displayName": "Prabhat",
    "voiceName": "en-IN-PrabhatNeural",
    "language": "English (India)",
    "gender": "Male",
    "country": "in"
}, {
    "voiceId": "CFE760",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-IN",
    "displayName": "Rehaan",
    "voiceName": "en-IN-RehaanNeural",
    "language": "English (India)",
    "gender": "Male",
    "country": "in"
}, {
    "voiceId": "DEA183",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-IN",
    "displayName": "Aarti",
    "voiceName": "en-IN-AartiNeural",
    "language": "English (India)",
    "gender": "Female",
    "country": "in"
}, {
    "voiceId": "CBE853",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-KE",
    "displayName": "Asilia",
    "voiceName": "en-KE-AsiliaNeural",
    "language": "English (Kenya)",
    "gender": "Female",
    "country": "ke"
}, {
    "voiceId": "DAD683",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-KE",
    "displayName": "Chilemba",
    "voiceName": "en-KE-ChilembaNeural",
    "language": "English (Kenya)",
    "gender": "Male",
    "country": "ke"
}, {
    "voiceId": "DAC092",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-NG",
    "displayName": "Ezinne",
    "voiceName": "en-NG-EzinneNeural",
    "language": "English (Nigeria)",
    "gender": "Female",
    "country": "ng"
}, {
    "voiceId": "AED121",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-NG",
    "displayName": "Abeo",
    "voiceName": "en-NG-AbeoNeural",
    "language": "English (Nigeria)",
    "gender": "Male",
    "country": "ng"
}, {
    "voiceId": "CCC507",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-NZ",
    "displayName": "Molly",
    "voiceName": "en-NZ-MollyNeural",
    "language": "English (New Zealand)",
    "gender": "Female",
    "country": "nz"
}, {
    "voiceId": "AFB651",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-NZ",
    "displayName": "Mitchell",
    "voiceName": "en-NZ-MitchellNeural",
    "language": "English (New Zealand)",
    "gender": "Male",
    "country": "nz"
}, {
    "voiceId": "DFB916",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-PH",
    "displayName": "Rosa",
    "voiceName": "en-PH-RosaNeural",
    "language": "English (Philippines)",
    "gender": "Female",
    "country": "ph"
}, {
    "voiceId": "EAD568",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-PH",
    "displayName": "James",
    "voiceName": "en-PH-JamesNeural",
    "language": "English (Philippines)",
    "gender": "Male",
    "country": "ph"
}, {
    "voiceId": "CAF695",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-SG",
    "displayName": "Luna",
    "voiceName": "en-SG-LunaNeural",
    "language": "English (Singapore)",
    "gender": "Female",
    "country": "sg"
}, {
    "voiceId": "CCB661",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-SG",
    "displayName": "Wayne",
    "voiceName": "en-SG-WayneNeural",
    "language": "English (Singapore)",
    "gender": "Male",
    "country": "sg"
}, {
    "voiceId": "ADA016",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-TZ",
    "displayName": "Imani",
    "voiceName": "en-TZ-ImaniNeural",
    "language": "English (Tanzania)",
    "gender": "Female",
    "country": "tz"
}, {
    "voiceId": "BBF667",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-TZ",
    "displayName": "Elimu",
    "voiceName": "en-TZ-ElimuNeural",
    "language": "English (Tanzania)",
    "gender": "Male",
    "country": "tz"
}, {
    "voiceId": "BED759",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Ava Multilingual",
    "voiceName": "en-US-AvaMultilingualNeural",
    "language": "English (United States)",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "FAC907",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Andrew Multilingual",
    "voiceName": "en-US-AndrewMultilingualNeural",
    "language": "English (United States)",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "DEF655",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Emma Multilingual",
    "voiceName": "en-US-EmmaMultilingualNeural",
    "language": "English (United States)",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "DBA118",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Brian Multilingual",
    "voiceName": "en-US-BrianMultilingualNeural",
    "language": "English (United States)",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "EBF147",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Ava",
    "voiceName": "en-US-AvaNeural",
    "language": "English (United States)",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "CCE434",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Andrew",
    "voiceName": "en-US-AndrewNeural",
    "language": "English (United States)",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "AAB629",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Emma",
    "voiceName": "en-US-EmmaNeural",
    "language": "English (United States)",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "BAA986",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Brian",
    "voiceName": "en-US-BrianNeural",
    "language": "English (United States)",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "FDD325",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Jenny",
    "voiceName": "en-US-JennyNeural",
    "language": "English (United States)",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "DEF051",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Guy",
    "voiceName": "en-US-GuyNeural",
    "language": "English (United States)",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "BEF805",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Aria",
    "voiceName": "en-US-AriaNeural",
    "language": "English (United States)",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "AEE505",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Davis",
    "voiceName": "en-US-DavisNeural",
    "language": "English (United States)",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "DAE133",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Jane",
    "voiceName": "en-US-JaneNeural",
    "language": "English (United States)",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "CCD201",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Jason",
    "voiceName": "en-US-JasonNeural",
    "language": "English (United States)",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "CDD190",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Kai",
    "voiceName": "en-US-KaiNeural",
    "language": "English (United States)",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "DEF279",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Luna",
    "voiceName": "en-US-LunaNeural",
    "language": "English (United States)",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "CAA807",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Sara",
    "voiceName": "en-US-SaraNeural",
    "language": "English (United States)",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "CFC401",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Tony",
    "voiceName": "en-US-TonyNeural",
    "language": "English (United States)",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "DEF220",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Nancy",
    "voiceName": "en-US-NancyNeural",
    "language": "English (United States)",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "BFC380",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Cora Multilingual",
    "voiceName": "en-US-CoraMultilingualNeural",
    "language": "English (United States)",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "FBE221",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Christopher Multilingual",
    "voiceName": "en-US-ChristopherMultilingualNeural",
    "language": "English (United States)",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "DBA195",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Brandon Multilingual",
    "voiceName": "en-US-BrandonMultilingualNeural",
    "language": "English (United States)",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "ABB946",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Amber",
    "voiceName": "en-US-AmberNeural",
    "language": "English (United States)",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "BFA479",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Ana",
    "voiceName": "en-US-AnaNeural",
    "language": "English (United States)",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "BBB991",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Ashley",
    "voiceName": "en-US-AshleyNeural",
    "language": "English (United States)",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "AFD336",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Brandon",
    "voiceName": "en-US-BrandonNeural",
    "language": "English (United States)",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "FAA822",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Christopher",
    "voiceName": "en-US-ChristopherNeural",
    "language": "English (United States)",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "BFF445",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Cora",
    "voiceName": "en-US-CoraNeural",
    "language": "English (United States)",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "ACB403",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Elizabeth",
    "voiceName": "en-US-ElizabethNeural",
    "language": "English (United States)",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "ACD331",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Eric",
    "voiceName": "en-US-EricNeural",
    "language": "English (United States)",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "DBF492",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Jacob",
    "voiceName": "en-US-JacobNeural",
    "language": "English (United States)",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "DAD243",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Jenny Multilingual",
    "voiceName": "en-US-JennyMultilingualNeural",
    "language": "English (United States)",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "BFC975",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Michelle",
    "voiceName": "en-US-MichelleNeural",
    "language": "English (United States)",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "EAB157",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Monica",
    "voiceName": "en-US-MonicaNeural",
    "language": "English (United States)",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "FFA688",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Roger",
    "voiceName": "en-US-RogerNeural",
    "language": "English (United States)",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "DEE812",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Ryan Multilingual",
    "voiceName": "en-US-RyanMultilingualNeural",
    "language": "English (United States)",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "EAA994",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Steffan",
    "voiceName": "en-US-SteffanNeural",
    "language": "English (United States)",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "CDF239",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Adam Multilingual",
    "voiceName": "en-US-AdamMultilingualNeural",
    "language": "English (United States)",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "CDC463",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "AIGenerate1",
    "voiceName": "en-US-AIGenerate1Neural",
    "language": "English (United States)",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "ACD375",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "AIGenerate2",
    "voiceName": "en-US-AIGenerate2Neural",
    "language": "English (United States)",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "BDF662",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Alloy Turbo Multilingual",
    "voiceName": "en-US-AlloyTurboMultilingualNeural",
    "language": "English (United States)",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "EEE926",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Amanda Multilingual",
    "voiceName": "en-US-AmandaMultilingualNeural",
    "language": "English (United States)",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "BEA952",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Blue",
    "voiceName": "en-US-BlueNeural",
    "language": "English (United States)",
    "gender": "Neutral",
    "country": "us"
}, {
    "voiceId": "CEC354",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Davis Multilingual",
    "voiceName": "en-US-DavisMultilingualNeural",
    "language": "English (United States)",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "FED247",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Derek Multilingual",
    "voiceName": "en-US-DerekMultilingualNeural",
    "language": "English (United States)",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "FDD345",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Dustin Multilingual",
    "voiceName": "en-US-DustinMultilingualNeural",
    "language": "English (United States)",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "AAD293",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Echo Turbo Multilingual",
    "voiceName": "en-US-EchoTurboMultilingualNeural",
    "language": "English (United States)",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "CDC953",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Evelyn Multilingual",
    "voiceName": "en-US-EvelynMultilingualNeural",
    "language": "English (United States)",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "FDB553",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Fable Turbo Multilingual",
    "voiceName": "en-US-FableTurboMultilingualNeural",
    "language": "English (United States)",
    "gender": "Neutral",
    "country": "us"
}, {
    "voiceId": "AAC709",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Lewis Multilingual",
    "voiceName": "en-US-LewisMultilingualNeural",
    "language": "English (United States)",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "BFD351",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Lola Multilingual",
    "voiceName": "en-US-LolaMultilingualNeural",
    "language": "English (United States)",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "FFA994",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Nancy Multilingual",
    "voiceName": "en-US-NancyMultilingualNeural",
    "language": "English (United States)",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "DCF524",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Nova Turbo Multilingual",
    "voiceName": "en-US-NovaTurboMultilingualNeural",
    "language": "English (United States)",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "BEB968",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Onyx Turbo Multilingual",
    "voiceName": "en-US-OnyxTurboMultilingualNeural",
    "language": "English (United States)",
    "gender": "Male",
    "avatar": "onyx.svg",
    "description": "Older, mature, and experienced",
    "country": "us"
}, {
    "voiceId": "CBF933",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Phoebe Multilingual",
    "voiceName": "en-US-PhoebeMultilingualNeural",
    "language": "English (United States)",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "EBB301",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Samuel Multilingual",
    "voiceName": "en-US-SamuelMultilingualNeural",
    "language": "English (United States)",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "CFF866",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Serena Multilingual",
    "voiceName": "en-US-SerenaMultilingualNeural",
    "language": "English (United States)",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "CAC450",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Shimmer Turbo Multilingual",
    "voiceName": "en-US-ShimmerTurboMultilingualNeural",
    "language": "English (United States)",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "BEC681",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Steffan Multilingual",
    "voiceName": "en-US-SteffanMultilingualNeural",
    "language": "English (United States)",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "FEF910",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Andrew Dragon HD Latest",
    "voiceName": "en-US-Andrew:DragonHDLatestNeural",
    "language": "English (United States)",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "CBF398",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Andrew2 Dragon HD Latest",
    "voiceName": "en-US-Andrew2:DragonHDLatestNeural",
    "language": "English (United States)",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "ABD835",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Aria Dragon HD Latest",
    "voiceName": "en-US-Aria:DragonHDLatestNeural",
    "language": "English (United States)",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "BBC940",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Ava Dragon HD Latest",
    "voiceName": "en-US-Ava:DragonHDLatestNeural",
    "language": "English (United States)",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "FCF001",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Brian Dragon HD Latest",
    "voiceName": "en-US-Brian:DragonHDLatestNeural",
    "language": "English (United States)",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "BFD012",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Davis Dragon HD Latest",
    "voiceName": "en-US-Davis:DragonHDLatestNeural",
    "language": "English (United States)",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "ABD704",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Emma Dragon HD Latest",
    "voiceName": "en-US-Emma:DragonHDLatestNeural",
    "language": "English (United States)",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "FAB085",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Emma2 Dragon HD Latest",
    "voiceName": "en-US-Emma2:DragonHDLatestNeural",
    "language": "English (United States)",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "DFC041",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Jenny Dragon HD Latest",
    "voiceName": "en-US-Jenny:DragonHDLatestNeural",
    "language": "English (United States)",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "EEE913",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Steffan Dragon HD Latest",
    "voiceName": "en-US-Steffan:DragonHDLatestNeural",
    "language": "English (United States)",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "CDC668",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-ZA",
    "displayName": "Leah",
    "voiceName": "en-ZA-LeahNeural",
    "language": "English (South Africa)",
    "gender": "Female",
    "country": "za"
}, {
    "voiceId": "CDF055",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-ZA",
    "displayName": "Luke",
    "voiceName": "en-ZA-LukeNeural",
    "language": "English (South Africa)",
    "gender": "Male",
    "country": "za"
}, {
    "voiceId": "DAD751",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-AR",
    "displayName": "Elena",
    "voiceName": "es-AR-ElenaNeural",
    "language": "Spanish (Argentina)",
    "gender": "Female",
    "country": "ar"
}, {
    "voiceId": "EAB459",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-AR",
    "displayName": "Tomas",
    "voiceName": "es-AR-TomasNeural",
    "language": "Spanish (Argentina)",
    "gender": "Male",
    "country": "ar"
}, {
    "voiceId": "EBC285",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-BO",
    "displayName": "Sofia",
    "voiceName": "es-BO-SofiaNeural",
    "language": "Spanish (Bolivia)",
    "gender": "Female",
    "country": "bo"
}, {
    "voiceId": "DFD262",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-BO",
    "displayName": "Marcelo",
    "voiceName": "es-BO-MarceloNeural",
    "language": "Spanish (Bolivia)",
    "gender": "Male",
    "country": "bo"
}, {
    "voiceId": "ADD653",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-CL",
    "displayName": "Catalina",
    "voiceName": "es-CL-CatalinaNeural",
    "language": "Spanish (Chile)",
    "gender": "Female",
    "country": "cl"
}, {
    "voiceId": "EDC723",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-CL",
    "displayName": "Lorenzo",
    "voiceName": "es-CL-LorenzoNeural",
    "language": "Spanish (Chile)",
    "gender": "Male",
    "country": "cl"
}, {
    "voiceId": "EBD099",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-CO",
    "displayName": "Salome",
    "voiceName": "es-CO-SalomeNeural",
    "language": "Spanish (Colombia)",
    "gender": "Female",
    "country": "co"
}, {
    "voiceId": "ACA467",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-CO",
    "displayName": "Gonzalo",
    "voiceName": "es-CO-GonzaloNeural",
    "language": "Spanish (Colombia)",
    "gender": "Male",
    "country": "co"
}, {
    "voiceId": "DCF183",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-CR",
    "displayName": "Mar\u00eda",
    "voiceName": "es-CR-MariaNeural",
    "language": "Spanish (Costa Rica)",
    "gender": "Female",
    "country": "cr"
}, {
    "voiceId": "BBA277",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-CR",
    "displayName": "Juan",
    "voiceName": "es-CR-JuanNeural",
    "language": "Spanish (Costa Rica)",
    "gender": "Male",
    "country": "cr"
}, {
    "voiceId": "DBF544",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-CU",
    "displayName": "Belkys",
    "voiceName": "es-CU-BelkysNeural",
    "language": "Spanish (Cuba)",
    "gender": "Female",
    "country": "cu"
}, {
    "voiceId": "CBD563",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-CU",
    "displayName": "Manuel",
    "voiceName": "es-CU-ManuelNeural",
    "language": "Spanish (Cuba)",
    "gender": "Male",
    "country": "cu"
}, {
    "voiceId": "CEB050",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-DO",
    "displayName": "Ramona",
    "voiceName": "es-DO-RamonaNeural",
    "language": "Spanish (Dominican Republic)",
    "gender": "Female",
    "country": "do"
}, {
    "voiceId": "FAA888",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-DO",
    "displayName": "Emilio",
    "voiceName": "es-DO-EmilioNeural",
    "language": "Spanish (Dominican Republic)",
    "gender": "Male",
    "country": "do"
}, {
    "voiceId": "BFD894",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-EC",
    "displayName": "Andrea",
    "voiceName": "es-EC-AndreaNeural",
    "language": "Spanish (Ecuador)",
    "gender": "Female",
    "country": "ec"
}, {
    "voiceId": "DBE985",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-EC",
    "displayName": "Luis",
    "voiceName": "es-EC-LuisNeural",
    "language": "Spanish (Ecuador)",
    "gender": "Male",
    "country": "ec"
}, {
    "voiceId": "ABB927",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-ES",
    "displayName": "Elvira",
    "voiceName": "es-ES-ElviraNeural",
    "language": "Spanish (Spain)",
    "gender": "Female",
    "country": "es"
}, {
    "voiceId": "CFF105",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-ES",
    "displayName": "\u00c1lvaro",
    "voiceName": "es-ES-AlvaroNeural",
    "language": "Spanish (Spain)",
    "gender": "Male",
    "country": "es"
}, {
    "voiceId": "EBA123",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "es-ES",
    "displayName": "Arabella Multilingual",
    "voiceName": "es-ES-ArabellaMultilingualNeural",
    "language": "Spanish (Spain)",
    "gender": "Female",
    "country": "es"
}, {
    "voiceId": "BAB359",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "es-ES",
    "displayName": "Isidora Multilingual",
    "voiceName": "es-ES-IsidoraMultilingualNeural",
    "language": "Spanish (Spain)",
    "gender": "Female",
    "country": "es"
}, {
    "voiceId": "CFD961",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "es-ES",
    "displayName": "Tristan Multilingual",
    "voiceName": "es-ES-TristanMultilingualNeural",
    "language": "Spanish (Spain)",
    "gender": "Male",
    "country": "es"
}, {
    "voiceId": "BCD163",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "es-ES",
    "displayName": "Ximena Multilingual",
    "voiceName": "es-ES-XimenaMultilingualNeural",
    "language": "Spanish (Spain)",
    "gender": "Female",
    "country": "es"
}, {
    "voiceId": "EAF962",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-ES",
    "displayName": "Abril",
    "voiceName": "es-ES-AbrilNeural",
    "language": "Spanish (Spain)",
    "gender": "Female",
    "country": "es"
}, {
    "voiceId": "DDE550",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-ES",
    "displayName": "Arnau",
    "voiceName": "es-ES-ArnauNeural",
    "language": "Spanish (Spain)",
    "gender": "Male",
    "country": "es"
}, {
    "voiceId": "FBA530",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-ES",
    "displayName": "Dario",
    "voiceName": "es-ES-DarioNeural",
    "language": "Spanish (Spain)",
    "gender": "Male",
    "country": "es"
}, {
    "voiceId": "BBF570",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-ES",
    "displayName": "Elias",
    "voiceName": "es-ES-EliasNeural",
    "language": "Spanish (Spain)",
    "gender": "Male",
    "country": "es"
}, {
    "voiceId": "BEB622",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-ES",
    "displayName": "Estrella",
    "voiceName": "es-ES-EstrellaNeural",
    "language": "Spanish (Spain)",
    "gender": "Female",
    "country": "es"
}, {
    "voiceId": "CDA392",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-ES",
    "displayName": "Irene",
    "voiceName": "es-ES-IreneNeural",
    "language": "Spanish (Spain)",
    "gender": "Female",
    "country": "es"
}, {
    "voiceId": "FED809",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-ES",
    "displayName": "Laia",
    "voiceName": "es-ES-LaiaNeural",
    "language": "Spanish (Spain)",
    "gender": "Female",
    "country": "es"
}, {
    "voiceId": "EFC051",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-ES",
    "displayName": "Lia",
    "voiceName": "es-ES-LiaNeural",
    "language": "Spanish (Spain)",
    "gender": "Female",
    "country": "es"
}, {
    "voiceId": "CDD342",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-ES",
    "displayName": "Nil",
    "voiceName": "es-ES-NilNeural",
    "language": "Spanish (Spain)",
    "gender": "Male",
    "country": "es"
}, {
    "voiceId": "BCA182",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-ES",
    "displayName": "Saul",
    "voiceName": "es-ES-SaulNeural",
    "language": "Spanish (Spain)",
    "gender": "Male",
    "country": "es"
}, {
    "voiceId": "EBF146",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-ES",
    "displayName": "Teo",
    "voiceName": "es-ES-TeoNeural",
    "language": "Spanish (Spain)",
    "gender": "Male",
    "country": "es"
}, {
    "voiceId": "BAF084",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-ES",
    "displayName": "Triana",
    "voiceName": "es-ES-TrianaNeural",
    "language": "Spanish (Spain)",
    "gender": "Female",
    "country": "es"
}, {
    "voiceId": "BFB687",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-ES",
    "displayName": "Vera",
    "voiceName": "es-ES-VeraNeural",
    "language": "Spanish (Spain)",
    "gender": "Female",
    "country": "es"
}, {
    "voiceId": "AEE460",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-ES",
    "displayName": "Ximena",
    "voiceName": "es-ES-XimenaNeural",
    "language": "Spanish (Spain)",
    "gender": "Female",
    "country": "es"
}, {
    "voiceId": "BDD664",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-GQ",
    "displayName": "Teresa",
    "voiceName": "es-GQ-TeresaNeural",
    "language": "Spanish (Equatorial Guinea)",
    "gender": "Female",
    "country": "gq"
}, {
    "voiceId": "DEC968",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-GQ",
    "displayName": "Javier",
    "voiceName": "es-GQ-JavierNeural",
    "language": "Spanish (Equatorial Guinea)",
    "gender": "Male",
    "country": "gq"
}, {
    "voiceId": "FAE376",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-GT",
    "displayName": "Marta",
    "voiceName": "es-GT-MartaNeural",
    "language": "Spanish (Guatemala)",
    "gender": "Female",
    "country": "gt"
}, {
    "voiceId": "ADE966",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-GT",
    "displayName": "Andr\u00e9s",
    "voiceName": "es-GT-AndresNeural",
    "language": "Spanish (Guatemala)",
    "gender": "Male",
    "country": "gt"
}, {
    "voiceId": "EFD195",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-HN",
    "displayName": "Karla",
    "voiceName": "es-HN-KarlaNeural",
    "language": "Spanish (Honduras)",
    "gender": "Female",
    "country": "hn"
}, {
    "voiceId": "CAD825",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-HN",
    "displayName": "Carlos",
    "voiceName": "es-HN-CarlosNeural",
    "language": "Spanish (Honduras)",
    "gender": "Male",
    "country": "hn"
}, {
    "voiceId": "BEE182",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-MX",
    "displayName": "Dalia",
    "voiceName": "es-MX-DaliaNeural",
    "language": "Spanish (Mexican)",
    "gender": "Female",
    "country": "mx"
}, {
    "voiceId": "DDC731",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-MX",
    "displayName": "Jorge",
    "voiceName": "es-MX-JorgeNeural",
    "language": "Spanish (Mexican)",
    "gender": "Male",
    "country": "mx"
}, {
    "voiceId": "FCF693",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-MX",
    "displayName": "Beatriz",
    "voiceName": "es-MX-BeatrizNeural",
    "language": "Spanish (Mexican)",
    "gender": "Female",
    "country": "mx"
}, {
    "voiceId": "FEF899",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-MX",
    "displayName": "Candela",
    "voiceName": "es-MX-CandelaNeural",
    "language": "Spanish (Mexican)",
    "gender": "Female",
    "country": "mx"
}, {
    "voiceId": "ABF392",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-MX",
    "displayName": "Carlota",
    "voiceName": "es-MX-CarlotaNeural",
    "language": "Spanish (Mexican)",
    "gender": "Female",
    "country": "mx"
}, {
    "voiceId": "CCD286",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-MX",
    "displayName": "Cecilio",
    "voiceName": "es-MX-CecilioNeural",
    "language": "Spanish (Mexican)",
    "gender": "Male",
    "country": "mx"
}, {
    "voiceId": "DBC869",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-MX",
    "displayName": "Gerardo",
    "voiceName": "es-MX-GerardoNeural",
    "language": "Spanish (Mexican)",
    "gender": "Male",
    "country": "mx"
}, {
    "voiceId": "FBC235",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-MX",
    "displayName": "Larissa",
    "voiceName": "es-MX-LarissaNeural",
    "language": "Spanish (Mexican)",
    "gender": "Female",
    "country": "mx"
}, {
    "voiceId": "CEC029",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-MX",
    "displayName": "Liberto",
    "voiceName": "es-MX-LibertoNeural",
    "language": "Spanish (Mexican)",
    "gender": "Male",
    "country": "mx"
}, {
    "voiceId": "CAB930",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-MX",
    "displayName": "Luciano",
    "voiceName": "es-MX-LucianoNeural",
    "language": "Spanish (Mexican)",
    "gender": "Male",
    "country": "mx"
}, {
    "voiceId": "FBE355",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-MX",
    "displayName": "Marina",
    "voiceName": "es-MX-MarinaNeural",
    "language": "Spanish (Mexican)",
    "gender": "Female",
    "country": "mx"
}, {
    "voiceId": "FBB408",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-MX",
    "displayName": "Nuria",
    "voiceName": "es-MX-NuriaNeural",
    "language": "Spanish (Mexican)",
    "gender": "Female",
    "country": "mx"
}, {
    "voiceId": "CBA576",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-MX",
    "displayName": "Pelayo",
    "voiceName": "es-MX-PelayoNeural",
    "language": "Spanish (Mexican)",
    "gender": "Male",
    "country": "mx"
}, {
    "voiceId": "DEB016",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-MX",
    "displayName": "Renata",
    "voiceName": "es-MX-RenataNeural",
    "language": "Spanish (Mexican)",
    "gender": "Female",
    "country": "mx"
}, {
    "voiceId": "BBA443",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-MX",
    "displayName": "Yago",
    "voiceName": "es-MX-YagoNeural",
    "language": "Spanish (Mexican)",
    "gender": "Male",
    "country": "mx"
}, {
    "voiceId": "BCD577",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-NI",
    "displayName": "Yolanda",
    "voiceName": "es-NI-YolandaNeural",
    "language": "Spanish (Nicaragua)",
    "gender": "Female",
    "country": "ni"
}, {
    "voiceId": "EAD483",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-NI",
    "displayName": "Federico",
    "voiceName": "es-NI-FedericoNeural",
    "language": "Spanish (Nicaragua)",
    "gender": "Male",
    "country": "ni"
}, {
    "voiceId": "BED737",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-PA",
    "displayName": "Margarita",
    "voiceName": "es-PA-MargaritaNeural",
    "language": "Spanish (Panama)",
    "gender": "Female",
    "country": "pa"
}, {
    "voiceId": "BBF947",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-PA",
    "displayName": "Roberto",
    "voiceName": "es-PA-RobertoNeural",
    "language": "Spanish (Panama)",
    "gender": "Male",
    "country": "pa"
}, {
    "voiceId": "DDF081",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-PE",
    "displayName": "Camila",
    "voiceName": "es-PE-CamilaNeural",
    "language": "Spanish (Peru)",
    "gender": "Female",
    "country": "pe"
}, {
    "voiceId": "BDA506",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-PE",
    "displayName": "Alex",
    "voiceName": "es-PE-AlexNeural",
    "language": "Spanish (Peru)",
    "gender": "Male",
    "country": "pe"
}, {
    "voiceId": "AAA796",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-PR",
    "displayName": "Karina",
    "voiceName": "es-PR-KarinaNeural",
    "language": "Spanish (Puerto Rico)",
    "gender": "Female",
    "country": "pr"
}, {
    "voiceId": "DCE770",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-PR",
    "displayName": "V\u00edctor",
    "voiceName": "es-PR-VictorNeural",
    "language": "Spanish (Puerto Rico)",
    "gender": "Male",
    "country": "pr"
}, {
    "voiceId": "DAC198",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-PY",
    "displayName": "Tania",
    "voiceName": "es-PY-TaniaNeural",
    "language": "Spanish (Paraguay)",
    "gender": "Female",
    "country": "py"
}, {
    "voiceId": "FFC179",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-PY",
    "displayName": "Mario",
    "voiceName": "es-PY-MarioNeural",
    "language": "Spanish (Paraguay)",
    "gender": "Male",
    "country": "py"
}, {
    "voiceId": "FED803",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-SV",
    "displayName": "Lorena",
    "voiceName": "es-SV-LorenaNeural",
    "language": "Spanish (El Salvador)",
    "gender": "Female",
    "country": "sv"
}, {
    "voiceId": "EAF388",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-SV",
    "displayName": "Rodrigo",
    "voiceName": "es-SV-RodrigoNeural",
    "language": "Spanish (El Salvador)",
    "gender": "Male",
    "country": "sv"
}, {
    "voiceId": "CDA703",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-US",
    "displayName": "Paloma",
    "voiceName": "es-US-PalomaNeural",
    "language": "Spanish (United States)",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "CCE845",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-US",
    "displayName": "Alonso",
    "voiceName": "es-US-AlonsoNeural",
    "language": "Spanish (United States)",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "DAE330",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-UY",
    "displayName": "Valentina",
    "voiceName": "es-UY-ValentinaNeural",
    "language": "Spanish (Uruguay)",
    "gender": "Female",
    "country": "uy"
}, {
    "voiceId": "EAE071",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-UY",
    "displayName": "Mateo",
    "voiceName": "es-UY-MateoNeural",
    "language": "Spanish (Uruguay)",
    "gender": "Male",
    "country": "uy"
}, {
    "voiceId": "BCD794",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-VE",
    "displayName": "Paola",
    "voiceName": "es-VE-PaolaNeural",
    "language": "Spanish (Venezuela)",
    "gender": "Female",
    "country": "ve"
}, {
    "voiceId": "DFA040",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "es-VE",
    "displayName": "Sebasti\u00e1n",
    "voiceName": "es-VE-SebastianNeural",
    "language": "Spanish (Venezuela)",
    "gender": "Male",
    "country": "ve"
}, {
    "voiceId": "BAC994",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "et-EE",
    "displayName": "Anu",
    "voiceName": "et-EE-AnuNeural",
    "language": "Estonian (Estonia)",
    "gender": "Female",
    "country": "ee"
}, {
    "voiceId": "DBF086",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "et-EE",
    "displayName": "Kert",
    "voiceName": "et-EE-KertNeural",
    "language": "Estonian (Estonia)",
    "gender": "Male",
    "country": "ee"
}, {
    "voiceId": "DDD589",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "eu-ES",
    "displayName": "Ainhoa",
    "voiceName": "eu-ES-AinhoaNeural",
    "language": "Basque (Spain)",
    "gender": "Female",
    "country": "es"
}, {
    "voiceId": "BCB337",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "eu-ES",
    "displayName": "Ander",
    "voiceName": "eu-ES-AnderNeural",
    "language": "Basque (Spain)",
    "gender": "Male",
    "country": "es"
}, {
    "voiceId": "EBA830",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "fa-IR",
    "displayName": "\u062f\u0644\u0627\u0631\u0627",
    "voiceName": "fa-IR-DilaraNeural",
    "language": "Persian (Iran)",
    "gender": "Female",
    "country": "ir"
}, {
    "voiceId": "AEF076",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "fa-IR",
    "displayName": "\u0641\u0631\u06cc\u062f",
    "voiceName": "fa-IR-FaridNeural",
    "language": "Persian (Iran)",
    "gender": "Male",
    "country": "ir"
}, {
    "voiceId": "AFE637",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "fi-FI",
    "displayName": "Selma",
    "voiceName": "fi-FI-SelmaNeural",
    "language": "Finnish (Finland)",
    "gender": "Female",
    "country": "fi"
}, {
    "voiceId": "AFE611",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "fi-FI",
    "displayName": "Harri",
    "voiceName": "fi-FI-HarriNeural",
    "language": "Finnish (Finland)",
    "gender": "Male",
    "country": "fi"
}, {
    "voiceId": "FAE193",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "fi-FI",
    "displayName": "Noora",
    "voiceName": "fi-FI-NooraNeural",
    "language": "Finnish (Finland)",
    "gender": "Female",
    "country": "fi"
}, {
    "voiceId": "DDA099",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "fil-PH",
    "displayName": "Blessica",
    "voiceName": "fil-PH-BlessicaNeural",
    "language": "Filipino (Philippines)",
    "gender": "Female",
    "country": "ph"
}, {
    "voiceId": "ADE209",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "fil-PH",
    "displayName": "Angelo",
    "voiceName": "fil-PH-AngeloNeural",
    "language": "Filipino (Philippines)",
    "gender": "Male",
    "country": "ph"
}, {
    "voiceId": "FEB024",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "fr-BE",
    "displayName": "Charline",
    "voiceName": "fr-BE-CharlineNeural",
    "language": "French (Belgium)",
    "gender": "Female",
    "country": "be"
}, {
    "voiceId": "DBB671",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "fr-BE",
    "displayName": "Gerard",
    "voiceName": "fr-BE-GerardNeural",
    "language": "French (Belgium)",
    "gender": "Male",
    "country": "be"
}, {
    "voiceId": "CAC688",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "fr-CA",
    "displayName": "Sylvie",
    "voiceName": "fr-CA-SylvieNeural",
    "language": "French (Canada)",
    "gender": "Female",
    "country": "ca"
}, {
    "voiceId": "FEB053",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "fr-CA",
    "displayName": "Jean",
    "voiceName": "fr-CA-JeanNeural",
    "language": "French (Canada)",
    "gender": "Male",
    "country": "ca"
}, {
    "voiceId": "ACE240",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "fr-CA",
    "displayName": "Antoine",
    "voiceName": "fr-CA-AntoineNeural",
    "language": "French (Canada)",
    "gender": "Male",
    "country": "ca"
}, {
    "voiceId": "ABB599",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "fr-CA",
    "displayName": "Thierry",
    "voiceName": "fr-CA-ThierryNeural",
    "language": "French (Canada)",
    "gender": "Male",
    "country": "ca"
}, {
    "voiceId": "CFC912",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "fr-CH",
    "displayName": "Ariane",
    "voiceName": "fr-CH-ArianeNeural",
    "language": "French (Switzerland)",
    "gender": "Female",
    "country": "ch"
}, {
    "voiceId": "DFE338",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "fr-CH",
    "displayName": "Fabrice",
    "voiceName": "fr-CH-FabriceNeural",
    "language": "French (Switzerland)",
    "gender": "Male",
    "country": "ch"
}, {
    "voiceId": "EFC155",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "fr-FR",
    "displayName": "Denise",
    "voiceName": "fr-FR-DeniseNeural",
    "language": "French (France)",
    "gender": "Female",
    "country": "fr"
}, {
    "voiceId": "BDE583",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "fr-FR",
    "displayName": "Henri",
    "voiceName": "fr-FR-HenriNeural",
    "language": "French (France)",
    "gender": "Male",
    "country": "fr"
}, {
    "voiceId": "ABD094",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "fr-FR",
    "displayName": "Vivienne Multilingue",
    "voiceName": "fr-FR-VivienneMultilingualNeural",
    "language": "French (France)",
    "gender": "Female",
    "country": "fr"
}, {
    "voiceId": "FDB451",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "fr-FR",
    "displayName": "R\u00e9my Multilingue",
    "voiceName": "fr-FR-RemyMultilingualNeural",
    "language": "French (France)",
    "gender": "Male",
    "country": "fr"
}, {
    "voiceId": "CDD073",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "fr-FR",
    "displayName": "Lucien Multilingual",
    "voiceName": "fr-FR-LucienMultilingualNeural",
    "language": "French (France)",
    "gender": "Male",
    "country": "fr"
}, {
    "voiceId": "AEA665",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "fr-FR",
    "displayName": "Alain",
    "voiceName": "fr-FR-AlainNeural",
    "language": "French (France)",
    "gender": "Male",
    "country": "fr"
}, {
    "voiceId": "DEB270",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "fr-FR",
    "displayName": "Brigitte",
    "voiceName": "fr-FR-BrigitteNeural",
    "language": "French (France)",
    "gender": "Female",
    "country": "fr"
}, {
    "voiceId": "AFD050",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "fr-FR",
    "displayName": "Celeste",
    "voiceName": "fr-FR-CelesteNeural",
    "language": "French (France)",
    "gender": "Female",
    "country": "fr"
}, {
    "voiceId": "ABB160",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "fr-FR",
    "displayName": "Claude",
    "voiceName": "fr-FR-ClaudeNeural",
    "language": "French (France)",
    "gender": "Male",
    "country": "fr"
}, {
    "voiceId": "FAB749",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "fr-FR",
    "displayName": "Coralie",
    "voiceName": "fr-FR-CoralieNeural",
    "language": "French (France)",
    "gender": "Female",
    "country": "fr"
}, {
    "voiceId": "ADA159",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "fr-FR",
    "displayName": "Eloise",
    "voiceName": "fr-FR-EloiseNeural",
    "language": "French (France)",
    "gender": "Female",
    "country": "fr"
}, {
    "voiceId": "CFC590",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "fr-FR",
    "displayName": "Jacqueline",
    "voiceName": "fr-FR-JacquelineNeural",
    "language": "French (France)",
    "gender": "Female",
    "country": "fr"
}, {
    "voiceId": "CDC502",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "fr-FR",
    "displayName": "Jerome",
    "voiceName": "fr-FR-JeromeNeural",
    "language": "French (France)",
    "gender": "Male",
    "country": "fr"
}, {
    "voiceId": "EDD530",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "fr-FR",
    "displayName": "Josephine",
    "voiceName": "fr-FR-JosephineNeural",
    "language": "French (France)",
    "gender": "Female",
    "country": "fr"
}, {
    "voiceId": "AAE458",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "fr-FR",
    "displayName": "Maurice",
    "voiceName": "fr-FR-MauriceNeural",
    "language": "French (France)",
    "gender": "Male",
    "country": "fr"
}, {
    "voiceId": "FDC248",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "fr-FR",
    "displayName": "Yves",
    "voiceName": "fr-FR-YvesNeural",
    "language": "French (France)",
    "gender": "Male",
    "country": "fr"
}, {
    "voiceId": "BAD427",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "fr-FR",
    "displayName": "Yvette",
    "voiceName": "fr-FR-YvetteNeural",
    "language": "French (France)",
    "gender": "Female",
    "country": "fr"
}, {
    "voiceId": "ADE833",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ga-IE",
    "displayName": "Orla",
    "voiceName": "ga-IE-OrlaNeural",
    "language": "Irish (Ireland)",
    "gender": "Female",
    "country": "ie"
}, {
    "voiceId": "FAB540",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ga-IE",
    "displayName": "Colm",
    "voiceName": "ga-IE-ColmNeural",
    "language": "Irish (Ireland)",
    "gender": "Male",
    "country": "ie"
}, {
    "voiceId": "CFF034",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "gl-ES",
    "displayName": "Sabela",
    "voiceName": "gl-ES-SabelaNeural",
    "language": "Galician (Spain)",
    "gender": "Female",
    "country": "es"
}, {
    "voiceId": "DAF782",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "gl-ES",
    "displayName": "Roi",
    "voiceName": "gl-ES-RoiNeural",
    "language": "Galician (Spain)",
    "gender": "Male",
    "country": "es"
}, {
    "voiceId": "CDD407",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "gu-IN",
    "displayName": "\u0aa7\u0acd\u0ab5\u0aa8\u0ac0",
    "voiceName": "gu-IN-DhwaniNeural",
    "language": "Gujarati (India)",
    "gender": "Female",
    "country": "in"
}, {
    "voiceId": "DED256",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "gu-IN",
    "displayName": "\u0aa8\u0abf\u0ab0\u0a82\u0a9c\u0aa8",
    "voiceName": "gu-IN-NiranjanNeural",
    "language": "Gujarati (India)",
    "gender": "Male",
    "country": "in"
}, {
    "voiceId": "AEA282",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "he-IL",
    "displayName": "\u05d4\u05d9\u05dc\u05d4",
    "voiceName": "he-IL-HilaNeural",
    "language": "Hebrew (Israel)",
    "gender": "Female",
    "country": "il"
}, {
    "voiceId": "FAF325",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "he-IL",
    "displayName": "\u05d0\u05d1\u05e8\u05d9",
    "voiceName": "he-IL-AvriNeural",
    "language": "Hebrew (Israel)",
    "gender": "Male",
    "country": "il"
}, {
    "voiceId": "DEC611",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "hi-IN",
    "displayName": "\u0906\u0930\u0935 ",
    "voiceName": "hi-IN-AaravNeural",
    "language": "Hindi (India)",
    "gender": "Male",
    "country": "in"
}, {
    "voiceId": "DBF690",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "hi-IN",
    "displayName": "\u0905\u0928\u0928\u094d\u092f\u093e",
    "voiceName": "hi-IN-AnanyaNeural",
    "language": "Hindi (India)",
    "gender": "Female",
    "country": "in"
}, {
    "voiceId": "CAA366",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "hi-IN",
    "displayName": "\u0915\u093e\u0935\u094d\u092f\u093e",
    "voiceName": "hi-IN-KavyaNeural",
    "language": "Hindi (India)",
    "gender": "Female",
    "country": "in"
}, {
    "voiceId": "BFA253",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "hi-IN",
    "displayName": "\u0915\u0941\u0928\u093e\u0932 ",
    "voiceName": "hi-IN-KunalNeural",
    "language": "Hindi (India)",
    "gender": "Male",
    "country": "in"
}, {
    "voiceId": "EBD601",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "hi-IN",
    "displayName": "\u0930\u0947\u0939\u093e\u0928",
    "voiceName": "hi-IN-RehaanNeural",
    "language": "Hindi (India)",
    "gender": "Male",
    "country": "in"
}, {
    "voiceId": "FDB647",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "hi-IN",
    "displayName": "\u0938\u094d\u0935\u0930\u093e",
    "voiceName": "hi-IN-SwaraNeural",
    "language": "Hindi (India)",
    "gender": "Female",
    "country": "in"
}, {
    "voiceId": "DFF325",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "hi-IN",
    "displayName": "\u092e\u0927\u0941\u0930",
    "voiceName": "hi-IN-MadhurNeural",
    "language": "Hindi (India)",
    "gender": "Male",
    "country": "in"
}, {
    "voiceId": "FCD830",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "hi-IN",
    "displayName": "Aarti",
    "voiceName": "hi-IN-AartiNeural",
    "language": "Hindi (India)",
    "gender": "Female",
    "country": "in"
}, {
    "voiceId": "FAE977",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "hr-HR",
    "displayName": "Gabrijela",
    "voiceName": "hr-HR-GabrijelaNeural",
    "language": "Croatian (Croatia)",
    "gender": "Female",
    "country": "hr"
}, {
    "voiceId": "BBC472",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "hr-HR",
    "displayName": "Sre\u0107ko",
    "voiceName": "hr-HR-SreckoNeural",
    "language": "Croatian (Croatia)",
    "gender": "Male",
    "country": "hr"
}, {
    "voiceId": "EFF861",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "hu-HU",
    "displayName": "No\u00e9mi",
    "voiceName": "hu-HU-NoemiNeural",
    "language": "Hungarian (Hungary)",
    "gender": "Female",
    "country": "hu"
}, {
    "voiceId": "FFD803",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "hu-HU",
    "displayName": "Tam\u00e1s",
    "voiceName": "hu-HU-TamasNeural",
    "language": "Hungarian (Hungary)",
    "gender": "Male",
    "country": "hu"
}, {
    "voiceId": "DEA116",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "hy-AM",
    "displayName": "\u0531\u0576\u0561\u0570\u056b\u057f",
    "voiceName": "hy-AM-AnahitNeural",
    "language": "Armenian (Armenia)",
    "gender": "Female",
    "country": "am"
}, {
    "voiceId": "FED601",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "hy-AM",
    "displayName": "\u0540\u0561\u0575\u056f",
    "voiceName": "hy-AM-HaykNeural",
    "language": "Armenian (Armenia)",
    "gender": "Male",
    "country": "am"
}, {
    "voiceId": "EBE281",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "id-ID",
    "displayName": "Gadis",
    "voiceName": "id-ID-GadisNeural",
    "language": "Indonesian (Indonesia)",
    "gender": "Female",
    "country": "id"
}, {
    "voiceId": "FDC915",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "id-ID",
    "displayName": "Ardi",
    "voiceName": "id-ID-ArdiNeural",
    "language": "Indonesian (Indonesia)",
    "gender": "Male",
    "country": "id"
}, {
    "voiceId": "CFA396",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "is-IS",
    "displayName": "Gu\u00f0r\u00fan",
    "voiceName": "is-IS-GudrunNeural",
    "language": "Icelandic (Iceland)",
    "gender": "Female",
    "country": "is"
}, {
    "voiceId": "DAA663",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "is-IS",
    "displayName": "Gunnar",
    "voiceName": "is-IS-GunnarNeural",
    "language": "Icelandic (Iceland)",
    "gender": "Male",
    "country": "is"
}, {
    "voiceId": "ADD640",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "it-IT",
    "displayName": "Elsa",
    "voiceName": "it-IT-ElsaNeural",
    "language": "Italian (Italy)",
    "gender": "Female",
    "country": "it"
}, {
    "voiceId": "AAA145",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "it-IT",
    "displayName": "Isabella",
    "voiceName": "it-IT-IsabellaNeural",
    "language": "Italian (Italy)",
    "gender": "Female",
    "country": "it"
}, {
    "voiceId": "FBC025",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "it-IT",
    "displayName": "Diego",
    "voiceName": "it-IT-DiegoNeural",
    "language": "Italian (Italy)",
    "gender": "Male",
    "country": "it"
}, {
    "voiceId": "CBF823",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "it-IT",
    "displayName": "Alessio Multilingual",
    "voiceName": "it-IT-AlessioMultilingualNeural",
    "language": "Italian (Italy)",
    "gender": "Male",
    "country": "it"
}, {
    "voiceId": "ABF926",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "it-IT",
    "displayName": "Isabella Multilingual",
    "voiceName": "it-IT-IsabellaMultilingualNeural",
    "language": "Italian (Italy)",
    "gender": "Female",
    "country": "it"
}, {
    "voiceId": "AAD920",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "it-IT",
    "displayName": "Giuseppe Multilingual",
    "voiceName": "it-IT-GiuseppeMultilingualNeural",
    "language": "Italian (Italy)",
    "gender": "Male",
    "country": "it"
}, {
    "voiceId": "DBA969",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "it-IT",
    "displayName": "Marcello Multilingual",
    "voiceName": "it-IT-MarcelloMultilingualNeural",
    "language": "Italian (Italy)",
    "gender": "Male",
    "country": "it"
}, {
    "voiceId": "BCD661",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "it-IT",
    "displayName": "Benigno",
    "voiceName": "it-IT-BenignoNeural",
    "language": "Italian (Italy)",
    "gender": "Male",
    "country": "it"
}, {
    "voiceId": "CDA264",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "it-IT",
    "displayName": "Calimero",
    "voiceName": "it-IT-CalimeroNeural",
    "language": "Italian (Italy)",
    "gender": "Male",
    "country": "it"
}, {
    "voiceId": "DEE109",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "it-IT",
    "displayName": "Cataldo",
    "voiceName": "it-IT-CataldoNeural",
    "language": "Italian (Italy)",
    "gender": "Male",
    "country": "it"
}, {
    "voiceId": "BCD244",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "it-IT",
    "displayName": "Fabiola",
    "voiceName": "it-IT-FabiolaNeural",
    "language": "Italian (Italy)",
    "gender": "Female",
    "country": "it"
}, {
    "voiceId": "BCB074",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "it-IT",
    "displayName": "Fiamma",
    "voiceName": "it-IT-FiammaNeural",
    "language": "Italian (Italy)",
    "gender": "Female",
    "country": "it"
}, {
    "voiceId": "FBA719",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "it-IT",
    "displayName": "Gianni",
    "voiceName": "it-IT-GianniNeural",
    "language": "Italian (Italy)",
    "gender": "Male",
    "country": "it"
}, {
    "voiceId": "DAC369",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "it-IT",
    "displayName": "Giuseppe",
    "voiceName": "it-IT-GiuseppeNeural",
    "language": "Italian (Italy)",
    "gender": "Male",
    "country": "it"
}, {
    "voiceId": "BCD760",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "it-IT",
    "displayName": "Imelda",
    "voiceName": "it-IT-ImeldaNeural",
    "language": "Italian (Italy)",
    "gender": "Female",
    "country": "it"
}, {
    "voiceId": "EDA427",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "it-IT",
    "displayName": "Irma",
    "voiceName": "it-IT-IrmaNeural",
    "language": "Italian (Italy)",
    "gender": "Female",
    "country": "it"
}, {
    "voiceId": "FDC385",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "it-IT",
    "displayName": "Lisandro",
    "voiceName": "it-IT-LisandroNeural",
    "language": "Italian (Italy)",
    "gender": "Male",
    "country": "it"
}, {
    "voiceId": "FDE493",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "it-IT",
    "displayName": "Palmira",
    "voiceName": "it-IT-PalmiraNeural",
    "language": "Italian (Italy)",
    "gender": "Female",
    "country": "it"
}, {
    "voiceId": "CCE179",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "it-IT",
    "displayName": "Pierina",
    "voiceName": "it-IT-PierinaNeural",
    "language": "Italian (Italy)",
    "gender": "Female",
    "country": "it"
}, {
    "voiceId": "EAB151",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "it-IT",
    "displayName": "Rinaldo",
    "voiceName": "it-IT-RinaldoNeural",
    "language": "Italian (Italy)",
    "gender": "Male",
    "country": "it"
}, {
    "voiceId": "BFF911",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "iu-Cans-CA",
    "displayName": "\u14ef\u157f\u14c2\u1585",
    "voiceName": "iu-Cans-CA-SiqiniqNeural",
    "language": "language Not Found",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "DDE148",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "iu-Cans-CA",
    "displayName": "\u1455\u1585\u146d\u1585",
    "voiceName": "iu-Cans-CA-TaqqiqNeural",
    "language": "language Not Found",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "BBC105",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "iu-Latn-CA",
    "displayName": "\u14ef\u157f\u14c2\u1585",
    "voiceName": "iu-Latn-CA-SiqiniqNeural",
    "language": "language Not Found",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "BED202",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "iu-Latn-CA",
    "displayName": "\u1455\u1585\u146d\u1585",
    "voiceName": "iu-Latn-CA-TaqqiqNeural",
    "language": "language Not Found",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "AEA523",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ja-JP",
    "displayName": "\u4e03\u6d77",
    "voiceName": "ja-JP-NanamiNeural",
    "language": "Japanese (Japan)",
    "gender": "Female",
    "country": "jp"
}, {
    "voiceId": "BEA148",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ja-JP",
    "displayName": "\u572d\u592a",
    "voiceName": "ja-JP-KeitaNeural",
    "language": "Japanese (Japan)",
    "gender": "Male",
    "country": "jp"
}, {
    "voiceId": "DFE747",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ja-JP",
    "displayName": "\u78a7\u8863",
    "voiceName": "ja-JP-AoiNeural",
    "language": "Japanese (Japan)",
    "gender": "Female",
    "country": "jp"
}, {
    "voiceId": "CBE947",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ja-JP",
    "displayName": "\u5927\u667a",
    "voiceName": "ja-JP-DaichiNeural",
    "language": "Japanese (Japan)",
    "gender": "Male",
    "country": "jp"
}, {
    "voiceId": "FBC700",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ja-JP",
    "displayName": "\u771f\u5915",
    "voiceName": "ja-JP-MayuNeural",
    "language": "Japanese (Japan)",
    "gender": "Female",
    "country": "jp"
}, {
    "voiceId": "FDE303",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ja-JP",
    "displayName": "\u76f4\u7d00",
    "voiceName": "ja-JP-NaokiNeural",
    "language": "Japanese (Japan)",
    "gender": "Male",
    "country": "jp"
}, {
    "voiceId": "DCB804",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ja-JP",
    "displayName": "\u5fd7\u7e54",
    "voiceName": "ja-JP-ShioriNeural",
    "language": "Japanese (Japan)",
    "gender": "Female",
    "country": "jp"
}, {
    "voiceId": "EBE518",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "ja-JP",
    "displayName": "\u52dd \u591a\u8a00\u8a9e",
    "voiceName": "ja-JP-MasaruMultilingualNeural",
    "language": "Japanese (Japan)",
    "gender": "Male",
    "country": "jp"
}, {
    "voiceId": "BAF461",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ja-JP",
    "displayName": "Masaru Dragon HD Latest",
    "voiceName": "ja-JP-Masaru:DragonHDLatestNeural",
    "language": "Japanese (Japan)",
    "gender": "Male",
    "country": "jp"
}, {
    "voiceId": "BFB817",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "jv-ID",
    "displayName": "Siti",
    "voiceName": "jv-ID-SitiNeural",
    "language": "Javanese (Latin, Indonesia)",
    "gender": "Female",
    "country": "id"
}, {
    "voiceId": "CDB894",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "jv-ID",
    "displayName": "Dimas",
    "voiceName": "jv-ID-DimasNeural",
    "language": "Javanese (Latin, Indonesia)",
    "gender": "Male",
    "country": "id"
}, {
    "voiceId": "BFE347",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ka-GE",
    "displayName": "\u10d4\u10d9\u10d0",
    "voiceName": "ka-GE-EkaNeural",
    "language": "Georgian (Georgia)",
    "gender": "Female",
    "country": "ge"
}, {
    "voiceId": "ADC857",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ka-GE",
    "displayName": "\u10d2\u10d8\u10dd\u10e0\u10d2\u10d8",
    "voiceName": "ka-GE-GiorgiNeural",
    "language": "Georgian (Georgia)",
    "gender": "Male",
    "country": "ge"
}, {
    "voiceId": "FAC332",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "kk-KZ",
    "displayName": "\u0410\u0439\u0433\u04af\u043b",
    "voiceName": "kk-KZ-AigulNeural",
    "language": "Kazakh (Kazakhstan)",
    "gender": "Female",
    "country": "kz"
}, {
    "voiceId": "EAC436",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "kk-KZ",
    "displayName": "\u0414\u04d9\u0443\u043b\u0435\u0442",
    "voiceName": "kk-KZ-DauletNeural",
    "language": "Kazakh (Kazakhstan)",
    "gender": "Male",
    "country": "kz"
}, {
    "voiceId": "CFE030",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "km-KH",
    "displayName": "\u179f\u17d2\u179a\u17b8\u1798\u17bb\u17c6",
    "voiceName": "km-KH-SreymomNeural",
    "language": "Khmer (Cambodia)",
    "gender": "Female",
    "country": "kh"
}, {
    "voiceId": "DAD158",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "km-KH",
    "displayName": "\u1796\u17b7\u179f\u17b7\u178a\u17d2\u178b",
    "voiceName": "km-KH-PisethNeural",
    "language": "Khmer (Cambodia)",
    "gender": "Male",
    "country": "kh"
}, {
    "voiceId": "CCB635",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "kn-IN",
    "displayName": "\u0cb8\u0caa\u0ccd\u0ca8\u0cbe",
    "voiceName": "kn-IN-SapnaNeural",
    "language": "Kannada (India)",
    "gender": "Female",
    "country": "in"
}, {
    "voiceId": "CDB911",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "kn-IN",
    "displayName": "\u0c97\u0c97\u0ca8\u0ccd",
    "voiceName": "kn-IN-GaganNeural",
    "language": "Kannada (India)",
    "gender": "Male",
    "country": "in"
}, {
    "voiceId": "CFC188",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ko-KR",
    "displayName": "\uc120\ud788",
    "voiceName": "ko-KR-SunHiNeural",
    "language": "Korean (South Korea)",
    "gender": "Female",
    "country": "kr"
}, {
    "voiceId": "EFF215",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ko-KR",
    "displayName": "\uc778\uc900",
    "voiceName": "ko-KR-InJoonNeural",
    "language": "Korean (South Korea)",
    "gender": "Male",
    "country": "kr"
}, {
    "voiceId": "EEC024",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "ko-KR",
    "displayName": "Hyunsu Multilingual",
    "voiceName": "ko-KR-HyunsuMultilingualNeural",
    "language": "Korean (South Korea)",
    "gender": "Male",
    "country": "kr"
}, {
    "voiceId": "ABD060",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ko-KR",
    "displayName": "\ubd09\uc9c4",
    "voiceName": "ko-KR-BongJinNeural",
    "language": "Korean (South Korea)",
    "gender": "Male",
    "country": "kr"
}, {
    "voiceId": "CFA822",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ko-KR",
    "displayName": "\uad6d\ubbfc",
    "voiceName": "ko-KR-GookMinNeural",
    "language": "Korean (South Korea)",
    "gender": "Male",
    "country": "kr"
}, {
    "voiceId": "FFA037",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ko-KR",
    "displayName": "\ud604\uc218",
    "voiceName": "ko-KR-HyunsuNeural",
    "language": "Korean (South Korea)",
    "gender": "Male",
    "country": "kr"
}, {
    "voiceId": "ACE595",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ko-KR",
    "displayName": "\uc9c0\ubbfc",
    "voiceName": "ko-KR-JiMinNeural",
    "language": "Korean (South Korea)",
    "gender": "Female",
    "country": "kr"
}, {
    "voiceId": "BDF297",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ko-KR",
    "displayName": "\uc11c\ud604",
    "voiceName": "ko-KR-SeoHyeonNeural",
    "language": "Korean (South Korea)",
    "gender": "Female",
    "country": "kr"
}, {
    "voiceId": "EBD735",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ko-KR",
    "displayName": "\uc21c\ubcf5",
    "voiceName": "ko-KR-SoonBokNeural",
    "language": "Korean (South Korea)",
    "gender": "Female",
    "country": "kr"
}, {
    "voiceId": "AEF234",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ko-KR",
    "displayName": "\uc720\uc9c4",
    "voiceName": "ko-KR-YuJinNeural",
    "language": "Korean (South Korea)",
    "gender": "Female",
    "country": "kr"
}, {
    "voiceId": "DFA089",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "lo-LA",
    "displayName": "\u0ec1\u0e81\u0ec9\u0ea7\u0ea1\u0eb0\u0e99\u0eb5",
    "voiceName": "lo-LA-KeomanyNeural",
    "language": "Lao (Laos)",
    "gender": "Female",
    "country": "la"
}, {
    "voiceId": "DBD125",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "lo-LA",
    "displayName": "\u0e88\u0eb1\u0e99\u0e97\u0eb0\u0ea7\u0ebb\u0e87",
    "voiceName": "lo-LA-ChanthavongNeural",
    "language": "Lao (Laos)",
    "gender": "Male",
    "country": "la"
}, {
    "voiceId": "CBA796",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "lt-LT",
    "displayName": "Ona",
    "voiceName": "lt-LT-OnaNeural",
    "language": "Lithuanian (Lithuania)",
    "gender": "Female",
    "country": "lt"
}, {
    "voiceId": "ADB657",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "lt-LT",
    "displayName": "Leonas",
    "voiceName": "lt-LT-LeonasNeural",
    "language": "Lithuanian (Lithuania)",
    "gender": "Male",
    "country": "lt"
}, {
    "voiceId": "FAF478",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "lv-LV",
    "displayName": "Everita",
    "voiceName": "lv-LV-EveritaNeural",
    "language": "Latvian (Latvia)",
    "gender": "Female",
    "country": "lv"
}, {
    "voiceId": "AAE184",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "lv-LV",
    "displayName": "Nils",
    "voiceName": "lv-LV-NilsNeural",
    "language": "Latvian (Latvia)",
    "gender": "Male",
    "country": "lv"
}, {
    "voiceId": "DBC705",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "mk-MK",
    "displayName": "\u041c\u0430\u0440\u0438\u0458\u0430",
    "voiceName": "mk-MK-MarijaNeural",
    "language": "Macedonian (North Macedonia)",
    "gender": "Female",
    "country": "mk"
}, {
    "voiceId": "ACA009",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "mk-MK",
    "displayName": "\u0410\u043b\u0435\u043a\u0441\u0430\u043d\u0434\u0430\u0440",
    "voiceName": "mk-MK-AleksandarNeural",
    "language": "Macedonian (North Macedonia)",
    "gender": "Male",
    "country": "mk"
}, {
    "voiceId": "ECB861",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ml-IN",
    "displayName": "\u0d36\u0d4b\u0d2d\u0d28",
    "voiceName": "ml-IN-SobhanaNeural",
    "language": "Malayalam (India)",
    "gender": "Female",
    "country": "in"
}, {
    "voiceId": "BFB758",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ml-IN",
    "displayName": "\u0d2e\u0d3f\u0d25\u0d41\u0d7b",
    "voiceName": "ml-IN-MidhunNeural",
    "language": "Malayalam (India)",
    "gender": "Male",
    "country": "in"
}, {
    "voiceId": "FDD465",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "mn-MN",
    "displayName": "\u0415\u0441\u04af\u0439",
    "voiceName": "mn-MN-YesuiNeural",
    "language": "Mongolian (Mongolia)",
    "gender": "Female",
    "country": "mn"
}, {
    "voiceId": "AFD144",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "mn-MN",
    "displayName": "\u0411\u0430\u0442\u0430\u0430",
    "voiceName": "mn-MN-BataaNeural",
    "language": "Mongolian (Mongolia)",
    "gender": "Male",
    "country": "mn"
}, {
    "voiceId": "FBC578",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "mr-IN",
    "displayName": "\u0906\u0930\u094b\u0939\u0940",
    "voiceName": "mr-IN-AarohiNeural",
    "language": "Marathi (India)",
    "gender": "Female",
    "country": "in"
}, {
    "voiceId": "CCB856",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "mr-IN",
    "displayName": "\u092e\u0928\u094b\u0939\u0930",
    "voiceName": "mr-IN-ManoharNeural",
    "language": "Marathi (India)",
    "gender": "Male",
    "country": "in"
}, {
    "voiceId": "DCB569",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ms-MY",
    "displayName": "Yasmin",
    "voiceName": "ms-MY-YasminNeural",
    "language": "Malay (Malaysia)",
    "gender": "Female",
    "country": "my"
}, {
    "voiceId": "EBE803",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ms-MY",
    "displayName": "Osman",
    "voiceName": "ms-MY-OsmanNeural",
    "language": "Malay (Malaysia)",
    "gender": "Male",
    "country": "my"
}, {
    "voiceId": "AFB080",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "mt-MT",
    "displayName": "Grace",
    "voiceName": "mt-MT-GraceNeural",
    "language": "Maltese (Malta)",
    "gender": "Female",
    "country": "mt"
}, {
    "voiceId": "BBD314",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "mt-MT",
    "displayName": "Joseph",
    "voiceName": "mt-MT-JosephNeural",
    "language": "Maltese (Malta)",
    "gender": "Male",
    "country": "mt"
}, {
    "voiceId": "AAF900",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "my-MM",
    "displayName": "\u1014\u102e\u101c\u102c",
    "voiceName": "my-MM-NilarNeural",
    "language": "Burmese (Myanmar)",
    "gender": "Female",
    "country": "mm"
}, {
    "voiceId": "FBD088",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "my-MM",
    "displayName": "\u101e\u102e\u101f",
    "voiceName": "my-MM-ThihaNeural",
    "language": "Burmese (Myanmar)",
    "gender": "Male",
    "country": "mm"
}, {
    "voiceId": "CAF797",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "nb-NO",
    "displayName": "Pernille",
    "voiceName": "nb-NO-PernilleNeural",
    "language": "Norwegian (Norway)",
    "gender": "Female",
    "country": "no"
}, {
    "voiceId": "AEE625",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "nb-NO",
    "displayName": "Finn",
    "voiceName": "nb-NO-FinnNeural",
    "language": "Norwegian (Norway)",
    "gender": "Male",
    "country": "no"
}, {
    "voiceId": "CCC355",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "nb-NO",
    "displayName": "Iselin",
    "voiceName": "nb-NO-IselinNeural",
    "language": "Norwegian (Norway)",
    "gender": "Female",
    "country": "no"
}, {
    "voiceId": "DBC803",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ne-NP",
    "displayName": "\u0939\u0947\u092e\u0915\u0932\u093e",
    "voiceName": "ne-NP-HemkalaNeural",
    "language": "Nepali (Nepal)",
    "gender": "Female",
    "country": "np"
}, {
    "voiceId": "FED374",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ne-NP",
    "displayName": "\u0938\u093e\u0917\u0930",
    "voiceName": "ne-NP-SagarNeural",
    "language": "Nepali (Nepal)",
    "gender": "Male",
    "country": "np"
}, {
    "voiceId": "EDE026",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "nl-BE",
    "displayName": "Dena",
    "voiceName": "nl-BE-DenaNeural",
    "language": "Dutch (Belgium)",
    "gender": "Female",
    "country": "be"
}, {
    "voiceId": "BAD005",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "nl-BE",
    "displayName": "Arnaud",
    "voiceName": "nl-BE-ArnaudNeural",
    "language": "Dutch (Belgium)",
    "gender": "Male",
    "country": "be"
}, {
    "voiceId": "ACB204",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "nl-NL",
    "displayName": "Fenna",
    "voiceName": "nl-NL-FennaNeural",
    "language": "Dutch (Netherlands)",
    "gender": "Female",
    "country": "nl"
}, {
    "voiceId": "ECE694",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "nl-NL",
    "displayName": "Maarten",
    "voiceName": "nl-NL-MaartenNeural",
    "language": "Dutch (Netherlands)",
    "gender": "Male",
    "country": "nl"
}, {
    "voiceId": "DBA063",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "nl-NL",
    "displayName": "Colette",
    "voiceName": "nl-NL-ColetteNeural",
    "language": "Dutch (Netherlands)",
    "gender": "Female",
    "country": "nl"
}, {
    "voiceId": "FBB524",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "or-IN",
    "displayName": "\u0b38\u0b41\u0b2d\u0b3e\u0b38\u0b3f\u0b28\u0b40",
    "voiceName": "or-IN-SubhasiniNeural",
    "language": "India",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "CDC290",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "or-IN",
    "displayName": "\u0b38\u0b41\u0b15\u0b3e\u0b28\u0b4d\u0b24",
    "voiceName": "or-IN-SukantNeural",
    "language": "India",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "DBF202",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "pa-IN",
    "displayName": "\u0a13\u0a1c\u0a38",
    "voiceName": "pa-IN-OjasNeural",
    "language": "Punjabi (India)",
    "gender": "Male",
    "country": "in"
}, {
    "voiceId": "AEA732",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "pa-IN",
    "displayName": "\u0a35\u0a3e\u0a28\u0a40",
    "voiceName": "pa-IN-VaaniNeural",
    "language": "Punjabi (India)",
    "gender": "Female",
    "country": "in"
}, {
    "voiceId": "FAC073",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "pl-PL",
    "displayName": "Agnieszka",
    "voiceName": "pl-PL-AgnieszkaNeural",
    "language": "Polish (Poland)",
    "gender": "Female",
    "country": "pl"
}, {
    "voiceId": "ADE166",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "pl-PL",
    "displayName": "Marek",
    "voiceName": "pl-PL-MarekNeural",
    "language": "Polish (Poland)",
    "gender": "Male",
    "country": "pl"
}, {
    "voiceId": "EAF786",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "pl-PL",
    "displayName": "Zofia",
    "voiceName": "pl-PL-ZofiaNeural",
    "language": "Polish (Poland)",
    "gender": "Female",
    "country": "pl"
}, {
    "voiceId": "ACC662",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ps-AF",
    "displayName": "\u0644\u0637\u064a\u0641\u0647",
    "voiceName": "ps-AF-LatifaNeural",
    "language": "Pashto (Afghanistan)",
    "gender": "Female",
    "country": "af"
}, {
    "voiceId": "BEE927",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ps-AF",
    "displayName": " \u06ab\u0644 \u0646\u0648\u0627\u0632",
    "voiceName": "ps-AF-GulNawazNeural",
    "language": "Pashto (Afghanistan)",
    "gender": "Male",
    "country": "af"
}, {
    "voiceId": "CDE376",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "pt-BR",
    "displayName": "Francisca",
    "voiceName": "pt-BR-FranciscaNeural",
    "language": "Portuguese (Brazil)",
    "gender": "Female",
    "country": "br"
}, {
    "voiceId": "BFF871",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "pt-BR",
    "displayName": "Ant\u00f4nio",
    "voiceName": "pt-BR-AntonioNeural",
    "language": "Portuguese (Brazil)",
    "gender": "Male",
    "country": "br"
}, {
    "voiceId": "EBC999",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "pt-BR",
    "displayName": "Macerio Multilingual",
    "voiceName": "pt-BR-MacerioMultilingualNeural",
    "language": "Portuguese (Brazil)",
    "gender": "Male",
    "country": "br"
}, {
    "voiceId": "BAA562",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "pt-BR",
    "displayName": "Thalita multil\u00edngue",
    "voiceName": "pt-BR-ThalitaMultilingualNeural",
    "language": "Portuguese (Brazil)",
    "gender": "Female",
    "country": "br"
}, {
    "voiceId": "AEE444",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "pt-BR",
    "displayName": "Brenda",
    "voiceName": "pt-BR-BrendaNeural",
    "language": "Portuguese (Brazil)",
    "gender": "Female",
    "country": "br"
}, {
    "voiceId": "BCE098",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "pt-BR",
    "displayName": "Donato",
    "voiceName": "pt-BR-DonatoNeural",
    "language": "Portuguese (Brazil)",
    "gender": "Male",
    "country": "br"
}, {
    "voiceId": "FBD566",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "pt-BR",
    "displayName": "Elza",
    "voiceName": "pt-BR-ElzaNeural",
    "language": "Portuguese (Brazil)",
    "gender": "Female",
    "country": "br"
}, {
    "voiceId": "BBD282",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "pt-BR",
    "displayName": "Fabio",
    "voiceName": "pt-BR-FabioNeural",
    "language": "Portuguese (Brazil)",
    "gender": "Male",
    "country": "br"
}, {
    "voiceId": "FEA026",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "pt-BR",
    "displayName": "Giovanna",
    "voiceName": "pt-BR-GiovannaNeural",
    "language": "Portuguese (Brazil)",
    "gender": "Female",
    "country": "br"
}, {
    "voiceId": "EDF340",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "pt-BR",
    "displayName": "Humberto",
    "voiceName": "pt-BR-HumbertoNeural",
    "language": "Portuguese (Brazil)",
    "gender": "Male",
    "country": "br"
}, {
    "voiceId": "CFB544",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "pt-BR",
    "displayName": "Julio",
    "voiceName": "pt-BR-JulioNeural",
    "language": "Portuguese (Brazil)",
    "gender": "Male",
    "country": "br"
}, {
    "voiceId": "CFB732",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "pt-BR",
    "displayName": "Leila",
    "voiceName": "pt-BR-LeilaNeural",
    "language": "Portuguese (Brazil)",
    "gender": "Female",
    "country": "br"
}, {
    "voiceId": "AED738",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "pt-BR",
    "displayName": "Leticia",
    "voiceName": "pt-BR-LeticiaNeural",
    "language": "Portuguese (Brazil)",
    "gender": "Female",
    "country": "br"
}, {
    "voiceId": "DFB605",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "pt-BR",
    "displayName": "Manuela",
    "voiceName": "pt-BR-ManuelaNeural",
    "language": "Portuguese (Brazil)",
    "gender": "Female",
    "country": "br"
}, {
    "voiceId": "EBE367",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "pt-BR",
    "displayName": "Nicolau",
    "voiceName": "pt-BR-NicolauNeural",
    "language": "Portuguese (Brazil)",
    "gender": "Male",
    "country": "br"
}, {
    "voiceId": "CAC598",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "pt-BR",
    "displayName": "Thalita",
    "voiceName": "pt-BR-ThalitaNeural",
    "language": "Portuguese (Brazil)",
    "gender": "Female",
    "country": "br"
}, {
    "voiceId": "ECB990",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "pt-BR",
    "displayName": "Valerio",
    "voiceName": "pt-BR-ValerioNeural",
    "language": "Portuguese (Brazil)",
    "gender": "Male",
    "country": "br"
}, {
    "voiceId": "EFA825",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "pt-BR",
    "displayName": "Yara",
    "voiceName": "pt-BR-YaraNeural",
    "language": "Portuguese (Brazil)",
    "gender": "Female",
    "country": "br"
}, {
    "voiceId": "CBD344",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "pt-PT",
    "displayName": "Raquel",
    "voiceName": "pt-PT-RaquelNeural",
    "language": "Portuguese (Portugal)",
    "gender": "Female",
    "country": "pt"
}, {
    "voiceId": "FFA889",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "pt-PT",
    "displayName": "Duarte",
    "voiceName": "pt-PT-DuarteNeural",
    "language": "Portuguese (Portugal)",
    "gender": "Male",
    "country": "pt"
}, {
    "voiceId": "FBD888",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "pt-PT",
    "displayName": "Fernanda",
    "voiceName": "pt-PT-FernandaNeural",
    "language": "Portuguese (Portugal)",
    "gender": "Female",
    "country": "pt"
}, {
    "voiceId": "AFC127",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ro-RO",
    "displayName": "Alina",
    "voiceName": "ro-RO-AlinaNeural",
    "language": "Romanian (Romania)",
    "gender": "Female",
    "country": "ro"
}, {
    "voiceId": "CBE226",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ro-RO",
    "displayName": "Emil",
    "voiceName": "ro-RO-EmilNeural",
    "language": "Romanian (Romania)",
    "gender": "Male",
    "country": "ro"
}, {
    "voiceId": "FDE970",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ru-RU",
    "displayName": "\u0421\u0432\u0435\u0442\u043b\u0430\u043d\u0430",
    "voiceName": "ru-RU-SvetlanaNeural",
    "language": "Russian (Russia)",
    "gender": "Female",
    "country": "ru"
}, {
    "voiceId": "ADD639",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ru-RU",
    "displayName": "\u0414\u043c\u0438\u0442\u0440\u0438\u0439",
    "voiceName": "ru-RU-DmitryNeural",
    "language": "Russian (Russia)",
    "gender": "Male",
    "country": "ru"
}, {
    "voiceId": "BDE513",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ru-RU",
    "displayName": "\u0414\u0430\u0440\u0438\u044f",
    "voiceName": "ru-RU-DariyaNeural",
    "language": "Russian (Russia)",
    "gender": "Female",
    "country": "ru"
}, {
    "voiceId": "BFA249",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "si-LK",
    "displayName": "\u0dad\u0dd2\u0dc5\u0dd2\u0dab\u0dd2",
    "voiceName": "si-LK-ThiliniNeural",
    "language": "Sinhala (Sri Lanka)",
    "gender": "Female",
    "country": "lk"
}, {
    "voiceId": "BBE464",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "si-LK",
    "displayName": "\u0dc3\u0db8\u0dd3\u0dbb",
    "voiceName": "si-LK-SameeraNeural",
    "language": "Sinhala (Sri Lanka)",
    "gender": "Male",
    "country": "lk"
}, {
    "voiceId": "DFA341",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "sk-SK",
    "displayName": "Vikt\u00f3ria",
    "voiceName": "sk-SK-ViktoriaNeural",
    "language": "Slovak (Slovakia)",
    "gender": "Female",
    "country": "sk"
}, {
    "voiceId": "DCC541",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "sk-SK",
    "displayName": "Luk\u00e1\u0161",
    "voiceName": "sk-SK-LukasNeural",
    "language": "Slovak (Slovakia)",
    "gender": "Male",
    "country": "sk"
}, {
    "voiceId": "BBD877",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "sl-SI",
    "displayName": "Petra",
    "voiceName": "sl-SI-PetraNeural",
    "language": "Slovenian (Slovenia)",
    "gender": "Female",
    "country": "si"
}, {
    "voiceId": "EEE288",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "sl-SI",
    "displayName": "Rok",
    "voiceName": "sl-SI-RokNeural",
    "language": "Slovenian (Slovenia)",
    "gender": "Male",
    "country": "si"
}, {
    "voiceId": "DCA103",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "so-SO",
    "displayName": "Ubax",
    "voiceName": "so-SO-UbaxNeural",
    "language": "Somali (Somalia)",
    "gender": "Female",
    "country": "so"
}, {
    "voiceId": "FBD315",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "so-SO",
    "displayName": "Muuse",
    "voiceName": "so-SO-MuuseNeural",
    "language": "Somali (Somalia)",
    "gender": "Male",
    "country": "so"
}, {
    "voiceId": "BCD598",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "sq-AL",
    "displayName": "Anila",
    "voiceName": "sq-AL-AnilaNeural",
    "language": "Albanian (Albania)",
    "gender": "Female",
    "country": "al"
}, {
    "voiceId": "FED959",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "sq-AL",
    "displayName": "Ilir",
    "voiceName": "sq-AL-IlirNeural",
    "language": "Albanian (Albania)",
    "gender": "Male",
    "country": "al"
}, {
    "voiceId": "DDC102",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "sr-Latn-RS",
    "displayName": "Nicholas",
    "voiceName": "sr-Latn-RS-NicholasNeural",
    "language": "language Not Found",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "BDA669",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "sr-Latn-RS",
    "displayName": "Sophie",
    "voiceName": "sr-Latn-RS-SophieNeural",
    "language": "language Not Found",
    "gender": "Female",
    "country": "us"
}, {
    "voiceId": "DCE197",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "sr-RS",
    "displayName": "\u0421\u043e\u0444\u0438\u0458\u0430",
    "voiceName": "sr-RS-SophieNeural",
    "language": "Serbian (Cyrillic)",
    "gender": "Female",
    "country": "rs"
}, {
    "voiceId": "EEF058",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "sr-RS",
    "displayName": "\u041d\u0438\u043a\u043e\u043b\u0430",
    "voiceName": "sr-RS-NicholasNeural",
    "language": "Serbian (Cyrillic)",
    "gender": "Male",
    "country": "rs"
}, {
    "voiceId": "EBB239",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "su-ID",
    "displayName": "Tuti",
    "voiceName": "su-ID-TutiNeural",
    "language": "Sundanese (Indonesia)",
    "gender": "Female",
    "country": "id"
}, {
    "voiceId": "CAA591",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "su-ID",
    "displayName": "Jajang",
    "voiceName": "su-ID-JajangNeural",
    "language": "Sundanese (Indonesia)",
    "gender": "Male",
    "country": "id"
}, {
    "voiceId": "FFB075",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "sv-SE",
    "displayName": "Sofie",
    "voiceName": "sv-SE-SofieNeural",
    "language": "Swedish (Sweden)",
    "gender": "Female",
    "country": "se"
}, {
    "voiceId": "EBF415",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "sv-SE",
    "displayName": "Mattias",
    "voiceName": "sv-SE-MattiasNeural",
    "language": "Swedish (Sweden)",
    "gender": "Male",
    "country": "se"
}, {
    "voiceId": "DDD590",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "sv-SE",
    "displayName": "Hillevi",
    "voiceName": "sv-SE-HilleviNeural",
    "language": "Swedish (Sweden)",
    "gender": "Female",
    "country": "se"
}, {
    "voiceId": "FEB114",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "sw-KE",
    "displayName": "Zuri",
    "voiceName": "sw-KE-ZuriNeural",
    "language": "Swahili (Kenya)",
    "gender": "Female",
    "country": "ke"
}, {
    "voiceId": "AAC235",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "sw-KE",
    "displayName": "Rafiki",
    "voiceName": "sw-KE-RafikiNeural",
    "language": "Swahili (Kenya)",
    "gender": "Male",
    "country": "ke"
}, {
    "voiceId": "CDD712",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "sw-TZ",
    "displayName": "Rehema",
    "voiceName": "sw-TZ-RehemaNeural",
    "language": "Swahili (Tanzania)",
    "gender": "Female",
    "country": "tz"
}, {
    "voiceId": "FAA431",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "sw-TZ",
    "displayName": "Daudi",
    "voiceName": "sw-TZ-DaudiNeural",
    "language": "Swahili (Tanzania)",
    "gender": "Male",
    "country": "tz"
}, {
    "voiceId": "DFF389",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ta-IN",
    "displayName": "\u0baa\u0bb2\u0bcd\u0bb2\u0bb5\u0bbf",
    "voiceName": "ta-IN-PallaviNeural",
    "language": "Tamil (India)",
    "gender": "Female",
    "country": "in"
}, {
    "voiceId": "ADB747",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ta-IN",
    "displayName": "\u0bb5\u0bb3\u0bcd\u0bb3\u0bc1\u0bb5\u0bb0\u0bcd",
    "voiceName": "ta-IN-ValluvarNeural",
    "language": "Tamil (India)",
    "gender": "Male",
    "country": "in"
}, {
    "voiceId": "DDF444",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ta-LK",
    "displayName": "\u0b9a\u0bb0\u0ba3\u0bcd\u0baf\u0bbe",
    "voiceName": "ta-LK-SaranyaNeural",
    "language": "Tamil (Sri Lanka)",
    "gender": "Female",
    "country": "lk"
}, {
    "voiceId": "BDA242",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ta-LK",
    "displayName": "\u0b95\u0bc1\u0bae\u0bbe\u0bb0\u0bcd",
    "voiceName": "ta-LK-KumarNeural",
    "language": "Tamil (Sri Lanka)",
    "gender": "Male",
    "country": "lk"
}, {
    "voiceId": "AED495",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ta-MY",
    "displayName": "\u0b95\u0ba9\u0bbf",
    "voiceName": "ta-MY-KaniNeural",
    "language": "Tamil (Malaysia)",
    "gender": "Female",
    "country": "my"
}, {
    "voiceId": "FEE915",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ta-MY",
    "displayName": "\u0b9a\u0bc2\u0bb0\u0bcd\u0baf\u0bbe",
    "voiceName": "ta-MY-SuryaNeural",
    "language": "Tamil (Malaysia)",
    "gender": "Male",
    "country": "my"
}, {
    "voiceId": "FCB749",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ta-SG",
    "displayName": "\u0bb5\u0bc6\u0ba3\u0bcd\u0baa\u0bbe",
    "voiceName": "ta-SG-VenbaNeural",
    "language": "Tamil (Singapore)",
    "gender": "Female",
    "country": "sg"
}, {
    "voiceId": "DCA284",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ta-SG",
    "displayName": "\u0b85\u0ba9\u0bcd\u0baa\u0bc1",
    "voiceName": "ta-SG-AnbuNeural",
    "language": "Tamil (Singapore)",
    "gender": "Male",
    "country": "sg"
}, {
    "voiceId": "ACA826",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "te-IN",
    "displayName": "\u0c36\u0c4d\u0c30\u0c41\u0c24\u0c3f",
    "voiceName": "te-IN-ShrutiNeural",
    "language": "Telugu (India)",
    "gender": "Female",
    "country": "in"
}, {
    "voiceId": "ADB222",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "te-IN",
    "displayName": "\u0c2e\u0c4b\u0c39\u0c28\u0c4d",
    "voiceName": "te-IN-MohanNeural",
    "language": "Telugu (India)",
    "gender": "Male",
    "country": "in"
}, {
    "voiceId": "BDA043",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "th-TH",
    "displayName": "\u0e40\u0e1b\u0e23\u0e21\u0e27\u0e14\u0e35",
    "voiceName": "th-TH-PremwadeeNeural",
    "language": "Thai (Thailand)",
    "gender": "Female",
    "country": "th"
}, {
    "voiceId": "ACE824",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "th-TH",
    "displayName": "\u0e19\u0e34\u0e27\u0e31\u0e12\u0e19\u0e4c",
    "voiceName": "th-TH-NiwatNeural",
    "language": "Thai (Thailand)",
    "gender": "Male",
    "country": "th"
}, {
    "voiceId": "FEF497",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "th-TH",
    "displayName": "\u0e2d\u0e31\u0e08\u0e09\u0e23\u0e32",
    "voiceName": "th-TH-AcharaNeural",
    "language": "Thai (Thailand)",
    "gender": "Female",
    "country": "th"
}, {
    "voiceId": "FFB135",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "tr-TR",
    "displayName": "Emel",
    "voiceName": "tr-TR-EmelNeural",
    "language": "Turkish (Turkey)",
    "gender": "Female",
    "country": "tr"
}, {
    "voiceId": "CEB805",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "tr-TR",
    "displayName": "Ahmet",
    "voiceName": "tr-TR-AhmetNeural",
    "language": "Turkish (Turkey)",
    "gender": "Male",
    "country": "tr"
}, {
    "voiceId": "EDD445",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "uk-UA",
    "displayName": "\u041f\u043e\u043b\u0456\u043d\u0430",
    "voiceName": "uk-UA-PolinaNeural",
    "language": "Ukrainian (Ukraine)",
    "gender": "Female",
    "country": "ua"
}, {
    "voiceId": "ACB055",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "uk-UA",
    "displayName": "\u041e\u0441\u0442\u0430\u043f",
    "voiceName": "uk-UA-OstapNeural",
    "language": "Ukrainian (Ukraine)",
    "gender": "Male",
    "country": "ua"
}, {
    "voiceId": "DFE547",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ur-IN",
    "displayName": "\u06af\u0644",
    "voiceName": "ur-IN-GulNeural",
    "language": "Urdu (India)",
    "gender": "Female",
    "country": "in"
}, {
    "voiceId": "AAA068",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ur-IN",
    "displayName": "\u0633\u0644\u0645\u0627\u0646",
    "voiceName": "ur-IN-SalmanNeural",
    "language": "Urdu (India)",
    "gender": "Male",
    "country": "in"
}, {
    "voiceId": "CBA498",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ur-PK",
    "displayName": "\u0639\u0638\u0645\u06cc\u0670",
    "voiceName": "ur-PK-UzmaNeural",
    "language": "Urdu (Pakistan)",
    "gender": "Female",
    "country": "pk"
}, {
    "voiceId": "AEB903",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "ur-PK",
    "displayName": "\u0627\u0633\u062f",
    "voiceName": "ur-PK-AsadNeural",
    "language": "Urdu (Pakistan)",
    "gender": "Male",
    "country": "pk"
}, {
    "voiceId": "DBE851",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "uz-UZ",
    "displayName": "Madina",
    "voiceName": "uz-UZ-MadinaNeural",
    "language": "Uzbek (Latin, Uzbekistan)",
    "gender": "Female",
    "country": "uz"
}, {
    "voiceId": "BCE563",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "uz-UZ",
    "displayName": "Sardor",
    "voiceName": "uz-UZ-SardorNeural",
    "language": "Uzbek (Latin, Uzbekistan)",
    "gender": "Male",
    "country": "uz"
}, {
    "voiceId": "DFE237",
    "server": "azure",
    "tab": "hqVoice",
    "order": 1,
    "languageCode": "vi-VN",
    "displayName": "Ho\u00e0i My",
    "voiceName": "vi-VN-HoaiMyNeural",
    "language": "Vietnamese (Vietnam)",
    "gender": "Female",
    "description": "Hanoi, Standard",
    "country": "vn"
}, {
    "voiceId": "BEB861",
    "server": "azure",
    "tab": "hqVoice",
    "order": 2,
    "languageCode": "vi-VN",
    "displayName": "Nam Minh",
    "voiceName": "vi-VN-NamMinhNeural",
    "language": "Vietnamese (Vietnam)",
    "gender": "Male",
    "description": "Saigon, Standard",
    "country": "vn"
}, {
    "voiceId": "CAF969",
    "server": "googletts",
    "tab": "hqVoice",
    "order": 3,
    "languageCode": "vi-VN",
    "displayName": "B\u00edch",
    "voiceName": "vi-VN-Standard-A",
    "language": "Vietnamese (Vietnam)",
    "gender": "Female",
    "description": "Young, energetic",
    "country": "vn"
}, {
    "voiceId": "EDB974",
    "server": "googletts",
    "tab": "hqVoice",
    "order": 4,
    "languageCode": "vi-VN",
    "displayName": "Ch\u00ed",
    "voiceName": "vi-VN-Standard-B",
    "language": "Vietnamese (Vietnam)",
    "gender": "Male",
    "description": "Young, energetic",
    "country": "vn"
}, {
    "voiceId": "DBD914",
    "server": "googletts",
    "tab": "hqVoice",
    "order": 5,
    "languageCode": "vi-VN",
    "displayName": "Cam",
    "voiceName": "vi-VN-Standard-C",
    "language": "Vietnamese (Vietnam)",
    "gender": "Female",
    "description": "Young, energetic",
    "country": "vn"
}, {
    "voiceId": "BEF813",
    "server": "googletts",
    "tab": "hqVoice",
    "order": 6,
    "languageCode": "vi-VN",
    "displayName": "Danh",
    "voiceName": "vi-VN-Standard-D",
    "language": "Vietnamese (Vietnam)",
    "gender": "Male",
    "description": "Young, energetic",
    "country": "vn"
}, {
    "voiceId": "CCB558",
    "server": "openaiv2",
    "tab": "hqVoice",
    "order": 11,
    "languageCode": "vi-VN",
    "displayName": "D\u0169ng",
    "voiceName": "onyx__hn_vietnamese",
    "language": "Vietnamese (Vietnam)",
    "gender": "Male",
    "avatar": "onyx.svg",
    "description": "Hanoi, Warm, Standard",
    "country": "vn"
}, {
    "voiceId": "CDC294",
    "server": "openaiv2",
    "tab": "hqVoice",
    "order": 12,
    "languageCode": "vi-VN",
    "displayName": "H\u00f9ng",
    "voiceName": "onyx__sg_vietnamese",
    "language": "Vietnamese (Vietnam)",
    "gender": "Male",
    "avatar": "onyx.svg",
    "description": "Saigon, Warm, Standard",
    "country": "vn"
}, {
    "voiceId": "ADC689",
    "server": "aiVoice",
    "tab": "hqVoice",
    "order": 13,
    "languageCode": "vi-VN",
    "displayName": "Kh\u00f4i",
    "voiceName": "hn_male_phuthang_stor80dt_48k-fhg",
    "language": "Vietnamese (Vietnam)",
    "gender": "male",
    "description": "Hanoi, Storytelling",
    "country": "vn"
}, {
    "voiceId": "DDC251",
    "server": "aiVoice",
    "tab": "hqVoice",
    "order": 14,
    "languageCode": "vi-VN",
    "displayName": "Huy\u1ec1n",
    "voiceName": "hn_female_ngochuyen_full_48k-fhg",
    "language": "Vietnamese (Vietnam)",
    "gender": "female",
    "description": "Hanoi, Standard",
    "country": "vn"
}, {
    "voiceId": "ABE674",
    "server": "azure",
    "tab": "hqVoice",
    "order": 15,
    "languageCode": "vi-VN",
    "displayName": "H\u01b0\u01a1ng Th\u1ea3o",
    "voiceName": "fr-FR-VivienneMultilingualNeural",
    "language": "Vietnamese (Vietnam)",
    "gender": "Female",
    "country": "vn"
}, {
    "voiceId": "CCB257",
    "server": "azure",
    "tab": "hqVoice",
    "order": 16,
    "languageCode": "vi-VN",
    "displayName": "Th\u1ee7y Ti\u00ean",
    "voiceName": "de-DE-SeraphinaMultilingualNeural",
    "language": "Vietnamese (Vietnam)",
    "gender": "Female",
    "country": "vn"
}, {
    "voiceId": "FDB203",
    "server": "azure",
    "tab": "hqVoice",
    "order": 17,
    "languageCode": "vi-VN",
    "displayName": "Th\u00f9y Linh",
    "voiceName": "pt-BR-ThalitaMultilingualNeural",
    "language": "Vietnamese (Vietnam)",
    "gender": "Female",
    "country": "vn"
}, {
    "voiceId": "DEE836",
    "server": "azure",
    "tab": "hqVoice",
    "order": 18,
    "languageCode": "vi-VN",
    "displayName": "Th\u00e1i S\u01a1n",
    "voiceName": "fr-FR-RemyMultilingualNeural",
    "language": "Vietnamese (Vietnam)",
    "gender": "Male",
    "country": "vn"
}, {
    "voiceId": "CAD978",
    "server": "azure",
    "tab": "hqVoice",
    "order": 19,
    "languageCode": "vi-VN",
    "displayName": "Anh H\u00f9ng",
    "voiceName": "en-US-BrianMultilingualNeural",
    "language": "Vietnamese (Vietnam)",
    "gender": "Male",
    "country": "vn"
}, {
    "voiceId": "EAF683",
    "server": "azure",
    "tab": "hqVoice",
    "order": 20,
    "languageCode": "vi-VN",
    "displayName": "Quang Vinh",
    "voiceName": "de-DE-FlorianMultilingualNeural",
    "language": "Vietnamese (Vietnam)",
    "gender": "Male",
    "country": "vn"
}, {
    "voiceId": "ACD133",
    "server": "azure",
    "tab": "hqVoice",
    "order": 21,
    "languageCode": "vi-VN",
    "displayName": "C\u00f4ng Ph\u01b0\u1ee3ng",
    "voiceName": "it-IT-GiuseppeMultilingualNeural",
    "language": "Vietnamese (Vietnam)",
    "gender": "Male",
    "country": "vn"
}, {
    "voiceId": "FBA132",
    "server": "azure",
    "tab": "hqVoice",
    "order": 22,
    "languageCode": "vi-VN",
    "displayName": "H\u1ea3i \u00c2u",
    "voiceName": "ko-KR-HyunsuMultilingualNeural",
    "language": "Vietnamese (Vietnam)",
    "gender": "Male",
    "country": "vn"
}, {
    "voiceId": "CFE558",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "wuu-CN",
    "displayName": "\u6653\u5f64",
    "voiceName": "wuu-CN-XiaotongNeural",
    "language": "Chinese (Wu, Simplified)",
    "gender": "Female",
    "country": "cn"
}, {
    "voiceId": "CCB929",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "wuu-CN",
    "displayName": "\u4e91\u54f2",
    "voiceName": "wuu-CN-YunzheNeural",
    "language": "Chinese (Wu, Simplified)",
    "gender": "Male",
    "country": "cn"
}, {
    "voiceId": "EFD439",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "yue-CN",
    "displayName": "\u6653\u654f",
    "voiceName": "yue-CN-XiaoMinNeural",
    "language": "Chinese (Cantonese, Simplified)",
    "gender": "Female",
    "country": "cn"
}, {
    "voiceId": "ACD326",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "yue-CN",
    "displayName": "\u4e91\u677e",
    "voiceName": "yue-CN-YunSongNeural",
    "language": "Chinese (Cantonese, Simplified)",
    "gender": "Male",
    "country": "cn"
}, {
    "voiceId": "BBD266",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "zh-CN",
    "displayName": "\u6653\u6653",
    "voiceName": "zh-CN-XiaoxiaoNeural",
    "language": "Chinese (Mandarin, Simplified)",
    "gender": "Female",
    "country": "cn"
}, {
    "voiceId": "DEC212",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "zh-CN",
    "displayName": "\u4e91\u5e0c",
    "voiceName": "zh-CN-YunxiNeural",
    "language": "Chinese (Mandarin, Simplified)",
    "gender": "Male",
    "country": "cn"
}, {
    "voiceId": "DBA094",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "zh-CN",
    "displayName": "\u4e91\u5065",
    "voiceName": "zh-CN-YunjianNeural",
    "language": "Chinese (Mandarin, Simplified)",
    "gender": "Male",
    "country": "cn"
}, {
    "voiceId": "FDE167",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "zh-CN",
    "displayName": "\u6653\u4f0a",
    "voiceName": "zh-CN-XiaoyiNeural",
    "language": "Chinese (Mandarin, Simplified)",
    "gender": "Female",
    "country": "cn"
}, {
    "voiceId": "CFD921",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "zh-CN",
    "displayName": "\u4e91\u626c",
    "voiceName": "zh-CN-YunyangNeural",
    "language": "Chinese (Mandarin, Simplified)",
    "gender": "Male",
    "country": "cn"
}, {
    "voiceId": "EAE639",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "zh-CN",
    "displayName": "\u6653\u8fb0",
    "voiceName": "zh-CN-XiaochenNeural",
    "language": "Chinese (Mandarin, Simplified)",
    "gender": "Female",
    "country": "cn"
}, {
    "voiceId": "DFF451",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "zh-CN",
    "displayName": "\u6653\u8fb0 \u591a\u8bed\u8a00",
    "voiceName": "zh-CN-XiaochenMultilingualNeural",
    "language": "Chinese (Mandarin, Simplified)",
    "gender": "Female",
    "country": "cn"
}, {
    "voiceId": "EDE258",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "zh-CN",
    "displayName": "\u6653\u6db5",
    "voiceName": "zh-CN-XiaohanNeural",
    "language": "Chinese (Mandarin, Simplified)",
    "gender": "Female",
    "country": "cn"
}, {
    "voiceId": "EDF992",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "zh-CN",
    "displayName": "\u6653\u68a6",
    "voiceName": "zh-CN-XiaomengNeural",
    "language": "Chinese (Mandarin, Simplified)",
    "gender": "Female",
    "country": "cn"
}, {
    "voiceId": "CAE776",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "zh-CN",
    "displayName": "\u6653\u58a8",
    "voiceName": "zh-CN-XiaomoNeural",
    "language": "Chinese (Mandarin, Simplified)",
    "gender": "Female",
    "country": "cn"
}, {
    "voiceId": "CAF807",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "zh-CN",
    "displayName": "\u6653\u79cb",
    "voiceName": "zh-CN-XiaoqiuNeural",
    "language": "Chinese (Mandarin, Simplified)",
    "gender": "Female",
    "country": "cn"
}, {
    "voiceId": "DDF824",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "zh-CN",
    "displayName": "\u6653\u67d4",
    "voiceName": "zh-CN-XiaorouNeural",
    "language": "Chinese (Mandarin, Simplified)",
    "gender": "Female",
    "country": "cn"
}, {
    "voiceId": "FCC286",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "zh-CN",
    "displayName": "\u6653\u777f",
    "voiceName": "zh-CN-XiaoruiNeural",
    "language": "Chinese (Mandarin, Simplified)",
    "gender": "Female",
    "country": "cn"
}, {
    "voiceId": "DCD935",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "zh-CN",
    "displayName": "\u6653\u53cc",
    "voiceName": "zh-CN-XiaoshuangNeural",
    "language": "Chinese (Mandarin, Simplified)",
    "gender": "Female",
    "country": "cn"
}, {
    "voiceId": "DFD138",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "zh-CN",
    "displayName": "\u6653\u6653 \u65b9\u8a00",
    "voiceName": "zh-CN-XiaoxiaoDialectsNeural",
    "language": "Chinese (Mandarin, Simplified)",
    "gender": "Female",
    "country": "cn"
}, {
    "voiceId": "CBA806",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "zh-CN",
    "displayName": "\u6653\u6653 \u591a\u8bed\u8a00",
    "voiceName": "zh-CN-XiaoxiaoMultilingualNeural",
    "language": "Chinese (Mandarin, Simplified)",
    "gender": "Female",
    "country": "cn"
}, {
    "voiceId": "CFA969",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "zh-CN",
    "displayName": "\u6653\u989c",
    "voiceName": "zh-CN-XiaoyanNeural",
    "language": "Chinese (Mandarin, Simplified)",
    "gender": "Female",
    "country": "cn"
}, {
    "voiceId": "CEE214",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "zh-CN",
    "displayName": "\u6653\u60a0",
    "voiceName": "zh-CN-XiaoyouNeural",
    "language": "Chinese (Mandarin, Simplified)",
    "gender": "Female",
    "country": "cn"
}, {
    "voiceId": "ACB496",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "zh-CN",
    "displayName": "\u6653\u5b87 \u591a\u8bed\u8a00",
    "voiceName": "zh-CN-XiaoyuMultilingualNeural",
    "language": "Chinese (Mandarin, Simplified)",
    "gender": "Female",
    "country": "cn"
}, {
    "voiceId": "CAD498",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "zh-CN",
    "displayName": "\u6653\u7504",
    "voiceName": "zh-CN-XiaozhenNeural",
    "language": "Chinese (Mandarin, Simplified)",
    "gender": "Female",
    "country": "cn"
}, {
    "voiceId": "FAD334",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "zh-CN",
    "displayName": "\u4e91\u67ab",
    "voiceName": "zh-CN-YunfengNeural",
    "language": "Chinese (Mandarin, Simplified)",
    "gender": "Male",
    "country": "cn"
}, {
    "voiceId": "BBD165",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "zh-CN",
    "displayName": "\u4e91\u7693",
    "voiceName": "zh-CN-YunhaoNeural",
    "language": "Chinese (Mandarin, Simplified)",
    "gender": "Male",
    "country": "cn"
}, {
    "voiceId": "DFD441",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "zh-CN",
    "displayName": "\u4e91\u6770",
    "voiceName": "zh-CN-YunjieNeural",
    "language": "Chinese (Mandarin, Simplified)",
    "gender": "Male",
    "country": "cn"
}, {
    "voiceId": "BEF393",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "zh-CN",
    "displayName": "\u4e91\u590f",
    "voiceName": "zh-CN-YunxiaNeural",
    "language": "Chinese (Mandarin, Simplified)",
    "gender": "Male",
    "country": "cn"
}, {
    "voiceId": "FAA902",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "zh-CN",
    "displayName": "\u4e91\u91ce",
    "voiceName": "zh-CN-YunyeNeural",
    "language": "Chinese (Mandarin, Simplified)",
    "gender": "Male",
    "country": "cn"
}, {
    "voiceId": "AAE235",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "zh-CN",
    "displayName": "\u4e91\u9038 \u591a\u8bed\u8a00",
    "voiceName": "zh-CN-YunyiMultilingualNeural",
    "language": "Chinese (Mandarin, Simplified)",
    "gender": "Male",
    "country": "cn"
}, {
    "voiceId": "BDC375",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "zh-CN",
    "displayName": "\u4e91\u6cfd",
    "voiceName": "zh-CN-YunzeNeural",
    "language": "Chinese (Mandarin, Simplified)",
    "gender": "Male",
    "country": "cn"
}, {
    "voiceId": "FED808",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "zh-CN",
    "displayName": "Yunfan Multilingual",
    "voiceName": "zh-CN-YunfanMultilingualNeural",
    "language": "Chinese (Mandarin, Simplified)",
    "gender": "Male",
    "country": "cn"
}, {
    "voiceId": "FCA652",
    "server": "azure",
    "tab": "hqVoice",
    "order": 100,
    "languageCode": "zh-CN",
    "displayName": "Yunxiao Multilingual",
    "voiceName": "zh-CN-YunxiaoMultilingualNeural",
    "language": "Chinese (Mandarin, Simplified)",
    "gender": "Male",
    "country": "cn"
}, {
    "voiceId": "FDB730",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "zh-CN",
    "displayName": "Xiaochen Dragon HD Latest",
    "voiceName": "zh-CN-Xiaochen:DragonHDLatestNeural",
    "language": "Chinese (Mandarin, Simplified)",
    "gender": "Female",
    "country": "cn"
}, {
    "voiceId": "BFB637",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "zh-CN-guangxi",
    "displayName": "\u4e91\u5947 \u5e7f\u897f",
    "voiceName": "zh-CN-guangxi-YunqiNeural",
    "language": "language Not Found",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "EBB030",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "zh-CN-henan",
    "displayName": "\u4e91\u767b",
    "voiceName": "zh-CN-henan-YundengNeural",
    "language": "Chinese (Zhongyuan Mandarin Henan, Simplified)",
    "gender": "Male",
    "country": "cn"
}, {
    "voiceId": "FEA855",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "zh-CN-liaoning",
    "displayName": "\u6653\u5317 \u8fbd\u5b81",
    "voiceName": "zh-CN-liaoning-XiaobeiNeural",
    "language": "Chinese (Northeastern Mandarin, Simplified)",
    "gender": "Female",
    "country": "cn"
}, {
    "voiceId": "ADD562",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "zh-CN-liaoning",
    "displayName": "\u4e91\u5f6a \u8fbd\u5b81",
    "voiceName": "zh-CN-liaoning-YunbiaoNeural",
    "language": "Chinese (Northeastern Mandarin, Simplified)",
    "gender": "Male",
    "country": "cn"
}, {
    "voiceId": "FFF942",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "zh-CN-shaanxi",
    "displayName": "\u6653\u59ae",
    "voiceName": "zh-CN-shaanxi-XiaoniNeural",
    "language": "Chinese (Zhongyuan Mandarin Shaanxi, Simplified)",
    "gender": "Female",
    "country": "cn"
}, {
    "voiceId": "BEE949",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "zh-CN-shandong",
    "displayName": "\u4e91\u7fd4",
    "voiceName": "zh-CN-shandong-YunxiangNeural",
    "language": "Chinese (Jilu Mandarin, Simplified)",
    "gender": "Male",
    "country": "cn"
}, {
    "voiceId": "CBB550",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "zh-CN-sichuan",
    "displayName": "\u4e91\u5e0c \u56db\u5ddd",
    "voiceName": "zh-CN-sichuan-YunxiNeural",
    "language": "Chinese (Southwestern Mandarin, Simplified)",
    "gender": "Male",
    "country": "cn"
}, {
    "voiceId": "EAD637",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "zh-HK",
    "displayName": "\u66c9\u66fc",
    "voiceName": "zh-HK-HiuMaanNeural",
    "language": "Chinese (Cantonese, Traditional)",
    "gender": "Female",
    "country": "hk"
}, {
    "voiceId": "DED876",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "zh-HK",
    "displayName": "\u96f2\u9f8d",
    "voiceName": "zh-HK-WanLungNeural",
    "language": "Chinese (Cantonese, Traditional)",
    "gender": "Male",
    "country": "hk"
}, {
    "voiceId": "FBC199",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "zh-HK",
    "displayName": "\u66c9\u4f73",
    "voiceName": "zh-HK-HiuGaaiNeural",
    "language": "Chinese (Cantonese, Traditional)",
    "gender": "Female",
    "country": "hk"
}, {
    "voiceId": "ACD203",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "zh-TW",
    "displayName": "\u66c9\u81fb",
    "voiceName": "zh-TW-HsiaoChenNeural",
    "language": "Chinese (Taiwanese Mandarin, Traditional)",
    "gender": "Female",
    "country": "tw"
}, {
    "voiceId": "EED115",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "zh-TW",
    "displayName": "\u96f2\u54f2",
    "voiceName": "zh-TW-YunJheNeural",
    "language": "Chinese (Taiwanese Mandarin, Traditional)",
    "gender": "Male",
    "country": "tw"
}, {
    "voiceId": "EED869",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "zh-TW",
    "displayName": "\u66c9\u96e8",
    "voiceName": "zh-TW-HsiaoYuNeural",
    "language": "Chinese (Taiwanese Mandarin, Traditional)",
    "gender": "Female",
    "country": "tw"
}, {
    "voiceId": "CCF099",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "zu-ZA",
    "displayName": "Thando",
    "voiceName": "zu-ZA-ThandoNeural",
    "language": "Zulu (South Africa)",
    "gender": "Female",
    "country": "za"
}, {
    "voiceId": "AFA686",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "zu-ZA",
    "displayName": "Themba",
    "voiceName": "zu-ZA-ThembaNeural",
    "language": "Zulu (South Africa)",
    "gender": "Male",
    "country": "za"
}, {
    "voiceId": "EFC483",
    "server": "tiktok",
    "tab": "hqVoice",
    "order": 25,
    "languageCode": "vi-VN",
    "displayName": "Thu Minh",
    "voiceName": "BV074_streaming",
    "language": "Vietnamese (Vietnam)",
    "gender": "Female",
    "description": "Cute, sweet",
    "country": "vn"
}, {
    "voiceId": "EFC481",
    "server": "tiktok",
    "tab": "hqVoice",
    "order": 25,
    "languageCode": "vi-VN",
    "displayName": "Tuy\u1ebft Mai",
    "voiceName": "BV562_streaming",
    "language": "Vietnamese (Vietnam)",
    "gender": "Female",
    "description": "Cute, sweet",
    "country": "vn"
}, {
    "voiceId": "CBF397",
    "server": "azure",
    "tab": "All",
    "order": 100,
    "languageCode": "en-US",
    "displayName": "Andrew3 Dragon HD Latest",
    "voiceName": "en-US-Andrew3:DragonHDLatestNeural",
    "language": "English (United States)",
    "gender": "Male",
    "country": "us"
}, {
    "voiceId": "CBF396",
    "server": "azure",
    "tab": "All",
    "order": 3,
    "languageCode": "vi-VN",
    "displayName": "Huy Ho\u00e0ng",
    "voiceName": "en-US-Andrew3:DragonHDLatestNeural",
    "language": "Vietnamese (Vietnam)",
    "gender": "Male",
    "description": "Podcast, Standard",
    "country": "vn"
}, {
    "voiceId": "CBF395",
    "server": "azure",
    "tab": "All",
    "order": 3,
    "languageCode": "vi-VN",
    "displayName": "Th\u00fay Vi",
    "voiceName": "en-US-Ava3:DragonHDLatestNeural",
    "language": "Vietnamese (Vietnam)",
    "gender": "Female",
    "description": "Podcast, Standard",
    "country": "vn"
}, {
    "voiceId": "FAB086",
    "server": "azure",
    "tab": "All",
    "order": 3,
    "languageCode": "vi-VN",
    "displayName": "B\u00edch Tuy\u1ec1n",
    "voiceName": "en-US-Emma2:DragonHDLatestNeural",
    "language": "Vietnamese (Vietnam)",
    "gender": "Female",
    "description": "Podcast, Standard",
    "country": "vn"
}, {
    "voiceId": "FFF940",
    "server": "openaiv2",
    "tab": "hqVoice",
    "order": 30,
    "languageCode": "vi-VN",
    "displayName": "Onyx (HaNoi)",
    "voiceName": "onyx__hn_vietnamese",
    "language": "Vietnamese (Vietnam)",
    "gender": "Male",
    "avatar": "onyx.svg",
    "description": "Older, mature, and experienced",
    "country": "vn"
}, {
    "voiceId": "BAC388",
    "server": "openaiv2",
    "tab": "hqVoice",
    "order": 30,
    "languageCode": "vi-VN",
    "displayName": "Echo (HaNoi)",
    "voiceName": "echo__hn_vietnamese",
    "language": "Vietnamese (Vietnam)",
    "gender": "Male",
    "description": "Warm, professional",
    "country": "vn"
}, {
    "voiceId": "AI2003",
    "server": "openaiv2",
    "tab": "hqVoice",
    "order": 30,
    "languageCode": "multi",
    "displayName": "Nova (HQ)",
    "voiceName": "nova__multi",
    "language": "Multilingual",
    "gender": "Female",
    "description": "Youthful, modern, energetic",
    "country": "multi"
}, {
    "voiceId": "AI2004",
    "server": "openaiv2",
    "tab": "hqVoice",
    "order": 30,
    "languageCode": "multi",
    "displayName": "Alloy (HQ)",
    "voiceName": "alloy__multi",
    "language": "Multilingual",
    "gender": "Male",
    "description": "Strong, steady, dependable",
    "country": "multi"
}, {
    "voiceId": "AI2005",
    "server": "openaiv2",
    "tab": "hqVoice",
    "order": 30,
    "languageCode": "multi",
    "displayName": "Ash (HQ)",
    "voiceName": "ash__multi",
    "language": "Multilingual",
    "gender": "Male",
    "description": "Deep, calm, neutral",
    "country": "multi"
}, {
    "voiceId": "AI2006",
    "server": "openaiv2",
    "tab": "hqVoice",
    "order": 30,
    "languageCode": "multi",
    "displayName": "Ballad (HQ)",
    "voiceName": "ballad__multi",
    "language": "Multilingual",
    "gender": "Male",
    "description": "Smooth, melodic, expressive",
    "country": "multi"
}, {
    "voiceId": "AI2007",
    "server": "openaiv2",
    "tab": "hqVoice",
    "order": 30,
    "languageCode": "multi",
    "displayName": "Coral (HQ)",
    "voiceName": "coral__multi",
    "language": "Multilingual",
    "gender": "Female",
    "description": "Bright, clear, crisp",
    "country": "multi"
}, {
    "voiceId": "AI2008",
    "server": "openaiv2",
    "tab": "hqVoice",
    "order": 30,
    "languageCode": "multi",
    "displayName": "Fable (HQ)",
    "voiceName": "fable__multi",
    "language": "Multilingual",
    "gender": "Male",
    "description": "Storytelling",
    "country": "multi"
}, {
    "voiceId": "AI2009",
    "server": "openaiv2",
    "tab": "hqVoice",
    "order": 30,
    "languageCode": "multi",
    "displayName": "Sage (HQ)",
    "voiceName": "sage__multi",
    "language": "Multilingual",
    "gender": "Female",
    "description": "Wise, calm",
    "country": "multi"
}, {
    "voiceId": "AI2010",
    "server": "openaiv2",
    "tab": "hqVoice",
    "order": 30,
    "languageCode": "multi",
    "displayName": "Shimmer (HQ)",
    "voiceName": "shimmer__multi",
    "language": "Multilingual",
    "gender": "Female",
    "description": "Soft, friendly",
    "country": "multi"
}, {
    "voiceId": "AI2011",
    "server": "openaiv2",
    "tab": "hqVoice",
    "order": 30,
    "languageCode": "multi",
    "displayName": "Verse (HQ)",
    "voiceName": "verse__multi",
    "language": "Multilingual",
    "gender": "Male",
    "description": "Poetic, smooth, lyrical",
    "country": "multi"
}]
