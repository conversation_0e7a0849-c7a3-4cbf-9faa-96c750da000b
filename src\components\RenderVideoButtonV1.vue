<template>
  <div>
    <!-- Ren<PERSON>ton -->
    <a-button
      type="primary"
      @click="showRenderModal"
      :disabled="!canRender || isProcessing"
      :loading="isProcessing"
    >
      Render Video
    </a-button>

    <!-- Render Options Modal -->
    <a-modal
      v-model:open="modalVisible"
      title="Video Render Options"
      @ok="handleRenderConfirm"
      @cancel="handleModalCancel"
      :confirm-loading="isProcessing"
      width="1200px"
      :body-style="{ maxHeight: '80vh', overflow: 'auto' }"
    >
      <a-row :gutter="24">
        <!-- Video Preview Column -->
        <a-col :span="12">
          <div class="video-preview-section">
            <h3>Video Preview</h3>
            
            <!-- Video Player -->
            <div class="video-container">
              <!-- <video
                ref="videoPlayer"
                :src="videoSrc"
                controls
                @loadedmetadata="onVideoLoaded"
                @timeupdate="onVideoTimeUpdate"
                class="preview-video"
              /> -->
          <VideoPlayer
              ref="videoPlayer"
              :src="ttsStore.currentSrtList?.path.replace('-ocr.srt', '.mp4').replace('.srt', '.mp4')"
              @timeupdate="onVideoTimeUpdate"
              @loadedmetadata="onVideoLoaded"
              size="full h-full" class="preview-video"
              />
              <!-- Drawing Canvas Overlay -->
              <canvas
                ref="drawingCanvas"
                class="drawing-canvas"
                @mousedown="startDrawing"
                @mousemove="continueDrawing"
                @mouseup="stopDrawing"
                @mouseleave="stopDrawing"
              />
            </div>

            <!-- Drawing Controls -->
            <div class="drawing-controls">
              <a-space>
                <a-button
                  :type="drawingMode === 'blur' ? 'primary' : 'default'"
                  @click="setDrawingMode('blur')"
                  size="small"
                >
                  <blur-outlined />
                  Blur Area
                </a-button>
                
                <a-button
                  :type="drawingMode === 'delogo' ? 'primary' : 'default'"
                  @click="setDrawingMode('delogo')"
                  size="small"
                >
                  <delete-outlined />
                  Remove Logo
                </a-button>
                
                <a-button
                  :type="drawingMode === 'subtitle' ? 'primary' : 'default'"
                  @click="setDrawingMode('subtitle')"
                  size="small"
                >
                  <font-colors-outlined />
                  Remove Subtitle
                </a-button>
                
                <a-button @click="clearDrawings" size="small">
                  <clear-outlined />
                  Clear All
                </a-button>
                
                <a-button @click="undoLastDrawing" size="small">
                  <undo-outlined />
                  Undo
                </a-button>
              </a-space>
            </div>

            <!-- Blur Areas List -->
            <div class="blur-areas-list" v-if="blurAreas.length > 0">
              <h4>Blur/Remove Areas:</h4>
              <a-list size="small" :data-source="blurAreas">
                <template #renderItem="{ item, index }">
                  <a-list-item>
                    <template #actions>
                      <a-button
                        size="small"
                        type="text"
                        danger
                        @click="removeBlurArea(index)"
                      >
                        <delete-outlined />
                      </a-button>
                    </template>
                    <a-list-item-meta>
                      <template #title>
                        {{ item.type.charAt(0).toUpperCase() + item.type.slice(1) }} Area {{ index + 1 }}
                      </template>
                      <template #description>
                        Position: {{ Math.round(item.x) }}, {{ Math.round(item.y) }} - 
                        Size: {{ Math.round(item.width) }} x {{ Math.round(item.height) }}
                      </template>
                    </a-list-item-meta>
                  </a-list-item>
                </template>
              </a-list>
            </div>

            <!-- Time Range Controls -->
            <div class="time-controls">
              <h4>Apply to Time Range:</h4>
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="Start Time (seconds)">
                    <a-input-number
                      v-model:value="timeRange.start"
                      :min="0"
                      :max="videoDuration"
                      :step="0.1"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="End Time (seconds)">
                    <a-input-number
                      v-model:value="timeRange.end"
                      :min="0"
                      :max="videoDuration"
                      :step="0.1"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-button @click="setCurrentTimeAsStart" size="small" style="margin-right: 8px">
                Set Current as Start
              </a-button>
              <a-button @click="setCurrentTimeAsEnd" size="small">
                Set Current as End
              </a-button>
            </div>
          </div>
        </a-col>

        <!-- Options Column -->
        <a-col :span="12">
          <a-form
            :model="renderOptions"
            layout="vertical"
          >
        <!-- Text Options -->
        <a-form-item label="Text Settings">
          <a-checkbox v-model:checked="renderOptions.showText">
            Show text/subtitles
          </a-checkbox>
          <div v-if="renderOptions.showText" class="mt-2">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="Font Size">
                  <a-input-number
                    v-model:value="renderOptions.fontSize"
                    :min="12"
                    :max="72"
                    addon-after="px"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="Text Color">
                  <a-input
                    v-model:value="renderOptions.textColor"
                    type="color"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </a-form-item>

        <!-- Logo Options -->
        <a-form-item label="Logo Settings">
          <a-checkbox v-model:checked="renderOptions.showLogo">
            Add logo/watermark
          </a-checkbox>
          <div v-if="renderOptions.showLogo" class="mt-2">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="Logo Position">
                  <a-select v-model:value="renderOptions.logoPosition">
                    <a-select-option value="top-left">Top Left</a-select-option>
                    <a-select-option value="top-right">Top Right</a-select-option>
                    <a-select-option value="bottom-left">Bottom Left</a-select-option>
                    <a-select-option value="bottom-right">Bottom Right</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="Logo Size">
                  <a-select v-model:value="renderOptions.logoSize">
                    <a-select-option value="small">Small</a-select-option>
                    <a-select-option value="medium">Medium</a-select-option>
                    <a-select-option value="large">Large</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
            <a-form-item label="Logo File">
              <a-upload
                v-model:file-list="logoFileList"
                :before-upload="beforeLogoUpload"
                accept="image/*"
                :max-count="1"
              >
                <a-button>
                  <upload-outlined />
                  Select Logo
                </a-button>
              </a-upload>
            </a-form-item>
          </div>
        </a-form-item>

        <!-- Audio Options -->
        <a-form-item label="Audio Settings">
          <a-checkbox v-model:checked="renderOptions.addBackgroundMusic">
            Add background music
          </a-checkbox>
          <div v-if="renderOptions.addBackgroundMusic" class="mt-2">
            <a-form-item label="Background Music File">
              <a-upload
                v-model:file-list="musicFileList"
                :before-upload="beforeMusicUpload"
                accept="audio/*"
                :max-count="1"
              >
                <a-button>
                  <upload-outlined />
                  Select Music
                </a-button>
              </a-upload>
            </a-form-item>
            <a-form-item label="Background Music Volume">
              <a-slider
                v-model:value="renderOptions.backgroundMusicVolume"
                :min="0"
                :max="100"
                :marks="{ 0: '0%', 25: '25%', 50: '50%', 75: '75%', 100: '100%' }"
              />
            </a-form-item>
          </div>

          <a-form-item label="Original Audio Volume">
            <a-slider
              v-model:value="renderOptions.originalAudioVolume"
              :min="0"
              :max="100"
              :marks="{ 0: '0%', 25: '25%', 50: '50%', 75: '75%', 100: '100%' }"
            />
          </a-form-item>

          <a-checkbox v-model:checked="renderOptions.extractVoiceOnly">
            Extract voice only (remove background music from original)
          </a-checkbox>
        </a-form-item>

        <!-- Output Options -->
        <a-form-item label="Output Settings">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="Video Quality">
                <a-select v-model:value="renderOptions.videoQuality">
                  <a-select-option value="720p">720p HD</a-select-option>
                  <a-select-option value="1080p">1080p Full HD</a-select-option>
                  <a-select-option value="4k">4K Ultra HD</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="Frame Rate">
                <a-select v-model:value="renderOptions.frameRate">
                  <a-select-option value="24">24 fps</a-select-option>
                  <a-select-option value="30">30 fps</a-select-option>
                  <a-select-option value="60">60 fps</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form-item>
        </a-form>
        </a-col>
      </a-row>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, reactive, nextTick, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { 
  UploadOutlined, 
  DeleteOutlined, 
  FontColorsOutlined, 
  ClearOutlined, 
  UndoOutlined 
} from '@ant-design/icons-vue';
import { useTTSStore } from '@/stores/ttsStore';
import VideoPlayer from './VideoPlayer.vue';

const ttsStore = useTTSStore();
const isProcessing = ref(false);
const modalVisible = ref(false);
const logoFileList = ref([]);
const musicFileList = ref([]);

// Video preview refs
const videoPlayer = ref(null);
const drawingCanvas = ref(null);
const videoSrc = ref(ttsStore.currentSrtList?.path.replace('-ocr.srt', '.mp4').replace('.srt', '.mp4')); // Will be set from ttsStore or file selection
const videoDuration = ref(0);
const currentTime = ref(0);

// Drawing state
const isDrawing = ref(false);
const drawingMode = ref('blur'); // 'blur', 'delogo', 'subtitle'
const blurAreas = ref([]);
const currentDrawing = ref(null);
const drawingHistory = ref([]);

// Time range for applying effects
const timeRange = reactive({
  start: 0,
  end: 0
});

// Render options configuration
const renderOptions = reactive({
  // Text options
  showText: true,
  fontSize: 24,
  textColor: '#ffffff',
  
  // Logo options
  showLogo: false,
  logoPosition: 'bottom-right',
  logoSize: 'medium',
  
  // Audio options
  addBackgroundMusic: false,
  backgroundMusicVolume: 30,
  originalAudioVolume: 80,
  extractVoiceOnly: false,
  
  // Output options
  videoQuality: '1080p',
  frameRate: '30'
});

const canRender = computed(() => {
  return ttsStore.currentSrtList && ttsStore.currentSrtList.items.length > 0;
});

function showRenderModal() {
  if (!canRender.value) return;
  modalVisible.value = true;
  
  // Load video source from ttsStore or show file selector
  nextTick(() => {
    initializeVideoPreview();
  });
}

function handleModalCancel() {
  modalVisible.value = false;
  clearDrawings();
}

function beforeLogoUpload(file) {
  // Validate logo file
  const isImage = file.type.startsWith('image/');
  if (!isImage) {
    message.error('Please select a valid image file!');
    return false;
  }
  
  const isLt5M = file.size / 1024 / 1024 < 5;
  if (!isLt5M) {
    message.error('Logo file must be smaller than 5MB!');
    return false;
  }
  
  renderOptions.logoFile = file;
  return false; // Prevent auto upload
}

function beforeMusicUpload(file) {
  // Validate music file
  const isAudio = file.type.startsWith('audio/');
  if (!isAudio) {
    message.error('Please select a valid audio file!');
    return false;
  }
  
  const isLt50M = file.size / 1024 / 1024 < 50;
  if (!isLt50M) {
    message.error('Music file must be smaller than 50MB!');
    return false;
  }
  
  renderOptions.musicFile = file;
  return false; // Prevent auto upload
}

async function handleRenderConfirm() {
  isProcessing.value = true;

  try {
    // Prepare render configuration
    const renderConfig = {
      srtItems: JSON.parse(JSON.stringify(ttsStore.currentSrtList.items)),
      srtPath: ttsStore.currentSrtList.path,
      blurAreas: blurAreas.value,
      timeRange: timeRange,
      options: {
        text: {
          enabled: renderOptions.showText,
          fontSize: renderOptions.fontSize,
          color: renderOptions.textColor
        },
        logo: {
          enabled: renderOptions.showLogo,
          position: renderOptions.logoPosition,
          size: renderOptions.logoSize,
          file: renderOptions.logoFile
        },
        audio: {
          backgroundMusic: {
            enabled: renderOptions.addBackgroundMusic,
            file: renderOptions.musicFile,
            volume: renderOptions.backgroundMusicVolume / 100
          },
          originalVolume: renderOptions.originalAudioVolume / 100,
          extractVoiceOnly: renderOptions.extractVoiceOnly
        },
        output: {
          quality: renderOptions.videoQuality,
          frameRate: parseInt(renderOptions.frameRate)
        }
      }
    };

    // Call the electron API with enhanced options
    electronAPI.processVideoWithOptions(renderConfig);
    
    // Simulate processing time
    await new Promise((resolve) => setTimeout(resolve, 2000));
    
    message.success('Video rendered successfully with custom options and blur areas!');
    modalVisible.value = false;
    
  } catch (error) {
    message.error('Error rendering video: ' + error.message);
  } finally {
    isProcessing.value = false;
  }
}

// Video preview functions
async function initializeVideoPreview() {
  // Try to get video source from ttsStore or electron API
  try {
    if (ttsStore.currentSrtList?.videoPath) {
      videoSrc.value = ttsStore.currentSrtList.videoPath;
    } else {
      // Request video file from electron
      const videoPath = await electronAPI.getVideoPath();
      if (videoPath) {
        videoSrc.value = videoPath;
      }
    }
  } catch (error) {
    console.warn('Could not load video preview:', error);
  }
}

function onVideoLoaded() {
  if (videoPlayer.value.$refs.video) {
    videoDuration.value = videoPlayer.value.$refs.video.duration;
    timeRange.end = videoDuration.value;
    setupCanvas();
  }
}

function onVideoTimeUpdate() {
  if (videoPlayer.value.$refs.video) {
    currentTime.value = videoPlayer.value.$refs.video.currentTime;
  }
}

function setupCanvas() {
  if (drawingCanvas.value && videoPlayer.value.$refs.video) {
    const canvas = drawingCanvas.value;
    const video = videoPlayer.value.$refs.video;
    
    canvas.width = video.clientWidth;
    canvas.height = video.clientHeight;
  }
}

// Drawing functions
function setDrawingMode(mode) {
  drawingMode.value = mode;
}

function startDrawing(e) {
  if (!drawingCanvas.value) return;
  
  isDrawing.value = true;
  const rect = drawingCanvas.value.getBoundingClientRect();
  const x = e.clientX - rect.left;
  const y = e.clientY - rect.top;
  
  currentDrawing.value = {
    type: drawingMode.value,
    x,
    y,
    width: 0,
    height: 0,
    timeStart: timeRange.start,
    timeEnd: timeRange.end
  };
  
  drawRectangle();
}

function continueDrawing(e) {
  if (!isDrawing.value || !currentDrawing.value || !drawingCanvas.value) return;
  
  const rect = drawingCanvas.value.getBoundingClientRect();
  const x = e.clientX - rect.left;
  const y = e.clientY - rect.top;
  
  currentDrawing.value.width = x - currentDrawing.value.x;
  currentDrawing.value.height = y - currentDrawing.value.y;
  
  drawRectangle();
}

function stopDrawing() {
  if (!isDrawing.value || !currentDrawing.value) return;
  
  isDrawing.value = false;
  
  // Only add if rectangle has meaningful size
  if (Math.abs(currentDrawing.value.width) > 10 && Math.abs(currentDrawing.value.height) > 10) {
    // Normalize negative dimensions
    if (currentDrawing.value.width < 0) {
      currentDrawing.value.x += currentDrawing.value.width;
      currentDrawing.value.width = Math.abs(currentDrawing.value.width);
    }
    if (currentDrawing.value.height < 0) {
      currentDrawing.value.y += currentDrawing.value.height;
      currentDrawing.value.height = Math.abs(currentDrawing.value.height);
    }
    
    blurAreas.value.push({ ...currentDrawing.value });
    drawingHistory.value.push([...blurAreas.value]);
  }
  
  currentDrawing.value = null;
  redrawCanvas();
}

function drawRectangle() {
  if (!drawingCanvas.value || !currentDrawing.value) return;
  
  const canvas = drawingCanvas.value;
  const ctx = canvas.getContext('2d');
  
  // Clear and redraw all areas
  redrawCanvas();
  
  // Draw current rectangle being drawn
  ctx.strokeStyle = getDrawingColor(currentDrawing.value.type);
  ctx.fillStyle = getDrawingColor(currentDrawing.value.type, 0.3);
  ctx.lineWidth = 2;
  ctx.setLineDash([5, 5]);
  
  ctx.fillRect(
    currentDrawing.value.x,
    currentDrawing.value.y,
    currentDrawing.value.width,
    currentDrawing.value.height
  );
  ctx.strokeRect(
    currentDrawing.value.x,
    currentDrawing.value.y,
    currentDrawing.value.width,
    currentDrawing.value.height
  );
}

function redrawCanvas() {
  if (!drawingCanvas.value) return;
  
  const canvas = drawingCanvas.value;
  const ctx = canvas.getContext('2d');
  
  // Clear canvas
  ctx.clearRect(0, 0, canvas.width, canvas.height);
  
  // Draw existing blur areas
  blurAreas.value.forEach((area, index) => {
    ctx.strokeStyle = getDrawingColor(area.type);
    ctx.fillStyle = getDrawingColor(area.type, 0.3);
    ctx.lineWidth = 2;
    ctx.setLineDash([]);
    
    ctx.fillRect(area.x, area.y, area.width, area.height);
    ctx.strokeRect(area.x, area.y, area.width, area.height);
    
    // Add label
    ctx.fillStyle = '#000';
    ctx.font = '12px Arial';
    ctx.fillText(
      `${area.type} ${index + 1}`,
      area.x + 5,
      area.y + 15
    );
  });
}

function getDrawingColor(type, alpha = 1) {
  const colors = {
    blur: `rgba(255, 255, 0, ${alpha})`, // Yellow
    delogo: `rgba(255, 0, 0, ${alpha})`, // Red
    subtitle: `rgba(0, 255, 0, ${alpha})` // Green
  };
  return colors[type] || `rgba(255, 255, 255, ${alpha})`;
}

function clearDrawings() {
  blurAreas.value = [];
  drawingHistory.value = [];
  redrawCanvas();
}

function undoLastDrawing() {
  if (blurAreas.value.length > 0) {
    blurAreas.value.pop();
    redrawCanvas();
  }
}

function removeBlurArea(index) {
  blurAreas.value.splice(index, 1);
  redrawCanvas();
}

function setCurrentTimeAsStart() {
  timeRange.start = currentTime.value;
}

function setCurrentTimeAsEnd() {
  timeRange.end = currentTime.value;
}
</script>

<style scoped>
.mt-2 {
  margin-top: 8px;
}

.video-preview-section {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
}

.video-container {
  position: relative;
  margin-bottom: 16px;
  border-radius: 4px;
  overflow: hidden;
}

.preview-video {
  width: 100%;
  height: auto;
  max-height: 300px;
  background: #000;
}

.drawing-canvas {
  position: absolute;
  top: 0;
  left: 0;
  cursor: crosshair;
  z-index: 10;
}

.drawing-controls {
  margin-bottom: 16px;
  padding: 12px;
  /* background: #fff; */
  border-radius: 4px;
  border: 1px solid #d9d9d9;
}

.blur-areas-list {
  margin-bottom: 16px;
  max-height: 200px;
  overflow-y: auto;
}

.time-controls {
  padding: 12px;
  /* background: #fff; */
  border-radius: 4px;
  border: 1px solid #d9d9d9;
}

.time-controls h4 {
  margin-bottom: 12px;
  color: #1890ff;
}
</style>