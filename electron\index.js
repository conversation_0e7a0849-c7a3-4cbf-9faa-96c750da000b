// Import all modules first

import './db/index.js'
import './db/chapters.js'
import './db/books.js'
import './db/db-manager.js'
import './db/clean-database.js'
import './db/batch-utils.js'
import './db/fix-database.js'
import './db/gpt_roles.js'
import './db/chapter-summaries.js'


import './server.js'
import './store.js'
import './services.js'
import './utils.js'
import './mergeFiles.js'
import './TikTokTTS.js'
import './mimimaxTTS.js'
import './adjustSpeed.js'
import './getSrtFWX.js'
import './videoOcr.js'
import './videoCutter.js'
import './ffmpegHandler.js'
import './videoRenderer.js'
import './ffmpegProcessor.js'
import './ffmpegUtils.js'
import './utils/demucs.js';
import './utils/processVideoWithOptions.js';
import './utils/videoRendererSimplified.js';
import './utils/assBuild.js';
import './utils/addAudioToVideo.js';
import './utils/systemUtils.js';
import './utils/sox.js';


// Import playwright modules
import './playwright/puppeteerService.js';
import './playwright/humanLikeBehavior.js';
import './playwright/playwrightLogic.js';
import './playwright/test-extraction.js';

// agent
import './agent/index.js';
import './agent/Agent.js';
import './agent/memory/NormalMemory.js';
import './agent/interpreter/JavaScriptInterpreter.js';
import './agent/llm/openai.js';
import './agent/skills/index.js';
import './agent/utils.js';
import './agent/summary.js';

// ipc 
import './ipc/summary-handlers.js';

// service
import './services/chapter-content-service.js'

// Import main.js last to start the app
import './main.js'




// import (.*) from ['"](.*)['"];? → const $1 = require('$2');
