<template>
  <div class="video-renderer-container p-4">
    <h2 class="text-xl font-bold mb-4">Video Renderer</h2>

    <div class="mb-4">
      <a-alert
        v-if="error"
        type="error"
        :message="error"
        class="mb-4"
        showIcon
      />

      <div class="flex flex-col gap-4">
        <!-- Video Selection -->
        <div class="flex flex-col">
          <div class="mb-2">
            <span class="font-medium">Select Video:</span>
            <a-button
              type="primary"
              @click="selectVideo"
              :disabled="isProcessing"
              class="ml-2"
            >
              Browse
            </a-button>
            <span v-if="videoFile" class="ml-2">
              {{ videoFile.name }}
            </span>
          </div>

          <!-- SRT Selection -->
          <div class="mb-2">
            <span class="font-medium">SRT Data:</span>
            <a-button
              v-if="!srtItems.length"
              @click="openSrtModal"
              :disabled="isProcessing"
              class="ml-2"
            >
              Select SRT
            </a-button>
            <span v-else class="ml-2">
              {{ srtItems.length }} subtitles loaded
            </span>
          </div>

          <!-- Output Directory -->
          <div class="mb-2">
            <span class="font-medium">Output Directory:</span>
            <a-button
              @click="selectOutputDir"
              :disabled="isProcessing"
              class="ml-2"
            >
              Browse
            </a-button>
            <span v-if="outputDir" class="ml-2">
              {{ outputDir }}
            </span>
          </div>

          <!-- Subtitle Style -->
          <div class="mb-2">
            <span class="font-medium">Subtitle Style:</span>
            <a-radio-group v-model:value="subtitleStyle" class="ml-2">
              <a-radio-button value="default">Default</a-radio-button>
              <a-radio-button value="white">White</a-radio-button>
              <a-radio-button value="yellow">Yellow</a-radio-button>
            </a-radio-group>
          </div>

          <!-- Add Logo -->
          <div class="mb-2">
            <a-checkbox v-model:checked="addLogo" class="mr-2">Add Logo</a-checkbox>
            <a-button
              v-if="addLogo"
              @click="selectLogoFile"
              :disabled="isProcessing"
              class="ml-2"
            >
              Select Logo
            </a-button>
            <span v-if="logoPath" class="ml-2">
              {{ logoPath.split('\\').pop() }}
            </span>
          </div>

          <!-- Add Text Overlay -->
          <div class="mb-2">
            <a-checkbox v-model:checked="addTextOverlay" class="mr-2">Add Text Overlay</a-checkbox>
            <a-input
              v-if="addTextOverlay"
              v-model:value="textOverlay"
              placeholder="Enter text to overlay"
              class="ml-2 w-64"
            />
            <a-select
              v-if="addTextOverlay"
              v-model:value="textPosition"
              class="ml-2 w-32"
            >
              <a-select-option value="top">Top</a-select-option>
              <a-select-option value="bottom">Bottom</a-select-option>
            </a-select>
          </div>

          <!-- Video Codec -->
          <div class="mb-2">
            <span class="font-medium">Video Codec:</span>
            <a-select v-model:value="videoCodec" class="ml-2 w-40">
              <a-select-option value="h264_nvenc">NVIDIA H.264</a-select-option>
              <a-select-option value="libx264">CPU H.264</a-select-option>
            </a-select>
          </div>

          <!-- Audio Bitrate -->
          <div class="mb-2">
            <span class="font-medium">Audio Bitrate (kbps):</span>
            <a-input-number
              v-model:value="audioBitrate"
              :min="64"
              :max="320"
              class="ml-2 w-20"
            />
          </div>
        </div>

        <!-- Render Button -->
        <div class="mt-4">
          <a-button
            type="primary"
            size="large"
            @click="renderVideo"
            :disabled="!canRender || isProcessing"
            :loading="isProcessing"
          >
            Render Video
          </a-button>
        </div>

        <!-- Progress -->
        <div v-if="isProcessing" class="mt-4">
          <a-progress :percent="progress" status="active" />
          <div class="mt-2">{{ processingStatus }}</div>
        </div>

        <!-- Result -->
        <div v-if="outputPath" class="mt-4 p-4 bg-gray-100 rounded">
          <div class="font-medium">Output:</div>
          <div class="mt-2">{{ outputPath }}</div>
          <div class="mt-2">
            <a-button type="primary" @click="openOutputFile">
              Open Video
            </a-button>
            <a-button class="ml-2" @click="openOutputFolder">
              Open Folder
            </a-button>
          </div>
        </div>
      </div>
    </div>

    <!-- SRT Modal -->
    <a-modal
      v-model:open="srtModalVisible"
      title="Select SRT Data"
      @ok="handleSrtModalOk"
      width="800px"
    >
      <SrtLists
        buttonText="Select SRT"
        @select="handleSelectSrt"
      />
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import SrtLists from './SrtLists.vue';
import { useTTSStore } from '../stores/ttsStore';

// Node.js modules (available through preload)
const path = window.electronAPI?.path || {
  dirname: (p) => p.substring(0, p.lastIndexOf('\\'))
};

// Store
const ttsStore = useTTSStore();

// State
const videoFile = ref(null);
const srtItems = ref([]);
const outputDir = ref('');
const subtitleStyle = ref('default');
const addLogo = ref(false);
const logoPath = ref('');
const addTextOverlay = ref(false);
const textOverlay = ref('');
const textPosition = ref('bottom');
const videoCodec = ref('h264_nvenc');
const audioBitrate = ref(192);
const isProcessing = ref(false);
const progress = ref(0);
const processingStatus = ref('');
const error = ref('');
const outputPath = ref('');
const srtModalVisible = ref(false);

// Computed
const canRender = computed(() => {
  return videoFile.value && srtItems.value.length > 0;
});

// Methods
const selectVideo = async () => {
  try {
    const result = await window.electronAPI.openFileDialog({
      title: 'Select Video File',
      filters: [
        { name: 'Video Files', extensions: ['mp4', 'avi', 'mkv', 'mov'] }
      ],
      properties: ['openFile']
    });

    if (!result.canceled && result.filePaths && result.filePaths.length > 0) {
      videoFile.value = {
        path: result.filePaths[0],
        name: result.filePaths[0].split('\\').pop()
      };
    }
  } catch (err) {
    error.value = `Error selecting video: ${err.message}`;
  }
};

const selectOutputDir = async () => {
  try {
    const result = await window.electronAPI.openFileDialog({
      title: 'Select Output Directory',
      properties: ['openDirectory']
    });

    if (!result.canceled && result.filePaths && result.filePaths.length > 0) {
      outputDir.value = result.filePaths[0];
    }
  } catch (err) {
    error.value = `Error selecting output directory: ${err.message}`;
  }
};

const selectLogoFile = async () => {
  try {
    const result = await window.electronAPI.openFileDialog({
      title: 'Select Logo Image',
      filters: [
        { name: 'Image Files', extensions: ['png', 'jpg', 'jpeg'] }
      ],
      properties: ['openFile']
    });

    if (!result.canceled && result.filePaths && result.filePaths.length > 0) {
      logoPath.value = result.filePaths[0];
    }
  } catch (err) {
    error.value = `Error selecting logo: ${err.message}`;
  }
};

const openSrtModal = () => {
  srtModalVisible.value = true;
};

const handleSrtModalOk = () => {
  srtModalVisible.value = false;
};

const handleSelectSrt = (srtList) => {
  srtItems.value = srtList.items;
  srtModalVisible.value = false;
  message.success(`Loaded ${srtItems.value.length} subtitles`);
};

const renderVideo = async () => {
  if (!canRender.value) return;

  isProcessing.value = true;
  progress.value = 0;
  error.value = '';
  processingStatus.value = 'Preparing to render video...';

  try {
    // Set up event listener for progress updates
    const progressListener = (event, data) => {
      if (data.code === 0) {
        processingStatus.value = data.data;

        // Try to extract progress percentage from status message
        const progressMatch = data.data.match(/(\d+)\/(\d+)/);
        if (progressMatch && progressMatch.length === 3) {
          const current = parseInt(progressMatch[1]);
          const total = parseInt(progressMatch[2]);
          progress.value = Math.floor((current / total) * 100);
        }
      } else if (data.code === 1) {
        error.value = data.data;
      }
    };

    // Add event listener
    window.electronAPI.onRenderVideoProgress(progressListener);

    // Call the render function
    const result = await window.electronAPI.renderVideoWithSrt({
      srtArray: JSON.parse(JSON.stringify(srtItems.value)),
      videoPath: videoFile.value.path,
      outputDir: outputDir.value || undefined,
      subtitleStyle: subtitleStyle.value,
      addLogo: addLogo.value,
      logoPath: logoPath.value || undefined,
      addText: addTextOverlay.value ? textOverlay.value : '',
      textPosition: textPosition.value,
      videoCodec: videoCodec.value,
      audioBitrate: audioBitrate.value.toString()
    });

    // Remove event listener
    window.electronAPI.removeRenderVideoProgress(progressListener);

    if (result.success) {
      outputPath.value = result.outputPath;
      message.success(result.message || 'Video rendered successfully!');
      progress.value = 100;
      processingStatus.value = 'Rendering complete!';
      console.log('Render result:', result);
    } else {
      error.value = result.error || 'Unknown error occurred';
      message.error('Error rendering video: ' + error.value);
    }
  } catch (err) {
    error.value = `Error rendering video: ${err.message}`;
    message.error(error.value);
  } finally {
    isProcessing.value = false;
  }
};

const openOutputFile = async () => {
  if (outputPath.value) {
    try {
      await window.electronAPI.openFile(outputPath.value);
    } catch (err) {
      console.error('Error opening file:', err);
      message.error(`Error opening file: ${err.message}`);
    }
  }
};

const openOutputFolder = async () => {
  if (outputPath.value) {
    try {
      // Get directory path regardless of platform
      const folderPath = path.dirname(outputPath.value);
      await window.electronAPI.openFolder(folderPath);
    } catch (err) {
      console.error('Error opening folder:', err);
      message.error(`Error opening folder: ${err.message}`);
    }
  }
};

// Lifecycle
onMounted(() => {
  // Initialize with default values if needed
});
</script>

<style scoped>
.video-renderer-container {
  max-width: 1200px;
  margin: 0 auto;
}
</style>
