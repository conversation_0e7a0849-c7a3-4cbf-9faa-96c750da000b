import { usePromptStore } from '@/stores/promptStore'

// Global prompt store instance
let promptStoreInstance = null

// Initialize prompt store
export function initPromptService() {
  try {
    promptStoreInstance = usePromptStore()
    console.log('Prompt service initialized successfully')
    return true
  } catch (error) {
    console.warn('Failed to initialize prompt service:', error.message)
    return false
  }
}

// Get current prompt template
export function getCurrentPromptTemplate() {
  if (!promptStoreInstance) {
    initPromptService()
  }
  
  return promptStoreInstance?.selectedTemplate || null
}

// Generate prompt using current template
export function generatePromptWithTemplate(sourceLang = 'auto', targetLang = 'Vietnamese', dictionary = {}) {
  if (!promptStoreInstance) {
    const initialized = initPromptService()
    if (!initialized) {
      return null // Return null to indicate fallback should be used
    }
  }
  
  try {
    return promptStoreInstance.generatePrompt(sourceLang, targetLang, dictionary)
  } catch (error) {
    console.warn('Error generating prompt with template:', error.message)
    return null
  }
}

// Generate prompt using specific template
export function generatePromptWithSpecificTemplate(templateId, sourceLang = 'auto', targetLang = 'Vietnamese', dictionary = {}) {
  if (!promptStoreInstance) {
    const initialized = initPromptService()
    if (!initialized) {
      return null
    }
  }
  
  try {
    const template = promptStoreInstance.promptTemplates.find(t => t.id === templateId)
    if (!template) {
      throw new Error(`Template with ID ${templateId} not found`)
    }
    
    return promptStoreInstance.generatePromptFromTemplate(template, sourceLang, targetLang, dictionary)
  } catch (error) {
    console.warn('Error generating prompt with specific template:', error.message)
    return null
  }
}

// Select template
export function selectPromptTemplate(templateId) {
  if (!promptStoreInstance) {
    const initialized = initPromptService()
    if (!initialized) {
      return false
    }
  }
  
  try {
    return promptStoreInstance.selectTemplate(templateId)
  } catch (error) {
    console.warn('Error selecting template:', error.message)
    return false
  }
}

// Get all templates
export function getAllPromptTemplates() {
  if (!promptStoreInstance) {
    const initialized = initPromptService()
    if (!initialized) {
      return []
    }
  }
  
  return promptStoreInstance?.promptTemplates || []
}

// Add new template
export function addPromptTemplate(template) {
  if (!promptStoreInstance) {
    const initialized = initPromptService()
    if (!initialized) {
      return null
    }
  }
  
  try {
    return promptStoreInstance.addTemplate(template)
  } catch (error) {
    console.warn('Error adding template:', error.message)
    return null
  }
}

// Update template
export function updatePromptTemplate(id, updates) {
  if (!promptStoreInstance) {
    const initialized = initPromptService()
    if (!initialized) {
      return null
    }
  }
  
  try {
    return promptStoreInstance.updateTemplate(id, updates)
  } catch (error) {
    console.warn('Error updating template:', error.message)
    return null
  }
}

// Delete template
export function deletePromptTemplate(id) {
  if (!promptStoreInstance) {
    const initialized = initPromptService()
    if (!initialized) {
      return false
    }
  }
  
  try {
    return promptStoreInstance.deleteTemplate(id)
  } catch (error) {
    console.warn('Error deleting template:', error.message)
    return false
  }
}

// Export templates
export function exportPromptTemplates() {
  if (!promptStoreInstance) {
    const initialized = initPromptService()
    if (!initialized) {
      return null
    }
  }
  
  try {
    return promptStoreInstance.exportTemplates()
  } catch (error) {
    console.warn('Error exporting templates:', error.message)
    return null
  }
}

// Import templates
export function importPromptTemplates(templates) {
  if (!promptStoreInstance) {
    const initialized = initPromptService()
    if (!initialized) {
      return []
    }
  }
  
  try {
    return promptStoreInstance.importTemplates(templates)
  } catch (error) {
    console.warn('Error importing templates:', error.message)
    return []
  }
}

// Fallback prompt function (same as in allTranslateService.js)
export function createFallbackPrompt(sourceLang = 'auto', targetLang = 'Vietnamese', dictionary = {}) {
  let dictionaryText = '';

  // Nếu có từ điển thuật ngữ, thêm vào prompt
  if (Object.keys(dictionary).length > 0) {
    dictionaryText = 'Từ điển thuật ngữ (sử dụng đồng nhất):\n';
    for (const [term, translation] of Object.entries(dictionary)) {
      dictionaryText += `- ${term}: ${translation}\n`;
    }
    dictionaryText += '\n';
  }

  return `Bạn là dịch giả phụ đề video chuyên nghiệp. Dịch từ ${
    sourceLang === 'auto' ? 'ngôn ngữ được phát hiện' : sourceLang
  } sang ${targetLang}.

${dictionaryText}
YÊU CẦU:
1. Dịch tự nhiên như người Việt nói, ưu tiên từ thuần Việt
2. Giữ nguyên số thứ tự. Chỉ trả về phần dịch, không bao gồm câu gốc
3. Giữ cảm xúc, ngữ điệu người nói
4. Phù hợp phụ đề (ngắn gọn, dễ đọc)
5. Tự động nhận diện loại video và điều chỉnh phong cách
6. KHÔNG giữ từ ngôn ngữ gốc, KHÔNG thêm ghi chú
${dictionaryText ? '7. Sử dụng từ điển thuật ngữ để đảm bảo tính nhất quán\n' : ''}
${dictionaryText ? '8.' : '7.'} Câu giống nhau → dịch đồng nhất

PHONG CÁCH TỰ ĐỘNG:
- Phim/Drama: Giữ tính văn học
- Vlog/YouTube: Thân thiện, đời thường  
- Tin tức: Trang trọng, chính xác
- Gaming: Sôi động, năng lượng

Dịch văn bản sau:`;
}

// Main function to get prompt (with fallback)
export function getTranslationPrompt(sourceLang = 'auto', targetLang = 'Vietnamese', dictionary = {}) {
  // Try to use prompt store first
  const prompt = generatePromptWithTemplate(sourceLang, targetLang, dictionary)
  
  // If prompt store is not available or fails, use fallback
  if (!prompt) {
    console.log('Using fallback prompt template')
    return createFallbackPrompt(sourceLang, targetLang, dictionary)
  }
  
  return prompt
}
