const { chromium } = require('playwright');
const fs = require('fs');

const {
    simulateMouseMovement,
    getRandomDelay,
} = require('./humanLikeBehavior');

// Biến toàn cục

const getRandomImageKeyword = () => {
    const keywords = ['nature', 'technology', 'science', 'art', 'animals'];
    return keywords[Math.floor(Math.random() * keywords.length)];
};

// Hàm khởi tạo trình duyệt Playwright// Thêm mutex để tránh khởi tạo đồng thời
let initializingPromise = null;

// Hàm khởi tạo trình duyệt Playwright
// Tìm đường dẫn đến Chrome đã cài đặt trên máy
function findChromePath() {
    const isWindows = process.platform === 'win32';
    const isMac = process.platform === 'darwin';

    if (isWindows) {
        // Các đường dẫn phổ biến của Chrome trên Windows
        const possibleWindowsPaths = [
            process.env['PROGRAMFILES(X86)'] + '\\Google\\Chrome\\Application\\chrome.exe',
            process.env['PROGRAMFILES'] + '\\Google\\Chrome\\Application\\chrome.exe',
            process.env['LOCALAPPDATA'] + '\\Google\\Chrome\\Application\\chrome.exe',
            'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
            'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe',
        ];

        // Kiểm tra từng đường dẫn
        for (const path of possibleWindowsPaths) {
            if (fs.existsSync(path)) {
                return path;
            }
        }
    } else if (isMac) {
        // Các đường dẫn phổ biến của Chrome trên macOS
        const possibleMacPaths = [
            '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
            '/Applications/Google Chrome Canary.app/Contents/MacOS/Google Chrome Canary',
            `${process.env.HOME}/Applications/Google Chrome.app/Contents/MacOS/Google Chrome`,
            `${process.env.HOME}/Applications/Google Chrome Canary.app/Contents/MacOS/Google Chrome Canary`,
        ];

        // Kiểm tra từng đường dẫn
        for (const path of possibleMacPaths) {
            if (fs.existsSync(path)) {
                return path;
            }
        }
    } else {
        // Cho Linux và các hệ điều hành khác
        const { execSync } = require('child_process');
        try {
            // Sử dụng which để tìm đường dẫn Chrome/Chromium
            return execSync('which google-chrome || which chrome || which chromium').toString().trim();
        } catch (error) {
            console.log('Cannot find Chrome executable in Linux environment');
        }
    }

    // Trả về null nếu không tìm thấy
    return null;
}

async function initializeBrowser() {
    const baseDir = C.path.join(C.configDirectory || process.cwd(), 'chrome-data/');

    // Tìm đường dẫn Chrome
    console.log('Base directory for Chrome data:', baseDir);
    const chromePath = findChromePath();
    F.l('Chrome path found:', chromePath);
    if (!chromePath) {
        throw new Error('Cannot find Chrome on your system. Please install Google Chrome.');
    }

    F.l('Using Chrome at:', chromePath);

    // Đảm bảo thư mục dữ liệu tồn tại
    if (!fs.existsSync(baseDir)) {
        fs.mkdirSync(baseDir, { recursive: true });
    }

    // Sử dụng launchPersistentContext với executablePath và anti-detection
    const context = await chromium.launchPersistentContext(baseDir, {
        headless: false, // Set to true in production
        slowMo: 100, // Slower to appear more human-like
        args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-blink-features=AutomationControlled',
            '--disable-extensions',
            '--no-first-run',
            '--disable-default-apps',
            '--disable-popup-blocking',
            '--disable-translate',
            '--disable-background-timer-throttling',
            '--disable-renderer-backgrounding',
            '--disable-device-discovery-notifications',
            '--disable-ipc-flooding-protection',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor'
        ],
        executablePath: chromePath,
        viewport: { width: 1920, height: 1080 },
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        extraHTTPHeaders: {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
    });

    // Add stealth scripts to avoid detection
    await context.addInitScript(() => {
        // Remove webdriver property
        delete navigator.__proto__.webdriver;

        // Mock plugins
        Object.defineProperty(navigator, 'plugins', {
            get: () => [1, 2, 3, 4, 5]
        });

        // Mock languages
        Object.defineProperty(navigator, 'languages', {
            get: () => ['en-US', 'en', 'zh-CN', 'zh']
        });

        // Mock permissions
        const originalQuery = window.navigator.permissions.query;
        window.navigator.permissions.query = (parameters) => (
            parameters.name === 'notifications' ?
                Promise.resolve({ state: Notification.permission }) :
                originalQuery(parameters)
        );

        // Mock chrome object
        window.chrome = {
            runtime: {}
        };
    });

    // Xử lý sự kiện khi trình duyệt bị đóng
    context.on('close', async () => {
        F.l('Browser context was closed by user');
        S.browser = null;
        S.page = null;
        S.context = null;
    });

    // Trong Playwright với launchPersistentContext, context đóng vai trò như browser và context cùng lúc
    return { context };
}

// Thiết lập User-Agent
async function setUserAgent(page) {
    const userAgent =
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) ' +
        'AppleWebKit/537.36 (KHTML, like Gecko) ' +
        'Chrome/120.0.0.0 Safari/537.36';

    try {
        // Trong playwright-core, setUserAgent có thể không tồn tại
        // Sử dụng context.addInitScript thay thế
        await page.context().addInitScript(function (userAgent) {
            Object.defineProperty(navigator, 'userAgent', {
                get: function () {
                    return userAgent;
                },
            });
        }, userAgent);

        // Thêm các HTTP headers
        await page.setExtraHTTPHeaders({
            'User-Agent': userAgent,
            'Accept-Language': 'en-US,en;q=0.9',
            Accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
        });
    } catch (err) {
        F.l('Error setting user agent:', err);
    }
}

// Xử lý form consent của Google
async function handleConsentForm(page) {
    const agreeButtonSelectors = [
        'button#L2AGLb',
        'button[aria-label="Accept all"]',
        'button[aria-label*="consent"]',
        'div[role="none"] button + button',
    ];

    try {
        for (const selector of agreeButtonSelectors) {
            const agreeButton = await page.$(selector);
            if (agreeButton) {
                await agreeButton.click();
                await page.waitForLoadState('networkidle');
                break;
            }
        }
    } catch (err) {
        F.l('No consent form found or error in handling consent:', err);
    }
}

// Điều hướng đến Google
async function navigateToGoogle(page) {
    await page.goto('https://www.google.com/search?sclient=img&udm=2', {
        waitUntil: 'networkidle',
    });
}

// Khởi tạo dịch vụ web Google
const initWebService = async (event, query = null) => {
    
    try {
        console.log('Initializing Google web service...',F,S);
        // Tránh khởi tạo đồng thời
        if (S.isInitializing) {
            F.l('Browser initialization already in progress, waiting...');
            if (initializingPromise) {
                await initializingPromise;
            } else {
                await new Promise((resolve) => setTimeout(resolve, 3000));
            }
        }

        // Nếu context không tồn tại, khởi tạo mới
        if (!S.context) {
            S.isInitializing = true;

            try {
                // Tạo một promise để theo dõi quá trình khởi tạo
                initializingPromise = (async () => {
                    F.l('Initializing browser context...');
                    const result = await initializeBrowser();
                    S.context = result.context;
                    // Khi sử dụng launchPersistentContext, không cần tạo browser riêng
                    S.browser = null;
                    F.l('Browser context initialized successfully');
                })();

                await initializingPromise;
            } catch (error) {
                F.l('Failed to initialize browser:', error);
                throw error;
            } finally {
                S.isInitializing = false;
                initializingPromise = null;
            }
        }

        // Nếu trang không tồn tại hoặc đã đóng, khởi tạo mới
        if (!S.page || S.page.isClosed()) {
            F.l('Creating new page...');
            S.page = await S.context.newPage();
            await setUserAgent(S.page);
            // await navigateToGoogle(S.page);
            // await handleConsentForm(S.page);
            F.l('New page created and navigated to Google');
        }

        // const searchQuery = query || getRandomImageKeyword();
        // if (!query) {
        //     await performSearch(null, searchQuery);
        // }
    } catch (error) {
        F.l('Error in googleWebService:', error);

        // Thử khởi tạo lại nếu có lỗi
        if (S.context) {
            try {
                await S.context.close();
            } catch (e) {}
            S.browser = null;
            S.page = null;
            S.context = null;
        }

        throw error;
    }
};

// Hàm thực hiện tìm kiếm
// Cập nhật hàm h_performSearch để sử dụng humanLikeSearch
const performSearchLikeHuman = async function (event, searchQuery) {
    try {
        // Kiểm tra context và page
        if (!S.context || !S.page || S.page.isClosed()) {
            F.l('Context or page not available, initializing...');
            await initWebService(null, null); // Chỉ khởi tạo trang, không tìm kiếm
            await new Promise((resolve) => setTimeout(resolve, getRandomDelay(1000, 2000)));
        }

        // Kiểm tra trạng thái trang
        try {
            await S.page.evaluate(() => document.title);
        } catch (e) {
            F.l('Page is not responsive, reinitializing...');
            await initWebService(null, null);
            await new Promise((resolve) => setTimeout(resolve, getRandomDelay(1000, 2000)));
        }

        F.l(`Starting human-like search for: "${searchQuery}"`);

        // Di chuyển chuột trước khi tìm kiếm
        await simulateMouseMovement(S.page);

        // Tìm input tìm kiếm
        const searchInputExists = await S.page.evaluate(() => {
            return !!document.querySelector('input[name="q"], textarea[name="q"]');
        });

        if (searchInputExists) {
            // Tìm đến input search với chuột
            const searchInput = await S.page.$('input[name="q"], textarea[name="q"]');
            if (searchInput) {
                const box = await searchInput.boundingBox();
                if (box) {
                    // Di chuyển đến gần input trước
                    await S.page.mouse.move(
                        box.x + box.width / 2 + (Math.random() * 20 - 10),
                        box.y - 20 + Math.random() * 10,
                        { steps: getRandomDelay(10, 20) },
                    );
                    await S.page.waitForTimeout(getRandomDelay(100, 300));

                    // Di chuyển đến input search
                    await S.page.mouse.move(
                        box.x + box.width / 2 + (Math.random() * 20 - 10),
                        box.y + box.height / 2 + (Math.random() * 10 - 5),
                        { steps: getRandomDelay(5, 10) },
                    );

                    // Click vào input
                    await S.page.mouse.click(
                        box.x + box.width / 2 + (Math.random() * 10 - 5),
                        box.y + box.height / 2 + (Math.random() * 6 - 3),
                    );

                    // Xóa nội dung hiện tại (nếu có)
                    await S.page.keyboard.press('Control+A');
                    await S.page.waitForTimeout(getRandomDelay(50, 150));
                    await S.page.keyboard.press('Backspace');

                    // Đợi một chút trước khi bắt đầu gõ
                    await S.page.waitForTimeout(getRandomDelay(200, 500));

                    // Gõ từng ký tự với tốc độ khác nhau
                    F.l('Typing search query...');
                    for (let i = 0; i < searchQuery.length; i++) {
                        // Tốc độ gõ ngẫu nhiên cho người tương đối nhanh
                        const typingDelay = getRandomDelay(30, 100);

                        // Gõ ký tự
                        await S.page.keyboard.type(searchQuery[i], { delay: typingDelay });

                        // Thỉnh thoảng dừng lại lâu hơn (giống người thật đang suy nghĩ)
                        if (Math.random() < 0.05) {
                            // 5% cơ hội dừng lâu hơn
                            await S.page.waitForTimeout(getRandomDelay(300, 700));
                        }
                    }

                    // Đợi một chút trước khi nhấn Enter
                    await S.page.waitForTimeout(getRandomDelay(200, 500));

                    // Nhấn Enter
                    F.l('Pressing Enter to search...');
                    await S.page.keyboard.press('Enter');
                }
            }
        } else {
            // Phương pháp dự phòng nếu không tìm thấy input
            F.l('Search input not found, using fallback method...');
            await S.page.evaluate((query) => {
                const input = document.querySelector('input[name="q"], textarea[name="q"]');
                if (input) {
                    input.value = query;
                    input.focus();
                    const form = input.closest('form');
                    if (form) form.submit();
                }
            }, searchQuery);
        }

        // Đợi trang chuyển hướng
        F.l('Waiting for search results to load...');
        await S.page.waitForLoadState('networkidle', { timeout: 30000 });

        // Kiểm tra CAPTCHA
        const hasCaptcha = await S.page.evaluate(() => {
            return !!document.querySelector('form#captcha-form, img[src*="captcha"], #recaptcha, .g-recaptcha');
        });

        if (hasCaptcha) {
            F.l('CAPTCHA detected! Waiting for user to solve it...');

            // Thông báo cho người dùng
            await S.page.evaluate(() => {
                if (!document.getElementById('captcha-notification')) {
                    const notification = document.createElement('div');
                    notification.id = 'captcha-notification';
                    notification.style.cssText =
                        'position:fixed;top:0;left:0;right:0;background:red;color:white;padding:15px;text-align:center;z-index:9999;';
                    notification.innerText = 'CAPTCHA detected! Please solve it to continue.';
                    document.body.appendChild(notification);
                }
            });

            // Chờ người dùng giải quyết CAPTCHA (tối đa 2 phút)
            await S.page
                .waitForFunction(
                    () => {
                        return !document.querySelector(
                            'form#captcha-form, img[src*="captcha"], #recaptcha, .g-recaptcha',
                        );
                    },
                    { timeout: 120000 },
                )
                .catch((e) => {
                    F.l('CAPTCHA waiting timeout:', e);
                });

            // Xóa thông báo
            await S.page.evaluate(() => {
                const notification = document.getElementById('captcha-notification');
                if (notification) notification.remove();
            });
        }

        // Mô phỏng xem xét kết quả
        F.l('Simulating result examination...');

        // Đợi kết quả xuất hiện
        await S.page.waitForTimeout(getRandomDelay(800, 1500));

        // Cuộn trang giống người thực
        await S.page.evaluate(() => {
            // Tạo hàm cuộn mượt
            const smoothScroll = (distance, duration) => {
                return new Promise((resolve) => {
                    const start = window.pageYOffset;
                    const startTime = performance.now();

                    function step(timestamp) {
                        const elapsed = timestamp - startTime;
                        const progress = Math.min(elapsed / duration, 1);
                        // Hiệu ứng easing
                        const easing =
                            progress < 0.5
                                ? 4 * progress * progress * progress
                                : 1 - Math.pow(-2 * progress + 2, 3) / 2;

                        window.scrollTo(0, start + distance * easing);

                        if (progress < 1) {
                            window.requestAnimationFrame(step);
                        } else {
                            resolve();
                        }
                    }

                    window.requestAnimationFrame(step);
                });
            };

            // Cuộn xuống một khoảng ngẫu nhiên
            const scrollDistance = Math.floor(Math.random() * 500) + 200;
            return smoothScroll(scrollDistance, 1000);
        });

        // Thêm thời gian "đọc" kết quả
        await S.page.waitForTimeout(getRandomDelay(1000, 1500));

        F.l(`Search completed for: "${searchQuery}"`);
        return true;
    } catch (err) {
        F.l('Search failed:', err.message);
        throw new Error(`Search failed: ${err.message}`);
    }
};

const performSearch = async function (event, searchQuery) {
    const maxRetries = 5; // Tăng số lần thử lại
    let retries = 0;
    let lastError = null;

    while (retries < maxRetries) {
        try {
            // Kiểm tra browser và page, khởi tạo lại nếu cần
            if (!S.context || !S.page || S.page.isClosed()) {
                F.l(`Attempt ${retries + 1}: Context or page not available, reinitializing...`);
                await initWebService(null, searchQuery);
                await new Promise((resolve) => setTimeout(resolve, 3000));
                continue; // Bắt đầu lại vòng lặp
            }

            // Kiểm tra trạng thái trang
            try {
                await S.page.evaluate(`document.title`);
            } catch (e) {
                F.l('Page is not responsive, reinitializing...', e);
                await initWebService(null, searchQuery);
                await new Promise((resolve) => setTimeout(resolve, 2000));
                continue; // Bắt đầu lại vòng lặp
            }

            // === PHƯƠNG PHÁP 1: THÔNG QUA THAO TÁC TRỰC TIẾP ===
            // Kiểm tra input tìm kiếm tồn tại
            const searchInputExists = await S.page.evaluate(`
                (function() {
                    const input = document.querySelector('input[name="q"], textarea[name="q"]');
                    // You can add more complex logic here if needed
                    return !!input;
                })()
                `);

            if (searchInputExists) {
                // Điền nội dung tìm kiếm và gửi
                await S.page.fill('input[name="q"], textarea[name="q"]', searchQuery);
                await S.page.press('input[name="q"], textarea[name="q"]', 'Enter');
            } else {
                // === PHƯƠNG PHÁP 2: THÔNG QUA JAVASCRIPT EVALUATE ===
                await S.page.evaluate(`
                        (function() {
                            // Tìm input tìm kiếm của Google
                            const input = document.querySelector('input[name="q"], textarea[name="q"]');
                            if (input) {
                            input.value = ${JSON.stringify(searchQuery)};
                            input.focus();

                            // Tạo sự kiện form submit
                            const form = input.closest('form');
                            if (form) {
                                form.submit();
                            } else {
                                // Nếu không tìm thấy form, tạo và kích hoạt sự kiện Enter
                                const event = new KeyboardEvent('keypress', {
                                key: 'Enter',
                                code: 'Enter',
                                keyCode: 13,
                                which: 13,
                                bubbles: true,
                                });
                                input.dispatchEvent(event);
                            }
                            }
                        })()
                    `);
            }

            // Đợi trang chuyển hướng với timeout lớn hơn
            await S.page.waitForLoadState('networkidle', { timeout: 30000 });

            // Thêm khoảng thời gian chờ ngắn để đảm bảo trang đã tải hoàn toàn
            await new Promise((resolve) => setTimeout(resolve, 1000));

            // Kiểm tra CAPTCHA
            const hasCaptcha = await S.page.evaluate(`
                !!document.querySelector('form#captcha-form, img[src*="captcha"], #recaptcha, .g-recaptcha')
                `);

            if (hasCaptcha) {
                F.l('CAPTCHA detected! Waiting for user to solve it...');

                // Thông báo cho người dùng
                await S.page.evaluate(`
                (function() {
                    if (!document.getElementById('captcha-notification')) {
                    const notification = document.createElement('div');
                    notification.id = 'captcha-notification';
                    notification.style.cssText = 
                        'position:fixed;top:0;left:0;right:0;background:red;color:white;padding:15px;text-align:center;z-index:9999;';
                    notification.innerText = 'CAPTCHA detected! Please solve it to continue.';
                    document.body.appendChild(notification);
                    }
                })()
                `);

                await S.page
                    .waitForFunction(
                        `!document.querySelector('form#captcha-form, img[src*="captcha"], #recaptcha, .g-recaptcha')`,
                        {
                            timeout: 120000,
                        },
                    )
                    .catch((e) => {
                        F.l('CAPTCHA waiting timeout:', e);
                    });

                // Xóa thông báo
                await S.page.evaluate(`
                    (function() {
                        const notification = document.getElementById('captcha-notification');
                        if (notification) notification.remove();
                    })()
                `);
            }

            // Tìm kiếm thành công, thoát khỏi vòng lặp
            F.l(`Search successful for: ${searchQuery}`);
            return;
        } catch (err) {
            F.l(`Attempt ${retries + 1} failed: ${err.message}`);
            retries++;

            // Xử lý lỗi Session closed hoặc Protocol error
            if (
                err.message.includes('Session closed') ||
                err.message.includes('Protocol error') ||
                err.message.includes('Target closed')
            ) {
                // Đóng và khởi tạo lại context
                if (S.context) {
                    try {
                        await S.context.close();
                    } catch (e) {
                        F.l('Error closing browser context:', e);
                    }
                    S.browser = null;
                    S.page = null;
                    S.context = null;
                }

                // Khởi tạo lại browser và page
                await initWebService(null, searchQuery);

                // Đợi một chút trước khi thử lại
                await new Promise((resolve) => setTimeout(resolve, 3000));
            } else {
                // Các lỗi khác
                await new Promise((resolve) => setTimeout(resolve, 2000));
            }
        }
    }

    // Nếu đã thử lại nhiều lần mà vẫn thất bại
    throw new Error(`Search failed after ${maxRetries} attempts for query: ${searchQuery}`);
};

// Hàm trích xuất kết quả tìm kiếm
const extractResults = async function (event) {
    try {
        // Kiểm tra page trước khi thực hiện
        if (!S.page || S.page.isClosed()) {
            throw new Error('Page is not available');
        }

        // Sử dụng evaluate để trích xuất kết quả
        const results = await S.page.evaluate(`
            (function() {
                const imageContainers = document.querySelectorAll('.eA0Zlc.WghbWd');
                const results = [];

                for (let i = 0; i < imageContainers.length; i++) {
                    const container = imageContainers[i];
                    const jsdata = container.getAttribute('jsdata');
                    if (!jsdata) continue;

                    const jsdataParts = jsdata.split(';');
                    const aj6Key = jsdataParts.length >= 3 ? jsdataParts[2] : null;
                    if (!aj6Key || !window.W_jd || !window.W_jd[aj6Key]) continue;

                    const imageData = window.W_jd[aj6Key];
                    
                    // Get the link, title, and source elements
                    const linkElement = container.querySelector('.EZAeBe');
                    const titleElement = container.querySelector('.toI8Rb');
                    const sourceElement = container.querySelector('.guK3rf');
                    
                    // Extract value from Symbol
                    const symbolKeys = Object.getOwnPropertySymbols(imageData);
                    let ksData = null;

                    // First pass: Look through symbol properties
                    for (let j = 0; j < symbolKeys.length; j++) {
                        const sym = symbolKeys[j];
                        const obj = imageData[sym];

                        if (obj && typeof obj === 'object') {
                            const objKeys = Object.keys(obj);
                            
                            // Look for ALL properties (not just "Ls")
                            for (let k = 0; k < objKeys.length; k++) {
                                const key = objKeys[k];
                                const potentialData = obj[key];

                                // Check if this property has the structure we expect
                                if (
                                    Array.isArray(potentialData) &&
                                    potentialData[1] &&
                                    Array.isArray(potentialData[1]) &&
                                    potentialData[1][3]
                                ) {
                                    ksData = potentialData;
                                    break;
                                }
                            }

                            if (ksData) break; // Exit the outer loop if we found what we need
                        }
                    }

                    // If we didn't find it in the first pass, try a deeper inspection
                    if (!ksData) {
                        for (let j = 0; j < symbolKeys.length; j++) {
                            const sym = symbolKeys[j];
                            const obj = imageData[sym];

                            if (obj && typeof obj === 'object') {
                                // Look for properties that are objects
                                const objKeys = Object.keys(obj);

                                for (let k = 0; k < objKeys.length; k++) {
                                    const key = objKeys[k];
                                    const nestedObj = obj[key];

                                    // Check second-level nested objects
                                    if (nestedObj && typeof nestedObj === 'object') {
                                        const nestedKeys = Object.keys(nestedObj);

                                        for (let l = 0; l < nestedKeys.length; l++) {
                                            const nestedKey = nestedKeys[l];
                                            const potentialData = nestedObj[nestedKey];

                                            if (
                                                Array.isArray(potentialData) &&
                                                potentialData[1] &&
                                                Array.isArray(potentialData[1]) &&
                                                potentialData[1][3]
                                            ) {
                                                ksData = potentialData;
                                                break;
                                            }
                                        }

                                        if (ksData) break;
                                    }
                                }

                                if (ksData) break;
                            }
                        }
                    }

                    if (ksData && Array.isArray(ksData)) {
                        try {
                            const thumbnailTmp = ksData[1][2] && ksData[1][2][0] ? ksData[1][2][0] : null;
                            const thumbnailUrl = ksData[1][3] && ksData[1][3][0] ? ksData[1][3][0] : null;
                            const imageHeight = ksData[1][3] && ksData[1][3][1] ? ksData[1][3][1] : null;
                            const imageWidth = ksData[1][3] && ksData[1][3][2] ? ksData[1][3][2] : null;

                            const pageUrl = linkElement && linkElement.href ? linkElement.href : null;
                            const title = titleElement && titleElement.textContent ? titleElement.textContent.trim() : null;
                            const source = sourceElement && sourceElement.textContent ? sourceElement.textContent.trim() : null;

                            results.push({
                                pageUrl: pageUrl,
                                title: title,
                                source: source,
                                thumbnail: thumbnailUrl,
                                url: thumbnailUrl,
                                thumbnailTmp: thumbnailTmp,
                                width: imageWidth,
                                height: imageHeight,
                                contextLink: pageUrl
                            });
                        } catch (e) {
                            // Error handling - can't use console.error in string evaluation
                        }
                    }
                }

                return results;
            })()
            `);

        return results;
    } catch (err) {
        F.l('Error in extractResults:', err);

        // Nếu lỗi là do session closed, thử khởi tạo lại
        if (
            err.message.includes('Session closed') ||
            err.message.includes('Protocol error') ||
            err.message.includes('Target closed')
        ) {
            // Khởi tạo lại browser và page
            await initWebService();

            // Thử lại việc trích xuất
            return await F.h_extractResults(event);
        }

        throw new Error('Search results extraction failed: ' + err.message);
    }
};

// Hàm kiểm tra và cài đặt browsers của Playwright
const installPlaywrightBrowsers = async () => {
    try {
        F.l('Checking Playwright browser installation...');

        // Chạy lệnh cài đặt browsers
        const { execSync } = require('child_process');
        execSync('npx playwright install chromium', { stdio: 'inherit' });

        F.l('Playwright browsers installed successfully');
        return true;
    } catch (error) {
        F.l('Failed to install Playwright browsers:', error);
        return false;
    }
};

// Đóng dịch vụ web Google
const closeBrowser = async (event) => {
    if (S.context) {
        try {
            await S.context.close();
        } catch (e) {
            F.l('Error closing browser context:', e);
        }
        S.browser = null;
        S.page = null;
        S.context = null;
    }
};

// Thêm hàm trợ giúp để kiểm tra trạng thái dịch vụ
const checkStatus = async () => {
    return {
        hasContext: !!S.context,
        hasPage: !!S.page && !S.page.isClosed(),
        status: S.context ? 'active' : 'inactive',
    };
};

// Hàm điều hướng đến trang mặc định
const navigateToPage = async (page, url = 'https://44xw.com/a/149/148289/') => {
    await page.goto(url, { waitUntil: 'networkidle' });
};

// Handle Cloudflare protection
const handleCloudflareProtection = async (page) => {
    try {
        // Wait a bit for the page to load
        await page.waitForTimeout(2000);

        // Check for Cloudflare challenge indicators
        const isCloudflare = await page.evaluate(() => {
            const indicators = [
                'Verifying you are human',
                'Checking your browser',
                'DDoS protection by Cloudflare',
                'Please wait while we check your browser',
                'This process is automatic',
                'cf-browser-verification',
                'cf-challenge-running',
                'cloudflare'
            ];

            const pageText = document.body.innerText.toLowerCase();
            const pageHTML = document.documentElement.innerHTML.toLowerCase();

            return indicators.some(indicator =>
                pageText.includes(indicator.toLowerCase()) ||
                pageHTML.includes(indicator.toLowerCase())
            );
        });

        if (isCloudflare) {
            F.l('🛡️ Cloudflare protection detected, waiting for bypass...');

            // Show notification to user
            await page.evaluate(() => {
                if (!document.getElementById('cloudflare-notification')) {
                    const notification = document.createElement('div');
                    notification.id = 'cloudflare-notification';
                    notification.style.cssText = `
                        position: fixed;
                        top: 20px;
                        left: 50%;
                        transform: translateX(-50%);
                        background: #ff9800;
                        color: white;
                        padding: 15px 25px;
                        border-radius: 8px;
                        z-index: 10000;
                        font-family: Arial, sans-serif;
                        font-size: 14px;
                        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                    `;
                    notification.innerHTML = `
                        🛡️ Cloudflare protection detected<br>
                        <small>Waiting for automatic bypass...</small>
                    `;
                    document.body.appendChild(notification);
                }
            });

            // Wait for Cloudflare to complete (up to 30 seconds)
            let attempts = 0;
            const maxAttempts = 30;

            while (attempts < maxAttempts) {
                await page.waitForTimeout(1000);
                attempts++;

                // Check if we're still on Cloudflare page
                const stillCloudflare = await page.evaluate(() => {
                    const indicators = [
                        'Verifying you are human',
                        'Checking your browser',
                        'cf-browser-verification',
                        'cf-challenge-running'
                    ];

                    const pageText = document.body.innerText.toLowerCase();
                    return indicators.some(indicator =>
                        pageText.includes(indicator.toLowerCase())
                    );
                });

                if (!stillCloudflare) {
                    F.l('✅ Cloudflare protection bypassed successfully');
                    break;
                }

                if (attempts % 5 === 0) {
                    F.l(`⏳ Still waiting for Cloudflare bypass... (${attempts}/${maxAttempts}s)`);
                }
            }

            // Remove notification
            await page.evaluate(() => {
                const notification = document.getElementById('cloudflare-notification');
                if (notification) notification.remove();
            });

            if (attempts >= maxAttempts) {
                F.l('⚠️ Cloudflare bypass timeout - continuing anyway');
            }

            // Wait a bit more for page to stabilize
            await page.waitForTimeout(2000);
        }

    } catch (error) {
        F.l('Error handling Cloudflare protection:', error.message);
        // Continue anyway
    }
};

const performExtraction = async function (event, url = null) {
    try {
        // Kiểm tra browser và page, khởi tạo lại nếu cần
        if (!S.context || !S.page || S.page.isClosed()) {
            F.l('Context or page not available, reinitializing...');
            await initWebService();
            await new Promise((resolve) => setTimeout(resolve, 2000));
        }

        // Kiểm tra trạng thái trang
        try {
            await S.page.evaluate(`document.title`);
        } catch (e) {
            F.l('Page is not responsive, reinitializing...', e);
            await initWebService();
            await new Promise((resolve) => setTimeout(resolve, 2000));
        }

        // Navigate to the target page
        if (url) {
            F.l(`Navigating to: ${url}`);
            try {
                await S.page.goto(url, { waitUntil: 'networkidle', timeout: 30000 });

                // Check for Cloudflare protection
                await handleCloudflareProtection(S.page);

            } catch (navError) {
                F.l('Navigation failed, trying with domcontentloaded:', navError.message);
                await S.page.goto(url, { waitUntil: 'domcontentloaded', timeout: 30000 });

                // Check for Cloudflare protection again
                await handleCloudflareProtection(S.page);
            }
        } else {
            // Use the existing navigateToPage function for default navigation
            await navigateToPage(S.page);
        }

        // Wait for page to load completely
        await S.page.waitForTimeout(2000);

        // Extract data from the page
        const extractedData = await S.page.evaluate(() => {
            const result = {
                chapters: [],
                pagination: {
                    currentPage: null,
                    nextPages: [],
                    hasNext: false
                },
                bookInfo: {
                    title: null,
                    author: null,
                    category: null,
                    status: null,
                    lastUpdate: null,
                    firstChapter: null,
                    latestChapter: null
                },
                currentUrl: window.location.href
            };

            // Extract book information from div.info
            const infoDiv = document.querySelector('div.info');
            if (infoDiv) {
                const infoPs = infoDiv.querySelectorAll('p');

                infoPs.forEach(p => {
                    const spanText = p.querySelector('span')?.textContent?.trim();
                    const strongElement = p.querySelector('strong');

                    if (spanText && strongElement) {
                        const strongText = strongElement.textContent?.trim();
                        const linkElement = strongElement.querySelector('a');

                        switch (spanText) {
                            case '分类：':
                                result.bookInfo.category = strongText;
                                break;
                            case '作者：':
                                result.bookInfo.author = strongText;
                                break;
                            case '状态：':
                                result.bookInfo.status = strongText;
                                break;
                            case '更新：':
                                result.bookInfo.lastUpdate = strongText;
                                break;
                            case '最初章：':
                                if (linkElement) {
                                    result.bookInfo.firstChapter = {
                                        title: linkElement.getAttribute('title') || strongText,
                                        href: linkElement.getAttribute('href'),
                                        fullUrl: new URL(linkElement.getAttribute('href'), window.location.href).href
                                    };
                                }
                                break;
                            case '最新章：':
                                if (linkElement) {
                                    result.bookInfo.latestChapter = {
                                        title: linkElement.getAttribute('title') || strongText,
                                        href: linkElement.getAttribute('href'),
                                        fullUrl: new URL(linkElement.getAttribute('href'), window.location.href).href
                                    };
                                }
                                break;
                        }
                    }
                });
            }

            // Extract book title from page title or h1/h2 elements if not found in info
            if (!result.bookInfo.title) {
                const titleElement = document.querySelector('h1, h2, .book-title, .title');
                if (titleElement) {
                    result.bookInfo.title = titleElement.textContent.trim();
                } else {
                    // Extract from page title, removing common suffixes
                    const pageTitle = document.title;
                    result.bookInfo.title = pageTitle.replace(/全文目录.*$/, '').replace(/第\d+页.*$/, '').trim();
                }
            }

            // Extract chapters using selector: dd > a[href]
            const chapterElements = document.querySelectorAll('dd > a[href]');
            chapterElements.forEach((element, index) => {
                const href = element.getAttribute('href');
                const title = element.getAttribute('title') || element.textContent.trim();
                const text = element.textContent.trim();

                if (href && title) {
                    result.chapters.push({
                        index: index + 1,
                        href: href,
                        title: title,
                        text: text,
                        fullUrl: new URL(href, window.location.href).href
                    });
                }
            });

            // Extract pagination using selector: .page_link a[href*="p"]
            const paginationElements = document.querySelectorAll('.page_link a[href*="p"]');
            paginationElements.forEach((element) => {
                const href = element.getAttribute('href');
                const text = element.textContent.trim();

                if (href && text) {
                    const fullUrl = new URL(href, window.location.href).href;
                    result.pagination.nextPages.push({
                        href: href,
                        text: text,
                        fullUrl: fullUrl,
                        pageNumber: text.match(/\d+/) ? parseInt(text.match(/\d+/)[0]) : null
                    });
                }
            });

            // Determine current page from URL or page content
            const urlMatch = window.location.href.match(/p(\d+)/);
            if (urlMatch) {
                result.pagination.currentPage = parseInt(urlMatch[1]);
            } else {
                // Try to find current page from pagination elements
                const currentPageElement = document.querySelector('.page_link span:not([class])');
                if (currentPageElement) {
                    const pageText = currentPageElement.textContent.trim();
                    const pageNum = parseInt(pageText);
                    if (!isNaN(pageNum)) {
                        result.pagination.currentPage = pageNum;
                    }
                }
            }

            // Check if there are next pages
            result.pagination.hasNext = result.pagination.nextPages.length > 0;

            // Get page title for context
            result.pageTitle = document.title;

            return result;
        });

        F.l(`Extraction completed. Found ${extractedData.chapters.length} chapters and ${extractedData.pagination.nextPages.length} pagination links`);

        return extractedData;

    } catch (err) {
        F.l('Error in performExtraction:', err);

        // Nếu lỗi là do session closed, thử khởi tạo lại
        if (
            err.message.includes('Session closed') ||
            err.message.includes('Protocol error') ||
            err.message.includes('Target closed')
        ) {
            // Khởi tạo lại browser và page
            await initWebService();

            // Thử lại việc trích xuất
            return await performExtraction(event, url);
        }

        throw new Error('Data extraction failed: ' + err.message);
    }
};

// Hàm trích xuất dữ liệu từ nhiều trang với referer support
const performExtractionMultiplePages = async function (event, startUrl = null, maxPages = 10) {
    try {
        let allChapters = [];
        let currentUrl = startUrl;
        let previousUrl = null; // Track previous URL for referer
        let pageCount = 0;

        F.l(`Starting multi-page extraction with referer support. Max pages: ${maxPages}`);

        while (currentUrl && pageCount < maxPages) {
            pageCount++;
            F.l(`Extracting page ${pageCount}: ${currentUrl}`);

            if (previousUrl) {
                F.l(`🔙 Using referer: ${previousUrl}`);
            }

            try {
                // Set referer headers for anti-Cloudflare
                const headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                    'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'DNT': '1',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1'
                };

                // Add referer if we have previous URL
                if (previousUrl) {
                    headers['Referer'] = previousUrl;
                }

                await S.page.setExtraHTTPHeaders(headers);

                // Navigate with referer
                await S.page.goto(currentUrl, {
                    waitUntil: 'networkidle',
                    timeout: 30000,
                    referer: previousUrl || undefined
                });

                // Check for Cloudflare protection
                await handleCloudflareProtection(S.page);

                // Wait for page to load
                await S.page.waitForTimeout(2000);

                // Extract data from current page
                const pageData = await extractPageData(S.page, currentUrl);

                if (pageData.chapters && pageData.chapters.length > 0) {
                    // Add page number to each chapter for reference
                    const chaptersWithPageInfo = pageData.chapters.map(chapter => ({
                        ...chapter,
                        pageNumber: pageCount,
                        sourceUrl: currentUrl
                    }));

                    allChapters = allChapters.concat(chaptersWithPageInfo);
                    F.l(`Found ${pageData.chapters.length} chapters on page ${pageCount}`);
                }

                // Update previousUrl for next iteration
                previousUrl = currentUrl;

                // Find next page URL
                currentUrl = null;
                if (pageData.pagination && pageData.pagination.nextPages.length > 0) {
                    // Look for the next sequential page
                    const currentPageNum = pageData.pagination.currentPage || pageCount;
                    const nextPageNum = currentPageNum + 1;

                    // Find the next page link
                    const nextPageLink = pageData.pagination.nextPages.find(page =>
                        page.pageNumber === nextPageNum ||
                        page.text.includes(nextPageNum.toString())
                    );

                    if (nextPageLink) {
                        currentUrl = nextPageLink.fullUrl;
                        F.l(`Next page found: ${currentUrl}`);
                    } else {
                        F.l('No more pages found');
                        break;
                    }
                } else {
                    F.l('No pagination found, stopping extraction');
                    break;
                }

                // Add delay between page requests to be respectful
                await new Promise(resolve => setTimeout(resolve, 1000));

            } catch (pageError) {
                F.l(`❌ Error extracting page ${pageCount}: ${pageError.message}`);

                // If it's a navigation error, try to continue with next page
                if (pageError.message.includes('Navigation') || pageError.message.includes('timeout')) {
                    F.l('⚠️ Navigation error, attempting to continue...');
                    // Still update previousUrl to maintain referer chain
                    previousUrl = currentUrl;
                    continue;
                } else {
                    // For other errors, break the loop
                    break;
                }
            }
        }

        F.l(`Multi-page extraction completed. Total chapters: ${allChapters.length} from ${pageCount} pages`);

        // Get book info from the first page extraction
        let bookInfo = null;
        if (pageCount > 0) {
            const firstPageData = await performExtraction(event, startUrl);
            bookInfo = firstPageData.bookInfo;
        }

        return {
            chapters: allChapters,
            totalPages: pageCount,
            totalChapters: allChapters.length,
            bookInfo: bookInfo
        };

    } catch (err) {
        F.l('Error in performExtractionMultiplePages:', err);
        throw new Error('Multi-page extraction failed: ' + err.message);
    }
};

module.exports = {
    initializeBrowser,
    installPlaywrightBrowsers,
    checkStatus,
    closeBrowser,
    performSearchLikeHuman,
    extractResults,
    initWebService,
    getRandomImageKeyword,
    performSearch,
    findChromePath,
    performExtraction,
    performExtractionMultiplePages,
    navigateToPage
};
