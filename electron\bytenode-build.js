const bytenode = require('bytenode');
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

async function compileFile(inputFile, outputFile, maxRetries = 3) {
  let retries = 0;

  while (retries < maxRetries) {
    try {
      // Verify input file exists
      if (!fs.existsSync(inputFile)) {
        throw new Error(`Input file not found: ${inputFile}`);
      }

      console.log(`Compiling ${path.basename(inputFile)} to bytecode...`);

      await Promise.race([
        new Promise((resolve, reject) => {
          try {
            bytenode.compileFile({
              filename: inputFile,
              electron: true,
              output: outputFile,
            });

            // Verify output file was created
            if (fs.existsSync(outputFile)) {
              console.log(`Bytenode compilation of ${path.basename(inputFile)} completed successfully.`);
              resolve();
            } else {
              reject(new Error(`Bytenode compilation failed: output file not created for ${path.basename(inputFile)}`));
            }
          } catch (error) {
            reject(error);
          }
        }),
        // Add 30 second timeout
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error(`Bytenode compilation timed out for ${path.basename(inputFile)}`)), 30000),
        ),
      ]);

      return true; // Success
    } catch (error) {
      retries++;
      console.error(`Attempt ${retries}/${maxRetries} failed for ${path.basename(inputFile)}:`, error);

      if (retries >= maxRetries) {
        throw error; // Re-throw if max retries reached
      }

      // Wait before retry
      await new Promise((resolve) => setTimeout(resolve, 2000));
    }
  }
}

async function createLoader(filePath, jscPath) {
  const fileName = path.basename(filePath, '.js');
  const loaderPath = path.join(path.dirname(jscPath), `${fileName}.js`);

  console.log(`Creating loader for ${fileName}...`);
  const loaderContent = `${fileName == 'preload' ? '' : "require('bytenode');"}
const path = require('path')
require(path.join(__dirname , '${fileName}.jsc'))
`;

  fs.writeFileSync(loaderPath, loaderContent);
  console.log(`Loader created successfully for ${fileName}.`);
}

async function build() {
  try {
    // Ensure jsc directory exists
    const jscDir = path.join(__dirname, '../jsc');
    const electronBuildDir = path.join(__dirname, '../electron-build');
    if (!fs.existsSync(jscDir)) {
      fs.mkdirSync(jscDir, { recursive: true });
    }

    // Step 1: Build using webpack
    console.log('Building with webpack...');
    // execSync('webpack --config webpack.electron.config.js', { stdio: 'inherit' });
    // execSync(`node obfuscate.js ${electronBuildDir}`, { stdio: 'inherit' });
    console.log('Webpack build completed successfully.');

    // Step 2: Compile files to bytecode
    const filesToCompile = [
      {
        input: path.join(__dirname, '../electron-build/main.js'),
        output: path.join(__dirname, '../jsc/main.jsc'),
      },
      // {
      //   input: path.join(__dirname, '../electron-build/preload.js'),
      //   output: path.join(__dirname, '../jsc/preload.jsc'),
      // },
      // Add more files here as needed
    ];

    // Compile all files
    for (const file of filesToCompile) {
      await compileFile(file.input, file.output);
      await createLoader(file.input, file.output);
    }

    console.log('Build completed successfully!');
  } catch (error) {
    console.error('Build failed:', error);

    // Check if JSC files exist despite error
    let allFilesExist = true;
    const filesToCheck = [
      path.join(__dirname, '../jsc/main.jsc'),
      // path.join(__dirname, '../jsc/preload.jsc')
    ];

    for (const file of filesToCheck) {
      if (!fs.existsSync(file)) {
        allFilesExist = false;
        console.error(`File not found: ${file}`);
      }
    }

    if (allFilesExist) {
      console.log('JSC files exist despite error, considering build successful');
      return;
    }

    process.exit(1);
  }
}

build().then(() => {
  console.log('Build completed successfully!');
  // copy files from
  const input = path.join(__dirname, '../electron-build/preload.js');
  const output = path.join(__dirname, '../jsc/preload.js');

  fs.copyFileSync(input, output);
  console.log('Preload.js copied successfully!');
});
