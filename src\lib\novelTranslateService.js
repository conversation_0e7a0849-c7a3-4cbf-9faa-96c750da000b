import { useTTSStore } from '@/stores/ttsStore';
import { message } from 'ant-design-vue';
import { NovelTranslator } from './NovelTranslator';

/**
 * Novel Translation Service
 * Uses NovelTranslator for novel-style translation with memory context
 */
export class NovelTranslateService {
  constructor() {
    this.isTranslating = false;
    this.currentBatch = 0;
    this.totalBatches = 0;
    this.shouldStop = false;
    this.currentTranslator = null;
    this.abortController = null;
  }

  /**
   * Translate SRT items using Novel translation approach
   * @param {Object} options - Translation options
   * @param {Array} options.subs - Array of subtitle objects to translate
   * @param {number} options.batchSize - Size of each translation batch (default: 20)
   * @param {string} options.targetLanguage - Target language (default: 'Vietnamese')
   * @param {Function} options.callback - Callback function for batch updates
   * @param {Function} options.onProgress - Progress callback function
   * @param {boolean} options.continueMode - Whether to continue from previous translation
   * @param {string} options.initialMemory - Initial memory to use (optional)
   * @returns {Promise<Array>} - Array of translated texts
   */
  async translateSrtService({
    subs,
    batchSize = 20,
    targetLanguage = 'Vietnamese',
    callback = null,
    onProgress = null,
    continueMode = false,
    initialMemory = null
  }) {
    const ttsStore = useTTSStore();
    
    if (!subs || subs.length === 0) {
      throw new Error('No subtitles provided for translation');
    }

    // Validate AI service configuration
    const activeService = ttsStore.getActiveAiService();
    if (!activeService || !activeService.enabled || !activeService.apiKey) {
      throw new Error('AI service not configured properly');
    }

    this.isTranslating = true;
    this.shouldStop = false;
    this.abortController = new AbortController();
    ttsStore.setNovelTranslating(true);

    try {
      // Initialize memory for current SRT list
      ttsStore.initializeNovelMemoryForCurrentSrt();

      // Get existing memory from current SRT list or use provided initial memory
      let memory = initialMemory || ttsStore.currentSrtList?.memory || '';

      if (initialMemory && initialMemory !== ttsStore.currentSrtList?.memory) {
        console.log('Using provided initial memory instead of current SRT memory');
        // Update current SRT memory with initial memory
        ttsStore.updateCurrentSrtMemory(initialMemory);
        memory = initialMemory;
      }
      
      // Prepare chunks for translation
      const chunks = subs.map(sub => sub.text);

      // Calculate batches
      this.totalBatches = Math.ceil(chunks.length / batchSize);
      ttsStore.setNovelBatchProgress(0, this.totalBatches);

      console.log(`🔍 Novel translation debug:`);
      console.log(`- continueMode: ${continueMode}`);
      console.log(`- subs.length: ${subs.length}`);
      console.log(`- chunks.length: ${chunks.length}`);
      console.log(`- totalBatches: ${this.totalBatches}`);
      if (subs.length > 0) {
        console.log(`- First sub ID: ${subs[0].index}`);
        console.log(`- Last sub ID: ${subs[subs.length - 1].index}`);
      }

      console.log(`Starting Novel translation: ${chunks.length} items in ${this.totalBatches} batches`);

      // Create NovelTranslator instance
      const providerName = this.getProviderName(activeService);
      const modelName = this.getModelForService(activeService);

      console.log(`Creating NovelTranslator with:`, {
        provider: providerName,
        model: modelName,
        serviceName: activeService.name,
        hasApiKey: !!activeService.apiKey,
        baseURL: activeService.baseURL
      });

      const translator = new NovelTranslator({
        provider: providerName,
        model: modelName,
        apiKey: activeService.apiKey,
        baseURL: activeService.baseURL,
        temperature: 0.1,
        windowSize: Math.min(batchSize, 30),
        overlap: Math.min(Math.floor(batchSize / 3), 10),
        abortSignal: this.abortController?.signal
      });

      // Store reference to current translator for stopping
      this.currentTranslator = translator;

      // Set initial memory
      if (memory) {
        translator.memory = memory;
      }

      console.log(`Starting Novel translation with ${chunks.length} chunks using ${activeService.name}`);

      // Get current SRT name for progress tracking
      const currentSrtName = ttsStore.currentSrtList?.name || 'unknown';

      // Initialize progress for this SRT if not exists
      if (!ttsStore.novelTranslation.progress[currentSrtName]) {
        ttsStore.updateNovelProgress(currentSrtName, {
          translatedCount: 0,
          totalCount: chunks.length,
          lastTranslatedIndex: -1,
          memory: memory || '',
          translatedTexts: []
        });
      }

      // Handle continue mode - get existing progress
      let existingProgress = null;
      if (continueMode) {
        existingProgress = ttsStore.getNovelProgress(currentSrtName);
        console.log(`Continue mode: Found existing progress with ${existingProgress.translatedCount} translated items`);
      }

      const translatedTexts = [];
      let processedItems = 0;

      // Translate chunks with progress callback that also handles batch updates
      console.log(`🚀 Starting translateChunks with ${chunks.length} chunks`);

      const windowTranslatedTexts = await translator.translateChunks(chunks, (current, total, message, batchMemory, windowResults) => {
        console.log(`📊 Progress callback called: ${current}/${total} - ${message}`);
        console.log(`📊 windowResults: ${windowResults ? windowResults.length : 'null'} items`);

        // Check if we should stop or if aborted
        if (this.shouldStop || this.abortController?.signal.aborted) {
          console.log('Translation stop requested, aborting...');
          throw new Error('Translation stopped by user');
        }

        console.log(`Translation progress: ${current}/${total} - ${message}`);

        // Update memory from translator
        if (batchMemory) {
          memory = batchMemory;
          ttsStore.updateCurrentSrtMemory(memory);

          // Update progress memory for this SRT
          const currentProgress = ttsStore.getNovelProgress(currentSrtName);
          currentProgress.memory = memory;
          ttsStore.updateNovelProgress(currentSrtName, currentProgress);
          console.log(`📝 Memory updated: ${batchMemory.length} characters`);
        }

        // Update progress
        this.currentBatch = current;
        ttsStore.setNovelBatchProgress(this.currentBatch, this.totalBatches);

        if (onProgress) {
          // Convert window progress to item progress
          const itemsPerWindow = Math.ceil(chunks.length / total);
          const currentItems = Math.min((current - 1) * itemsPerWindow + itemsPerWindow, chunks.length);
          console.log(`📈 Calling onProgress: ${currentItems}/${chunks.length}`);
          onProgress(currentItems, chunks.length);
        }

        // If we have window results, call callback immediately for real-time update
        if (windowResults && callback) {
          console.log(`🔄 Processing window results: ${windowResults.length} items`);

          let startIndex;

          if (continueMode && existingProgress) {
            // In continue mode, startIndex should be the index within subsForTranslation array
            // NOT the total progress count (which would be out of bounds)
            startIndex = processedItems;
            console.log(`🔍 Continue mode callback:`);
            console.log(`- window: ${current}`);
            console.log(`- existingProgress.translatedCount: ${existingProgress.translatedCount}`);
            console.log(`- processedItems: ${processedItems}`);
            console.log(`- startIndex (within subsForTranslation): ${startIndex}`);
            console.log(`- windowResults.length: ${windowResults.length}`);
          } else {
            // Normal mode: use processed items as start index
            startIndex = processedItems;
            console.log(`🔍 Normal mode callback: startIndex=${startIndex}, items=${windowResults.length}`);
          }

          try {
            console.log(`📞 Calling callback with ${windowResults.length} items at index ${startIndex}`);
            callback(windowResults, startIndex);
            console.log(`✅ Callback completed successfully`);
          } catch (callbackError) {
            console.error(`❌ Error in callback:`, callbackError);
            throw callbackError; // Re-throw to stop translation
          }

          // Update processed items counter AFTER callback
          processedItems += windowResults.length;
          console.log(`📊 Updated processedItems: ${processedItems}`);

          // Update progress for this SRT
          const currentProgress = ttsStore.getNovelProgress(currentSrtName);
          if (continueMode && existingProgress) {
            // In continue mode, add to existing count
            currentProgress.translatedCount = existingProgress.translatedCount + processedItems;
            currentProgress.lastTranslatedIndex = existingProgress.translatedCount + processedItems - 1;
          } else {
            // Normal mode: use processed items directly
            currentProgress.translatedCount = processedItems;
            currentProgress.lastTranslatedIndex = processedItems - 1;
          }
          ttsStore.updateNovelProgress(currentSrtName, currentProgress);
          console.log(`💾 Progress updated: ${currentProgress.translatedCount} items`);
        } else {
          console.log(`⚠️ No window results or callback - windowResults: ${!!windowResults}, callback: ${!!callback}`);
        }
      });

      // Ensure all results are captured in final array
      translatedTexts.push(...windowTranslatedTexts);

      console.log(`Total processed via callbacks: ${processedItems}, Total translated: ${windowTranslatedTexts.length}`);

      // Final memory update
      if (translator.memory) {
        ttsStore.updateCurrentSrtMemory(translator.memory);

        // Update final progress for this SRT
        const finalProgress = ttsStore.getNovelProgress(currentSrtName);
        finalProgress.memory = translator.memory;

        if (continueMode && existingProgress) {
          // In continue mode, add to existing count
          finalProgress.translatedCount = existingProgress.translatedCount + translatedTexts.length;
          finalProgress.lastTranslatedIndex = existingProgress.translatedCount + translatedTexts.length - 1;
          // Don't overwrite existing translatedTexts in continue mode
          console.log(`Continue mode: Final count ${finalProgress.translatedCount} (existing: ${existingProgress.translatedCount} + new: ${translatedTexts.length})`);
        } else {
          // Normal mode: use translated texts directly
          finalProgress.translatedCount = translatedTexts.length;
          finalProgress.lastTranslatedIndex = translatedTexts.length - 1;
          finalProgress.translatedTexts = translatedTexts;
          console.log(`Normal mode: Final count ${finalProgress.translatedCount}`);
        }

        ttsStore.updateNovelProgress(currentSrtName, finalProgress);
      }

      console.log(`Novel translation completed successfully for ${currentSrtName}`);
      console.log(`Total translated: ${translatedTexts.length} items`);
      console.log(`Final memory length: ${translator.memory?.length || 0} characters`);

      return translatedTexts;

    } catch (error) {
      if (this.shouldStop) {
        console.log('Novel translation stopped by user');
        message.info('Translation đã được dừng');
      } else {
        console.error('Novel translation failed:', error);
        throw error;
      }
    } finally {
      this.isTranslating = false;
      this.shouldStop = false;
      this.currentTranslator = null;
      this.abortController = null;
      ttsStore.setNovelTranslating(false);
      ttsStore.setNovelBatchProgress(0, 0);
    }
  }



  /**
   * Get provider name for NovelTranslator
   * @param {Object} activeService - Active AI service configuration
   * @returns {string} - Provider name
   */
  getProviderName(activeService) {
    const serviceName = activeService.name.toLowerCase();

    console.log(`Getting provider name for service: "${serviceName}"`);

    switch (serviceName) {
      case 'openai':
        return 'openai';
      case 'deepseek':
        return 'deepseek';
      case 'anthropic claude':
      case 'claude':
        return 'openrouter'; // Claude via OpenRouter
      case 'google gemini':
      case 'gemini':
        console.log('Matched Gemini service');
        return 'gemini';
      case 'openrouter':
        return 'openrouter';
      default:
        console.warn(`Unknown service name: "${serviceName}", using openai fallback`);
        return 'openai'; // Default fallback
    }
  }

  /**
   * Get appropriate model for the AI service
   * @param {Object} activeService - Active AI service configuration
   * @returns {string} - Model name
   */
  getModelForService(activeService) {
    const serviceName = activeService.name.toLowerCase();

    console.log(`Getting model for service: "${serviceName}"`);

    switch (serviceName) {
      case 'openai':
        return 'gpt-4o-mini';
      case 'deepseek':
        return 'deepseek-chat';
      case 'anthropic claude':
      case 'claude':
        return 'claude-3-haiku-20240307';
      case 'google gemini':
      case 'gemini':
        // Try different Gemini models based on availability
        const geminiModels = [
          'gemini-1.5-flash',
          'gemini-1.5-pro',
          'gemini-2.0-flash-exp',
          'gemini-pro'
        ];
        const selectedModel = geminiModels[0]; // Use first available
        console.log(`Using Gemini model: ${selectedModel}`);
        return selectedModel;
      case 'openrouter':
        return 'deepseek/deepseek-r1:free'; // Default OpenRouter model
      default:
        console.warn(`Unknown service name: "${serviceName}", using gpt-4o-mini fallback`);
        return 'gpt-4o-mini'; // Default fallback
    }
  }

  /**
   * Check if novel translation is currently in progress
   * @returns {boolean}
   */
  isNovelTranslating() {
    return this.isTranslating;
  }

  /**
   * Get current translation progress
   * @returns {Object} - Progress information
   */
  getTranslationProgress() {
    return {
      currentBatch: this.currentBatch,
      totalBatches: this.totalBatches,
      isTranslating: this.isTranslating
    };
  }

  /**
   * Stop the current translation process
   */
  stopTranslation() {
    if (this.isTranslating) {
      console.log('Stopping novel translation...');
      this.shouldStop = true;

      // Abort any ongoing API calls
      if (this.abortController) {
        console.log('Aborting API calls...');
        this.abortController.abort('Translation stopped by user');
      }

      // If we have a current translator, we could potentially add stop logic there too
      if (this.currentTranslator) {
        console.log('Translation stop signal sent');
      }

      return true;
    }
    return false;
  }
}

// Create singleton instance
export const novelTranslateService = new NovelTranslateService();

// Export default
export default novelTranslateService;
