<template>
  <div class="video-preview-editor h-full w-full flex flex-col bg-gray-900">
    <!-- Header Controls -->
    <div class="video-header flex items-center justify-between p-2 bg-gray-800 border-b border-gray-600">
      <div class="flex items-center gap-2">
        <h3 class="text-xs font-medium text-white">Preview</h3>
        <span v-if="videoInfo.duration" class="text-xs text-gray-400">
          {{ formatTime(videoInfo.duration) }}
        </span>
      </div>

      <!-- View Controls -->
      <div class="flex items-center gap-2">
        <a-button-group size="small">
          <a-button :type="viewMode === 'preview' ? 'primary' : 'default'" @click="viewMode = 'preview'">
            Preview
          </a-button>
          <a-button :type="viewMode === 'layers' ? 'primary' : 'default'" @click="viewMode = 'layers'">
            Layers
          </a-button>
        </a-button-group>

        <a-divider type="vertical" />

        <!-- Quality -->
        <!-- <a-select v-model:value="previewQuality" size="small" style="width: 80px">
          <a-select-option value="low">Low</a-select-option>
          <a-select-option value="medium">Med</a-select-option>
          <a-select-option value="high">High</a-select-option>
        </a-select> -->

        <!-- Zoom -->
        <a-select v-model:value="previewZoom" size="small" style="width: 70px">
          <a-select-option :value="50">50%</a-select-option>
          <a-select-option :value="75">75%</a-select-option>
          <a-select-option :value="100">100%</a-select-option>
          <a-select-option :value="125">125%</a-select-option>
          <a-select-option :value="150">150%</a-select-option>
        </a-select>
      </div>
    </div>

    <!-- Main Content -->
    <div class="flex-1 flex min-h-0">
      <!-- Video Preview Area -->
      <div class="flex-1 flex w-full h-full overflow-auto flex-col bg-gray-900">
        <!-- Video Container -->
        <div class="flex-1 flex items-center justify-center p-4 relative">
          <div class="video-viewport relative bg-black overflow-hidden" :style="viewportStyle"
            @click="handleVideoClick">
            <!-- Video Element -->
            <CropSelector ref="cropSelector" :active="cropMode" :content-width="videoDimensions.width"
              :content-height="videoDimensions.height" :show-coordinates="true" @crop-selected="onCropSelected"
              @crop-change="onCropChange" @crop-cleared="onCropCleared">
              <video ref="videoElement" class="w-full h-full" :src="getVideoSrc(videoSrc)"
                @loadedmetadata="onVideoLoaded" @timeupdate="onTimeUpdate" @play="onPlay" @pause="onPause" />
            </CropSelector>
            <!-- Overlay Layers -->
            <div class="absolute inset-0 pointer-events-none">
              <!-- Subtitle Layer -->
              <SubtitleOverlay v-if="subtitleLayer?.enabled" :layer="subtitleLayer" :current-time="currentTime"
                :video-dimensions="videoDimensions" />

              <!-- Text Overlays -->
              <TextOverlay v-for="textLayer in enabledTextLayers" :key="textLayer.id" :layer="textLayer"
                :current-time="currentTime" :video-dimensions="videoDimensions" />

              <!-- Image Overlays -->
              <ImageOverlay v-for="imageLayer in enabledImageLayers" :key="imageLayer.id" :layer="imageLayer"
                :current-time="currentTime" :video-dimensions="videoDimensions" />

              <!-- Effect Overlays -->
              <EffectOverlay v-for="effect in activeEffects" :key="effect.id" :effect="effect"
                :current-time="currentTime" :video-dimensions="videoDimensions" />
            </div>

            <!-- Grid Overlay -->
            <div v-if="showGrid" class="absolute inset-0 pointer-events-none">
              <GridOverlay :video-dimensions="videoDimensions" />
            </div>

            <!-- Safe Area Overlay -->
            <div v-if="showSafeArea" class="absolute inset-0 pointer-events-none">
              <SafeAreaOverlay :video-dimensions="videoDimensions" />
            </div>
          </div>
        </div>

      </div>


      <!-- Layers Panel (when in layers mode) -->
      <div v-if="viewMode === 'layers'" class="w-80 border-l border-gray-600">
        <VideoLayersPanel />
      </div>
    </div>

    <!-- Effects Toolbar -->
    <div class="effects-toolbar p-2 bg-gray-800 border-t border-gray-600">
      <!-- Video Controls -->
      <div class="video-controls p-2 bg-gray-800">
        <!-- extractedText -->
         <div class="flex items-center gap-2" v-if="extractedText">
          <div @click="copyText">{{ extractedText }}</div>
          <!-- insert -->
          <a-button size="small" @click="insertText"><PlusCircleIcon size="14" /></a-button>
        </div>
        <div class="flex items-center gap-4">
          <!-- Play/Pause -->
          <a-button
            @click="togglePlayPause"
            size="small"
            :type="state.isGlobalPlayback ? 'primary' : 'default'"
            :title="getPlayButtonTitle()"
          >
            <template #icon>
              <svg v-if="!isPlaying" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                stroke-width="2">
                <polygon points="5,3 19,12 5,21" />
              </svg>
              <svg v-else width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                stroke-width="2">
                <rect x="6" y="4" width="4" height="16" />
                <rect x="14" y="4" width="4" height="16" />
              </svg>
            </template>
            {{ state.isGlobalPlayback ? 'Sync' : '' }}
          </a-button>

          <!-- Time Display -->
          <span class="text-xs text-gray-400 font-mono">
            {{ formatTime(currentTime) }} / {{ formatTime(videoInfo.duration) }}
          </span>

          <!-- Progress Bar -->
          <div class="flex-1">
            <a-slider :value="currentTime" :max="videoInfo.duration" :step="0.1" @change="seekTo"
              :tip-formatter="formatTime" />
          </div>

          <!-- Volume -->
          <div class="flex items-center gap-2">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
              class="text-gray-400">
              <polygon points="11 5,6 9,2 9,2 15,6 15,11 19,11 5" />
              <path d="M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07" />
            </svg>
            <a-slider v-model:value="volume" :max="100" style="width: 60px" size="small" @change="updateVolume" />
          </div>

          <!-- Audio Status Indicator -->
          <div class="flex items-center gap-1 text-xs">
            <span :class="state.audioEnabled ? 'text-green-400' : 'text-gray-400'">
              {{ state.audioEnabled ? '🔊 Audio Ready' : '🔇 Audio Loading...' }}
            </span>
            <span class="text-gray-500">({{ globalAudioManager.audioElements.size }})</span>
          </div>


        </div>
      </div>
      <div class="flex items-center p-2 gap-2 flex-wrap">
        <!-- Layer Toggles -->
        <a-button-group size="small">
          <a-button :type="subtitleLayer?.enabled ? 'primary' : 'default'" @click="toggleLayer('subtitles')">
            <template #icon>
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
              </svg>
            </template>
            Subtitle
          </a-button>

          <a-button :type="showGrid ? 'primary' : 'default'" @click="showGrid = !showGrid">
            <template #icon>
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2" />
                <line x1="9" y1="9" x2="9" y2="15" />
                <line x1="15" y1="9" x2="15" y2="15" />
                <line x1="9" y1="9" x2="15" y2="9" />
                <line x1="9" y1="15" x2="15" y2="15" />
              </svg>
            </template>
            Grid
          </a-button>

          <a-button :type="showSafeArea ? 'primary' : 'default'" @click="showSafeArea = !showSafeArea">
            <template #icon>
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path
                  d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3" />
              </svg>
            </template>
            Safe
          </a-button>
        </a-button-group>

        <a-divider type="vertical" />

        <!-- Quick Effects -->
        <!-- <a-button size="small" @click="addTextOverlay">
          <template #icon>
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <polyline points="4,7 4,4 20,4 20,7" />
              <line x1="9" y1="20" x2="15" y2="20" />
              <line x1="12" y1="4" x2="12" y2="20" />
            </svg>
          </template>
          Add Text
        </a-button>

        <a-button size="small" @click="addImageOverlay">
          <template #icon>
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2" />
              <circle cx="8.5" cy="8.5" r="1.5" />
              <polyline points="21,15 16,10 5,21" />
            </svg>
          </template>
          Add Image
        </a-button> -->

        <!-- <a-button size="small" @click="addBlurEffect">
          <template #icon>
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="3" />
              <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1" />
            </svg>
          </template>
          Blur
        </a-button> -->

        <!-- <a-divider type="vertical" /> -->

        <!-- Speed Balancing Controls -->
        <a-button-group size="small">
          <a-button
            @click="toggleSpeedBalancing"
            :type="state.speedBalancingEnabled ? 'primary' : 'default'"
            title="Toggle speed balancing for video-audio sync"
          >
            <template #icon>
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polygon points="13,2 3,14 12,14 11,22 21,10 12,10" />
              </svg>
            </template>
            Speed Balance
          </a-button>

          <a-button
            v-if="state.speedBalancingEnabled"
            @click="showSpeedSettings = !showSpeedSettings"
            :type="showSpeedSettings ? 'primary' : 'default'"
            title="Speed balancing settings"
          >
            <template #icon>
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="3" />
                <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1" />
              </svg>
            </template>
          </a-button>
        </a-button-group>

        <a-divider type="vertical" />

        <!-- Crop Controls -->
        <div class="gap-1">
          <button @click="toggleCropMode" class="px-2 py-1 rounded text-xs"
            :class="cropMode ? 'bg-blue-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'"
            title="Toggle crop selection mode">
            🎯
          </button>

          <button v-if="currentCrop && (currentCrop.width > 0 || currentCrop.height > 0)" @click="extractText"
            class="px-2 py-1 bg-green-600 hover:bg-green-500 text-white rounded text-xs"
            title="Extract text from selected area">
            OCR
          </button>

          <button v-if="currentCrop && (currentCrop.width > 0 || currentCrop.height > 0)" @click="clearCrop"
            class="px-1 py-1 bg-red-600 hover:bg-red-500 text-white rounded text-xs" title="Clear selection">
            ✕
          </button>
        </div>
      </div>

      <!-- Speed Balancing Settings Panel -->
      <div v-if="showSpeedSettings && state.speedBalancingEnabled" class="bg-gray-800 border-t border-gray-700 p-3">
        <div class="flex items-center gap-4 text-sm">
          <div class="flex items-center gap-2">
            <label class="text-gray-300">Join Time:</label>
            <a-input-number
              v-model:value="joinTimeInput"
              @change="updateJoinTime"
              :min="0"
              :max="2"
              :step="0.1"
              :precision="1"
              size="small"
              style="width: 80px"
            />
            <span class="text-gray-400">seconds</span>
          </div>

          <div class="flex items-center gap-2">
            <label class="text-gray-300">Default Speed:</label>
            <a-input-number
              v-model:value="defaultVideoSpeed"
              @change="updateDefaultSpeed"
              :min="0.5"
              :max="2"
              :step="0.1"
              :precision="1"
              size="small"
              style="width: 80px"
            />
            <span class="text-gray-400">x</span>
          </div>

          <a-divider type="vertical" />

          <!-- Status Indicators -->
          <div class="flex items-center gap-3 text-xs">
            <span :class="state.webAudioSupported ? 'text-green-400' : 'text-red-400'">
              {{ state.webAudioSupported ? '🔊 Web Audio: OK' : '🔇 Web Audio: Not Supported' }}
            </span>

            <span v-if="audioCombineListLength > 0" class="text-blue-400">
              📊 Segments: {{ audioCombineListLength }}
            </span>

            <span v-if="state.isGlobalPlayback" class="text-purple-400">
              🎵 Sync Active
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { PlusCircleIcon } from 'lucide-vue-next'
import { useVideoLayersStore } from '@/stores/video-layers-store'
import { useTimelineStore } from '@/stores/timeline-store'
import { useTTSStore } from '@/stores/ttsStore'
import { state } from '@/lib/state'
import { getVideoSrc, timeToSeconds } from '@/lib/utils'

// Components
import VideoLayersPanel from './VideoLayersPanel.vue'
import SubtitleOverlay from './overlays/SubtitleOverlay.vue'
import TextOverlay from './overlays/TextOverlay.vue'
import ImageOverlay from './overlays/ImageOverlay.vue'
import EffectOverlay from './overlays/EffectOverlay.vue'
import GridOverlay from './overlays/GridOverlay.vue'
import SafeAreaOverlay from './overlays/SafeAreaOverlay.vue'
import CropSelector from '../CropSelector.vue'
import { message } from 'ant-design-vue'
import { globalAudioManager } from '@/lib/globalAudioManager'

// Stores
const layersStore = useVideoLayersStore()
const timelineStore = useTimelineStore()
const ttsStore = useTTSStore()

// Refs
const videoElement = ref(null)
const activeSubtitleId = ref(null)
const isManualSeek = ref(false)

state.videoElement = videoElement
const cropSelector = ref(null)

// Local state
const viewMode = ref('preview') // 'preview' | 'layers'
const previewQuality = ref('medium')
const previewZoom = ref(100)
const showGrid = ref(false)
const showSafeArea = ref(false)
const volume = ref(50)
const currentTime = ref(0)
const isPlaying = ref(false)

const cropMode = ref(false)
const currentCrop = ref(null)
const extractedText = ref('')

// Speed balancing state
const showSpeedSettings = ref(false)
const joinTimeInput = ref(0.5)
const defaultVideoSpeed = ref(1)
const audioCombineListLength = ref(0)

const props = defineProps({
  onInsert: {
    type: Function,
    default: () => {}
  }
})

// Video info
const videoInfo = computed(() => ttsStore.currentSrtList?.info || {})
const videoSrc = computed(() => ttsStore.currentSrtList?.path || '')
const videoDimensions = computed(() => ({
  width: videoInfo.value.width,
  height: videoInfo.value.height
}))

// Layers
const subtitleLayer = computed(() => layersStore.subtitleLayer)
const enabledTextLayers = computed(() =>
  layersStore.enabledLayers.filter(layer => layer.type === 'text')
)
const enabledImageLayers = computed(() =>
  layersStore.enabledLayers.filter(layer => layer.type === 'image')
)
const activeEffects = computed(() => layersStore.activeEffects)

// Viewport styling
const viewportStyle = computed(() => {
  const zoom = previewZoom.value / 100

  const width = videoInfo.value.width
  const height = videoInfo.value.height

  // Tỷ lệ khung hình gốc
  const aspectRatio = width / height

  // Giới hạn tối đa cho viewport
  const maxBase = 800  // Hoặc chỉnh tùy vào container

  // Nếu video ngang (16:9), giới hạn theo width
  // Nếu video dọc (9:16), giới hạn theo height
  let maxWidth
  let maxHeight

  if (aspectRatio >= 1) {
    // Video ngang
    maxWidth = maxBase * zoom
    maxHeight = (maxBase / aspectRatio) * zoom
  } else {
    // Video dọc
    maxHeight = maxBase * zoom
    maxWidth = (maxBase * aspectRatio) * zoom
  }

  return {
    maxWidth: maxWidth + 'px',
    maxHeight: maxHeight + 'px',
    transform: `scale(${zoom})`,
    transformOrigin: 'center center',
  }
})


// Methods
const formatTime = (seconds) => {
  if (!seconds || isNaN(seconds)) return '00:00'

  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)

  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

const getPlayButtonTitle = () => {
  if (isPlaying.value) {
    if (state.isGlobalPlayback) {
      return 'Pause Global Synchronized Playback (Video + All Audio)'
    }
    return 'Pause Video'
  } else {
    const audioCount = globalAudioManager.audioElements.size
    if (audioCount > 0) {
      return `Play Global Synchronized Video + ${audioCount} Audio Tracks`
    }
    return 'Play Video'
  }
}



const onVideoLoaded = () => {
  if (videoElement.value) {
    const video = videoElement.value
    layersStore.setVideoInfo({
      width: video.videoWidth,
      height: video.videoHeight,
      duration: video.duration,
      src: video.src
    })

    // Initialize global audio manager with subtitle items
    initializeGlobalAudioManager()
  }
}

const initializeGlobalAudioManager = async () => {
  const subtitleItems = ttsStore.currentSrtList?.items || []
  if (subtitleItems.length > 0 && videoElement.value) {
    await globalAudioManager.initialize(subtitleItems, videoElement.value)
    // console.log('Global Audio Manager initialized with', subtitleItems.length, 'subtitle items')

    // Update web audio support status
    state.webAudioSupported = globalAudioManager.isWebAudioSupported

    // Update audio combine list length
    updateAudioCombineListLength()
  }
}

const onTimeUpdate = () => {
  if (videoElement.value) {
    currentTime.value = videoElement.value.currentTime
    layersStore.setCurrentTime(currentTime.value)
    timelineStore.setCurrentTime(currentTime.value)
    handleVideoTimeUpdate(currentTime.value)
  }
}

const onPlay = () => {
  isPlaying.value = true
  layersStore.setPlaying(true)
}

const onPause = () => {
  isPlaying.value = false
  layersStore.setPlaying(false)
}

const togglePlayPause = async () => {
  // console.log('🎬 togglePlayPause called, isPlaying:', isPlaying.value)

  if (!videoElement.value) {
    console.error('❌ No video element available')
    return
  }

  try {
    if (isPlaying.value) {
      // console.log('⏸️ Pausing playback...')
      // Pause global synchronized playback
      globalAudioManager.pauseGlobalPlayback()
      isPlaying.value = false
      layersStore.setPlaying(false)
      // console.log('✅ Playback paused')
    } else {
      // console.log('▶️ Starting playback...')

      // Ensure user interaction for autoplay policy
      try {
        // console.log('🔧 Testing video play capability...')
        await videoElement.value.play()
        videoElement.value.pause()
        // console.log('✅ Video play test successful')
      } catch (e) {
        console.log('⚠️ Initial play test failed:', e.message)
      }

      // Start global synchronized playback
      const currentVideoTime = videoElement.value.currentTime
      // console.log('🚀 Attempting to start global playback from time:', currentVideoTime)
      // console.log('📊 Audio elements available:', globalAudioManager.audioElements.size)
      // console.log('🔊 Audio enabled:', state.audioEnabled)

      const success = await globalAudioManager.startGlobalPlayback(currentVideoTime)
      // console.log('🎯 Global playback result:', success)

      if (success) {
        isPlaying.value = true
        layersStore.setPlaying(true)
        // Update audio combine list length for UI
        updateAudioCombineListLength()
        // console.log('✅ Global synchronized playback started from time:', currentVideoTime)
      } else {
        // console.warn('⚠️ Failed to start global synchronized playback, falling back to video only')
        // Fallback to normal video playback
        await videoElement.value.play()
        isPlaying.value = true
        layersStore.setPlaying(true)
      }
    }
  } catch (error) {
    console.error('❌ Error toggling global playback:', error)
    console.error('Error details:', {
      name: error.name,
      message: error.message,
      stack: error.stack
    })

    // Fallback to normal video playback
    try {
      if (isPlaying.value) {
        videoElement.value.pause()
      } else {
        await videoElement.value.play()
      }
      isPlaying.value = !isPlaying.value
      layersStore.setPlaying(isPlaying.value)
      console.log('🔄 Fallback to normal video playback successful')
    } catch (fallbackError) {
      console.error('❌ Even fallback failed:', fallbackError)
    }
  }
}

const seekTo = (time) => {
  if (videoElement.value) {
    // Use global audio manager for seeking if available
    if (state.isGlobalPlayback) {
      globalAudioManager.seekTo(time)
    } else {
      videoElement.value.currentTime = time
    }
    currentTime.value = time
  }
}

const updateVolume = (vol) => {
  if (videoElement.value) {
    videoElement.value.volume = vol / 100
  }
}

const handleVideoClick = (event) => {
  // Handle click for positioning elements
  const rect = event.currentTarget.getBoundingClientRect()
  const x = ((event.clientX - rect.left) / rect.width) * 100
  const y = ((event.clientY - rect.top) / rect.height) * 100

  console.log('Video clicked at:', { x, y })
}

const toggleLayer = (layerId) => {
  layersStore.toggleLayer(layerId)
}

const addTextOverlay = () => {
  layersStore.addLayer({
    type: 'text',
    name: 'Text Overlay',
    properties: {
      text: 'Sample Text',
      fontSize: 32,
      color: '#ffffff',
      position: { x: 50, y: 50 }
    }
  })
}

const addImageOverlay = () => {
  layersStore.addLayer({
    type: 'image',
    name: 'Image Overlay',
    properties: {
      src: '',
      position: { x: 50, y: 50 },
      scale: 100
    }
  })
}

const addBlurEffect = () => {
  layersStore.addEffect('blur', {
    x: 10,
    y: 10,
    width: 100,
    height: 50,
    intensity: 10
  })
}

const renderVideo = () => {
  // TODO: Implement video rendering
  console.log('Rendering video with layers:', layersStore.enabledLayers)
}

// Speed balancing methods
const toggleSpeedBalancing = () => {
  state.speedBalancingEnabled = !state.speedBalancingEnabled
  globalAudioManager.setSpeedBalancing(state.speedBalancingEnabled)

  if (state.speedBalancingEnabled) {
    message.success('Speed balancing enabled')
    updateAudioCombineListLength()
  } else {
    message.info('Speed balancing disabled')
    showSpeedSettings.value = false
  }
}

const updateJoinTime = () => {
  state.joinTime = joinTimeInput.value
  globalAudioManager.setJoinTime(joinTimeInput.value)
  updateAudioCombineListLength()
  console.log('Join time updated to:', joinTimeInput.value)
}

const updateDefaultSpeed = () => {
  globalAudioManager.defaultVideoSpeed = defaultVideoSpeed.value
  globalAudioManager.buildAudioCombineList()
  updateAudioCombineListLength()
  console.log('Default video speed updated to:', defaultVideoSpeed.value)
}

const updateAudioCombineListLength = () => {
  audioCombineListLength.value = globalAudioManager.audioCombineList?.length || 0
}

function handleVideoTimeUpdate(time) {
  // Update current time
  currentTime.value = time

  // Update state for global access
  state.currentTime = time

  // Update layers store current time
  layersStore.setCurrentTime(time)

  const currentSubtitle = ttsStore.currentSrtList?.items.find(item => item.startTime <= time && item.endTime >= time);

  if (currentSubtitle && currentSubtitle.id !== state.currentPlayingSubtitleId) {
    // console.log('handleVideoTimeUpdate', time, currentSubtitle);

    // Đánh dấu rằng đây là thay đổi từ video, không phải manual
    isManualSeek.value = true;

    // Cập nhật state
    state.currentPlayingSubtitleId = currentSubtitle.id;
    activeSubtitleId.value = currentSubtitle.id;
  }
}



// Sync with timeline
watch(() => timelineStore.currentTime, (newTime) => {
  if (Math.abs(newTime - currentTime.value) > 0.1) {
    seekTo(newTime)
  }
})

// Watch for subtitle items changes to update global audio manager
watch(() => ttsStore.currentSrtList?.items, (newItems) => {
  if (newItems && videoElement.value) {
    globalAudioManager.updateSubtitleItems(newItems)
    // console.log('Global Audio Manager updated with', newItems.length, 'subtitle items')
  }
}, { deep: true })

// Watch cho external changes (từ UI clicks, buttons, etc.)
watch(() => state.currentPlayingSubtitleId, (newId, oldId) => {
  // Chỉ xử lý khi không phải từ video timeupdate
  if (!isManualSeek.value && newId !== activeSubtitleId.value) {
    activeSubtitleId.value = newId;

    // Seek video đến subtitle được chọn từ bên ngoài
    if (state.videoElement && newId) {
      const subtitle = ttsStore.currentSrtList?.items.find(sub => sub.id === newId);
      if (subtitle) {
        const startTime = timeToSeconds(subtitle.start);
        state.videoElement.currentTime = startTime;
      }
    }
  }

  // Reset flag
  isManualSeek.value = false;
});



// Lifecycle
onMounted(() => {
  // Set video player reference for timeline
  state.videoPlayer = { value: { $refs: { video: videoElement.value } } }

  // Initialize global audio manager when component mounts
  nextTick(() => {
    initializeGlobalAudioManager()
  })
})

onUnmounted(() => {
  // Cleanup global audio manager
  globalAudioManager.destroy()
  state.videoPlayer = null
})





// crop handler
const onCropSelected = (cropData) => {
  // console.log('Crop selected:', cropData);
  currentCrop.value = cropData.normalized;

};

// Watch for subtitle items changes to update global audio manager
watch(() => ttsStore.currentSrtList?.items, (newItems) => {
  if (newItems && newItems.length > 0) {
    initializeGlobalAudioManager()
  }
}, { deep: true })

// Watch for state changes to sync with UI
watch(() => state.joinTime, (newJoinTime) => {
  joinTimeInput.value = newJoinTime
})

watch(() => state.speedBalancingEnabled, (enabled) => {
  if (!enabled) {
    showSpeedSettings.value = false
  }
})

// Initialize speed balancing values
onMounted(() => {
  joinTimeInput.value = state.joinTime
  defaultVideoSpeed.value = globalAudioManager.defaultVideoSpeed
})

const onCropChange = (cropData) => {
  // console.log('Crop changed:', cropData);
  currentCrop.value = cropData.normalized;
};

const onCropCleared = () => {
  // console.log('Crop cleared');
  currentCrop.value = null;
  extractedText.value = '';
};
const clearCrop = () => {
  if (cropSelector.value) {
    cropSelector.value.clearSelection();
    extractedText.value = '';
  }
};

function toggleCropMode() {
  cropMode.value = !cropMode.value;
  if (cropSelector.value) {
    if (cropMode.value) {
      cropSelector.value.activate();
    } else {
      cropSelector.value.deactivate();
    }
  }
}

async function extractText() {
  if (!currentCrop.value || !currentCrop.value.width || !currentCrop.value.height) {
    console.warn('No crop area selected');
    return;
  }

  try {
    // Pause video for stable frame capture
    const wasPlaying = isPlaying.value;
    if (wasPlaying) {
      videoElement.value.pause();
      isPlaying.value = false;
    }

    // Create canvas to capture video frame
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    // Set canvas size to video dimensions
    canvas.width = videoDimensions.value.width;
    canvas.height = videoDimensions.value.height;

    // Draw current video frame
    ctx.drawImage(videoElement.value, 0, 0, canvas.width, canvas.height);

    // Extract cropped region
    const cropCanvas = document.createElement('canvas');
    const cropCtx = cropCanvas.getContext('2d');

    cropCanvas.width = currentCrop.value.width;
    cropCanvas.height = currentCrop.value.height;

    cropCtx.drawImage(
      canvas,
      currentCrop.value.x, currentCrop.value.y, currentCrop.value.width, currentCrop.value.height,
      0, 0, currentCrop.value.width, currentCrop.value.height
    );

    // Convert to blob for OCR processing
    const blob = await new Promise(resolve => cropCanvas.toBlob(resolve, 'image/png'));

    // Emit OCR request with crop data
    // this.$emit('ocr-request', {
    //   imageBlob: blob,
    //   cropData: {
    //     ...currentCrop.value,
    //     timestamp: currentTime.value,
    //     videoDimensions: videoDimensions.value
    //   }
    // });

    // Resume playing if it was playing before
    if (wasPlaying) {
      videoElement.value.play();
      isPlaying.value = true;
    }

    // For demo purposes - you would replace this with actual OCR API call
    simulateOCR(blob);

  } catch (error) {
    console.error('Error extracting text:', error);
    // this.$emit('ocr-error', error);
  }
}

async function simulateOCR(imageBlob) {
  // Simulate API delay
  const x1 = currentCrop.value.x / videoDimensions.value.width;
  const y1 = currentCrop.value.y / videoDimensions.value.height;
  const x2 = (currentCrop.value.x + currentCrop.value.width) / videoDimensions.value.width;
  const y2 = (currentCrop.value.y + currentCrop.value.height) / videoDimensions.value.height;
  const normalized = [x1, y1, x2, y2];
  const rounded = normalized.map(num => Math.round(num * 100) / 100);
  console.log(rounded);
  state.cropData = rounded
  message.info('Đang lấy đoạn text từ frame video', 5);
  const videoPath = ttsStore.currentSrtList?.path.replace('-ocr.srt', '.mp4').replace('.srt', '.mp4')
  const cloned = JSON.parse(JSON.stringify({
    videoPath,
    fromSecond: currentTime.value,
    cropData: rounded
  }));
  const res = await electronAPI.getTextFromFrameVideo(cloned)
    .catch(err => {
      console.error('Error getting text from frame video:', err);
      message.error('Error getting text from frame video: ' + err.message, 5);
    });
  console.log('res', res);
  if (res?.text) {
    message.success('Lấy đoạn text thành công', 5);
    state.cropText = res.text;
    extractedText.value = res.text;
  } else {
    message.error('Không lấy được đoạn text từ frame video hoặc lỗi', 5);
  }
}
function copyText() {
  if (!extractedText.value) return;

  navigator.clipboard.writeText(extractedText.value)
    .then(() => {
      message.success('Text copied to clipboard', 5);
    })
    .catch(err => {
      console.error('Error copying text:', err);
      message.error('Error copying text', 5);
    });
}

function insertText() {
  if (!extractedText.value) return;
  props.onInsert(extractedText.value);
}

</script>

<style scoped>
.video-preview-editor {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.video-viewport {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  border: 1px solid #374151;
}

.video-controls :deep(.ant-slider) {
  margin: 0;
}

.effects-toolbar {
  min-height: 64px;
}
</style>
