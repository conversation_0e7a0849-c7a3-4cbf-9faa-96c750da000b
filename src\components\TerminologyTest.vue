<template>
  <div class="p-4 max-w-4xl mx-auto">
    <h1 class="text-2xl font-bold mb-4">Terminology Dictionary Service Test</h1>
    
    <!-- Service Selection -->
    <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 mb-6">
      <h2 class="text-lg font-semibold mb-3">Test Configuration</h2>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label class="block text-sm font-medium mb-1">Service</label>
          <a-select v-model:value="testService" class="w-full" @change="updateService">
            <a-select-option value="openai">OpenAI</a-select-option>
            <a-select-option value="deepseek">DeepSeek</a-select-option>
            <a-select-option value="gemini">Gemini</a-select-option>
            <a-select-option value="claude">Claude</a-select-option>
            <a-select-option value="openrouter">OpenRouter</a-select-option>
          </a-select>
        </div>
        <div>
          <label class="block text-sm font-medium mb-1">Model</label>
          <a-input v-model:value="testModel" placeholder="Model name" />
        </div>
        <div>
          <label class="block text-sm font-medium mb-1">API Key</label>
          <a-input 
            v-model:value="testApiKey" 
            type="password" 
            placeholder="Enter API key"
          />
        </div>
      </div>
      
      <!-- Sample Text -->
      <div class="mt-4">
        <label class="block text-sm font-medium mb-1">Sample Text (Chinese)</label>
        <a-textarea 
          v-model:value="sampleText" 
          :rows="3"
          placeholder="Enter Chinese text to extract terminology..."
        />
      </div>
      
      <div class="mt-4 flex gap-2">
        <a-button @click="testTermExtraction" :loading="testing" type="primary">
          Test Term Extraction
        </a-button>
        <a-button @click="testDictionaryBuilding" :loading="testing" type="primary" ghost>
          Test Dictionary Building
        </a-button>
        <a-button @click="clearResults" type="default">
          Clear Results
        </a-button>
      </div>
    </div>

    <!-- Current Configuration Display -->
    <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 mb-6">
      <h3 class="font-medium mb-2">Current Configuration</h3>
      <div class="text-sm space-y-1">
        <div><strong>Service:</strong> {{ currentConfig.service || 'Not configured' }}</div>
        <div><strong>Model:</strong> {{ currentConfig.model || 'Not configured' }}</div>
        <div><strong>API Key:</strong> {{ currentConfig.apiKey ? '✓ Configured' : '✗ Missing' }}</div>
        <div><strong>Base URL:</strong> {{ currentConfig.baseURL || 'Not configured' }}</div>
      </div>
    </div>

    <!-- Test Results -->
    <div v-if="testResults.length > 0" class="space-y-4">
      <h2 class="text-lg font-semibold">Test Results</h2>
      <div 
        v-for="(result, index) in testResults" 
        :key="index"
        class="border rounded-lg p-4"
        :class="{
          'border-green-200 bg-green-50': result.success,
          'border-red-200 bg-red-50': !result.success
        }"
      >
        <div class="flex items-center justify-between mb-2">
          <h3 class="font-medium">{{ result.type }} - {{ result.service }} - {{ result.timestamp }}</h3>
          <a-tag :color="result.success ? 'green' : 'red'">
            {{ result.success ? 'Success' : 'Failed' }}
          </a-tag>
        </div>
        
        <div class="text-sm text-gray-600">
          <div><strong>Duration:</strong> {{ result.duration }}ms</div>
          
          <div v-if="result.success && result.type === 'Term Extraction'">
            <strong>Extracted Terms:</strong>
            <div class="mt-2 space-y-2">
              <div v-if="result.data.characters?.length">
                <strong>Characters:</strong> {{ result.data.characters.join(', ') }}
              </div>
              <div v-if="result.data.locations?.length">
                <strong>Locations:</strong> {{ result.data.locations.join(', ') }}
              </div>
              <div v-if="result.data.terms?.length">
                <strong>Terms:</strong> {{ result.data.terms.join(', ') }}
              </div>
            </div>
          </div>
          
          <div v-if="result.success && result.type === 'Dictionary Building'">
            <strong>Dictionary Entries:</strong> {{ Object.keys(result.data).length }}
            <div class="mt-2 max-h-40 overflow-y-auto">
              <div v-for="(translation, term) in result.data" :key="term" class="text-xs">
                {{ term }} → {{ translation }}
              </div>
            </div>
          </div>
          
          <div v-if="!result.success" class="text-red-600">
            <strong>Error:</strong> {{ result.error }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { useTTSStore } from '../stores/ttsStore';
import { configureTranslateService, getCurrentConfig } from '../lib/allTranslateService';

const ttsStore = useTTSStore();

const testService = ref('openai');
const testModel = ref('gpt-3.5-turbo');
const testApiKey = ref('');
const sampleText = ref('这是一个测试文本，包含了一些角色名字如张三、李四，地点如北京、上海，以及一些专业术语。');
const testing = ref(false);
const testResults = ref([]);
const currentConfig = ref({});

// Update current config display
function updateCurrentConfig() {
  currentConfig.value = getCurrentConfig();
}

function updateService(service) {
  testService.value = service;
  // Set default models for each service
  const defaultModels = {
    openai: 'gpt-3.5-turbo',
    deepseek: 'deepseek-chat',
    gemini: 'gemini-2.0-flash-exp',
    claude: 'claude-3-5-sonnet-20241022',
    openrouter: 'deepseek/deepseek-r1:free'
  };
  testModel.value = defaultModels[service] || 'default-model';
}

async function testTermExtraction() {
  if (!testApiKey.value.trim()) {
    message.warning('Please enter an API key');
    return;
  }

  if (!sampleText.value.trim()) {
    message.warning('Please enter sample text');
    return;
  }

  testing.value = true;
  const startTime = Date.now();
  
  try {
    // Configure the service
    configureTranslateService({
      service: testService.value,
      model: testModel.value,
      apiKey: testApiKey.value,
      baseURL: getBaseURL(testService.value)
    });
    
    updateCurrentConfig();
    
    // Import the function dynamically to avoid circular imports
    const { extractImportantTerms } = await import('../lib/allTranslateService');
    
    // Test term extraction
    const terms = await extractImportantTerms(sampleText.value);
    const duration = Date.now() - startTime;
    
    testResults.value.unshift({
      type: 'Term Extraction',
      service: testService.value.toUpperCase(),
      success: true,
      data: terms,
      duration,
      timestamp: new Date().toLocaleTimeString()
    });
    
    message.success(`Term extraction completed in ${duration}ms`);
    
  } catch (error) {
    const duration = Date.now() - startTime;
    
    testResults.value.unshift({
      type: 'Term Extraction',
      service: testService.value.toUpperCase(),
      success: false,
      error: error.message,
      duration,
      timestamp: new Date().toLocaleTimeString()
    });
    
    message.error(`Term extraction failed: ${error.message}`);
  } finally {
    testing.value = false;
  }
}

async function testDictionaryBuilding() {
  if (!testApiKey.value.trim()) {
    message.warning('Please enter an API key');
    return;
  }

  if (!sampleText.value.trim()) {
    message.warning('Please enter sample text');
    return;
  }

  testing.value = true;
  const startTime = Date.now();
  
  try {
    // Configure the service
    configureTranslateService({
      service: testService.value,
      model: testModel.value,
      apiKey: testApiKey.value,
      baseURL: getBaseURL(testService.value)
    });
    
    updateCurrentConfig();
    
    // Import the function dynamically
    const { buildTerminologyDictionary } = await import('../lib/allTranslateService');
    
    // Test dictionary building
    const dictionary = await buildTerminologyDictionary(sampleText.value, 'Chinese');
    const duration = Date.now() - startTime;
    
    testResults.value.unshift({
      type: 'Dictionary Building',
      service: testService.value.toUpperCase(),
      success: true,
      data: dictionary,
      duration,
      timestamp: new Date().toLocaleTimeString()
    });
    
    message.success(`Dictionary building completed in ${duration}ms`);
    
  } catch (error) {
    const duration = Date.now() - startTime;
    
    testResults.value.unshift({
      type: 'Dictionary Building',
      service: testService.value.toUpperCase(),
      success: false,
      error: error.message,
      duration,
      timestamp: new Date().toLocaleTimeString()
    });
    
    message.error(`Dictionary building failed: ${error.message}`);
  } finally {
    testing.value = false;
  }
}

function getBaseURL(service) {
  const urls = {
    openai: 'https://api.openai.com/v1',
    deepseek: 'https://api.deepseek.com/v1',
    gemini: 'https://generativelanguage.googleapis.com/v1beta',
    claude: 'https://api.anthropic.com/v1',
    openrouter: 'https://openrouter.ai/api/v1'
  };
  return urls[service] || '';
}

function clearResults() {
  testResults.value = [];
}

onMounted(() => {
  updateCurrentConfig();
});
</script>
