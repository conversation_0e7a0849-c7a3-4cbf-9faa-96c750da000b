import React from 'react';
import { useEffect } from 'react';
import { Video, Audio, Sequence, useCurrentFrame, useVideoConfig, AbsoluteFill } from 'remotion';

export const VideoComposition = ({ videoPath, segments, totalDurationInFrames }) => {
  const { fps } = useVideoConfig();
segments.forEach((seg, i) => {
  const prev = segments[i - 1];
  if (prev && seg.startFrame !== prev.startFrame + prev.durationInFrames) {
    console.warn(`⛔ Gap at segment ${i}: prevEnd=${prev.startFrame + prev.durationInFrames}, thisStart=${seg.startFrame}`);
  }
});

  return (
    <AbsoluteFill style={{ backgroundColor: 'black' }}>
      {segments.map((segment, index) => (
        <VideoSegment key={segment.id} segment={segment} videoPath={videoPath} fps={fps} />
      ))}
    </AbsoluteFill>
  );
};

// Component cho mỗi video segment
const VideoSegment = ({ segment, videoPath, fps }) => {
  const {
    startFrame,
    durationInFrames,
    videoStart,
    videoDuration,
    videoSpeed,
    audioPath,
    audioDuration,
    segmentDuration,
    volume,
  } = segment;
  const startFrom = Math.round(videoStart * fps);
  const endAt = Math.round((videoStart + videoDuration) * fps);
  const audioEndAt = Math.round(Math.min(audioDuration, segmentDuration) * fps);
  useEffect(() => {
    // console.log('startFrom:', startFrom, 'endAt:', endAt, 'audioEndAt:', audioEndAt, 'videoSpeed:', videoSpeed);
    
  }, [startFrom]);

  return (
    <Sequence from={startFrame} durationInFrames={durationInFrames}>
        {/* <AbsoluteFill style={{ backgroundColor: 'black' }} /> */}
      {/* Video Layer */}
      {endAt > startFrom && (
        <Video
          src={videoPath}
          startFrom={startFrom}
          endAt={endAt}
          playbackRate={videoSpeed}
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'cover',
          }}
          volume={0.8}
        />
      )}

      {/* Audio Layer */}
      {audioPath && <Audio
        src={audioPath}
        startFrom={0}
        endAt={audioEndAt}
        volume={volume * 2.5} // Boost volume (equivalent to 14dB)
      />}
    </Sequence>
  );
};
