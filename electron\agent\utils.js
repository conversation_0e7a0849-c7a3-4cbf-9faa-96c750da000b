/**
 * Utility functions for GeneralAgent
 */

/**
 * Count tokens in a string (simplified implementation)
 * @param {string} text 
 * @returns {number}
 */
function stringTokenCount(text) {
    // Simplified token counting - roughly 4 characters per token
    return Math.ceil(text.length / 4);
}

/**
 * Cut messages to fit within token limit
 * @param {Array} messages 
 * @param {number} tokenLimit 
 * @returns {Array}
 */
function cutMessages(messages, tokenLimit) {
    let totalTokens = 0;
    const result = [];
    
    // Start from the end and work backwards
    for (let i = messages.length - 1; i >= 0; i--) {
        const message = messages[i];
        const messageTokens = stringTokenCount(JSON.stringify(message));
        
        if (totalTokens + messageTokens <= tokenLimit) {
            result.unshift(message);
            totalTokens += messageTokens;
        } else {
            break;
        }
    }
    
    return result;
}

/**
 * Generate unique name for files
 * @returns {string}
 */
function uniqueName() {
    return `file_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Sleep for specified milliseconds
 * @param {number} ms 
 * @returns {Promise}
 */
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Default output callback function
 * @param {string|null} token 
 */
function defaultOutputCallback(token) {
    if (token !== null) {
        process.stdout.write(token);
    } else {
        process.stdout.write('\n');
    }
}

/**
 * Default check function for user confirmation
 * @param {string} checkContent
 * @returns {string|null}
 */
function defaultCheck(checkContent = null) {
    // Note: This is a simplified version. In a real implementation,
    // you might want to use a proper readline interface
    console.log("Xác nhận | Tiếp tục (yes/y/có/ok) hoặc chỉ cần nhập suy nghĩ của bạn");
    if (checkContent !== null) {
        console.log(`${checkContent}\n`);
    }

    // For now, return null (confirmed) - in real usage, implement proper input
    return null;
}

module.exports = { stringTokenCount, cutMessages, uniqueName, sleep, defaultOutputCallback, defaultCheck };