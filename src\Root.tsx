import React from 'react';
import { Composition } from 'remotion';
import { VideoComposition } from './remotion/VideoComposition';

const srtArray = [
  {
    index: 1,
    id: 1,
    text: '<PERSON><PERSON><PERSON> viên quần chúng một ngày một nghìn, đi không?',
    startTime: 0,
    endTime: 1.6800000000000002,
    start: '00:00:00,000',
    end: '00:00:01,680',
    translatedText: '<PERSON>ễn viên quần chúng một ngày một nghìn, đi không?',
    status: 'translated',
    selectedSpeaker: 'tts.other.BV075_streaming',
    speechRate: 0,
    audioUrl:
      'http://localhost:8082/assets?path=I:\\ReviewDao\\con-nho\\1111_audio\\dien-vien-quan-chung-mot1747995697765.mp3',
    duration: 2.742857,
    isGenerated: false,
    isVoice: 2,
    audioUrl2:
      'http://localhost:8082/assets?path=I:\\ReviewDao\\con-nho\\1111_audio\\dien-vien-quan-chung-mot1747995697765.mp3',
    audioDuration2: 2742.857,
    isGenerated2: true,
    speedFactor: 1,
    videoPath:
      'http://localhost:8082/assets?path=C:\\Users\\<USER>\\AppData\\Local\\Temp\\video_segments\\segment_1.mp4',
  },
  {
    index: 2,
    id: 2,
    text: 'Lại định đánh tôi nữa à?',
    startTime: 1.6800000000000002,
    endTime: 3.7,
    start: '00:00:01,680',
    end: '00:00:03,700',
    translatedText: 'Lại định đánh tôi nữa à?',
    status: 'translated',
    selectedSpeaker: 'tts.other.BV075_streaming',
    speechRate: 0,
    audioUrl:
      'http://localhost:8082/assets?path=I:\\ReviewDao\\con-nho\\1111_audio\\lai-dinh-danh-toi-nua-a1747995699414.mp3',
    duration: 1.464,
    isGenerated: false,
    isVoice: 1,
    audioUrl1:
      'http://localhost:8082/assets?path=I:\\ReviewDao\\con-nho\\1111_audio\\lai-dinh-danh-toi-nua-a1747995699414.mp3',
    audioDuration1: 1464,
    isGenerated1: true,
    speedFactor: 0.7247524752475247,
    videoPath:
      'http://localhost:8082/assets?path=C:\\Users\\<USER>\\AppData\\Local\\Temp\\video_segments\\segment_2.mp4',
  },
  {
    index: 3,
    id: 3,
    text: 'Không có, lần này thì...',
    startTime: 3.7,
    endTime: 5.5,
    start: '00:00:03,700',
    end: '00:00:05,500',
    translatedText: 'Không có, lần này thì...',
    status: 'translated',
    selectedSpeaker: 'tts.other.BV075_streaming',
    speechRate: 0,
    audioUrl:
      'http://localhost:8082/assets?path=I:\\ReviewDao\\con-nho\\1111_audio\\khong-co-lan-nay-thi1747995700236.mp3',
    duration: 1.828571,
    isGenerated: false,
    isVoice: 2,
    audioUrl2:
      'http://localhost:8082/assets?path=I:\\ReviewDao\\con-nho\\1111_audio\\khong-co-lan-nay-thi1747995700236.mp3',
    audioDuration2: 1828.571,
    isGenerated2: true,
    speedFactor: 1,
    videoPath:
      'http://localhost:8082/assets?path=C:\\Users\\<USER>\\AppData\\Local\\Temp\\video_segments\\segment_3.mp4',
  },
  {
    index: 4,
    id: 4,
    text: 'Em bịt kín vùng môi một chút, ngồi trong xe là được rồi',
    startTime: 5.5,
    endTime: 8,
    start: '00:00:05,500',
    end: '00:00:08,000',
    translatedText: 'Em bịt kín vùng môi một chút, ngồi trong xe là được rồi',
    status: 'translated',
    selectedSpeaker: 'tts.other.BV075_streaming',
    speechRate: 0,
    audioUrl:
      'http://localhost:8082/assets?path=I:\\ReviewDao\\con-nho\\1111_audio\\em-bit-kin-vung-moi-mot-1747995733997.mp3',
    duration: 3.082449,
    isGenerated: false,
    isVoice: 2,
    audioUrl1:
      'http://localhost:8082/assets?path=I:\\ReviewDao\\con-nho\\1111_audio\\em-bit-kin-vung-moi-mot-1747995701697.mp3',
    audioDuration1: 2768.98,
    isGenerated1: true,
    audioUrl2:
      'http://localhost:8082/assets?path=I:\\ReviewDao\\con-nho\\1111_audio\\em-bit-kin-vung-moi-mot-1747995733997.mp3',
    audioDuration2: 3082.449,
    isGenerated2: true,
    speedFactor: 1,
    videoPath:
      'http://localhost:8082/assets?path=C:\\Users\\<USER>\\AppData\\Local\\Temp\\video_segments\\segment_4.mp4',
  },
  {
    index: 5,
    id: 5,
    text: 'Nghiêm túc không',
    startTime: 8,
    endTime: 10.38,
    start: '00:00:08,000',
    end: '00:00:10,380',
    translatedText: 'Nghiêm túc không',
    status: 'translated',
    selectedSpeaker: 'tts.other.BV075_streaming',
    speechRate: 0,
    audioUrl: 'http://localhost:8082/assets?path=I:\\ReviewDao\\con-nho\\1111_audio\\nghiem-tuc-khong1747995704463.mp3',
    duration: 0.984,
    isGenerated: false,
    isVoice: 1,
    audioUrl1:
      'http://localhost:8082/assets?path=I:\\ReviewDao\\con-nho\\1111_audio\\nghiem-tuc-khong1747995704463.mp3',
    audioDuration1: 984,
    isGenerated1: true,
    speedFactor: 0.41344537815126037,
    videoPath:
      'http://localhost:8082/assets?path=C:\\Users\\<USER>\\AppData\\Local\\Temp\\video_segments\\segment_5.mp4',
  },
  {
    index: 6,
    id: 6,
    text: 'Lần nào mà không nghiêm túc chứ?',
    startTime: 10.38,
    endTime: 13,
    start: '00:00:10,380',
    end: '00:00:13,000',
    translatedText: 'Lần nào mà không nghiêm túc chứ?',
    status: 'translated',
    selectedSpeaker: 'tts.other.BV075_streaming',
    speechRate: 0,
    audioUrl:
      'http://localhost:8082/assets?path=I:\\ReviewDao\\con-nho\\1111_audio\\lan-nao-ma-khong-nghiem-1747995705239.mp3',
    duration: 1.525,
    isGenerated: false,
    isVoice: 2,
    audioUrl2:
      'http://localhost:8082/assets?path=I:\\ReviewDao\\con-nho\\1111_audio\\lan-nao-ma-khong-nghiem-1747995705239.mp3',
    audioDuration2: 1525,
    isGenerated2: true,
    speedFactor: 0.5820610687022902,
    videoPath:
      'http://localhost:8082/assets?path=C:\\Users\\<USER>\\AppData\\Local\\Temp\\video_segments\\segment_6.mp4',
  },
  {
    index: 7,
    id: 7,
    text: 'Ngồi xe này',
    startTime: 13.72,
    endTime: 14.72,
    start: '00:00:13,720',
    end: '00:00:14,720',
    translatedText: 'Ngồi xe này',
    status: 'translated',
    selectedSpeaker: 'tts.other.BV075_streaming',
    speechRate: 0,
    audioUrl: 'http://localhost:8082/assets?path=I:\\ReviewDao\\con-nho\\1111_audio\\ngoi-xe-nay1747995706442.mp3',
    duration: 0.895,
    isGenerated: false,
    isVoice: 1,
    audioUrl1: 'http://localhost:8082/assets?path=I:\\ReviewDao\\con-nho\\1111_audio\\ngoi-xe-nay1747995706442.mp3',
    audioDuration1: 895,
    isGenerated1: true,
    speedFactor: 0.895,
    videoPath:
      'http://localhost:8082/assets?path=C:\\Users\\<USER>\\AppData\\Local\\Temp\\video_segments\\segment_7.mp4',
  },
  {
    index: 8,
    id: 8,
    text: 'Đúng vậy',
    startTime: 14.72,
    endTime: 16.16,
    start: '00:00:14,720',
    end: '00:00:16,160',
    translatedText: 'Đúng vậy',
    status: 'translated',
    selectedSpeaker: 'tts.other.BV075_streaming',
    speechRate: 0,
    audioUrl: 'http://localhost:8082/assets?path=I:\\ReviewDao\\con-nho\\1111_audio\\dung-vay1747995707288.mp3',
    duration: 0.744,
    isGenerated: false,
    isVoice: 2,
    audioUrl2: 'http://localhost:8082/assets?path=I:\\ReviewDao\\con-nho\\1111_audio\\dung-vay1747995707288.mp3',
    audioDuration2: 744,
    isGenerated2: true,
    speedFactor: 0.5166666666666668,
    videoPath:
      'http://localhost:8082/assets?path=C:\\Users\\<USER>\\AppData\\Local\\Temp\\video_segments\\segment_8.mp4',
  },
  {
    index: 9,
    id: 9,
    text: 'Người chết ba ngày trước từng mưu phản',
    startTime: 16.16,
    endTime: 21.46,
    start: '00:00:16,160',
    end: '00:00:21,460',
    translatedText: 'Người chết ba ngày trước từng mưu phản',
    status: 'translated',
    selectedSpeaker: 'tts.other.BV075_streaming',
    speechRate: 0,
    audioUrl:
      'http://localhost:8082/assets?path=I:\\ReviewDao\\con-nho\\1111_audio\\nguoi-chet-ba-ngay-truoc1747995707833.mp3',
    duration: 1.944,
    isGenerated: false,
    isVoice: 1,
    audioUrl1:
      'http://localhost:8082/assets?path=I:\\ReviewDao\\con-nho\\1111_audio\\nguoi-chet-ba-ngay-truoc1747995707833.mp3',
    audioDuration1: 1944,
    isGenerated1: true,
    speedFactor: 0.36679245283018863,
    videoPath:
      'http://localhost:8082/assets?path=C:\\Users\\<USER>\\AppData\\Local\\Temp\\video_segments\\segment_9.mp4',
  },
  {
    index: 10,
    id: 10,
    text: 'Bảy ngày năm mươi tội chém đầu bốn lần',
    startTime: 21.46,
    endTime: 23.58,
    start: '00:00:21,460',
    end: '00:00:23,580',
    translatedText: 'Bảy ngày năm mươi tội chém đầu bốn lần',
    status: 'translated',
    selectedSpeaker: 'tts.other.BV075_streaming',
    speechRate: 0,
    audioUrl:
      'http://localhost:8082/assets?path=I:\\ReviewDao\\con-nho\\1111_audio\\bay-ngay-nam-muoi-toi-ch1747995708665.mp3',
    duration: 2.232,
    isGenerated: false,
    isVoice: 1,
    audioUrl1:
      'http://localhost:8082/assets?path=I:\\ReviewDao\\con-nho\\1111_audio\\bay-ngay-nam-muoi-toi-ch1747995708665.mp3',
    audioDuration1: 2232,
    isGenerated1: true,
    speedFactor: 1,
    videoPath:
      'http://localhost:8082/assets?path=C:\\Users\\<USER>\\AppData\\Local\\Temp\\video_segments\\segment_10.mp4',
  },
  {
    index: 11,
    id: 11,
    text: 'Tới đây Tới đây Tới đây',
    startTime: 23.58,
    endTime: 25.2,
    start: '00:00:23,580',
    end: '00:00:25,200',
    translatedText: 'Tới đây Tới đây Tới đây',
    status: 'translated',
    selectedSpeaker: 'tts.other.BV075_streaming',
    speechRate: 0,
    audioUrl:
      'http://localhost:8082/assets?path=I:\\ReviewDao\\con-nho\\1111_audio\\toi-day-toi-day-toi-day1747995709586.mp3',
    duration: 1.416,
    isGenerated: false,
    isVoice: 3,
    audioUrl3:
      'http://localhost:8082/assets?path=I:\\ReviewDao\\con-nho\\1111_audio\\toi-day-toi-day-toi-day1747995709586.mp3',
    audioDuration3: 1416,
    isGenerated3: true,
    speedFactor: 0.8740740740740734,
    videoPath:
      'http://localhost:8082/assets?path=C:\\Users\\<USER>\\AppData\\Local\\Temp\\video_segments\\segment_11.mp4',
  },
  {
    index: 12,
    id: 12,
    text: 'Bên trong biệt thự mở cửa rộng',
    startTime: 30,
    endTime: 43.37,
    start: '00:00:30,000',
    end: '00:00:43,370',
    translatedText: 'Bên trong biệt thự mở cửa rộng',
    status: 'translated',
    selectedSpeaker: 'tts.other.BV075_streaming',
    speechRate: 0,
    audioUrl:
      'http://localhost:8082/assets?path=I:\\ReviewDao\\con-nho\\1111_audio\\ben-trong-biet-thu-mo-cu1747995710374.mp3',
    duration: 1.704,
    isGenerated: false,
    isVoice: 2,
    audioUrl2:
      'http://localhost:8082/assets?path=I:\\ReviewDao\\con-nho\\1111_audio\\ben-trong-biet-thu-mo-cu1747995710374.mp3',
    audioDuration2: 1704,
    isGenerated2: true,
    speedFactor: 0.1274495138369484,
    videoPath:
      'http://localhost:8082/assets?path=C:\\Users\\<USER>\\AppData\\Local\\Temp\\video_segments\\segment_12.mp4',
  },
];

const totalDuration = srtArray.reduce((sum, srt) => {
  return sum + Math.max(srt.duration, srt.endTime - srt.startTime);
}, 0);
// 4. Setup composition
const videoConfig: any = {
  duration: totalDuration,
  fps: 30,
  height: 1920,
  width: 1080,
};

export const RemotionRoot: React.FC = () => {
  return (
    <>
      <Composition
        id="TikTok"
        component={VideoComposition}
        calculateMetadata={async ({ props }) => {
          const { videoConfig = {}, stylingConfig = {} }:any = props;
          if (!props.videoConfig) {
            throw new Error('❌ videoConfig bị thiếu trong inputProps ' + JSON.stringify(props));
          }
          const config = videoConfig;
          const playbackRate = stylingConfig?.playbackRate ?? 1.0;
          return {
            durationInFrames: Math.round((config.duration * config.fps) / playbackRate),
            fps: config.fps,
            height: config.height,
            width: config.width,
          };
        }}
        defaultProps={{
          srtArray,
          videoConfig,
        }}
      />
    </>
  );
};
