const axios = require('axios');

class TS {
  constructor(srclang = 'zh', tgtlang = 'vi', proxy = null, timeout = 10000) {
    this.srclang = srclang;
    this.tgtlang = tgtlang;
    this.proxy = proxy;
    this.timeout = timeout;
  }

  async translate(content) {
    const headers = {
      'authority': 'translate-crx.bytedance.com',
      'accept': 'application/json, text/plain, */*',
      'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
      'auth': 'auth-v1/mt.content.ext/1664604057493/300/a15ee3975145d64ee59308207a8ca0192114b25fc52f10531a8f43e2488a77b',
      'cache-control': 'no-cache',
      'origin': 'chrome-extension://klgfhbiooeogdfodpopgppeadghjjemk',
      'pragma': 'no-cache',
      'sec-fetch-dest': 'empty',
      'sec-fetch-mode': 'cors',
      'sec-fetch-site': 'none',
      'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/105.0.0.0 Safari/537.36 Edg/105.0.1343.53',
    };

    const data = {
      src_lang: this.srclang,
      tgt_lang: this.tgtlang,
      mode: 0,
      from: 'https://tieba.baidu.com/',
      text: content,
      browser: 0
    };

    try {
      const response = await axios.post(
        'https://translate-crx.bytedance.com/e1/flask/translation',
        data,
        {
          headers: headers,
          timeout: this.timeout,
          proxy: this.proxy || false
        }
      );

      return response.data.results[0].translate[0];
    } catch (error) {
      console.error('Translation failed:', error);
      return null;
    }
  }
}


(async () => {
  const translator = new TS('zh', 'vi', null, 10000);
  const result = await translator.translate('你好，世界！');
  console.log(result);
})();


module.exports = TS;
