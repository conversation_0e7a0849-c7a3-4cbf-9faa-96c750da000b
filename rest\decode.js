const axios = require('axios');
const fs = require('fs');
const path = require('path');

const groupId = '1909853211993314096';
const apiKey = 'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************.B6HbWRa_5cxuVnz1tN5x0Z-P7AGRXSpzkGDDD7lY480iPMEX9SY1_vvw-Dy5ehRVNSWvaKPvVJGAO7DENb4bucDJCKdCF749zvEYVM-mmbnF0tKoO2M77BqEFfoaM-jmH27SF7lUnx0-VVtY9jFyMTDBAebY62lK2TiiDgw2DWSdRkuVX9ThLXEaKrQiYIkTfqAhBmIUaHeYxvrhpbdCNrRmOYO55wmR76KEadYu_BJnKSVwEQaRyDC0O1PYPTNQt8h_SB5sm2HL75i0dm3qSgixIQ6z2mG1qfRzx5vwoTOgEFoWOpJTxkYJkbltpvgawu9FtgRVB3fmdpTez_tk1w';

const apiUrl = `https://api.minimax.io/v1/t2a_v2?GroupId=${groupId}`;
// nữ: moss_audio_5214a9b1-1545-11f0-9885-1a8691f98463
const payload = {
  model: 'speech-02-hd', // speech-02-turbo
  text: `Nửa đêm tỉnh dậy, tôi thấy bà cố đang gọi hàng trăm con chuột từ chum gạo`,
  stream: false,
  voice_setting: {
    voice_id: 'moss_audio_e662b4eb-4fe9-11f0-9e4a-3ee691490080',
    speed: 1,
    vol: 1,
    pitch: 0
  },
  audio_setting: {
    sample_rate: 32000,
    bitrate: 128000,
    format: 'mp3',
    channel: 1
  }
};

const headers = {
  'Authorization': `Bearer ${apiKey}`,
  'Content-Type': 'application/json'
};

const outputDir = './output';
fs.mkdirSync(outputDir, { recursive: true });

async function callMinimaxT2A() {
  try {
    const response = await axios.post(apiUrl, payload, { headers });
    const res = response.data;

    if (res.base_resp?.status_code !== 0) {
      throw new Error(`API error: ${res.base_resp?.status_msg || 'Unknown error'}`);
    }

    // ===== Save audio file from hex =====
    const hexAudio = res.data.audio;
    const format = res.extra_info.audio_format || 'mp3';
    const audioFile = path.join(outputDir, `audio_${Date.now()}.${format}`);

    fs.writeFileSync(audioFile, Buffer.from(hexAudio, 'hex'));
    console.log('✅ Đã lưu file audio tại:', audioFile);

    // ===== Download subtitle file (.srt) if available =====
    const subtitleUrl = res.data.subtitle_file;
    if (subtitleUrl) {
      const subtitleFile = path.join(outputDir, `subtitle_${Date.now()}.srt`);
      const subtitleStream = await axios.get(subtitleUrl, { responseType: 'stream' });

      const writer = fs.createWriteStream(subtitleFile);
      subtitleStream.data.pipe(writer);

      writer.on('finish', () => {
        console.log('✅ Đã lưu subtitle tại:', subtitleFile);
      });
    }

  } catch (err) {
    console.error('❌ Lỗi khi gọi API:', err.message);
  }
}

callMinimaxT2A();

// curl --location 'https://api.minimax.io/v1/image_generation' \
// --header 'Content-Type: application/json' \
// --header 'Authorization: Bearer {api_key}' \
// --data '{
//     "model": "image-01",
//     "prompt": "men Dressing in white t shirt, full-body stand front view image :25, outdoor, Venice beach sign, full-body image, Los Angeles, Fashion photography of 90s, documentary, Film grain, photorealistic",
//     "aspect_ratio": "16:9",
//     "response_format": "url",
//     "n": 3,
//     "prompt_optimizer": true
// }'