<script setup>
import { ref, computed, reactive, watch, onMounted } from 'vue';
import { useTTSStore } from '../stores/ttsStore';
import { useSRTStore } from '../stores/srtStore';
import { message } from 'ant-design-vue';
import Audio from './Audio.vue';
import SubtitlePreview from './SubtitlePreview.vue';
import SrtLists from './SrtLists.vue';
import DragDropUpload from './DragDropUpload.vue'
import { parseSRT,formatTime } from '@/lib/utils';
import SubtitleTable from './SubtitleTable.vue';


const ttsStore = useTTSStore();
const srtStore = useSRTStore();
const srtFile = ref(null);
const srtItems = ref([]);
const isProcessing = ref(false);
const isLoading = computed(() => ttsStore.isLoading);
const speakers = computed(() => ttsStore.speakers);
const defaultSpeaker = computed(() => ttsStore.selectedSpeaker);
const isGenerating = ref(false);
const isStopping = ref(false);
const generatedCount = ref(0);
const totalItems = ref(0);
const selectedItems = ref([]);
const bulkSpeaker = ref('');
const bulkSpeechRate = ref(0);
const itemLoadingStates = ref({});

// Pagination
const currentPage = ref(1);
const pageSize = ref(10);
const paginatedItems = computed(() => {
  const startIndex = (currentPage.value - 1) * pageSize.value;
  const endIndex = startIndex + pageSize.value;
  return srtItems.value.slice(startIndex, endIndex);
});
const totalPages = computed(() => Math.ceil(srtItems.value.length / pageSize.value));

// Joining MP3s
const isJoining = ref(false);
const outputFileName = ref('combined-audio.mp3');
const hasGeneratedAudios = computed(() => {
  return srtItems.value.some(item => item.isGenerated && item.audioUrl);
});

// Watch for changes to srtItems and update the current SRT list in the store
watch(srtItems, (newItems) => {
  if (ttsStore.currentSrtList && newItems.length > 0) {
    // Find the index of the current SRT list
    const index = ttsStore.srtLists.findIndex(item => item.name === ttsStore.currentSrtList.name);
    if (index !== -1) {
      // Update the items in the store
      ttsStore.srtLists[index].items = [...newItems];
    }
  }
}, { deep: true });

watch(() => ttsStore.activeTab, async (newVal) => {
  if (newVal === 'srt-table') {
    await handleMounded();
  }
});

async function handleMounded() {
  if (srtStore.srtContent && srtStore.srtFile) {
    if(ttsStore.currentSrtList?.path === srtStore.srtFile?.path) return;
    await processSrtFile(srtStore.srtContent);
  }
}

async function processSrtFile(content) {
  try {
    isProcessing.value = true;
    // Parse SRT content
    const parsedItems = parseSRT(content);

    // Add additional properties for voice selection and speed
    srtItems.value = parsedItems.map(item => ({
      ...item,
      selectedSpeaker: defaultSpeaker.value,
      speechRate: 0, // Default speech rate (normal speed)
      audioUrl: '',
      duration: 0,
      isGenerated: false
    }));

    srtFile.value = srtStore.srtFile || srtFile.value

    // Create a new SRT list entry
    const newSrtList = {
      name: srtFile.value.name+'_'+new Date().getTime(),
      items: [...srtItems.value],
      path: srtFile.value.path,
      layers: [] // Initialize empty layers array
    };

    // Add to the store
    ttsStore.srtLists.push(newSrtList);

    // Set as current SRT list
    ttsStore.currentSrtList = newSrtList;
    await electronAPI.setCurrentDir(srtFile.value.path)

    message.success(`Imported ${parsedItems.length} items from SRT file`);
  } catch (error) {
    console.error('Error processing SRT file:', error);
    message.error('Error processing SRT file: ' + error.message);
  } finally {
    isProcessing.value = false;
  }
}

// Function to handle file upload
async function handleFileUpload(e) {
  const file = e?.target?.files?.[0] || e?.[0];
  if (!file) return;

  srtFile.value = file;
  isProcessing.value = true;

  try {
    // Read the SRT file
    const reader = new FileReader();
    const content = await new Promise((resolve, reject) => {
      reader.onload = (e) => resolve(e.target.result);
      reader.onerror = (e) => reject(e);
      reader.readAsText(file);
    });

    // Parse SRT content
   await processSrtFile(content);

    message.success(`Imported ${parsedItems.length} items from SRT file`);
  } catch (error) {
    console.error('Error processing SRT file:', error);
    message.error('Error processing SRT file: ' + error.message);
  } finally {
    isProcessing.value = false;
  }
}

// reset file selected
async function resetFileSelected() {
  srtFile.value = null;
  srtItems.value = [];
  selectedItems.value = [];
  currentPage.value = 1;
  ttsStore.currentSrtList = null;
  await electronAPI.setCurrentDir()
}





// Format speech rate value to a multiplier format (1x, 1.2x, etc.)
function formatSpeechRate(value) {
  // Convert from slider value (-50 to 100) to multiplier (0.5x to 2x)
  const multiplier = 1 + (value / 100);
  return `${multiplier.toFixed(1)}x`;
}

// Generate TTS for all items or selected items
async function generateAllTTS() {
  if (srtItems.value.length === 0) return;


  isGenerating.value = true;
  generatedCount.value = 0;

  // If items are selected, only process those
  const itemsToProcess = selectedItems.value.length > 0
    ? selectedItems.value
    : Array.from({ length: srtItems.value.length }, (_, i) => i);

  totalItems.value = itemsToProcess.length;

  try {
    for (const i of itemsToProcess) {
      const item = srtItems.value[i];

      // Skip if already generated
      if (item.isGenerated && item.audioUrl) {
        generatedCount.value++;
        continue;
      }
      if (isStopping.value) {
        isStopping.value = false;
        break;
      }

      // Generate TTS for this item
      await generateTTSForItem(i);
      generatedCount.value++;
    }

    message.success(`Generated TTS for ${generatedCount.value} items successfully`);
  } catch (error) {
    console.error('Error generating TTS:', error);
    message.error('Error generating TTS: ' + error.message);
  } finally {
    isGenerating.value = false;
  }
}

async function stopGeneratingTTS() {
  isStopping.value = true;
  isGenerating.value = false;
}

// Generate TTS for a specific item
async function generateTTSForItem(index) {
  const item = srtItems.value[index];
  itemLoadingStates.value[index] = true;
  try {
    // Set text and speaker for this item
    ttsStore.setText(item.text);
    ttsStore.setSelectedSpeaker(item.selectedSpeaker);

    // Set speech rate
    ttsStore.updateTTSParams({ speech_rate: item.speechRate });

    // Generate TTS
    await ttsStore.generateTTS();

    // Update item with generated audio
    srtItems.value[index] = {
      ...item,
      audioUrl: ttsStore.audioUrl,
      duration: ttsStore.audioDuration,
      isGenerated: true
    };
    itemLoadingStates.value[index] = false;
    return true;
  } catch (error) {
    console.error(`Error generating TTS for item ${index + 1}:`, error);
    itemLoadingStates.value[index] = false;
    return false;
  }
}

// Function to trigger file input click
function openFileInput() {
  document.getElementById('srt-table-file').click();
}

// Function to select all items
function selectAll(e) {
  if (e.target.checked) {
    // Select all items
    selectedItems.value = srtItems.value.map((_, index) => index);
  } else {
    // Deselect all items
    selectedItems.value = [];
  }
}

// Function to toggle item selection
function toggleItemSelection(index) {
  const itemIndex = selectedItems.value.indexOf(index);
  if (itemIndex === -1) {
    // Item not selected, add it
    selectedItems.value.push(index);
  } else {
    // Item already selected, remove it
    selectedItems.value.splice(itemIndex, 1);
  }
}

// Function to apply bulk voice change
function applyBulkVoiceChange() {
  if (!bulkSpeaker.value || selectedItems.value.length === 0) return;

  // Apply the selected voice to all selected items
  selectedItems.value.forEach(index => {
    srtItems.value[index].selectedSpeaker = bulkSpeaker.value;
  });

  message.success(`Applied voice to ${selectedItems.value.length} items`);
}

// Function to apply bulk speech rate change
function applyBulkSpeechRateChange() {
  if (selectedItems.value.length === 0) return;

  // Apply the selected speech rate to all selected items
  selectedItems.value.forEach(index => {
    srtItems.value[index].speechRate = bulkSpeechRate.value;
  });

  message.success(`Applied speed (${formatSpeechRate(bulkSpeechRate.value)}) to ${selectedItems.value.length} items`);
}

// Function to download audio
function downloadAudio(audioUrl, index) {
  const a = document.createElement('a');
  a.href = audioUrl;
  a.download = `segment-${index}.mp3`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
}

// Function to join MP3 files using FFmpeg
async function joinMp3Files() {
  if (!hasGeneratedAudios.value) {
    message.warning('No audio files have been generated yet');
    return;
  }

  isJoining.value = true;

  try {
    // Create a temporary directory to store the files
    // const tempDir = 'temp_audio';
    const tempDir =(await electronAPI.getCurrentDir()).currentDir;

    // Save all audio files to the temp directory
    const fileList = [];
    const validItems = srtItems.value.filter(item => item.isGenerated && item.audioUrl);

    for (let i = 0; i < validItems.length; i++) {
      const item = validItems[i];
      const fileName = `segment-${item.index}.mp3`;
      const filePath = `${tempDir}/${fileName}`;
      const protocol = item.audioUrl.startsWith('file://')
      if(!protocol){
        // Download the audio file
        await electronAPI.downloadFile(item.audioUrl, filePath);
        fileList.push(filePath);
      } else{
        fileList.push(item.audioUrl.replace('file://', ''));
      }

    }

    // Create a file list for FFmpeg
    const fileListPath = `${tempDir}/filelist.txt`;
    const fileListContent = fileList.map(file => `file '${file}'`).join('\n');
    await electronAPI.writeFile(fileListPath, fileListContent);

    // Run FFmpeg to join the files
    const outputPath = `${tempDir}/${outputFileName.value}`;
    const result = await electronAPI.runFFmpeg(fileListPath, outputPath);

    if (result.success) {
      // Provide download link for the combined file
      message.success('MP3 files combined successfully');

      // Open the file in the default player
      await electronAPI.openFile(outputPath);
    } else {
      message.error('Error combining MP3 files: ' + result.error);
    }
  } catch (error) {
    console.error('Error joining MP3 files:', error);
    message.error('Error joining MP3 files: ' + error.message);
  } finally {
    isJoining.value = false;
  }
}

async function joinMp3FilesV2() {
  if (!hasGeneratedAudios.value) {
    message.warning('No audio files have been generated yet');
    return;
  }

  isJoining.value = true;

  try {
    // Create a temporary directory to store the files
    // const tempDir = 'temp_audio';
    const tempDir =(await electronAPI.getCurrentDir()).currentDir;

    // Save all audio files to the temp directory
    const fileList = [];
    const validItems = srtItems.value.filter(item => item.isGenerated && item.audioUrl);

    for (let i = 0; i < validItems.length; i++) {
      const item = validItems[i];
      const fileName = `segment-${item.index}.mp3`;
      const filePath = `${tempDir}\\${fileName}`;
      const protocol = item.audioUrl.startsWith('file://')

      if(!protocol){
        // Download the audio file
        await electronAPI.downloadFile(item.audioUrl, filePath);
        fileList.push(filePath);
      } else{
        fileList.push(item.audioUrl.replace('file://', ''));
      }

    }

    // Create a file list for FFmpeg
    // const fileListPath = `${tempDir}/filelist.txt`;
    // const fileListContent = fileList.map(file => `file '${file}'`).join('\n');
    // await electronAPI.writeFile(fileListPath, fileListContent);


    // Run FFmpeg to join the files
    const outputPath = `${tempDir}\\${outputFileName.value}`;
    const result = await electronAPI.mergeAudioFiles(fileList, outputPath,JSON.parse(JSON.stringify(srtItems.value)));

    if (result.success) {
      // Provide download link for the combined file
      message.success('MP3 files combined successfully');

      // Open the file in the default player
      await electronAPI.openFile(outputPath);
    } else {
      console.error('Error combining MP3 files:', result);

      // Display a more detailed error message
      let errorMessage = 'Error combining MP3 files';
      if (result.error) {
        errorMessage += ': ' + result.error;
      }
      if (result.details) {
        errorMessage += '\nDetails: ' + result.details;
      }
      if (result.stack) {
        console.error('Error stack:', result.stack);
      }

      // Show error message with more details
      message.error(errorMessage);
    }
  } catch (error) {
    console.error('Error joining MP3 files:', error);
    message.error('Error joining MP3 files: ' + error.message);
  } finally {
    isJoining.value = false;
  }
}



function setCurrentPlayingSubtitleId(id) {
  srtStore.currentPlayingSubtitleId = id;

  // Calculate which page this subtitle should be on
  const targetPage = Math.floor(id / pageSize.value) + 1;

  // Check if the subtitle is on the current page
  if (targetPage !== currentPage.value) {
    // If not, navigate to the correct page
    currentPage.value = targetPage;
    console.log(`Navigated to page ${targetPage} for subtitle ID ${id}`);
  }

  // Check if this is the last item on the current page
  const isLastItemOnPage = (id + 1) % pageSize.value === 0;
  const hasNextPage = targetPage < totalPages.value;

  // If it's the last item and there's a next page, prepare to move to next page on next subtitle
  if (isLastItemOnPage && hasNextPage) {
    console.log(`Subtitle ${id} is the last item on page ${targetPage}, next subtitle will be on page ${targetPage + 1}`);
  }
}

function setLayoutMode(mode) {
  srtStore.layoutMode = mode;
}

// Function to handle selecting an SRT file from the list
function handleSelectSrt(srtList) {
  if (!srtList || !srtList.items || srtList.items.length === 0) {
    message.error('Selected SRT file has no items');
    return;
  }

  // Reset current state
  srtItems.value = [];
  selectedItems.value = [];
  currentPage.value = 1;

  // Set the selected SRT file items
  srtItems.value = [...srtList.items];

  // Set the file name for display
  const fileName = srtList.name.split('_')[0];
  srtFile.value = { name: fileName };

  message.success(`Loaded ${srtItems.value.length} items from selected SRT file`);
}

// Function to handle importing a new SRT file
function handleImportNew() {
  openFileInput();
}


</script>

<template>
  <!-- Container chiếm full height, có scroll riêng -->
  <div class="flex-1 flex flex-col bg-gray-900 text-white">
    <!-- Header - cố định không scroll -->
    <div class="flex-shrink-0 p-4 border-b border-gray-700">
      <div class="flex justify-between items-center">
        <h2 class="text-lg font-medium text-white">SRT Table Processor</h2>
        <div class="flex items-center space-x-2">
          <input
            type="file"
            id="srt-table-file"
            accept=".srt"
            @change="handleFileUpload"
            class="hidden"
          />
          <a-button @click="openFileInput" :loading="isProcessing">
            Import New SRT File
          </a-button>
          <SrtLists
            buttonText="Select Existing SRT"
            @select="handleSelectSrt"
            @import-new="handleImportNew"
          />
          <a-button @click="resetFileSelected" :disabled="!srtFile">
            Reset
          </a-button>
          <span v-if="srtFile" class="text-sm text-gray-400">
            Current: {{ srtFile.name }}
          </span>
        </div>
      </div>
    </div>

    <!-- Content area - có thể scroll -->
    <div class="flex-1 min-h-0 overflow-auto">
      <div class="p-4">

      <!-- Table of SRT items -->
      <div v-if="srtItems.length > 0" class="mt-4">
        <div class="flex justify-between items-center mb-2">
          <SubtitlePreview
            :subtitles="srtItems"
            :isTranslating="false"
            :selectedMode="srtStore.layoutMode"
            @modeChange="setLayoutMode"
            :currentPlayingSubtitleId="srtStore.currentPlayingSubtitleId"
            @subtitle-change="setCurrentPlayingSubtitleId"
        />
        </div>
        <div class="flex justify-between items-center mb-2">
          <div class="flex items-center space-x-4">
            <div class="flex items-center">
              <a-checkbox @change="selectAll" class="mr-2"></a-checkbox>
              <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                Select All
              </span>
            </div>

            <div v-if="selectedItems.length > 0" class="flex flex-col space-y-2">
              <div class="flex items-center">
                <span class="text-sm text-gray-700 dark:text-gray-300 mr-2 w-32">
                  Apply voice:
                </span>
                <a-select
                  v-model:value="bulkSpeaker"
                  style="width: 150px"
                  size="small"
                  placeholder="Select voice"
                >
                  <a-select-option
                    v-for="(speaker, idx) in speakers"
                    :key="idx"
                    :value="speaker.id"
                  >
                    {{ speaker.name }}
                  </a-select-option>
                </a-select>
                <a-button
                  type="primary"
                  size="small"
                  class="ml-2"
                  @click="applyBulkVoiceChange"
                  :disabled="!bulkSpeaker || selectedItems.length === 0"
                >
                  Apply
                </a-button>
              </div>

              <div class="flex items-center">
                <span class="text-sm text-gray-700 dark:text-gray-300 mr-2 w-32">
                  Apply speed:
                </span>
                <div class="flex items-center" style="width: 150px">
                  <a-slider
                    v-model:value="bulkSpeechRate"
                    :min="-50"
                    :max="100"
                    :step="10"
                    size="small"
                    class="flex-1 mr-2"
                  />
                  <span class="text-xs w-10">{{ formatSpeechRate(bulkSpeechRate) }}</span>
                </div>
                <a-button
                  type="primary"
                  size="small"
                  class="ml-2"
                  @click="applyBulkSpeechRateChange"
                  :disabled="selectedItems.length === 0"
                >
                  Apply
                </a-button>
              </div>
            </div>
          </div>
          <!-- generate -->
          <div class="flex space-x-2">
          <a-button
            type="primary"
            @click="generateAllTTS"
            :loading="isGenerating"
            :disabled="srtItems.length === 0"
          >
            {{ isGenerating ? `Generating (${generatedCount}/${totalItems})` :
              selectedItems.length > 0 ? `Generate TTS (${selectedItems.length} selected)` : 'Generate TTS' }}
          </a-button>
          <!-- stop -->
          <a-button
            type="primary"
            @click="stopGeneratingTTS"
            :disabled="!isGenerating"
          >
            Stop
          </a-button>
          </div>
        </div>
        <!-- <SubtitleTable
            v-if="srtItems.length > 0"
            :subtitles="srtItems"
            onRetry={handleRetrySubtitle}
            onRetryBatch={handleRetryBatch}
            onUpdateTranslation={handleUpdateSubtitle}
            translating={translating}
            batchSize={BATCH_SIZE}
            highlightedSubtitleId={currentPlayingSubtitleId}
            onSuggestTranslation={handleSuggestBetterTranslation}
        /> -->
        <!-- Table with fixed header -->
        <div class="h-[400px] overflow-auto border border-gray-200 dark:border-gray-700 rounded-lg">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th scope="col" class="w-10 px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"></th>
                <th scope="col" class="w-10 px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">ID</th>
                <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Text</th>
                <th scope="col" class="w-40 px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Translated Text</th>
                <th scope="col" class="w-40 px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Voice</th>
                <th scope="col" class="w-48 px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Speed</th>
                <th scope="col" class="w-64 px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <!-- const isPlaying = subtitle.id === highlightedSubtitleId; -->
            <tbody class="bg-white dark:bg-gray-700 divide-y divide-gray-200 dark:divide-gray-600">
              <tr v-for="(item, pIndex) in paginatedItems" :key="pIndex" :class="[srtStore.currentPlayingSubtitleId === (currentPage - 1) * pageSize + pIndex ? 'bg-yellow-100 dark:bg-yellow-900' : '']" @click="setCurrentPlayingSubtitleId((currentPage - 1) * pageSize + pIndex)">
                <td class="px-3 py-4 whitespace-nowrap">
                  <a-checkbox
                    class="item-checkbox"
                    :checked="selectedItems.includes((currentPage - 1) * pageSize + pIndex)"
                    @change="() => toggleItemSelection((currentPage - 1) * pageSize + pIndex)"
                  ></a-checkbox>
                </td>
                <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">
                  {{ item.index }} <span class="text-xs text-gray-500">(#{{ (currentPage - 1) * pageSize + pIndex + 1 }})</span>
                </td>
                <td class="px-3 py-4 text-sm text-gray-700 dark:text-gray-300">
                  <div class="line-clamp-2">{{ item.text }}</div>
                  <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {{ formatTime(item.startTime) }} - {{ formatTime(item.endTime) }}
                  </div>
                </td>
                <td class="px-3 py-4 text-sm text-gray-700 dark:text-gray-300">
                  <div class="line-clamp-25">{{ item.translatedText }}</div>
                </td>
                <td class="px-3 py-4 whitespace-nowrap">
                  <a-select
                    v-model:value="item.selectedSpeaker"
                    class="w-full"
                    size="small"
                    placeholder="Select voice"
                  >
                    <a-select-option
                      v-for="(speaker, idx) in speakers"
                      :key="idx"
                      :value="speaker.id"
                    >
                      {{ speaker.name }}
                    </a-select-option>
                  </a-select>
                </td>
                <td class="px-3 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <a-slider
                      v-model:value="item.speechRate"
                      :min="-50"
                      :max="100"
                      :step="10"
                      class="flex-1 mr-2"
                    />
                    <span class="text-xs w-10">{{ formatSpeechRate(item.speechRate) }}</span>
                  </div>
                </td>
                <td class="px-3 py-4 whitespace-nowrap">
                  <div class="flex items-center space-x-2">
                    <a-button
                      type="primary"
                      size="small"
                      @click="generateTTSForItem((currentPage - 1) * pageSize + pIndex)"
                      :disabled="isGenerating"
                      :loading="itemLoadingStates[(currentPage - 1) * pageSize + pIndex]"
                    >
                      Generate
                    </a-button>
                    <!-- <audio v-if="item.audioUrl" :src="item.audioUrl" controls class="h-8 w-24"></audio> -->
                    <Audio v-if="item.audioUrl"  :item="item" />
                    <a-button
                      v-if="item.audioUrl"
                      type="default"
                      size="small"
                      @click="downloadAudio(item.audioUrl, item.index)"
                    >
                      <i class="fas fa-download"></i>
                    </a-button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div v-if="totalPages > 1" class="flex justify-between items-center mt-4">
          <div class="text-sm text-gray-700 dark:text-gray-300">
            Showing {{ (currentPage - 1) * pageSize + 1 }} to {{ Math.min(currentPage * pageSize, srtItems.length) }} of {{ srtItems.length }} items
          </div>
          <div class="flex space-x-2">
            <a-button
              size="small"
              @click="currentPage = Math.max(1, currentPage - 1)"
              :disabled="currentPage === 1"
            >
              Previous
            </a-button>
            <a-button
              v-for="page in Math.min(5, totalPages)"
              :key="page"
              size="small"
              :type="page === currentPage ? 'primary' : 'default'"
              @click="currentPage = page"
            >
              {{ page }}
            </a-button>
            <a-button
              size="small"
              @click="currentPage = Math.min(totalPages, currentPage + 1)"
              :disabled="currentPage === totalPages"
            >
              Next
            </a-button>
          </div>
        </div>

        <!-- Join MP3 Files -->
        <div class="mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <h3 class="text-md font-medium text-gray-900 dark:text-white mb-3">Join MP3 Files</h3>
          <div class="flex items-center space-x-4">
            <a-input
              v-model:value="outputFileName"
              placeholder="Output filename"
              class="w-64"
              :disabled="isJoining"
            />
            <a-button
              type="primary"
              @click="joinMp3FilesV2"
              :loading="isJoining"
              :disabled="!hasGeneratedAudios"
            >
              Join MP3 Files
            </a-button>
          </div>
          <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
            This will combine all generated audio files into a single MP3 file using FFmpeg.
          </p>
        </div>
      </div>

      <!-- Empty state - No items in SRT file -->
      <div v-else-if="!isProcessing && srtFile && srtItems.length === 0" class="text-center py-8 text-gray-500 dark:text-gray-400">
        No items found in the SRT file.
      </div>

      <!-- Empty state - No SRT file selected -->
      <div v-else-if="!isProcessing && !srtFile" class="text-center py-8 text-gray-500 dark:text-gray-400">
        <p class="mb-4">No SRT file selected.</p>
        <div class="flex justify-center space-x-4 mb-2">
          <a-button @click="openFileInput">Import New SRT File</a-button>
          <SrtLists
            buttonText="Select Existing SRT"
            @select="handleSelectSrt"
            @import-new="handleImportNew"
          />
        </div>
          <DragDropUpload
            accept=".srt,application/x-subrip,text/srt"
            :max-size="100 * 1024 * 1024"
            @files-selected="handleFileUpload"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
