import { ChatOpenAI } from '@langchain/openai';
import { ChatGoogleGenerativeAI } from '@langchain/google-genai';
import { HumanMessage, AIMessage, SystemMessage } from '@langchain/core/messages';

class AIClient {
  constructor({
    provider, // "openai" hoặc "gemini"
    apiKey, // API key (OpenAI hoặc Google)
    model, // Tên model, tuỳ provider
    proxyAddress = '',
    proxy = '',
  }) {
    this.provider = provider;
    this.configParams = {};

    // Cấu hình basePath/proxy
    if (proxyAddress) {
      this.configParams.basePath = proxyAddress;
    }
    if (proxy) {
      this.configParams.proxy = {
        http: 'http://' + proxy,
        https: 'http://' + proxy,
      };
    }

    // Khởi tạo chat client tuỳ provider
    if (provider === 'openai') {
      this.chat = new ChatOpenAI(
        {
          modelName: model || 'gpt-3.5-turbo',
          openAIApiKey: apiKey,
          maxRetries: 2,
          temperature: 0.6,
        },
        this.configParams,
      );
    } else if (provider === 'gemini') {
      this.chat = new ChatGoogleGenerativeAI(
        {
          model: model || 'gemini-pro',
          apiKey: apiKey,
          maxRetries: 2,
          temperature: 0.6,
        },
        this.configParams,
      );
    } else if (provider === 'deepseek') {
      this.chat = new ChatOpenAI(
        {
          modelName: model || 'deepseek-chat',
          openAIApiKey: apiKey,
          maxRetries: 2,
          temperature: 0.6,
          configuration: {
            baseURL: 'https://api.deepseek.com/v1',
          },
        },
        this.configParams,
      );
    } else if (provider === 'openrouter') {
      this.chat = new ChatOpenAI(
        {
          modelName: model || 'deepseek/deepseek-r1:free',
          openAIApiKey: apiKey,
          maxRetries: 2,
          temperature: 0.6,
          configuration: {
            baseURL: 'https://openrouter.ai/api/v1',
          },
        },
        this.configParams,
      );
    } else {
      throw new Error('Unsupported AI provider!');
    }
  }

  async call(chat_msg) {
    return await this.chat.invoke(chat_msg);
  }

  async train(prompt, list) {
    let chat_msg = [];
    chat_msg.push(new SystemMessage(prompt));
    list.forEach((item, index) => {
      if (item.type === 0) {
        chat_msg.push(new HumanMessage(item.text));
      } else {
        if (index !== list.length - 1) {
          chat_msg.push(new AIMessage(item.text));
        }
      }
    });
    return await this.chat.invoke(chat_msg);
  }

  async getText(word) {
    let chat_msg = [];
    let rule_txt = 'Viết lại nghĩa và diễn đạt câu theo cách khác:' + word;
    chat_msg.push(new HumanMessage(rule_txt));
    return await this.chat.invoke(chat_msg);
  }

  async getEnText(word) {
    let chat_msg = [];
    let rule_txt =
      'Please translate the following content into English. Just output the content, no explanation is needed:\n' +
      word;
    chat_msg.push(new HumanMessage(rule_txt));
    return await this.chat.invoke(chat_msg);
  }
}

export default AIClient;
