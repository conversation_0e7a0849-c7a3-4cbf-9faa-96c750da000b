<template>
  <div class="prompt-template-indicator">
    <a-tooltip :title="tooltipContent">
      <a-tag 
        :color="selectedTemplate?.isDefault ? 'gold' : 'blue'" 
        class="cursor-pointer"
        @click="$emit('open-manager')"
      >
        <template #icon>
          <FileTextOutlined />
        </template>
        {{ selectedTemplate?.name || 'No Template' }}
      </a-tag>
    </a-tooltip>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { usePromptStore } from '@/stores/promptStore'
import { FileTextOutlined } from '@ant-design/icons-vue'

// Emits
defineEmits(['open-manager'])

const promptStore = usePromptStore()

// Computed properties
const selectedTemplate = computed(() => promptStore.selectedTemplate)

const tooltipContent = computed(() => {
  if (!selectedTemplate.value) return 'Không có template nào được chọn'
  
  return `Template: ${selectedTemplate.value.name}
Mô tả: ${selectedTemplate.value.description}
Loại: ${selectedTemplate.value.isDefault ? 'Mặc định' : 'Tùy chỉnh'}

Click để mở quản lý templates`
})
</script>

<style scoped>
.prompt-template-indicator .ant-tag {
  transition: all 0.3s ease;
}

.prompt-template-indicator .ant-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
</style>
