import { defineStore } from 'pinia'
import { message, Modal } from 'ant-design-vue'

export const useErrorStore = defineStore('error', () => {
  // Actions
  const handleError = (error: any, type: 'message' | 'alert' = 'message') => {
    console.error('Error:', error)
    
    const errorMessage = getErrorMessage(error)
    
    if (type === 'alert') {
      Modal.error({
        title: 'Error',
        content: errorMessage,
        okText: 'OK'
      })
    } else {
      message.error(errorMessage)
    }
  }

  const getErrorMessage = (error: any): string => {
    if (typeof error === 'string') {
      return error
    }
    
    if (error?.message) {
      return error.message
    }
    
    if (error?.response?.data?.message) {
      return error.response.data.message
    }
    
    if (error?.response?.statusText) {
      return error.response.statusText
    }
    
    return 'An unknown error occurred'
  }

  const showSuccess = (msg: string) => {
    message.success(msg)
  }

  const showWarning = (msg: string) => {
    message.warning(msg)
  }

  const showInfo = (msg: string) => {
    message.info(msg)
  }

  return {
    // Actions
    handleError,
    getErrorMessage,
    showSuccess,
    showWarning,
    showInfo
  }
})
