const path = require('path');
const nodeExternals = require('webpack-node-externals');
const Dotenv = require('dotenv-webpack');

module.exports = {
    mode: 'production',
    target: 'electron-main', // Build cho Electron Main Process
    entry: {
        main: './electron/main.js',
        preload: './electron/preload.js'
    },
    plugins: [
        new Dotenv({
            path: path.resolve(__dirname, '.env'), // Chỉ định path tới file .env
            systemvars: true, // Load tất cả system variables
            safe: false // Load .env.example (nếu có)
        })
    ],
    output: {
        path: path.resolve(__dirname, 'electron-build'),
        filename: '[name].js',
    },
    externals: [
        nodeExternals(),
        {
        'puppeteer': 'commonjs puppeteer',
        'puppeteer-core': 'commonjs puppeteer-core',
        'puppeteer-extra': 'commonjs puppeteer-extra',
        'puppeteer-extra-plugin-stealth': 'commonjs puppeteer-extra-plugin-stealth',
        'playwright': 'commonjs playwright'
        }
    ], // Loại bỏ module hệ thống khỏi bundle
    module: {
        rules: [
            {
                test: /\.js$/,
                exclude: /node_modules/,
                use: {
                    loader: 'babel-loader',
                    options: { presets: ['@babel/preset-env'] },
                },
            },
        ],
    },
};
