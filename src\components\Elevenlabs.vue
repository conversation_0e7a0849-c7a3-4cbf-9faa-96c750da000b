<template>
  <div class="flex-1 flex flex-col bg-gray-900 text-white">
    <!-- Header -->
    <div class="flex-shrink-0 p-4 border-b border-gray-700">
      <div class="flex justify-between items-center">
        <h2 class="text-lg font-medium text-white">ElevenLabs TTS</h2>
        <div class="flex items-center space-x-2">
          <a-button @click="loadAllVoices" :loading="elevenlabsStore.loadingVoices" type="primary">
            Load All Voices
          </a-button>
          <a-button @click="resetAll" :disabled="isProcessing">
            Reset
          </a-button>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="flex-1 flex flex-col overflow-hidden">
      <!-- Configuration Panel -->
      <div class="flex-shrink-0 p-4 border-b border-gray-700">
        <a-row :gutter="16">
          <!-- API Keys Management -->
          <a-col :span="8">
            <div class="space-y-2">
              <div class="flex items-center justify-between">
                <label class="text-sm font-medium text-gray-300">API Keys ({{ elevenlabsStore.apiKeyCount }})</label>
                <a-button @click="showApiKeyModal = true" size="small" type="primary">
                  Manage Keys
                </a-button>
              </div>
              <a-input-password
                v-model:value="newApiKey"
                placeholder="Enter new API Key"
                class="bg-gray-800 border-gray-600"
                @pressEnter="addApiKey"
              />
              <div class="text-xs text-gray-400">
                Current: {{ elevenlabsStore.currentApiKey ? '***' + elevenlabsStore.currentApiKey.slice(-8) : 'None' }}
              </div>
            </div>
          </a-col>

          <!-- Voice Search -->
          <a-col :span="8">
            <div class="space-y-2">
              <label class="text-sm font-medium text-gray-300">Search Voice</label>
              <a-input-search
                v-model:value="voiceSearch"
                placeholder="Search by name (e.g. 'viet')"
                class="bg-gray-800 border-gray-600"
                @search="searchVoices"
                @pressEnter="searchVoices"
                :loading="elevenlabsStore.loadingSearch"
              />
              <div class="text-xs text-gray-400">
                {{ elevenlabsStore.searchResults.length > 0 ? `Found ${elevenlabsStore.searchResults.length} voices` : 'Enter search term' }}
              </div>
            </div>
          </a-col>

          <!-- Voice Selection -->
          <a-col :span="8">
            <div class="space-y-2">
              <label class="text-sm font-medium text-gray-300">Selected Voice</label>
              <a-select
                v-model:value="elevenlabsStore.selectedVoice"
                placeholder="Select a voice"
                class="w-full"
                :options="elevenlabsStore.voiceOptions"
                show-search
                :filter-option="false"
                @change="onVoiceChange"
              />
              <div class="text-xs text-gray-400">
                {{ selectedVoiceInfo }}
              </div>
            </div>
          </a-col>
        </a-row>

        <a-row :gutter="16" class="mt-4">
          <!-- Model Selection -->
          <a-col :span="6">
            <div class="space-y-2">
              <label class="text-sm font-medium text-gray-300">Model</label>
              <a-select
                v-model:value="elevenlabsStore.selectedModel"
                placeholder="Select model"
                class="w-full"
                :options="modelOptions"
              />
            </div>
          </a-col>

          <!-- Speed -->
          <a-col :span="6">
            <div class="space-y-2">
              <label class="text-sm font-medium text-gray-300">Speed ({{ elevenlabsStore.speed }})</label>
              <a-slider
                v-model:value="elevenlabsStore.speed"
                :min="0.25"
                :max="4.0"
                :step="0.05"
                :tooltip-formatter="(value) => `${value}x`"
              />
            </div>
          </a-col>

          <!-- Stability -->
          <a-col :span="6">
            <div class="space-y-2">
              <label class="text-sm font-medium text-gray-300">Stability ({{ elevenlabsStore.stability }})</label>
              <a-slider
                v-model:value="elevenlabsStore.stability"
                :min="0"
                :max="1"
                :step="0.01"
                :tooltip-formatter="(value) => value.toFixed(2)"
              />
            </div>
          </a-col>

          <!-- Clarity + Similarity Enhancement -->
          <a-col :span="6">
            <div class="space-y-2">
              <label class="text-sm font-medium text-gray-300">Clarity ({{ elevenlabsStore.clarityBoost }})</label>
              <a-slider
                v-model:value="elevenlabsStore.clarityBoost"
                :min="0"
                :max="1"
                :step="0.01"
                :tooltip-formatter="(value) => value.toFixed(2)"
              />
            </div>
          </a-col>
        </a-row>

        <!-- Proxy Configuration -->
        <a-row :gutter="16" class="mt-4">
          <a-col :span="12">
            <div class="space-y-2">
              <div class="flex items-center space-x-2">
                <a-checkbox v-model:checked="elevenlabsStore.useProxy">Use Proxy</a-checkbox>
                <a-button
                  v-if="elevenlabsStore.useProxy"
                  @click="loadProxyFile"
                  size="small"
                  :loading="loadingProxy"
                >
                  Load Proxy File
                </a-button>
              </div>
              <div v-if="elevenlabsStore.useProxy" class="text-xs text-gray-400">
                {{ elevenlabsStore.proxyList.length }} proxies loaded
              </div>
            </div>
          </a-col>

          <!-- Concurrency -->
          <a-col :span="6">
            <div class="space-y-2">
              <label class="text-sm font-medium text-gray-300">Concurrency</label>
              <a-input-number
                v-model:value="elevenlabsStore.concurrency"
                :min="1"
                :max="10"
                class="w-full"
              />
            </div>
          </a-col>

          <!-- Output Format -->
          <a-col :span="6">
            <div class="space-y-2">
              <label class="text-sm font-medium text-gray-300">Output Format</label>
              <a-select
                v-model:value="elevenlabsStore.outputFormat"
                class="w-full"
                :options="formatOptions"
              />
            </div>
          </a-col>
        </a-row>
      </div>

      <!-- File Import and Controls -->
      <div class="flex-shrink-0 p-4 border-b border-gray-700">
        <div class="flex justify-between items-center">
          <div class="flex items-center space-x-4">
            <input
              type="file"
              id="file-input"
              accept=".srt,.txt"
              @change="handleFileUpload"
              class="hidden"
            />
            <a-button @click="openFileInput" :disabled="isProcessing">
              Import SRT/TXT File
            </a-button>
            <span v-if="importedFile" class="text-sm text-gray-400">
              {{ importedFile.name }}
            </span>
          </div>

          <div class="flex items-center space-x-2">
            <a-button
              @click="startProcessing"
              :disabled="!canStartProcessing"
              :loading="isProcessing"
              type="primary"
            >
              {{ isProcessing ? 'Processing...' : 'Start Task' }}
            </a-button>
            <a-button
              @click="stopProcessing"
              :disabled="!isProcessing"
              danger
            >
              Stop Task
            </a-button>
          </div>
        </div>
      </div>

      <!-- Tabs for Table and Text Input -->
      <div class="flex-1 flex flex-col overflow-hidden">
        <a-tabs v-model:activeKey="activeTab" class="flex-1 flex flex-col">
          <!-- Table Tab -->
          <a-tab-pane key="table" tab="Task Table" class="flex-1 flex flex-col">
            <div class="flex-1 overflow-auto p-4">
              <a-table
                :columns="tableColumns"
                :data-source="taskItems"
                :pagination="false"
                :scroll="{ y: 400 }"
                size="small"
                class="bg-gray-800"
                :row-class-name="() => 'bg-gray-800 text-white'"
              >
                <template #bodyCell="{ column, record, index }">
                  <template v-if="column.key === 'id'">
                    {{ index + 1 }}
                  </template>
                  <template v-else-if="column.key === 'content'">
                    <div class="max-w-xs truncate" :title="record.content">
                      {{ record.content }}
                    </div>
                  </template>
                  <template v-else-if="column.key === 'voice'">
                    <a-select
                      v-model:value="record.voice"
                      size="small"
                      class="w-full"
                      :options="elevenlabsStore.voiceOptions"
                      @change="updateTaskVoice(index, $event)"
                    />
                  </template>
                  <template v-else-if="column.key === 'status'">
                    <a-tag
                      :color="getStatusColor(record.status)"
                      class="text-xs"
                    >
                      {{ record.status }}
                    </a-tag>
                  </template>
                  <template v-else-if="column.key === 'actions'">
                    <div class="flex space-x-1">
                      <a-button
                        size="small"
                        @click="playAudio(record)"
                        :disabled="!record.output"
                      >
                        Play
                      </a-button>
                      <a-button
                        size="small"
                        @click="retryTask(index)"
                        :disabled="record.status === 'processing'"
                      >
                        Retry
                      </a-button>
                    </div>
                  </template>
                </template>
              </a-table>
            </div>
          </a-tab-pane>

          <!-- Text Input Tab -->
          <a-tab-pane key="text" tab="Text Input" class="flex-1 flex flex-col">
            <div class="flex-1 flex flex-col p-4">
              <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium">Text Input</h3>
                <div class="flex space-x-2">
                  <a-button @click="combineAllContent" size="small">
                    Combine All Content
                  </a-button>
                  <a-button @click="clearTextInput" size="small">
                    Clear
                  </a-button>
                </div>
              </div>

              <a-textarea
                v-model:value="textInput"
                placeholder="Enter text here or combine all content from table..."
                :rows="15"
                class="flex-1 bg-gray-800 border-gray-600 text-white"
              />

              <div class="mt-4 flex justify-between items-center">
                <div class="text-sm text-gray-400">
                  Characters: {{ textInput.length }}
                </div>
                <div class="flex space-x-2">
                  <a-button
                    @click="processTextInput"
                    :disabled="!textInput.trim() || !elevenlabsStore.selectedVoice"
                    :loading="isProcessingText"
                    type="primary"
                  >
                    Generate Audio
                  </a-button>
                  <a-button
                    @click="playTextAudio"
                    :disabled="!textAudioUrl"
                    type="default"
                  >
                    Play Audio
                  </a-button>
                  <a-button
                    @click="downloadTextAudio"
                    :disabled="!textAudioUrl"
                    type="default"
                  >
                    Download
                  </a-button>
                </div>
              </div>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>

    <!-- API Keys Management Modal -->
    <a-modal
      v-model:open="showApiKeyModal"
      title="Manage API Keys"
      width="600px"
      @ok="showApiKeyModal = false"
    >
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium mb-2">Add New API Key</label>
          <div class="flex space-x-2">
            <a-input-password
              v-model:value="newApiKey"
              placeholder="Enter ElevenLabs API Key"
              @pressEnter="addApiKey"
            />
            <a-button @click="addApiKey" type="primary">Add</a-button>
          </div>
        </div>

        <div>
          <label class="block text-sm font-medium mb-2">Current API Keys ({{ elevenlabsStore.apiKeyCount }})</label>
          <div class="space-y-2 max-h-60 overflow-y-auto">
            <div
              v-for="(key, index) in elevenlabsStore.apiKeys"
              :key="index"
              class="flex items-center justify-between p-3 border rounded"
              :class="index === elevenlabsStore.currentApiKeyIndex ? 'border-blue-500 bg-blue-50' : 'border-gray-300'"
            >
              <div class="flex-1">
                <div class="font-mono text-sm">
                  {{ key.slice(0, 8) }}...{{ key.slice(-8) }}
                </div>
                <div class="text-xs text-gray-500">
                  {{ index === elevenlabsStore.currentApiKeyIndex ? 'Current' : `Index ${index}` }}
                </div>
              </div>
              <div class="flex space-x-2">
                <a-button
                  @click="testApiKeyAtIndex(index)"
                  size="small"
                  type="default"
                >
                  Test
                </a-button>
                <a-button
                  v-if="index !== elevenlabsStore.currentApiKeyIndex"
                  @click="elevenlabsStore.currentApiKeyIndex = index"
                  size="small"
                >
                  Use
                </a-button>
                <a-button
                  @click="elevenlabsStore.removeApiKey(index)"
                  size="small"
                  danger
                >
                  Remove
                </a-button>
              </div>
            </div>
          </div>
        </div>

        <div class="text-sm text-gray-600">
          <p><strong>Tips:</strong></p>
          <ul class="list-disc list-inside space-y-1">
            <li>Add multiple API keys for load balancing</li>
            <li>System will automatically rotate keys on failures</li>
            <li>Current key is used for voice loading and TTS generation</li>
          </ul>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, reactive, onMounted, watch } from 'vue';
import { message } from 'ant-design-vue';
import { parseSRT } from '@/lib/utils';
import { useElevenlabsStore } from '@/stores/elevenlabsStore';
import axios from 'axios';

// Store
const elevenlabsStore = useElevenlabsStore();

// Reactive state
const newApiKey = ref('');
const voiceSearch = ref('');
const activeTab = ref('table');
const textInput = ref('');
const textAudioUrl = ref('');
const showApiKeyModal = ref(false);

// Loading states
const loadingProxy = ref(false);
const isProcessing = ref(false);
const isProcessingText = ref(false);

// Data
const importedFile = ref(null);
const taskItems = ref([]);

// ElevenLabs models
const modelOptions = [
  { value: 'eleven_multilingual_v2', label: 'Multilingual V2' },
  { value: 'eleven_multilingual_v1', label: 'Multilingual V1' },
  { value: 'eleven_monolingual_v1', label: 'Monolingual V1' },
  { value: 'eleven_english_v1', label: 'English V1' },
  { value: 'eleven_turbo_v2', label: 'Turbo V2' },
];

// Output format options
const formatOptions = [
  { value: 'mp3_44100_128', label: 'MP3 44.1kHz 128kbps' },
  { value: 'mp3_22050_32', label: 'MP3 22.05kHz 32kbps' },
  { value: 'pcm_16000', label: 'PCM 16kHz' },
  { value: 'pcm_22050', label: 'PCM 22.05kHz' },
  { value: 'pcm_24000', label: 'PCM 24kHz' },
  { value: 'pcm_44100', label: 'PCM 44.1kHz' },
];

// Table columns
const tableColumns = [
  { title: 'ID', dataIndex: 'id', key: 'id', width: 60 },
  { title: 'Output', dataIndex: 'output', key: 'output', width: 100 },
  { title: 'Timing', dataIndex: 'timing', key: 'timing', width: 120 },
  { title: 'Content', dataIndex: 'content', key: 'content', ellipsis: true },
  { title: 'Voice', dataIndex: 'voice', key: 'voice', width: 150 },
  { title: 'Status', dataIndex: 'status', key: 'status', width: 100 },
  { title: 'Actions', key: 'actions', width: 120 },
];

// Computed properties
const canStartProcessing = computed(() => {
  return elevenlabsStore.hasValidApiKey && elevenlabsStore.selectedVoice && taskItems.value.length > 0 && !isProcessing.value;
});

const selectedVoiceInfo = computed(() => {
  if (!elevenlabsStore.selectedVoice) return 'No voice selected';

  const allVoices = [...elevenlabsStore.voices, ...elevenlabsStore.searchResults];
  const voice = allVoices.find(v => v.voice_id === elevenlabsStore.selectedVoice);

  if (voice) {
    const labels = voice.labels || {};
    return `${labels.language || 'Unknown'} | ${labels.gender || 'Unknown'} | ${labels.age || 'Unknown'}`;
  }

  return 'Voice info not available';
});

// Functions
const addApiKey = async () => {
  if (newApiKey.value.trim()) {
    const keyToAdd = newApiKey.value.trim();

    // Test API key first
    message.loading('Testing API key...', 0);
    const testResult = await elevenlabsStore.testApiKey(keyToAdd);
    message.destroy();

    if (!testResult.valid) {
      message.error(`Invalid API key: ${testResult.error} (Status: ${testResult.status})`);
      return;
    }

    const success = elevenlabsStore.addApiKey(keyToAdd);
    if (success) {
      newApiKey.value = '';
      message.success('API key added and verified successfully!');

      // Debug: Log current state
      console.log('API Key added. Current state:', {
        apiKeys: elevenlabsStore.apiKeys,
        currentIndex: elevenlabsStore.currentApiKeyIndex,
        currentKey: elevenlabsStore.currentApiKey,
        hasValidKey: elevenlabsStore.hasValidApiKey
      });
    }
  }
};

const testApiKeyAtIndex = async (index) => {
  const apiKey = elevenlabsStore.apiKeys[index];
  if (!apiKey) return;

  message.loading('Testing API key...', 0);
  const testResult = await elevenlabsStore.testApiKey(apiKey);
  message.destroy();

  if (testResult.valid) {
    message.success(`API key is valid! User: ${testResult.data?.subscription?.character_limit || 'Unknown'} characters`);
  } else {
    message.error(`API key test failed: ${testResult.error} (Status: ${testResult.status})`);
  }
};

const loadAllVoices = async () => {
  if (!elevenlabsStore.hasValidApiKey) {
    message.error('Please add API key first');
    return;
  }
  await elevenlabsStore.loadAllVoices();
};

const searchVoices = async () => {
  if (!elevenlabsStore.hasValidApiKey) {
    message.error('Please add API key first');
    return;
  }
  await elevenlabsStore.searchVoices(voiceSearch.value);
};

const onVoiceChange = (voiceId) => {
  // Update all pending tasks with new voice
  taskItems.value.forEach(task => {
    if (task.status === 'pending' && !task.voice) {
      task.voice = voiceId;
    }
  });
};

const loadProxyFile = async () => {
  loadingProxy.value = true;
  try {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.txt,.json';

    input.onchange = async (e) => {
      const file = e.target.files[0];
      if (file) {
        await elevenlabsStore.loadProxyFile(file);
      }
      loadingProxy.value = false;
    };

    input.click();
  } catch (error) {
    message.error('Failed to load proxy file');
    loadingProxy.value = false;
  }
};

const openFileInput = () => {
  document.getElementById('file-input').click();
};

const handleFileUpload = async (event) => {
  const file = event.target.files[0];
  if (!file) return;

  importedFile.value = file;

  try {
    const text = await file.text();

    if (file.name.endsWith('.srt')) {
      const srtData = parseSRT(text);
      taskItems.value = srtData.map((item, index) => ({
        id: index + 1,
        output: '',
        timing: `${item.start} --> ${item.end}`,
        content: item.text,
        voice: elevenlabsStore.selectedVoice || '',
        status: 'pending'
      }));
    } else if (file.name.endsWith('.txt')) {
      // Split text by lines or sentences
      const lines = text.split('\n').filter(line => line.trim());
      taskItems.value = lines.map((line, index) => ({
        id: index + 1,
        output: '',
        timing: '',
        content: line.trim(),
        voice: elevenlabsStore.selectedVoice || '',
        status: 'pending'
      }));
    }

    message.success(`Imported ${taskItems.value.length} items`);
  } catch (error) {
    message.error('Failed to parse file: ' + error.message);
  }
};

const startProcessing = async () => {
  if (!canStartProcessing.value) return;

  isProcessing.value = true;
  const pendingTasks = taskItems.value.filter(item => item.status === 'pending' || item.status === 'error');

  try {
    // Process tasks with concurrency control
    const chunks = [];
    for (let i = 0; i < pendingTasks.length; i += elevenlabsStore.concurrency) {
      chunks.push(pendingTasks.slice(i, i + elevenlabsStore.concurrency));
    }

    for (const chunk of chunks) {
      if (!isProcessing.value) break; // Stop if user clicked stop

      await Promise.all(chunk.map(async (task) => {
        await processTask(task);
      }));
    }

    message.success('Processing completed');
  } catch (error) {
    message.error('Processing failed: ' + error.message);
  } finally {
    isProcessing.value = false;
  }
};

const stopProcessing = () => {
  isProcessing.value = false;
  message.info('Processing stopped');
};

const processTask = async (task) => {
  task.status = 'processing';

  try {
    // Use store's generateTTS method with automatic API key rotation
    const audioData = await elevenlabsStore.generateTTS(task.content, task.voice);

    // Save audio file
    const audioBlob = new Blob([audioData], { type: 'audio/mpeg' });
    const audioUrl = URL.createObjectURL(audioBlob);

    task.output = audioUrl;
    task.status = 'completed';

  } catch (error) {
    console.error('Task processing error:', error);
    task.status = 'error';
    task.error = error.response?.data?.detail || error.message;
  }
};

const retryTask = async (index) => {
  const task = taskItems.value[index];
  task.status = 'pending';
  await processTask(task);
};

const updateTaskVoice = (index, voiceId) => {
  taskItems.value[index].voice = voiceId;
};

const getStatusColor = (status) => {
  switch (status) {
    case 'completed': return 'green';
    case 'processing': return 'blue';
    case 'error': return 'red';
    case 'pending': return 'orange';
    default: return 'default';
  }
};

const playAudio = (record) => {
  if (record.output) {
    const audio = new Audio(record.output);
    audio.play().catch(error => {
      message.error('Failed to play audio: ' + error.message);
    });
  }
};

const combineAllContent = () => {
  const allContent = taskItems.value.map(item => item.content).join('\n\n');
  textInput.value = allContent;
  message.success('Combined all content');
};

const clearTextInput = () => {
  textInput.value = '';
  if (textAudioUrl.value) {
    URL.revokeObjectURL(textAudioUrl.value);
    textAudioUrl.value = '';
  }
};

const processTextInput = async () => {
  if (!textInput.value.trim() || !elevenlabsStore.selectedVoice) return;

  // Debug: Log current state before processing
  console.log('Processing text input:', {
    text: textInput.value.slice(0, 50) + '...',
    selectedVoice: elevenlabsStore.selectedVoice,
    hasValidApiKey: elevenlabsStore.hasValidApiKey,
    currentApiKey: elevenlabsStore.currentApiKey ? `${elevenlabsStore.currentApiKey.slice(0, 8)}...` : 'null',
    apiKeyCount: elevenlabsStore.apiKeyCount
  });

  isProcessingText.value = true;

  try {
    // Use store's generateTTS method
    const audioData = await elevenlabsStore.generateTTS(textInput.value);

    // Save audio URL for later use
    const audioBlob = new Blob([audioData], { type: 'audio/mpeg' });
    textAudioUrl.value = URL.createObjectURL(audioBlob);

    message.success('Audio generated successfully');
  } catch (error) {
    console.error('Text input processing error:', error);
    message.error('Failed to generate audio: ' + (error.response?.data?.detail || error.message));
  } finally {
    isProcessingText.value = false;
  }
};

const playTextAudio = () => {
  if (textAudioUrl.value) {
    const audio = new Audio(textAudioUrl.value);
    audio.play().catch(error => {
      message.error('Failed to play audio: ' + error.message);
    });
  }
};

const downloadTextAudio = () => {
  if (textAudioUrl.value) {
    const link = document.createElement('a');
    link.href = textAudioUrl.value;
    link.download = `elevenlabs_audio_${Date.now()}.mp3`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    message.success('Audio download started');
  }
};

const resetAll = () => {
  // Reset component data
  taskItems.value = [];
  importedFile.value = null;
  textInput.value = '';
  voiceSearch.value = '';
  newApiKey.value = '';

  // Clear text audio
  if (textAudioUrl.value) {
    URL.revokeObjectURL(textAudioUrl.value);
    textAudioUrl.value = '';
  }

  // Reset store data
  elevenlabsStore.resetAll();

  // Reset file input
  const fileInput = document.getElementById('file-input');
  if (fileInput) fileInput.value = '';

  message.success('Reset completed');
};

// Watch for voice selection changes
watch(() => elevenlabsStore.selectedVoice, (newVoice) => {
  // Update all pending tasks with new voice
  taskItems.value.forEach(task => {
    if (task.status === 'pending' && !task.voice) {
      task.voice = newVoice;
    }
  });
});

// Auto-search when API key is added
watch(() => elevenlabsStore.apiKeyCount, (newCount) => {
  if (newCount > 0 && elevenlabsStore.hasValidApiKey) {
    // Auto search for Vietnamese voices as example
    searchVoices();
  }
});
</script>
