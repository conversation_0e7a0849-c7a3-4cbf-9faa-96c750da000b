import React from 'react';
import {
  AbsoluteFill,
  Video,
  Audio,
  Sequence,
  useVideoConfig,
  interpolate,
  useCurrentFrame,
} from 'remotion';

export const VideoComposition = ({ srtArray, videoPath }) => {
  const { fps } = useVideoConfig();
  const frame = useCurrentFrame();

  // Tạo overlap 0.2 giây giữa các đoạn để audio mượt hơn
  const CROSSFADE_DURATION = 0.2;
  
  let cumulativeTime = 0;
  const segmentsWithTiming = srtArray.map((srt, index) => {
    const startTime = cumulativeTime;
    const duration = Math.max(srt.duration, srt.endTime - srt.startTime);
    const endTime = startTime + duration;
    
    // Trừ đi thời gian overlap (trừ đoạn cuối)
    if (index < srtArray.length - 1) {
      cumulativeTime = endTime - CROSSFADE_DURATION;
    } else {
      cumulativeTime = endTime;
    }

    return {
      ...srt,
      absoluteStartTime: startTime,
      absoluteEndTime: endTime,
      finalDuration: duration,
    };
  });

  return (
    <AbsoluteFill>
      {segmentsWithTiming.map((srt, index) => {
        const durationInFrames = Math.ceil(srt.finalDuration * fps);
        const videoStartFrame = Math.floor(srt.startTime * fps);
        const sequenceStartFrame = Math.floor(srt.absoluteStartTime * fps);
        
        // Tính playback rate chính xác hơn
        const originalDuration = srt.endTime - srt.startTime;
        const playbackRate = originalDuration > 0 ? originalDuration / srt.finalDuration : 1;

        // Tính crossfade opacity cho audio mượt
        const crossfadeDurationFrames = Math.floor(CROSSFADE_DURATION * fps);
        const isLastSegment = index === segmentsWithTiming.length - 1;
        const isFirstSegment = index === 0;
        
        // Fade in cho audio (trừ đoạn đầu)
        let audioVolume = 1;
        if (!isFirstSegment && frame >= sequenceStartFrame && frame < sequenceStartFrame + crossfadeDurationFrames) {
          audioVolume = interpolate(
            frame,
            [sequenceStartFrame, sequenceStartFrame + crossfadeDurationFrames],
            [0, 1],
            { extrapolateRight: 'clamp', extrapolateLeft: 'clamp' }
          );
        }
        
        // Fade out cho audio (trừ đoạn cuối)
        if (!isLastSegment && frame >= sequenceStartFrame + durationInFrames - crossfadeDurationFrames && frame < sequenceStartFrame + durationInFrames) {
          audioVolume = interpolate(
            frame,
            [sequenceStartFrame + durationInFrames - crossfadeDurationFrames, sequenceStartFrame + durationInFrames],
            [1, 0],
            { extrapolateRight: 'clamp', extrapolateLeft: 'clamp' }
          );
        }

        return (
          <Sequence
            key={index}
            from={sequenceStartFrame}
            durationInFrames={durationInFrames}
          >
            <AbsoluteFill>
              {/* Video với cài đặt tối ưu */}
              <Video
                src={videoPath}
                startFrom={videoStartFrame}
                playbackRate={playbackRate}
                volume={0.3}
                muted={false}
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover'
                }}
              />
              
              {/* Audio với crossfade */}
              <Audio
                src={srt.audioUrl.replace('file://', '')}
                volume={audioVolume}
                startFrom={0}
              />
              
              {/* Subtitle với fade effect mượt */}
              <div
                style={{
                  position: 'absolute',
                  bottom: 50,
                  left: 20,
                  right: 20,
                  textAlign: 'center',
                  color: 'white',
                  fontSize: 28,
                  fontWeight: 'bold',
                  textShadow: '3px 3px 6px rgba(0,0,0,0.9)',
                  padding: '15px 20px',
                  backgroundColor: 'rgba(0,0,0,0.7)',
                  borderRadius: '8px',
                  lineHeight: 1.4,
                  opacity: interpolate(
                    frame - sequenceStartFrame,
                    [0, Math.min(15, durationInFrames * 0.1), durationInFrames * 0.9, durationInFrames],
                    [0, 1, 1, 0],
                    { extrapolateRight: 'clamp', extrapolateLeft: 'clamp' }
                  )
                }}
              >
                {srt.text}
              </div>
            </AbsoluteFill>
          </Sequence>
        );
      })}
    </AbsoluteFill>
  );
};