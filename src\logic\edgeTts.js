import { fileSystem } from "./fileSystem";
import {device } from "./device";
import {summaryConstants} from "@/constants";
import common from "@/logic/commonHandler";

class EdgeTTS {
    constructor() {
        this.versionFileTTS = 1;
        this.voicesEdgeTTS = []
    }
    async getTtsPath() {
        const t = await device.getPlatform();
        if (t === "win32")
            return await fileSystem.appData(summaryConstants.FOLDER_PY, "tts.exe");
        if (t === "darwin")
            return await fileSystem.appData(summaryConstants.FOLDER_PY, "tts");
        throw new Error("Not support platform")
    }
    async getUrlDownTTS() {
        const t = await device.getPlatform();
        if (t === "win32")
            return `${summaryConstants.URL_CRM_APP}/download/py/tts.exe`;
        if (t === "darwin")
            return `${summaryConstants.URL_CRM_APP}/download/py/tts`;
        throw new Error("Not support platform")
    }
    async checkDownloadAndUpdateTts() {
        const t = await this.getTtsPath();
        if (!await fileSystem.existsSync(t))
            return !0;
        const n = await fileSystem.appData(summaryConstants.FOLDER_PY, "version.json");
        if (!await fileSystem.existsSync(n))
            return !0;
        const a = await fileSystem.readFileSyncUtf8(n)
          , i = common.handleGetValue( () => JSON.parse(a));
        return !(i != null && i.version) || i.version < this.versionFileTTS
    }
    async handleDownloadFileTTS(t) {
        if (!await this.checkDownloadAndUpdateTts())
            return;
        const n = await this.getTtsPath();
        await fileSystem.existsSync(n) && await fileSystem.unlinkSync(n);
        const a = await this.getUrlDownTTS();
        await EY({
            url: a,
            method: "GET"
        }, n, u => {
            const s = Math.round(u);
            t(s)
        }
        );
        const i = await device.getPlatform();
        i === "darwin" && await device.executeCommand(`chmod +x "${n}"`),
        i === "win32" && await device.executeCommand(`icacls "${n}" /grant Everyone:F`);
        const l = await fileSystem.appData(summaryConstants.FOLDER_PY, "version.json");
        await fileSystem.writeFileSync(l, JSON.stringify({
            version: this.versionFileTTS
        }))
    }
    async runSpawnCommand(t, ...r) {
        const n = await this.getTtsPath()
          , o = await device.spawnCommand(n, [t, ...r]);
        if (typeof o == "string" && o.startsWith("Error: "))
            throw new Error(o.substring(7));
        return o
    }
    async runExecuteCommand(t, ...r) {
        const o = `"${await this.getTtsPath()}" ${t} ${r.map(i => `"${i}"`).join(" ")}`
          , a = await device.executeCommand(o);
        if (typeof a == "string" && a.startsWith("Error: "))
            throw new Error(a.substring(7));
        return a
    }
    async generateAudioByEdgeTTS(t) {
        const r = `${common.convertToSignedString(t.rate ?? 1)}%`
          , n = `${common.convertToSignedString(t.volume ?? 1)}%`
          , o = `${common.convertToSignedString(t.pitch ?? 1)}Hz`;
        return await this.runSpawnCommand("tts", t.text.replace(/\r?\n/g, " ").replace(/"/g, '\\"'), t.voice, t.outputPath, r, n, o)
    }
    async getVoices() {
        if (this.voicesEdgeTTS.length > 0)
            return this.voicesEdgeTTS;
        const t = await this.runExecuteCommand("voices");
        return this.voicesEdgeTTS = JSON.parse(t),
        this.voicesEdgeTTS
    }
    async audioToText(t, r, n) {
        const o = await window.electronAPI.invoke("ffmpeg:ffmpegPath");
        console.log(t, r, n, o);
        
        return await this.runSpawnCommand("audio_to_text", t, r, n, o)
    }
}

export const edgeTts = new EdgeTTS();