<template>
  <div class="batch-replace-demo p-6">
    <h1 class="text-2xl font-bold mb-6">Demo Batch Replace System</h1>
    
    <!-- Sample Data -->
    <a-card title="Sample Subtitles" class="mb-6">
      <a-button @click="loadSampleData" type="primary" class="mb-4">
        Load Sample Data
      </a-button>
      
      <div class="subtitle-list max-h-60 overflow-y-auto">
        <div 
          v-for="subtitle in sampleSubtitles" 
          :key="subtitle.id"
          class="subtitle-item p-3 mb-2 border rounded bg-gray-50"
        >
          <div class="subtitle-header flex justify-between items-center mb-2">
            <span class="font-medium text-sm text-gray-600">
              Subtitle #{{ subtitle.id }}
            </span>
            <a-tag :color="subtitle.status === 'translated' ? 'green' : 'orange'">
              {{ subtitle.status }}
            </a-tag>
          </div>
          
          <div class="subtitle-content">
            <div class="original text-sm text-gray-700 mb-1">
              <strong>Original:</strong> {{ subtitle.text }}
            </div>
            <div v-if="subtitle.translatedText" class="translated text-sm text-blue-700">
              <strong>Translated:</strong> {{ subtitle.translatedText }}
            </div>
          </div>
        </div>
      </div>
    </a-card>

    <!-- Batch Replace Controls -->
    <a-card title="Batch Replace Controls" class="mb-6">
      <div class="flex gap-4 mb-4">
        <a-button 
          type="primary" 
          @click="openBatchReplace" 
          :disabled="sampleSubtitles.length === 0"
        >
          <template #icon><FindReplaceOutlined /></template>
          Open Batch Replace
        </a-button>
        
        <a-button @click="resetData" :disabled="sampleSubtitles.length === 0">
          <template #icon><ReloadOutlined /></template>
          Reset Data
        </a-button>
      </div>

      <!-- Quick Replace Examples -->
      <div class="quick-examples">
        <h4 class="text-sm font-medium mb-2">Quick Examples:</h4>
        <div class="flex flex-wrap gap-2">
          <a-button 
            size="small" 
            @click="quickReplace('hello', 'xin chào')"
            :disabled="sampleSubtitles.length === 0"
          >
            hello → xin chào
          </a-button>
          <a-button 
            size="small" 
            @click="quickReplace('world', 'thế giới')"
            :disabled="sampleSubtitles.length === 0"
          >
            world → thế giới
          </a-button>
          <a-button 
            size="small" 
            @click="quickReplace('application', 'ứng dụng')"
            :disabled="sampleSubtitles.length === 0"
          >
            application → ứng dụng
          </a-button>
        </div>
      </div>
    </a-card>

    <!-- Statistics -->
    <a-card title="Statistics" v-if="sampleSubtitles.length > 0">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-statistic title="Total Subtitles" :value="sampleSubtitles.length" />
        </a-col>
        <a-col :span="6">
          <a-statistic 
            title="Translated" 
            :value="translatedCount" 
            :value-style="{ color: '#3f8600' }"
          />
        </a-col>
        <a-col :span="6">
          <a-statistic 
            title="Pending" 
            :value="pendingCount" 
            :value-style="{ color: '#cf1322' }"
          />
        </a-col>
        <a-col :span="6">
          <a-statistic 
            title="Last Modified" 
            :value="lastModified"
            :formatter="formatTime"
          />
        </a-col>
      </a-row>
    </a-card>

    <!-- Batch Replace Modal -->
    <BatchReplaceModal
      v-model:open="batchReplaceVisible"
      :subtitles="sampleSubtitles"
      @replace="handleBatchReplace"
      @close="closeBatchReplace"
    />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import BatchReplaceModal from './BatchReplaceModal.vue'
import { 
  FindReplaceOutlined, 
  ReloadOutlined 
} from '@ant-design/icons-vue'

// Reactive data
const sampleSubtitles = ref([])
const batchReplaceVisible = ref(false)
const lastModified = ref(Date.now())

// Computed properties
const translatedCount = computed(() => 
  sampleSubtitles.value.filter(sub => sub.status === 'translated').length
)

const pendingCount = computed(() => 
  sampleSubtitles.value.filter(sub => sub.status === 'pending').length
)

// Methods
const loadSampleData = () => {
  sampleSubtitles.value = [
    {
      id: 1,
      index: 1,
      text: "Hello world! Welcome to our application.",
      translatedText: "Hello world! Welcome to our application.",
      status: "translated",
      startTime: 0,
      endTime: 3
    },
    {
      id: 2,
      index: 2,
      text: "This is a sample subtitle for testing.",
      translatedText: "This is a sample subtitle for testing.",
      status: "translated",
      startTime: 3,
      endTime: 6
    },
    {
      id: 3,
      index: 3,
      text: "Hello everyone, welcome to the world of programming.",
      translatedText: "Hello everyone, welcome to the world of programming.",
      status: "translated",
      startTime: 6,
      endTime: 10
    },
    {
      id: 4,
      index: 4,
      text: "Our application provides many useful features.",
      translatedText: "Our application provides many useful features.",
      status: "translated",
      startTime: 10,
      endTime: 14
    },
    {
      id: 5,
      index: 5,
      text: "Thank you for using our application!",
      translatedText: "Thank you for using our application!",
      status: "translated",
      startTime: 14,
      endTime: 17
    },
    {
      id: 6,
      index: 6,
      text: "Hello world, this is another test.",
      translatedText: "",
      status: "pending",
      startTime: 17,
      endTime: 20
    }
  ]
  
  lastModified.value = Date.now()
  message.success('Loaded sample data')
}

const resetData = () => {
  sampleSubtitles.value = []
  lastModified.value = Date.now()
  message.success('Reset data')
}

const openBatchReplace = () => {
  batchReplaceVisible.value = true
}

const closeBatchReplace = () => {
  batchReplaceVisible.value = false
}

const handleBatchReplace = (replacements) => {
  console.log('Batch replace:', replacements)
  
  try {
    // Update subtitles with new translated text
    replacements.forEach(replacement => {
      const index = sampleSubtitles.value.findIndex(sub => sub.id === replacement.id)
      if (index !== -1) {
        sampleSubtitles.value[index].translatedText = replacement.newText
        sampleSubtitles.value[index].status = "translated"
      }
    })
    
    lastModified.value = Date.now()
    message.success(`Updated ${replacements.length} subtitles`)
    closeBatchReplace()
  } catch (error) {
    message.error('Error in batch replace: ' + error.message)
  }
}

const quickReplace = (searchText, replaceText) => {
  // Simulate quick replace
  const updatedCount = sampleSubtitles.value.filter(sub => {
    if (sub.translatedText && sub.translatedText.toLowerCase().includes(searchText.toLowerCase())) {
      sub.translatedText = sub.translatedText.replace(
        new RegExp(searchText, 'gi'), 
        replaceText
      )
      sub.status = "translated"
      return true
    }
    return false
  }).length
  
  if (updatedCount > 0) {
    lastModified.value = Date.now()
    message.success(`Quick replaced "${searchText}" with "${replaceText}" in ${updatedCount} subtitles`)
  } else {
    message.info(`No occurrences of "${searchText}" found`)
  }
}

const formatTime = (value) => {
  return new Date(value).toLocaleTimeString()
}

// Auto load sample data on mount
import { onMounted } from 'vue'
onMounted(() => {
  loadSampleData()
})
</script>

<style scoped>
.subtitle-item {
  transition: all 0.2s ease;
}

.subtitle-item:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.quick-examples .ant-btn {
  margin-bottom: 8px;
}
</style>
