const { app, BrowserWindow, shell } = require('electron');
const dns = require('dns');
const exec = require('child_process').exec;
const spawn = require('child_process').spawn;
const path = require('path');
// const machineId = require('node-machine-id').machineIdSync;

// System utilities class
class SystemUtils {
  constructor() {
    this.platform = process.platform;
  }

  machineIdSync() {
    return machineId.machineIdSync();
  }

  lookup(hostname) {
    return new Promise((resolve, reject) => {
      dns.lookup(hostname, (error, address, family) => {
        if (error) {
          reject(error);
        } else {
          resolve(address);
        }
      });
    });
  }

  executeCommand(command) {
    return new Promise((resolve, reject) => {
      exec(command, (error, stdout, stderr) => {
        if (error) {
          reject("Error: " + error.message);
          return;
        }
        resolve(stdout);
      });
    });
  }

  spawnCommand(command, args) {
    return new Promise((resolve, reject) => {
      let output = '';
      const childProcess = spawn(command, args, {
        stdio: ['ignore', "pipe", "pipe"],
        shell: false,
        windowsHide: true
      });

      childProcess.stdout.on("data", data => {
        output += data.toString();
      });

      childProcess.on('close', code => {
        if (code === 0) {
          resolve(output.trim());
        } else {
          reject(new Error("Process exited with code " + code));
        }
      });

      childProcess.on('error', error => {
        reject(new Error("Failed to start process: " + error.message));
      });
    });
  }

  async getRegistryValue(keyPath, valueName) {
    return new Promise((resolve, reject) => {
      new Registry({
        hive: Registry.HKCU,
        key: keyPath
      }).get(valueName, (error, item) => {
        if (error) {
          resolve('');
          return;
        }
        resolve(item.value || '');
      });
    });
  }

  async setRegistryValue(keyPath, valueName, value) {
    return new Promise((resolve, reject) => {
      new Registry({
        hive: Registry.HKCU,
        key: keyPath
      }).set(valueName, Registry.REG_SZ, value, error => {
        if (error) {
          reject(error);
          return;
        }
        resolve(undefined);
      });
    });
  }

//   async extractAllAsar(asarPath, outputPath) {
//     return asar.extractAll(asarPath, outputPath);
//   }

//   async createPackageAsar(sourcePath, outputPath) {
//     return await asar.createPackage(sourcePath, outputPath);
//   }

  restartApp() {
    BrowserWindow.getAllWindows().forEach(window => window.close());
    app.relaunch();
    app.exit(0);
  }

  async updateMainApp() {
    const appRoot = process.env.APP_ROOT;
    const parentDir = path.dirname(appRoot);
    const targetPath = path.join(parentDir, "dist-electron", 'main.js');
    const downloadUrl = PRODUCTION_SERVER_URL + '/download/index.js';

    try {
      await downloadFile({
        url: downloadUrl,
        method: "GET"
      }, targetPath, percent => {});
    } catch (error) {
        console.error("Error updating main app:", error);
    //   logger.logDebugging("Lỗi updateMainApp:", error.message, downloadUrl, targetPath);
    }
  }

  openExternal(url) {
    return shell.openExternal(url);
  }

  showItemInFolder(filePath) {
    console.log(filePath);
    return shell.showItemInFolder(filePath);
  }
}

module.exports = SystemUtils