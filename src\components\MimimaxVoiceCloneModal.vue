<template>
  <a-modal
    v-model:open="visible"
    title="Manage Voice Clones"
    width="800px"
    :footer="null"
    @cancel="handleCancel"
  >
    <div class="voice-clone-modal">
      <!-- Add New Voice Clone Section -->
      <div class="mb-6 p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Add New Voice Clone
        </h3>
        
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Voice Name
            </label>
            <a-input
              v-model:value="newVoice.name"
              placeholder="Enter voice name"
              class="w-full"
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Voice ID
            </label>
            <a-input
              v-model:value="newVoice.id"
              placeholder="Enter Mimimax voice ID"
              class="w-full"
            />
          </div>
          
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Gender
              </label>
              <a-select
                v-model:value="newVoice.gender"
                class="w-full"
                placeholder="Select gender"
              >
                <a-select-option value="male">Male</a-select-option>
                <a-select-option value="female">Female</a-select-option>
              </a-select>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Language
              </label>
              <a-select
                v-model:value="newVoice.language"
                class="w-full"
                placeholder="Select language"
              >
                <a-select-option value="zh">Chinese</a-select-option>
                <a-select-option value="en">English</a-select-option>
                <a-select-option value="vi">Vietnamese</a-select-option>
              </a-select>
            </div>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Description (Optional)
            </label>
            <a-textarea
              v-model:value="newVoice.description"
              placeholder="Enter voice description"
              :rows="2"
              class="w-full"
            />
          </div>
          
          <div class="flex justify-end space-x-2">
            <a-button @click="resetNewVoice">Reset</a-button>
            <a-button 
              type="primary" 
              @click="addVoiceClone"
              :disabled="!canAddVoice"
            >
              Add Voice Clone
            </a-button>
          </div>
        </div>
      </div>

      <!-- Existing Voice Clones Section -->
      <div>
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Existing Voice Clones
        </h3>
        
        <div v-if="voiceClones.length === 0" class="text-center py-8 text-gray-500 dark:text-gray-400">
          No voice clones added yet. Add your first voice clone above.
        </div>
        
        <div v-else class="space-y-3">
          <div
            v-for="voice in voiceClones"
            :key="voice.id"
            class="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-600 rounded-lg"
          >
            <div class="flex-1">
              <div class="flex items-center space-x-3">
                <div class="flex-1">
                  <h4 class="font-medium text-gray-900 dark:text-white">
                    {{ voice.name }}
                  </h4>
                  <p class="text-sm text-gray-500 dark:text-gray-400">
                    ID: {{ voice.id }}
                  </p>
                  <div class="flex items-center space-x-4 mt-1">
                    <span class="text-xs px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded">
                      {{ voice.gender }}
                    </span>
                    <span class="text-xs px-2 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded">
                      {{ voice.language }}
                    </span>
                  </div>
                  <p v-if="voice.description" class="text-sm text-gray-600 dark:text-gray-300 mt-1">
                    {{ voice.description }}
                  </p>
                </div>
              </div>
            </div>
            
            <div class="flex items-center space-x-2">
              <a-button 
                size="small" 
                @click="testVoice(voice)"
                :loading="testingVoice === voice.id"
              >
                Test
              </a-button>
              <a-button 
                size="small" 
                @click="editVoice(voice)"
              >
                Edit
              </a-button>
              <a-button 
                size="small" 
                danger 
                @click="removeVoice(voice.id)"
              >
                Remove
              </a-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { message } from 'ant-design-vue';
import { useTTSStore } from '@/stores/ttsStore';

const props = defineProps({
  open: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:open', 'voiceSelected']);

const ttsStore = useTTSStore();
const visible = ref(false);
const testingVoice = ref(null);

// New voice form data
const newVoice = ref({
  name: '',
  id: '',
  gender: 'female',
  language: 'zh',
  description: ''
});

// Watch for prop changes
watch(() => props.open, (newVal) => {
  visible.value = newVal;
});

watch(visible, (newVal) => {
  emit('update:open', newVal);
});

// Computed properties
const voiceClones = computed(() => ttsStore.mimimaxTTS.voices.custom);

const canAddVoice = computed(() => {
  return newVoice.value.name.trim() && newVoice.value.id.trim();
});

// Methods
const handleCancel = () => {
  visible.value = false;
};

const resetNewVoice = () => {
  newVoice.value = {
    name: '',
    id: '',
    gender: 'female',
    language: 'zh',
    description: ''
  };
};

const addVoiceClone = () => {
  if (!canAddVoice.value) {
    message.error('Please fill in required fields');
    return;
  }

  // Check if voice ID already exists
  const existingVoice = voiceClones.value.find(v => v.id === newVoice.value.id);
  if (existingVoice) {
    message.error('Voice ID already exists');
    return;
  }

  const voice = {
    ...newVoice.value,
    isDefault: false,
    createdAt: new Date().toISOString()
  };

  ttsStore.addMimimaxVoiceClone(voice);
  message.success('Voice clone added successfully');
  resetNewVoice();
};

const removeVoice = (voiceId) => {
  ttsStore.removeMimimaxVoiceClone(voiceId);
  message.success('Voice clone removed');
};

const editVoice = (voice) => {
  // For now, just populate the form with existing data
  newVoice.value = { ...voice };
  message.info('Edit the voice details above and click "Add Voice Clone" to update');
};

const testVoice = async (voice) => {
  if (!ttsStore.mimimaxTTS.apiKey || !ttsStore.mimimaxTTS.groupId) {
    message.error('Please configure Mimimax API key and Group ID first');
    return;
  }

  testingVoice.value = voice.id;
  
  try {
    const testText = 'This is a test of the voice clone.';
    
    const response = await window.electronAPI.generateTTS({
      text: testText,
      speaker: voice.id,
      typeEngine: 'mimimax',
      mimimaxConfig: {
        apiKey: ttsStore.mimimaxTTS.apiKey,
        groupId: ttsStore.mimimaxTTS.groupId,
        selectedVoice: voice.id,
        voiceSettings: ttsStore.mimimaxTTS.voiceSettings,
        audioSettings: ttsStore.mimimaxTTS.audioSettings
      }
    });

    if (response.success) {
      const audio = new Audio(response.audioUrl);
      audio.play();
      message.success('Voice test successful!');
    } else {
      message.error('Voice test failed: ' + response.error);
    }
  } catch (error) {
    console.error('Voice test error:', error);
    message.error('Voice test failed: ' + error.message);
  } finally {
    testingVoice.value = null;
  }
};
</script>

<style scoped>
.voice-clone-modal {
  max-height: 70vh;
  overflow-y: auto;
}
</style>
