<template>
  <div class="prompt-template-selector">
    <div class="flex items-center gap-3">
      <label class="text-sm font-medium text-gray-700">Prompt Template:</label>
      
      <a-select
        v-model:value="selectedTemplateId"
        style="min-width: 200px"
        @change="handleTemplateChange"
      >
        <a-select-option 
          v-for="template in promptTemplates" 
          :key="template.id" 
          :value="template.id"
        >
          <div class="flex items-center justify-between">
            <span>{{ template.name }}</span>
            <a-tag v-if="template.isDefault" color="gold" size="small">Mặc định</a-tag>
          </div>
        </a-select-option>
      </a-select>

      <a-button 
        type="text" 
        size="small" 
        @click="showPreview = !showPreview"
        :icon="showPreview ? h(EyeInvisibleOutlined) : h(EyeOutlined)"
      >
        {{ showPreview ? 'Ẩn' : 'Xem' }}
      </a-button>

      <a-button 
        type="text" 
        size="small" 
        @click="openManager"
        :icon="h(SettingOutlined)"
      >
        <PERSON><PERSON><PERSON><PERSON> lý
      </a-button>
    </div>

    <!-- Preview template -->
    <div v-if="showPreview" class="mt-3 p-3 bg-gray-50 rounded border">
      <div class="flex justify-between items-center mb-2">
        <h4 class="text-sm font-medium text-gray-800">{{ selectedTemplate.name }}</h4>
        <a-tag :color="selectedTemplate.isDefault ? 'gold' : 'blue'" size="small">
          {{ selectedTemplate.isDefault ? 'Mặc định' : 'Tùy chỉnh' }}
        </a-tag>
      </div>
      
      <p class="text-xs text-gray-600 mb-3">{{ selectedTemplate.description }}</p>
      
      <div class="template-preview bg-white p-3 rounded border text-xs font-mono max-h-40 overflow-y-auto">
        <pre class="whitespace-pre-wrap">{{ previewText }}</pre>
      </div>
    </div>

    <!-- Manager Modal -->
    <a-modal
      v-model:open="showManager"
      title="Quản lý Prompt Templates"
      width="1200px"
      :footer="null"
      :destroyOnClose="true"
    >
      <PromptTemplateManager />
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, h } from 'vue'
import { usePromptStore } from '@/stores/promptStore'
import PromptTemplateManager from './PromptTemplateManager.vue'
import {
  EyeOutlined,
  EyeInvisibleOutlined,
  SettingOutlined
} from '@ant-design/icons-vue'

const promptStore = usePromptStore()

// Props
const props = defineProps({
  sourceLang: {
    type: String,
    default: 'auto'
  },
  targetLang: {
    type: String,
    default: 'Vietnamese'
  },
  dictionary: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['template-changed'])

// Reactive data
const showPreview = ref(false)
const showManager = ref(false)

// Computed properties
const promptTemplates = computed(() => promptStore.promptTemplates)
const selectedTemplate = computed(() => promptStore.selectedTemplate)

const selectedTemplateId = computed({
  get: () => promptStore.selectedTemplateId,
  set: (value) => {
    promptStore.selectTemplate(value)
    emit('template-changed', promptStore.selectedTemplate)
  }
})

const previewText = computed(() => {
  if (!selectedTemplate.value) return ''
  
  // Generate preview with current props
  return promptStore.generatePromptFromTemplate(
    selectedTemplate.value,
    props.sourceLang,
    props.targetLang,
    props.dictionary
  )
})

// Methods
const handleTemplateChange = (templateId) => {
  const template = promptStore.selectTemplate(templateId)
  emit('template-changed', template)
}

const openManager = () => {
  showManager.value = true
}

// Expose methods for parent component
defineExpose({
  getCurrentPrompt: () => previewText.value,
  getSelectedTemplate: () => selectedTemplate.value
})
</script>

<style scoped>
.template-preview {
  word-break: break-word;
}

.template-preview pre {
  margin: 0;
  font-family: 'Courier New', monospace;
  font-size: 11px;
  line-height: 1.4;
}

:deep(.ant-select-selection-item) {
  display: flex !important;
  align-items: center;
}
</style>
