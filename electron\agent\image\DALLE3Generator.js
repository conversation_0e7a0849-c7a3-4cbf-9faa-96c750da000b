/**
 * DALL·E 3 Image Generator
 * Wrapper cho OpenAI DALL·E 3 API
 */

import OpenAI from 'openai';
import fs from 'fs';
import path from 'path';

export class DALLE3Generator {
    constructor(apiKey = null, baseUrl = null) {
        this.client = new OpenAI({
            apiKey: apiKey || process.env.OPENAI_API_KEY,
            baseURL: baseUrl || process.env.OPENAI_API_BASE
        });
        
        this.defaultSettings = {
            model: "dall-e-3",
            size: "1024x1024",
            quality: "standard", // "standard" hoặc "hd"
            style: "vivid", // "vivid" hoặc "natural"
            n: 1
        };
    }

    /**
     * Tạo một ảnh từ prompt
     * @param {string} prompt - Mô tả ảnh cần tạo
     * @param {Object} options - T<PERSON><PERSON> chọn
     * @returns {Promise<Object>}
     */
    async generateImage(prompt, options = {}) {
        try {
            console.log(`🎨 Tạo ảnh: ${prompt.substring(0, 50)}...`);
            
            const settings = {
                ...this.defaultSettings,
                ...options,
                prompt: prompt
            };

            const response = await this.client.images.generate(settings);
            
            const result = {
                success: true,
                imageUrl: response.data[0].url,
                revisedPrompt: response.data[0].revised_prompt,
                originalPrompt: prompt,
                settings: settings,
                timestamp: new Date().toISOString()
            };

            console.log(`✅ Ảnh đã tạo thành công`);
            return result;

        } catch (error) {
            console.error(`❌ Lỗi tạo ảnh:`, error.message);
            return {
                success: false,
                error: error.message,
                originalPrompt: prompt,
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * Tạo nhiều ảnh từ danh sách prompts
     * @param {Array} prompts - Danh sách prompts
     * @param {Object} options - Tùy chọn chung
     * @param {number} delay - Delay giữa các request (ms)
     * @returns {Promise<Array>}
     */
    async generateMultipleImages(prompts, options = {}, delay = 2000) {
        const results = [];
        
        console.log(`🎨 Tạo ${prompts.length} ảnh với DALL·E 3...`);
        
        for (let i = 0; i < prompts.length; i++) {
            const prompt = prompts[i];
            console.log(`\n📸 Ảnh ${i + 1}/${prompts.length}`);
            
            const result = await this.generateImage(prompt, options);
            results.push(result);
            
            // Delay để tránh rate limit
            if (i < prompts.length - 1) {
                console.log(`⏳ Chờ ${delay/1000}s...`);
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
        
        const successCount = results.filter(r => r.success).length;
        console.log(`\n🎉 Hoàn thành: ${successCount}/${prompts.length} ảnh thành công`);
        
        return results;
    }

    /**
     * Tải ảnh từ URL về local
     * @param {string} imageUrl - URL của ảnh
     * @param {string} fileName - Tên file
     * @param {string} outputDir - Thư mục lưu
     * @returns {Promise<string>}
     */
    async downloadImage(imageUrl, fileName, outputDir = 'generated_images') {
        try {
            // Tạo thư mục nếu chưa có
            if (!fs.existsSync(outputDir)) {
                fs.mkdirSync(outputDir, { recursive: true });
            }

            console.log(`💾 Tải ảnh: ${fileName}`);
            
            const response = await fetch(imageUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const imageBuffer = await response.arrayBuffer();
            const filePath = path.join(outputDir, fileName);
            
            fs.writeFileSync(filePath, Buffer.from(imageBuffer));
            
            console.log(`✅ Đã lưu: ${filePath}`);
            return filePath;
            
        } catch (error) {
            console.error(`❌ Lỗi tải ảnh ${fileName}:`, error.message);
            throw error;
        }
    }

    /**
     * Tạo ảnh và tự động tải về
     * @param {string} prompt - Prompt
     * @param {string} fileName - Tên file (không cần extension)
     * @param {Object} options - Tùy chọn
     * @returns {Promise<Object>}
     */
    async generateAndDownload(prompt, fileName, options = {}) {
        const result = await this.generateImage(prompt, options);
        
        if (result.success) {
            try {
                const fullFileName = fileName.endsWith('.png') ? fileName : `${fileName}.png`;
                const localPath = await this.downloadImage(result.imageUrl, fullFileName);
                
                return {
                    ...result,
                    localPath: localPath,
                    fileName: fullFileName
                };
            } catch (downloadError) {
                return {
                    ...result,
                    downloadError: downloadError.message
                };
            }
        }
        
        return result;
    }

    /**
     * Batch generation với auto download
     * @param {Array} prompts - Array of {prompt, fileName}
     * @param {Object} options - Tùy chọn
     * @returns {Promise<Array>}
     */
    async batchGenerateAndDownload(prompts, options = {}) {
        const results = [];
        
        console.log(`🎨 Batch tạo ${prompts.length} ảnh...`);
        
        for (let i = 0; i < prompts.length; i++) {
            const { prompt, fileName } = prompts[i];
            console.log(`\n📸 Ảnh ${i + 1}/${prompts.length}: ${fileName}`);
            
            const result = await this.generateAndDownload(prompt, fileName, options);
            results.push(result);
            
            // Delay
            if (i < prompts.length - 1) {
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
        }
        
        return results;
    }

    /**
     * Tạo HTML gallery từ kết quả
     * @param {Array} results - Kết quả từ batch generation
     * @param {string} title - Tiêu đề gallery
     * @param {string} outputFile - Tên file HTML
     */
    createHTMLGallery(results, title = "DALL·E 3 Gallery", outputFile = "dalle3_gallery.html") {
        let html = `<!DOCTYPE html>
<html>
<head>
    <title>${title}</title>
    <meta charset="UTF-8">
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f5f5f5; 
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: white; 
            padding: 20px; 
            border-radius: 10px; 
        }
        .image-item { 
            margin-bottom: 40px; 
            border: 1px solid #ddd; 
            padding: 20px; 
            border-radius: 8px; 
            background: #fafafa; 
        }
        .image-item img { 
            max-width: 100%; 
            height: auto; 
            border-radius: 8px; 
            box-shadow: 0 4px 8px rgba(0,0,0,0.1); 
        }
        .prompt { 
            background: #e8f4f8; 
            padding: 15px; 
            margin: 15px 0; 
            border-radius: 5px; 
            border-left: 4px solid #2196F3; 
        }
        .error { 
            background: #ffebee; 
            color: #c62828; 
            padding: 15px; 
            border-radius: 5px; 
            border-left: 4px solid #f44336; 
        }
        .meta { 
            color: #666; 
            font-size: 0.9em; 
            margin-top: 10px; 
        }
        h1 { color: #333; text-align: center; }
        h3 { color: #2196F3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 ${title}</h1>
        <p style="text-align: center; color: #666;">Generated on ${new Date().toLocaleString()}</p>
        <hr>
`;

        results.forEach((result, index) => {
            html += `\n        <div class="image-item">`;
            html += `\n            <h3>Image ${index + 1}</h3>`;
            
            if (result.success && result.localPath) {
                html += `\n            <img src="${result.localPath}" alt="Generated Image ${index + 1}">`;
                html += `\n            <div class="prompt">`;
                html += `\n                <strong>Original Prompt:</strong><br>`;
                html += `\n                ${result.originalPrompt}`;
                html += `\n            </div>`;
                html += `\n            <div class="prompt">`;
                html += `\n                <strong>DALL·E 3 Revised Prompt:</strong><br>`;
                html += `\n                ${result.revisedPrompt}`;
                html += `\n            </div>`;
                html += `\n            <div class="meta">`;
                html += `\n                <strong>Settings:</strong> ${result.settings.size} | ${result.settings.quality} | ${result.settings.style}<br>`;
                html += `\n                <strong>File:</strong> ${result.fileName}<br>`;
                html += `\n                <strong>Generated:</strong> ${new Date(result.timestamp).toLocaleString()}`;
                html += `\n            </div>`;
            } else {
                html += `\n            <div class="error">`;
                html += `\n                <strong>❌ Error:</strong> ${result.error}<br>`;
                html += `\n                <strong>Prompt:</strong> ${result.originalPrompt}`;
                html += `\n            </div>`;
            }
            
            html += `\n        </div>`;
        });

        html += `\n    </div>
</body>
</html>`;

        fs.writeFileSync(outputFile, html);
        console.log(`📄 HTML gallery đã tạo: ${outputFile}`);
    }

    /**
     * Ước tính chi phí
     * @param {number} imageCount - Số lượng ảnh
     * @param {string} quality - "standard" hoặc "hd"
     * @returns {number}
     */
    estimateCost(imageCount, quality = "standard") {
        const pricePerImage = quality === "hd" ? 0.08 : 0.04; // USD
        return imageCount * pricePerImage;
    }
}
