import fs from 'fs/promises';
import path from 'path';
import { program } from 'commander';
import { Translator } from './translator.mjs';
import { Summarizer } from './summarizer.mjs';

async function saveProgress(logFile, progressData) {
    try {
        await fs.writeFile(logFile, JSON.stringify(progressData, null, 2), 'utf-8');
    } catch (error) {
        console.error('Error saving progress:', error);
    }
}

async function loadProgress(logFile) {
    try {
        const data = await fs.readFile(logFile, 'utf-8');
        return JSON.parse(data);
    } catch (error) {
        return { chunks: [], memory: "" };
    }
}

async function translateText({
    inputFile,
    outputFile = 'translated.txt',
    model = 'gpt-4o-mini',
    maxChunks = -1,
    resume = false,
    chunkType = 'paragraph'
}) {
    const logFile = "translation_progress.json";
    
    try {
        // Read input file
        console.log(`Reading input file: ${inputFile}`);
        const text = await fs.readFile(inputFile, 'utf-8');
        
        // Split text into chunks
        const chunks = Translator.splitTextIntoChunks(text, chunkType);
        console.log(`Split text into ${chunks.length} ${chunkType}s`);
        
        // Load or initialize progress
        let progressData = await loadProgress(logFile);
        
        // Initialize translator
        const translator = new Translator({ model });
        
        // Restore memory if resuming
        if (resume && progressData.memory) {
            console.log('Restoring memory from previous session');
            translator.memory = progressData.memory;
        }
        
        // Determine which chunks to process
        let chunksToProcess = chunks;
        let startIndex = 0;
        
        if (resume && progressData.chunks && progressData.chunks.length > 0) {
            startIndex = progressData.chunks.length;
            chunksToProcess = chunks.slice(startIndex);
            console.log(`Resuming from chunk ${startIndex}`);
        }
        
        // Limit chunks if specified
        if (maxChunks !== -1) {
            const remainingChunks = Math.min(maxChunks - startIndex, chunksToProcess.length);
            chunksToProcess = chunksToProcess.slice(0, remainingChunks);
            console.log(`Limited to ${remainingChunks} chunks`);
        }
        
        if (chunksToProcess.length === 0) {
            console.log('No chunks to process');
            return;
        }
        
        // Progress callback
        const onProgress = (current, total, message) => {
            console.log(`Progress: ${current}/${total} - ${message}`);
        };
        
        // Translate chunks
        console.log(`Starting translation of ${chunksToProcess.length} chunks`);
        const translatedChunks = await translator.translateChunks(chunksToProcess, onProgress);
        
        // Update progress data
        const allTranslatedChunks = [...(progressData.chunks || []), ...translatedChunks];
        progressData = {
            chunks: allTranslatedChunks,
            memory: translator.memory,
            originalChunkCount: chunks.length,
            completedChunkCount: allTranslatedChunks.length
        };
        
        // Save progress
        await saveProgress(logFile, progressData);
        
        // Generate output text
        let outputText;
        if (resume && progressData.chunks.length < chunks.length) {
            // Partial translation - include progress info
            outputText = Translator.joinTranslatedChunks(allTranslatedChunks, chunkType);
            outputText += `\n\n[Translation Progress: ${allTranslatedChunks.length}/${chunks.length} chunks completed]`;
        } else {
            // Complete translation
            outputText = Translator.joinTranslatedChunks(allTranslatedChunks, chunkType);
        }
        
        // Write output file
        await fs.writeFile(outputFile, outputText, 'utf-8');
        
        console.log(`\nTranslation completed!`);
        console.log(`Input: ${inputFile}`);
        console.log(`Output: ${outputFile}`);
        console.log(`Chunks processed: ${translatedChunks.length}`);
        console.log(`Total chunks translated: ${allTranslatedChunks.length}/${chunks.length}`);
        console.log(`Progress saved to: ${logFile}`);
        
    } catch (error) {
        console.error('Translation failed:', error);
        process.exit(1);
    }
}

async function summarizeText({
    inputFile,
    outputFile = 'summary.txt',
    model = 'gpt-4o-mini',
    maxChunks = -1,
    resume = false,
    chunkType = 'paragraph'
}) {
    const logFile = "summary_progress.json";

    try {
        // Read input file
        console.log(`Reading input file: ${inputFile}`);
        const text = await fs.readFile(inputFile, 'utf-8');

        // Split text into chunks
        const chunks = Summarizer.splitTextIntoChunks(text, chunkType);
        console.log(`Split text into ${chunks.length} ${chunkType}s`);

        // Load or initialize progress
        let progressData = await loadProgress(logFile);

        // Initialize summarizer
        const summarizer = new Summarizer({ model });

        // Restore memory if resuming
        if (resume && progressData.memory) {
            console.log('Restoring memory from previous session');
            summarizer.memory = progressData.memory;
        }

        // Determine which chunks to process
        let chunksToProcess = chunks;
        let startIndex = 0;

        if (resume && progressData.chunks && progressData.chunks.length > 0) {
            startIndex = progressData.chunks.length;
            chunksToProcess = chunks.slice(startIndex);
            console.log(`Resuming from chunk ${startIndex}`);
        }

        // Limit chunks if specified
        if (maxChunks !== -1) {
            const remainingChunks = Math.min(maxChunks - startIndex, chunksToProcess.length);
            chunksToProcess = chunksToProcess.slice(0, remainingChunks);
            console.log(`Limited to ${remainingChunks} chunks`);
        }

        if (chunksToProcess.length === 0) {
            console.log('No chunks to process');
            return;
        }

        // Progress callback
        const onProgress = (current, total, message) => {
            console.log(`Progress: ${current}/${total} - ${message}`);
        };

        // Summarize chunks
        console.log(`Starting summarization of ${chunksToProcess.length} chunks`);
        const summarizedChunks = await summarizer.summarizeChunks(chunksToProcess, onProgress);

        // Update progress data
        const allSummarizedChunks = [...(progressData.chunks || []), ...summarizedChunks];
        progressData = {
            chunks: allSummarizedChunks,
            memory: summarizer.memory,
            originalChunkCount: chunks.length,
            completedChunkCount: allSummarizedChunks.length
        };

        // Save progress
        await saveProgress(logFile, progressData);

        // Generate output text
        let outputText;
        if (resume && progressData.chunks.length < chunks.length) {
            // Partial summarization - include progress info
            outputText = Summarizer.joinSummarizedChunks(allSummarizedChunks, chunkType);
            outputText += `\n\n[Summarization Progress: ${allSummarizedChunks.length}/${chunks.length} chunks completed]`;
        } else {
            // Complete summarization
            outputText = Summarizer.joinSummarizedChunks(allSummarizedChunks, chunkType);
        }

        // Write output file
        await fs.writeFile(outputFile, outputText, 'utf-8');

        console.log(`\nSummarization completed!`);
        console.log(`Input: ${inputFile}`);
        console.log(`Output: ${outputFile}`);
        console.log(`Chunks processed: ${summarizedChunks.length}`);
        console.log(`Total chunks summarized: ${allSummarizedChunks.length}/${chunks.length}`);
        console.log(`Progress saved to: ${logFile}`);

    } catch (error) {
        console.error('Summarization failed:', error);
        process.exit(1);
    }
}

// CLI setup
program
    .name('novel-translator')
    .description('Translate and summarize Chinese text files to Vietnamese using AI with memory context')
    .version('1.0.0');

program
    .command('translate')
    .description('Translate a text file')
    .requiredOption('-i, --input <file>', 'Input text file path')
    .option('-o, --output <file>', 'Output file path', 'translated.txt')
    .option('-m, --model <model>', 'OpenAI model name', 'gpt-4o-mini')
    .option('--max-chunks <number>', 'Maximum number of chunks to translate (-1 for all)', (val) => parseInt(val), -1)
    .option('-r, --resume', 'Resume translation from where it stopped', false)
    .option('-t, --chunk-type <type>', 'How to split text: paragraph or sentence', 'paragraph')
    .action(async (options) => {
        await translateText({
            inputFile: options.input,
            outputFile: options.output,
            model: options.model,
            maxChunks: options.maxChunks,
            resume: options.resume,
            chunkType: options.chunkType
        });
    });

program
    .command('resume')
    .description('Resume previous translation')
    .requiredOption('-i, --input <file>', 'Input text file path')
    .option('-o, --output <file>', 'Output file path', 'translated.txt')
    .option('-m, --model <model>', 'OpenAI model name', 'gpt-4o-mini')
    .action(async (options) => {
        await translateText({
            inputFile: options.input,
            outputFile: options.output,
            model: options.model,
            maxChunks: -1,
            resume: true,
            chunkType: 'paragraph'
        });
    });

program
    .command('summarize')
    .description('Summarize a Chinese text file to Vietnamese')
    .requiredOption('-i, --input <file>', 'Input text file path')
    .option('-o, --output <file>', 'Output file path', 'summary.txt')
    .option('-m, --model <model>', 'OpenAI model name', 'gpt-4o-mini')
    .option('--max-chunks <number>', 'Maximum number of chunks to summarize (-1 for all)', (val) => parseInt(val), -1)
    .option('-r, --resume', 'Resume summarization from where it stopped', false)
    .option('-t, --chunk-type <type>', 'How to split text: paragraph or sentence', 'paragraph')
    .action(async (options) => {
        await summarizeText({
            inputFile: options.input,
            outputFile: options.output,
            model: options.model,
            maxChunks: options.maxChunks,
            resume: options.resume,
            chunkType: options.chunkType
        });
    });

program
    .command('resume-summary')
    .description('Resume previous summarization')
    .requiredOption('-i, --input <file>', 'Input text file path')
    .option('-o, --output <file>', 'Output file path', 'summary.txt')
    .option('-m, --model <model>', 'OpenAI model name', 'gpt-4o-mini')
    .action(async (options) => {
        await summarizeText({
            inputFile: options.input,
            outputFile: options.output,
            model: options.model,
            maxChunks: -1,
            resume: true,
            chunkType: 'paragraph'
        });
    });

// If this file is run directly
console.log(`Running file:///${process.argv[1].replace(/\\/g, '/')}...`,import.meta.url);

if (import.meta.url === `file:///${process.argv[1].replace(/\\/g, '/')}`) {
    console.log(`Starting novel translator...`);
    
    program.parse();
}

export { translateText, summarizeText };