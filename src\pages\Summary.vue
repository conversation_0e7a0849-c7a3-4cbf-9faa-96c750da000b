<template>
  <div class="flex flex-col h-full p-6 overflow-auto">
    <!-- Header -->
     <!-- back -->
      <div class="flex justify-between items-center mb-6">
        <div>
          <a-button
            type="primary"
            @click="$router.back()"
          >
            ⬅️ Back
          </a-button>
        </div>
      </div>
    <div class="flex justify-between items-center mb-6">
      <div>
        <h1 class="text-2xl font-bold text-gray-300 mb-2">📚 Novel Management & Summary</h1>
        <p class="text-gray-500"><PERSON><PERSON><PERSON><PERSON> lý truy<PERSON>n, l<PERSON>y nội dung chương và tóm tắt</p>
      </div>
      <div>
        <a-button
          type="primary"
          @click="loadBooks"
          :loading="loading"
        >
          🔄 Refresh
        </a-button>
      </div>
    </div>

    <!-- URL Input for New Extraction -->
    <a-card class="mb-6">
      <template #title>
        <span class="text-lg font-semibold">🔗 Lấy chapters từ URL mới</span>
      </template>

      <div class="space-y-4">
        <div class="flex space-x-4">
          <div class="flex-1">
            <label class="block text-sm font-medium text-gray-400 mb-1">
              URL truyện
              <span class="text-xs text-gray-500">(ví dụ: https://44xw.com/a/149/148289/)</span>
            </label>
            <a-input
              v-model:value="extractionUrl"
              placeholder="https://44xw.com/a/149/148289/"
              class="w-full"
            />
          </div>

          <div class="flex items-end">
            <a-button
              type="primary"
              @click="extractFromUrl"
              :loading="extracting"
              :disabled="!extractionUrl.trim()"
            >
              🔍 Lấy chapters
            </a-button>
          </div>
        </div>

        <!-- Extraction Progress -->
        <div v-if="extracting" class="bg-blue-50 p-4 rounded-lg">
          <div class="flex items-center space-x-2 mb-2">
            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            <span class="text-sm font-medium text-blue-800">Đang lấy danh sách chapters...</span>
          </div>
          <div class="text-xs text-blue-600">
            Vui lòng đợi, quá trình này có thể mất vài phút...
          </div>
        </div>

        <!-- Extraction Results -->
        <div v-if="extractionResult" class="bg-green-50 p-4 rounded-lg">
          <div class="text-sm font-medium text-green-800 mb-2">
            ✅ Extraction hoàn thành!
          </div>
          <div class="text-xs text-green-600 space-y-1">
            <div>📚 Truyện: {{ extractionResult.bookTitle }}</div>
            <div>📖 Chapters: {{ extractionResult.totalChapters }}</div>
            <div>💾 Đã lưu vào database</div>
            <div class="mt-2">
              <a-button
                type="link"
                size="small"
                @click="clearExtractionResult"
              >
                Ẩn thông báo
              </a-button>
            </div>
          </div>
        </div>
      </div>
    </a-card>

    <!-- Chapter Text Viewer -->
    <a-card v-if="selectedBook" class="mb-6">
      <template #title>
        <span class="text-lg font-semibold">📖 Xem và Copy Text Chapters</span>
      </template>

      <div class="space-y-4">
        <!-- Chapter Range Input for Text Viewing -->
        <div class="grid grid-cols-4 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-1">
              Từ chương
            </label>
            <a-input-number
              v-model:value="textViewRange.start"
              :min="1"
              :max="9999"
              class="w-full"
              placeholder="576"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-300 mb-1">
              Đến chương
            </label>
            <a-input-number
              v-model:value="textViewRange.end"
              :min="textViewRange.start || 1"
              :max="9999"
              class="w-full"
              placeholder="580"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-300 mb-1">
              Loại text
            </label>
            <a-select
              v-model:value="textViewOptions.type"
              class="w-full"
            >
              <a-select-option value="original_text">📄 Nội dung gốc</a-select-option>
              <a-select-option value="summary_text">📝 Tóm tắt</a-select-option>
            </a-select>
          </div>

          <div class="flex items-end">
            <a-space>
              <a-button
                type="primary"
                @click="loadChapterTexts"
                :loading="loadingTexts"
                :disabled="!isValidTextRange"
              >
                📖 Xem text
              </a-button>

              <a-button
                @click="copyAllTexts"
                :disabled="!chapterTexts.length"
              >
                📋 Copy tất cả
              </a-button>
            </a-space>
          </div>
        </div>

        <!-- Text Display -->
        <div v-if="chapterTexts.length > 0" class="space-y-4">
          <div class="flex justify-between items-center">
            <span class="text-sm font-medium text-gray-300">
              Tìm thấy {{ chapterTexts.length }} chapters với text
            </span>
            <a-space>
              <a-button size="small" @click="selectAllTexts">
                ✅ Chọn tất cả
              </a-button>
              <a-button size="small" @click="deselectAllTexts">
                ❌ Bỏ chọn tất cả
              </a-button>
            </a-space>
          </div>

          <!-- Chapter Text Items -->
          <div class="max-h-96 overflow-y-auto space-y-3">
            <div
              v-for="chapter in chapterTexts"
              :key="chapter.id"
              class="border rounded-lg p-4"
              :class="chapter.selected ? 'border-blue-500 bg-blue-50' : 'border-gray-200'"
            >
              <div class="flex justify-between items-start mb-2">
                <div class="flex items-center space-x-2">
                  <a-checkbox
                    v-model:checked="chapter.selected"
                    @change="updateSelection"
                  />
                  <span class="font-medium text-gray-300">
                    Chapter {{ chapter.realChapterNumber }} (ID: {{ chapter.chapter_id }})
                  </span>
                  <a-tag :color="chapter.status === 'completed' ? 'green' : 'orange'">
                    {{ chapter.status }}
                  </a-tag>
                </div>

                <a-space>
                  <a-button
                    size="small"
                    @click="copyChapterText(chapter)"
                    :disabled="!chapter.text"
                  >
                    📋 Copy
                  </a-button>

                  <a-button
                    size="small"
                    @click="toggleChapterExpand(chapter.id)"
                  >
                    {{ chapter.expanded ? '🔼 Thu gọn' : '🔽 Xem' }}
                  </a-button>
                </a-space>
              </div>

              <div class="text-sm text-gray-500 mb-2">
                {{ chapter.chapter_title }}
              </div>

              <!-- Text Preview/Full -->
              <div v-if="chapter.text" class="bg-gray-50 p-3 rounded">
                <div v-if="!chapter.expanded" class="text-sm text-gray-300">
                  {{ chapter.text.substring(0, 200) }}{{ chapter.text.length > 200 ? '...' : '' }}
                  <span class="text-xs text-gray-500 ml-2">
                    ({{ chapter.text.length }} ký tự)
                  </span>
                </div>

                <div v-else class="text-sm text-gray-300 whitespace-pre-wrap">
                  {{ chapter.text }}
                  <div class="text-xs text-gray-500 mt-2 pt-2 border-t">
                    Tổng: {{ chapter.text.length }} ký tự
                  </div>
                </div>
              </div>

              <div v-else class="text-sm text-gray-400 italic">
                Chưa có {{ textViewOptions.type === 'original_text' ? 'nội dung gốc' : 'tóm tắt' }}
              </div>
            </div>
          </div>

          <!-- Copy Selected Button -->
          <div v-if="selectedTexts.length > 0" class="text-center">
            <a-button
              type="primary"
              size="large"
              @click="copySelectedTexts"
            >
              📋 Copy {{ selectedTexts.length }} chapters đã chọn
            </a-button>
          </div>
        </div>

        <!-- No Text Found -->
        <div v-else-if="loadingTexts === false && textViewRange.start && textViewRange.end"
             class="text-center py-8 text-gray-500">
          <div class="text-lg mb-2">📭 Không tìm thấy text</div>
          <div class="text-sm">
            Không có {{ textViewOptions.type === 'original_text' ? 'nội dung gốc' : 'tóm tắt' }}
            cho chapters {{ textViewRange.start }}-{{ textViewRange.end }}
          </div>
          <div class="text-xs mt-2">
            Hãy chạy "Lấy nội dung" hoặc "Tóm tắt" trước
          </div>
        </div>
      </div>
    </a-card>

    <!-- Books List -->
    <a-card class="mb-6">
      <template #title>
        <span class="text-lg font-semibold">📖 Danh sách truyện</span>
      </template>

      <a-table
        :dataSource="books"
        :columns="bookColumns"
        :loading="loading"
        :pagination="{ pageSize: 10 }"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ record.status }}
            </a-tag>
          </template>

          <template v-if="column.key === 'progress'">
            <div class="flex items-center space-x-2">
              <div class="flex-1">
                <a-progress
                  :percent="Math.round((record.extracted_chapters / record.total_chapters) * 100)"
                  size="small"
                  :show-info="false"
                />
              </div>
              <span class="text-sm text-gray-500">
                {{ record.extracted_chapters }}/{{ record.total_chapters }}
              </span>
            </div>
          </template>
          <template v-if="column.key === 'action'">
            <a-button type="text" @click.stop="selectBook(record)">
              Chọn
            </a-button>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- Selected Book Details -->
    <a-card v-if="selectedBook" class="mb-6">
      <template #title>
        <div class="flex justify-between items-center">
          <div>
            <h2 class="text-xl font-semibold text-gray-400 mb-1">📚 {{ selectedBook.title }}</h2>
            <p class="text-sm text-gray-600">
              👤 {{ selectedBook.author }} | 📂 {{ selectedBook.category }} | 📅 {{ selectedBook.last_update_info }}
            </p>
          </div>
          <a-button
            type="text"
            @click="selectedBook = null"
            class="text-gray-400 hover:text-gray-400"
          >
            ✕
          </a-button>
        </div>
      </template>

      <!-- Summary Statistics -->
      <div class="grid grid-cols-4 gap-4 mb-6">
        <a-card size="small" class="text-center">
          <div class="text-2xl font-bold text-blue-600">{{ summaryStats.total }}</div>
          <div class="text-sm text-gray-500">Total Summaries</div>
        </a-card>

        <a-card size="small" class="text-center">
          <div class="text-2xl font-bold text-green-600">{{ summaryStats.completed }}</div>
          <div class="text-sm text-gray-500">Completed</div>
        </a-card>

        <a-card size="small" class="text-center">
          <div class="text-2xl font-bold text-yellow-600">{{ summaryStats.pending }}</div>
          <div class="text-sm text-gray-500">Pending</div>
        </a-card>

        <a-card size="small" class="text-center">
          <div class="text-2xl font-bold text-red-600">{{ summaryStats.failed }}</div>
          <div class="text-sm text-gray-500">Failed</div>
        </a-card>
      </div>

      <!-- Chapter Range Input -->
      <div class="grid grid-cols-4 gap-2 mb-6">
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-1">
            Chương bắt đầu
            <span class="text-xs text-gray-500">(số chương thực tế, ví dụ: 576)</span>
          </label>
          <a-input-number
            v-model:value="chapterRange.start"
            :min="1"
            :max="9999"
            class="w-full"
            placeholder="Ví dụ: 576"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-300 mb-1">
            Chương kết thúc
            <span class="text-xs text-gray-500">(số chương thực tế, ví dụ: 580)</span>
          </label>
          <a-input-number
            v-model:value="chapterRange.end"
            :min="chapterRange.start || 1"
            :max="9999"
            class="w-full"
            placeholder="Ví dụ: 580"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-300 mb-1">Delay (giây)</label>
          <a-input-number
            v-model:value="processingOptions.delay"
            :min="5"
            :max="60"
            class="w-full"
            addon-after="s"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-300 mb-1">Thao tác</label>
          <a-space>
            <a-button
              type="primary"
              @click="startProcessing('content')"
              :disabled="isProcessing || !isValidRange"
            >
              📥 Lấy nội dung
            </a-button>

            <a-button
              @click="startProcessing('summary')"
              :disabled="isProcessing || !isValidRange"
            >
              📝 Tóm tắt
            </a-button>

            <a-button
              type="primary"
              @click="startProcessing('both')"
              :disabled="isProcessing || !isValidRange"
            >
              ▶️ Cả hai
            </a-button>

            <a-button
              type="primary"
              ghost
              @click="startNovelSummarization"
              :disabled="isProcessing || isNovelSummarizing || !isValidRange"
              :loading="isNovelSummarizing"
            >
              🧠 Novel Tóm tắt
            </a-button>
          </a-space>
        </div>
      </div>

      <!-- Processing Options -->
      <div class="flex space-x-6 mb-6">
        <a-checkbox
          v-model:checked="processingOptions.extractContent"
        >
          Lấy nội dung chương
        </a-checkbox>

        <a-checkbox
          v-model:checked="processingOptions.generateSummaries"
        >
          Tạo tóm tắt
        </a-checkbox>

        <a-checkbox
          v-model:checked="processingOptions.skipExisting"
        >
          Bỏ qua đã có
        </a-checkbox>
      </div>

      <!-- Book Memory Status -->
      <div v-if="selectedBook" class="mb-6">
        <a-card size="small">
          <template #title>
            <span class="text-sm font-medium">📝 Book Memory Status</span>
          </template>

          <div v-if="bookMemory" class="space-y-2">
            <div class="text-sm text-gray-600">
              <strong>Memory length:</strong> {{ bookMemory.length }} characters
            </div>
            <div class="text-sm text-gray-600">
              <strong>Estimated tokens:</strong> {{ Math.ceil(bookMemory.length / 3) }}
            </div>
            <div class="text-xs text-gray-500 bg-gray-50 p-2 rounded max-h-20 overflow-y-auto">
              {{ bookMemory.substring(0, 200) }}{{ bookMemory.length > 200 ? '...' : '' }}
            </div>
          </div>

          <div v-else class="text-sm text-gray-500">
            Chưa có memory cho book này. Memory sẽ được tạo khi chạy Novel Summarization.
          </div>
        </a-card>
      </div>

      <!-- Novel Summarization Progress -->
      <div v-if="isNovelSummarizing || novelSummaryProgress.status !== 'idle'" class="mb-6">
        <a-card>
          <div class="flex justify-between items-center mb-4">
            <div>
              <h3 class="text-lg font-medium">
                🧠 Novel Summarization Progress
              </h3>
              <p class="text-sm text-gray-500">
                Sử dụng AI để tóm tắt với memory context
              </p>
            </div>
            <a-button
              v-if="isNovelSummarizing"
              danger
              @click="stopNovelSummarization"
            >
              ⏹️ Dừng
            </a-button>
          </div>

          <a-progress
            :percent="novelSummaryProgress.total > 0 ? Math.round((novelSummaryProgress.current / novelSummaryProgress.total) * 100) : 0"
            class="mb-2"
          />

          <p class="text-sm text-gray-600 mb-4">
            {{ novelSummaryProgress.current }}/{{ novelSummaryProgress.total }} chương
            <span v-if="novelSummaryProgress.status !== 'idle'" class="ml-2">
              - {{ novelSummaryProgress.status }}
            </span>
          </p>

          <!-- Memory Status -->
          <div v-if="bookMemory" class="bg-blue-50 p-3 rounded mb-4">
            <div class="text-sm font-medium text-blue-800 mb-1">
              📝 Book Memory Status
            </div>
            <div class="text-xs text-blue-600">
              Memory length: {{ bookMemory.length }} characters
              ({{ Math.ceil(bookMemory.length / 3) }} tokens estimated)
            </div>
          </div>
        </a-card>
      </div>

      <!-- Processing Progress -->
      <div v-if="isProcessing || processingProgress.status !== 'idle'" class="mb-6">
        <a-card>
          <div class="flex justify-between items-center mb-4">
            <div>
              <h3 class="text-lg font-medium">
                🔄 {{ getProcessingStatusText(processingProgress.status) }}
              </h3>
            </div>
            <a-button
              v-if="isProcessing"
              danger
              @click="stopProcessing"
            >
              ⏹️ Dừng
            </a-button>
          </div>

          <a-progress
            :percent="Math.round((processingProgress.current / processingProgress.total) * 100)"
            class="mb-2"
          />

          <p class="text-sm text-gray-600 mb-4">
            {{ processingProgress.current }}/{{ processingProgress.total }} chương
            <span v-if="processingProgress.currentChapter" class="ml-2">
              - Đang xử lý: {{ processingProgress.currentChapter.title }}
            </span>
          </p>

          <!-- Errors -->
          <div v-if="processingProgress.errors.length > 0">
            <a-collapse>
              <a-collapse-panel
                :key="1"
                :header="`${processingProgress.errors.length} lỗi`"
                class="text-red-600"
              >
                <div class="space-y-1">
                  <div
                    v-for="error in processingProgress.errors"
                    :key="error.chapter"
                    class="text-sm text-red-600"
                  >
                    Chương {{ error.chapter }}: {{ error.error }}
                  </div>
                </div>
              </a-collapse-panel>
            </a-collapse>
          </div>
        </a-card>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { novelSummaryService } from '@/lib/NovelSummaryService'

// Reactive data
const loading = ref(false)
const books = ref([])
const selectedBook = ref(null)
const summaryStats = ref({
  total: 0,
  pending: 0,
  processing: 0,
  completed: 0,
  failed: 0
})

const chapterRange = reactive({
  start: 1,
  end: 10
})

const processingOptions = reactive({
  extractContent: true,
  generateSummaries: true,
  skipExisting: true,
  delay: 10
})

const processingProgress = ref({
  current: 0,
  total: 0,
  status: 'idle',
  currentChapter: null,
  errors: []
})

const isProcessing = ref(false)

// URL Extraction
const extractionUrl = ref('')
const extracting = ref(false)
const extractionResult = ref(null)

// Text Viewer
const textViewRange = reactive({
  start: 1,
  end: 5
})

const textViewOptions = reactive({
  type: 'original_text' // 'original_text' or 'summary_text'
})

const loadingTexts = ref(false)
const chapterTexts = ref([])
const selectedTexts = computed(() => chapterTexts.value.filter(ch => ch.selected))

// Novel Summarization
const isNovelSummarizing = ref(false)
const novelSummaryProgress = ref({
  current: 0,
  total: 0,
  status: 'idle'
})
const bookMemory = ref('')

// Table columns
const bookColumns = [
  {
    title: 'Tên truyện',
    dataIndex: 'title',
    key: 'title',
    sorter: true
  },
  {
    title: 'Tác giả',
    dataIndex: 'author',
    key: 'author',
    sorter: true
  },
  {
    title: 'Thể loại',
    dataIndex: 'category',
    key: 'category'
  },
  {
    title: 'Trạng thái',
    dataIndex: 'status',
    key: 'status',
    align: 'center'
  },
  {
    title: 'Tiến độ',
    key: 'progress',
    align: 'center'
  },
  {
    title: 'Cập nhật',
    dataIndex: 'last_update_info',
    key: 'last_update_info',
    align: 'center'
  },
  {
    title: 'Action',
    key: 'action',
    align: 'center'
  }
]

// Computed properties
const isValidRange = computed(() => {
  return chapterRange.start && chapterRange.end &&
         chapterRange.start <= chapterRange.end &&
         chapterRange.start >= 1 &&
         (selectedBook.value ? chapterRange.end <= selectedBook.value.total_chapters : true)
})

const isValidTextRange = computed(() => {
  return textViewRange.start && textViewRange.end &&
         textViewRange.start <= textViewRange.end &&
         textViewRange.start >= 1
})

// Methods
const loadBooks = async () => {
  loading.value = true
  try {
    const result = await window.electronAPI.invoke('db-get-books')
    if (result.success) {
      books.value = result.data
    } else {
      message.error('Lỗi tải danh sách truyện: ' + result.error)
    }
  } catch (error) {
    console.error('Error loading books:', error)
    message.error('Lỗi kết nối database')
  } finally {
    loading.value = false
  }
}

const selectBook = async (record) => {
  selectedBook.value = record
  chapterRange.start = 1
  chapterRange.end = Math.min(10, record.total_chapters)

  // Load summary statistics for this book
  await loadSummaryStats(record.id)

  // Load book memory for Novel Summarization
  await loadBookMemory(record.id)
}

const loadSummaryStats = async (bookId) => {
  try {
    const result = await window.electronAPI.invoke('db-get-summary-stats', bookId)
    if (result.success) {
      summaryStats.value = result.data
    }
    console.log('summaryStats.value', result);
    
  } catch (error) {
    console.error('Error loading summary stats:', error)
  }
}

const getStatusColor = (status) => {
  const colors = {
    'ongoing': 'blue',
    'completed': 'green',
    'dropped': 'red'
  }
  return colors[status] || 'default'
}

const getProcessingStatusText = (status) => {
  const texts = {
    'idle': 'Chờ',
    'starting': 'Đang khởi động...',
    'processing': 'Đang xử lý...',
    'completed': 'Hoàn thành',
    'failed': 'Thất bại',
    'stopping': 'Đang dừng...'
  }

  if (status.startsWith('extracting_')) {
    return `Đang lấy nội dung chương ${status.split('_')[1]}`
  }

  if (status.startsWith('summarizing_')) {
    return `Đang tóm tắt chương ${status.split('_')[1]}`
  }

  return texts[status] || status
}

const startProcessing = async (type) => {
  if (!selectedBook.value || !isValidRange.value) {
    message.warning('Vui lòng chọn truyện và nhập khoảng chương hợp lệ')
    return
  }

  // Set processing options based on type
  switch (type) {
    case 'content':
      processingOptions.extractContent = true
      processingOptions.generateSummaries = false
      break
    case 'summary':
      processingOptions.extractContent = false
      processingOptions.generateSummaries = true
      break
    case 'both':
      processingOptions.extractContent = true
      processingOptions.generateSummaries = true
      break
  }

  isProcessing.value = true
  processingProgress.value = {
    current: 0,
    total: chapterRange.end - chapterRange.start + 1,
    status: 'starting',
    currentChapter: null,
    errors: []
  }

  try {
    const result = await window.electronAPI.invoke('process-chapters-range', {
      bookId: selectedBook.value.id,
      startIndex: chapterRange.start,
      endIndex: chapterRange.end,
      options: {
        extractContent: processingOptions.extractContent,
        generateSummaries: processingOptions.generateSummaries,
        delay: processingOptions.delay * 1000, // Convert to milliseconds
        skipExisting: processingOptions.skipExisting
      }
    })

    if (result.success) {
      message.success(`Xử lý hoàn thành! ${result.data.processed}/${result.data.total} chương thành công`)

      // Reload summary stats
      await loadSummaryStats(selectedBook.value.id)
    } else {
      message.error('Lỗi xử lý: ' + result.error)
    }
  } catch (error) {
    console.error('Error processing chapters:', error)
    message.error('Lỗi xử lý chương: ' + error.message)
  } finally {
    isProcessing.value = false
    processingProgress.value.status = 'completed'
  }
}

const stopProcessing = async () => {
  try {
    await window.electronAPI.invoke('stop-processing')
    isProcessing.value = false
    processingProgress.value.status = 'stopping'

    message.info('Đã dừng xử lý')
  } catch (error) {
    console.error('Error stopping processing:', error)
  }
}

// URL Extraction
const extractFromUrl = async () => {
  if (!extractionUrl.value.trim()) {
    message.warning('Vui lòng nhập URL truyện')
    return
  }

  extracting.value = true
  extractionResult.value = null

  try {
    console.log('Extracting from URL:', extractionUrl.value)

    // Call the extraction service (similar to runTests)
    const result = await window.electronAPI.invoke('extract-from-url', {
      url: extractionUrl.value.trim()
    })

    if (result.success) {
      extractionResult.value = {
        bookTitle: result.data.bookInfo?.title || 'Unknown',
        totalChapters: result.data.totalChapters || 0,
        bookId: result.data.databaseResult?.bookId
      }

      message.success(`Extraction thành công! Đã lấy ${result.data.totalChapters} chapters`)

      // Reload books list to show the new book
      await loadBooks()

      // Auto-select the new book if available
      if (result.data.databaseResult?.bookId) {
        const newBook = books.value.find(book => book.id === result.data.databaseResult.bookId)
        if (newBook) {
          await selectBook(newBook)
        }
      }

    } else {
      message.error('Lỗi extraction: ' + result.error)
    }

  } catch (error) {
    console.error('Error extracting from URL:', error)
    message.error('Lỗi kết nối: ' + error.message)
  } finally {
    extracting.value = false
  }
}

const clearExtractionResult = () => {
  extractionResult.value = null
}

// Text Viewer Functions
const loadChapterTexts = async () => {
  if (!selectedBook.value || !isValidTextRange.value) {
    message.warning('Vui lòng chọn truyện và nhập khoảng chương hợp lệ')
    return
  }

  loadingTexts.value = true
  chapterTexts.value = []

  try {
    console.log(`Loading ${textViewOptions.type} for chapters ${textViewRange.start}-${textViewRange.end}`)

    // Get summaries from database
    const result = await electronAPI.invoke('database', 'chapterSummaries.getSummariesByBook', selectedBook.value.id)

    if (result.length) {
      // Filter by chapter range and extract chapter numbers from titles
      const ChapterContentService = {
        extractChapterNumber(title) {
          const patterns = [
            /第(\d+)章/,           // 第576章
            /Chapter\s*(\d+)/i,    // Chapter 576
            /(\d+)章/,             // 576章
            /第(\d+)回/,           // 第576回
            /(\d+)回/,             // 576回
            /^(\d+)[\.\s]/,        // 576. or 576
            /第(\d+)节/,           // 第576节
            /(\d+)节/              // 576节
          ];

          for (const pattern of patterns) {
            const match = title.match(pattern);
            if (match) {
              return parseInt(match[1], 10);
            }
          }

          const numberMatch = title.match(/(\d+)/);
          if (numberMatch) {
            return parseInt(numberMatch[1], 10);
          }

          return null;
        }
      }

      // Process summaries and filter by range
      const filteredTexts = result
        .map(summary => {
          const realChapterNumber = ChapterContentService.extractChapterNumber(summary.chapter_title || '')
          return {
            ...summary,
            realChapterNumber,
            text: textViewOptions.type === 'original_text' ? summary.original_text : summary.summary_text,
            selected: false,
            expanded: false
          }
        })
        .filter(summary =>
          summary.chapter_id !== null &&
          summary.chapter_id >= textViewRange.start &&
          summary.chapter_id <= textViewRange.end &&
          summary.text && summary.text.trim().length > 0
        )
        .sort((a, b) => a.chapter_id - b.chapter_id)

      chapterTexts.value = filteredTexts

      if (filteredTexts.length === 0) {
        message.info(`Không tìm thấy ${textViewOptions.type === 'original_text' ? 'nội dung gốc' : 'tóm tắt'} cho chapters ${textViewRange.start}-${textViewRange.end}`)
      } else {
        message.success(`Tìm thấy ${filteredTexts.length} chapters với ${textViewOptions.type === 'original_text' ? 'nội dung gốc' : 'tóm tắt'}`)
      }
    } else {
      message.error('Lỗi tải dữ liệu: ' + (result.error || 'Unknown error'))
    }
  } catch (error) {
    console.error('Error loading chapter texts:', error)
    message.error('Lỗi kết nối database: ' + error.message)
  } finally {
    loadingTexts.value = false
  }
}

const selectAllTexts = () => {
  chapterTexts.value.forEach(chapter => {
    chapter.selected = true
  })
}

const deselectAllTexts = () => {
  chapterTexts.value.forEach(chapter => {
    chapter.selected = false
  })
}

const updateSelection = () => {
  // This function is called when individual checkboxes change
  // Vue's reactivity will handle the updates automatically
}

const toggleChapterExpand = (chapterId) => {
  const chapter = chapterTexts.value.find(ch => ch.id === chapterId)
  if (chapter) {
    chapter.expanded = !chapter.expanded
  }
}

const copyChapterText = async (chapter) => {
  if (!chapter.text) {
    message.warning('Chapter này chưa có text')
    return
  }

  try {
    await navigator.clipboard.writeText(chapter.text)
    message.success(`Đã copy Chapter ${chapter.chapter_id}`)
  } catch (error) {
    console.error('Copy failed:', error)
    message.error('Lỗi copy text')
  }
}

const copyAllTexts = async () => {
  if (chapterTexts.value.length === 0) {
    message.warning('Không có text để copy')
    return
  }

  const allText = chapterTexts.value
    .map(chapter => `=== Chapter ${chapter.chapter_id} ===\n${chapter.text}\n`)
    .join('\n')

  try {
    await navigator.clipboard.writeText(allText)
    message.success(`Đã copy tất cả ${chapterTexts.value.length} chapters`)
  } catch (error) {
    console.error('Copy failed:', error)
    message.error('Lỗi copy text')
  }
}

const copySelectedTexts = async () => {
  const selected = selectedTexts.value
  if (selected.length === 0) {
    message.warning('Chưa chọn chapter nào')
    return
  }

  const selectedText = selected
    .sort((a, b) => a.realChapterNumber - b.realChapterNumber)
    .map(chapter => `=== Chapter ${chapter.realChapterNumber} ===\n${chapter.text}\n`)
    .join('\n')

  try {
    await navigator.clipboard.writeText(selectedText)
    message.success(`Đã copy ${selected.length} chapters đã chọn`)
  } catch (error) {
    console.error('Copy failed:', error)
    message.error('Lỗi copy text')
  }
}

// Novel Summarization Methods
const startNovelSummarization = async () => {
  if (!selectedBook.value || !isValidRange.value) {
    message.warning('Vui lòng chọn truyện và nhập khoảng chương hợp lệ')
    return
  }

  isNovelSummarizing.value = true
  novelSummaryProgress.value = {
    current: 0,
    total: chapterRange.end - chapterRange.start + 1,
    status: 'starting'
  }

  try {
    // Load existing book memory from database
    await loadBookMemory(selectedBook.value.id)

    // Get chapters data from database
    const chaptersResult = await electronAPI.invoke('database', 'chapterSummaries.getSummariesByBook', selectedBook.value.id)

    if (!chaptersResult || chaptersResult.length === 0) {
      message.error('Không tìm thấy chapters để tóm tắt. Vui lòng lấy nội dung trước.')
      return
    }

    // Filter chapters by range and ensure they have original_text
    const chaptersToSummarize = chaptersResult
      .filter(chapter =>
        chapter.chapter_id >= chapterRange.start &&
        chapter.chapter_id <= chapterRange.end &&
        chapter.original_text &&
        chapter.original_text.trim().length > 0
      )
      .sort((a, b) => a.chapter_id - b.chapter_id)

    if (chaptersToSummarize.length === 0) {
      message.error('Không tìm thấy chapters có nội dung gốc trong khoảng đã chọn')
      return
    }

    console.log(`Starting Novel Summarization for ${chaptersToSummarize.length} chapters`)

    novelSummaryProgress.value.total = chaptersToSummarize.length
    novelSummaryProgress.value.status = 'processing'

    // Use NovelSummaryService
    const result = await novelSummaryService.summarizeChapters({
      chapters: chaptersToSummarize,
      batchSize: 5, // Smaller batch for summaries
      bookMemory: bookMemory.value,
      callback: updateSummarizedBatch,
      onProgress: (current, total) => {
        novelSummaryProgress.value.current = current
        novelSummaryProgress.value.total = total
      }
    })

    // Update book memory in database
    if (result.memory) {
      await updateBookMemory(selectedBook.value.id, result.memory)
      bookMemory.value = result.memory
    }

    message.success(`Novel Summarization hoàn thành! Đã tóm tắt ${result.summaries.length} chương`)

    // Reload summary stats
    await loadSummaryStats(selectedBook.value.id)

  } catch (error) {
    console.error('Novel Summarization failed:', error)
    message.error('Lỗi Novel Summarization: ' + error.message)
  } finally {
    isNovelSummarizing.value = false
    novelSummaryProgress.value.status = 'completed'
  }
}

const stopNovelSummarization = () => {
  // Note: This is a simple stop - in a real implementation,
  // you might want to add cancellation token support to NovelSummaryService
  isNovelSummarizing.value = false
  novelSummaryProgress.value.status = 'stopped'
  message.info('Đã dừng Novel Summarization')
}

const updateSummarizedBatch = async (summaryBatch, startIndex) => {
  console.log(`Updating ${summaryBatch.length} summarized chapters starting at index ${startIndex}`)

  try {
    // Update database with summarized texts
    for (const summary of summaryBatch) {
      await electronAPI.invoke('database', 'chapterSummaries.updateSummary', {
        bookId: selectedBook.value.id,
        chapterId: summary.chapter_id,
        summaryText: summary.summary_text,
        memory: summary.memory
      })
    }

    console.log(`Successfully updated ${summaryBatch.length} summaries in database`)

  } catch (error) {
    console.error('Error updating summarized batch:', error)
  }
}

const loadBookMemory = async (bookId) => {
  try {
    const result = await electronAPI.invoke('database', 'books.getBookMemory', bookId)
    if (result && result.memory) {
      bookMemory.value = result.memory
      console.log(`Loaded book memory: ${result.memory.length} characters`)
    } else {
      bookMemory.value = ''
      console.log('No existing book memory found')
    }
  } catch (error) {
    console.error('Error loading book memory:', error)
    bookMemory.value = ''
  }
}

const updateBookMemory = async (bookId, memory) => {
  try {
    await electronAPI.invoke('database', 'books.updateBookMemory', {
      bookId: bookId,
      memory: memory
    })
    console.log(`Updated book memory: ${memory.length} characters`)
  } catch (error) {
    console.error('Error updating book memory:', error)
  }
}

// Lifecycle
onMounted(() => {
  loadBooks()
})
</script>

<style scoped>
.cursor-pointer {
  cursor: pointer;
}

.ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5 !important;
}

.ant-table-tbody > tr.ant-table-row:hover {
  background-color: #f5f5f5;
}
</style>