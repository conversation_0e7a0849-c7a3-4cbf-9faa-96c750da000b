const { FFCreator, FFScene, FFImage, FFText,FFVideo } = require('ffcreatorlite');
const path = require('path');
const fs = require('fs');
const { execSync } = require('child_process');

const { generateASSSubtitle } = require('./assBuild');
const { getVideoInfo } = require('../ffmpegHandler');

class FFCreatorVideoRenderer {
  constructor() {
    this.ffmpegPath = 'ffmpeg';
  }

  async renderVideoWithSrt(srtArray, videoPath, outputDir, outputFileName, options = {}) {
    const {
      subtitleStyle = 'default',
      addLogo = false,
      logoPath = '',
      addText = '',
      textPosition = 'bottom'
    } = options;

    const outputRaw = path.join(outputDir, 'temp_nosub.mp4');
    const finalOutput = path.join(outputDir, outputFileName);

    // Tạo FFCreator
    const creator = new FFCreator({
      cacheDir: outputDir,
      outputDir,
      width: 1920,
      height: 1080,
      fps: 30,
      output: outputRaw,
      log: true,
    });
    const videoInfo = await getVideoInfo(null, videoPath);
    const scene = new FFScene();
    scene.setDuration(videoInfo.duration); // Hoặc điều chỉnh theo độ dài video/audio
    scene.setBgColor('#000000');

    // Video nền
    const videoBg = new FFVideo({ path: videoPath });
    videoBg.addOutput('-c:v h264_nvenc -preset medium -crf 23 -c:a aac -b:a 192k');
    // videoBg.setXY(960, 540); // Giữa màn hình 1920x1080
    scene.addChild(videoBg);

    // Logo
    if (addLogo && logoPath) {
      const logo = new FFImage({ path: logoPath, x: 100, y: 100 });
      scene.addChild(logo);
    }

    // Text overlay
    if (addText) {
      const text = new FFText({ text: addText, x: 960, y: textPosition === 'bottom' ? 1000 : 100 });
      text.setColor('#ffffff');
      text.setFontSize(40);
      scene.addChild(text);
    }

    creator.addChild(scene);

    creator.start();

    creator.on('progress', (e) => {
      console.log(`Rendering: ${(e.percent * 100).toFixed(0)}%`);
    });

    creator.on('complete', (e) => {
      console.log('Render complete:', e.output);

      // Tạo file ASS từ srtArray
    //   const assContent = generateASSSubtitle(srtArray, {
    //     resolution: { width: 1920, height: 1080 },
    //   });
    //   const assPath = path.join(outputDir, 'subtitles.ass');
    //   fs.writeFileSync(assPath, assContent, 'utf8');

      // Thêm phụ đề vào video bằng ffmpeg
    //   const cmd = `${this.ffmpegPath} -y -i "${outputRaw}" -vf "ass='${assPath.replace(/\\/g, '/')}'" -c:a copy "${finalOutput}"`;
    //   execSync(cmd, { stdio: 'inherit' });

      console.log('Final video with subtitles saved to:', finalOutput);
    });
  }
}

module.exports = {
  FFCreatorVideoRenderer,
};
