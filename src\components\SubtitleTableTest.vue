<template>
  <div class="p-6 bg-gray-50 min-h-screen">
    <div class="max-w-7xl mx-auto">
      <h1 class="text-2xl font-bold mb-6 text-gray-800">
        Test SubtitleTable với tính năng mới
      </h1>
      
      <!-- Control Panel -->
      <div class="bg-white p-4 rounded-lg shadow mb-6">
        <h2 class="text-lg font-semibold mb-4"><PERSON><PERSON><PERSON><PERSON></h2>
        <div class="flex gap-4 flex-wrap">
          <a-button @click="addSampleSubtitle" type="primary">
            Thêm subtitle mẫu
          </a-button>
          <a-button @click="clearSubtitles" danger>
            Xó<PERSON> tất cả
          </a-button>
          <a-button @click="exportSubtitles">
            Export JSON
          </a-button>
          <a-button @click="showStats">
            Thống kê
          </a-button>
        </div>
      </div>

      <!-- Stats -->
      <div v-if="stats.show" class="bg-blue-50 p-4 rounded-lg mb-6">
        <h3 class="font-semibold mb-2">Thống kê:</h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>Tổng số: <span class="font-bold">{{ subtitles.length }}</span></div>
          <div>Đã dịch: <span class="font-bold text-green-600">{{ stats.translated }}</span></div>
          <div>Lỗi: <span class="font-bold text-red-600">{{ stats.errors }}</span></div>
          <div>Tổng thời lượng: <span class="font-bold">{{ stats.totalDuration }}s</span></div>
        </div>
      </div>

      <!-- Subtitle Table -->
      <div class="bg-white rounded-lg shadow">
        <SubtitleTable
          :subtitles="subtitles"
          :on-retry="handleRetry"
          :on-update-translation="handleUpdateTranslation"
          :on-delete="handleDelete"
          :on-insert="handleInsert"
          :on-split="handleSplit"
          :on-merge="handleMerge"
          :on-reorder="handleReorder"
          :translating="false"
          :batch-size="10"
        />
      </div>

      <!-- Log Panel -->
      <div v-if="logs.length > 0" class="mt-6 bg-gray-800 text-green-400 p-4 rounded-lg">
        <h3 class="font-semibold mb-2 text-white">Activity Log:</h3>
        <div class="max-h-40 overflow-y-auto text-sm font-mono">
          <div v-for="(log, index) in logs.slice(-10)" :key="index" class="mb-1">
            [{{ log.time }}] {{ log.message }}
          </div>
        </div>
        <a-button size="small" @click="clearLogs" class="mt-2">Clear Logs</a-button>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, computed } from 'vue';
import SubtitleTable from './SubtitleTable.vue';
import { message } from 'ant-design-vue';
import { 
  reorderSubtitleIds, 
  adjustSubtitleTimes, 
  mergeSubtitles,
  fixOverlappingSubtitles,
  secondsToSRTTime
} from '@/lib/subtitleUtils';

export default defineComponent({
  name: 'SubtitleTableTest',
  components: {
    SubtitleTable
  },
  setup() {
    const subtitles = ref([]);
    const logs = ref([]);
    const stats = ref({ show: false });

    // Computed stats
    const computedStats = computed(() => ({
      translated: subtitles.value.filter(s => s.status === 'translated').length,
      errors: subtitles.value.filter(s => s.status === 'error').length,
      totalDuration: subtitles.value.reduce((sum, s) => sum + (s.endTime - s.startTime), 0).toFixed(2)
    }));

    const addLog = (message) => {
      logs.value.push({
        time: new Date().toLocaleTimeString(),
        message
      });
    };

    const createSampleSubtitle = (id, startTime, text) => ({
      id,
      index: id,
      start: secondsToSRTTime(startTime),
      end: secondsToSRTTime(startTime + 2),
      startTime,
      endTime: startTime + 2,
      text,
      translatedText: `Dịch: ${text}`,
      status: 'translated',
      isEnabled: true,
      isGenerated: false,
      isGenerated1: false,
      isGenerated2: false,
      isGenerated3: false,
      audioUrl: '',
      audioUrl1: '',
      audioUrl2: '',
      audioUrl3: '',
      duration: 0,
      selectedSpeaker: '',
      speechRate: 0,
      isPlayable: false,
      isVoice: 1
    });

    const addSampleSubtitle = () => {
      const newId = subtitles.value.length + 1;
      const startTime = subtitles.value.length * 3; // 3 seconds apart
      const sampleTexts = [
        'Hello world',
        'This is a test subtitle',
        'Another subtitle for testing',
        'Long subtitle with more text to test splitting functionality',
        'Short text',
        'Medium length subtitle for demonstration',
        'Final test subtitle'
      ];
      
      const text = sampleTexts[Math.floor(Math.random() * sampleTexts.length)];
      const newSubtitle = createSampleSubtitle(newId, startTime, text);
      
      subtitles.value.push(newSubtitle);
      addLog(`Added sample subtitle: "${text}"`);
      message.success('Added sample subtitle');
    };

    const clearSubtitles = () => {
      subtitles.value = [];
      addLog('Cleared all subtitles');
      message.info('Cleared all subtitles');
    };

    const exportSubtitles = () => {
      const data = JSON.stringify(subtitles.value, null, 2);
      console.log('Exported subtitles:', data);
      addLog(`Exported ${subtitles.value.length} subtitles to console`);
      message.success('Exported to console');
    };

    const showStats = () => {
      stats.value = { show: true, ...computedStats.value };
      addLog('Showed statistics');
    };

    const clearLogs = () => {
      logs.value = [];
    };

    // Event handlers
    const handleRetry = (id) => {
      addLog(`Retry subtitle ${id}`);
      message.info(`Retrying subtitle ${id}`);
    };

    const handleUpdateTranslation = (id, newTranslation) => {
      const subtitle = subtitles.value.find(s => s.id === id);
      if (subtitle) {
        subtitle.translatedText = newTranslation;
        addLog(`Updated translation for subtitle ${id}`);
        message.success(`Updated subtitle ${id}`);
      }
    };

    const handleDelete = (id) => {
      const index = subtitles.value.findIndex(s => s.id === id);
      if (index !== -1) {
        subtitles.value.splice(index, 1);
        subtitles.value = reorderSubtitleIds(subtitles.value);
        addLog(`Deleted subtitle ${id}, reordered IDs`);
        message.success(`Deleted subtitle ${id}`);
      }
    };

    const handleInsert = (newSubtitle, targetId, position, adjustTiming = true) => {
      const targetIndex = subtitles.value.findIndex(s => s.id === targetId);
      if (targetIndex === -1) return;

      const insertIndex = position === 'before' ? targetIndex : targetIndex + 1;
      
      subtitles.value.splice(insertIndex, 0, newSubtitle);
      subtitles.value = reorderSubtitleIds(subtitles.value);
      
      if (adjustTiming) {
        const insertDuration = newSubtitle.endTime - newSubtitle.startTime;
        subtitles.value = adjustSubtitleTimes(subtitles.value, insertIndex + 1, insertDuration);
      }
      
      subtitles.value = fixOverlappingSubtitles(subtitles.value);
      
      addLog(`Inserted subtitle ${position} ${targetId}, adjustTiming: ${adjustTiming}`);
      message.success(`Inserted new subtitle ${position} subtitle ${targetId}`);
    };

    const handleSplit = (id, splitSubtitles) => {
      const index = subtitles.value.findIndex(s => s.id === id);
      if (index === -1) return;

      subtitles.value.splice(index, 1, ...splitSubtitles);
      subtitles.value = reorderSubtitleIds(subtitles.value);
      
      addLog(`Split subtitle ${id} into ${splitSubtitles.length} parts`);
      message.success(`Split subtitle ${id} into ${splitSubtitles.length} parts`);
    };

    const handleMerge = (ids, mergedSubtitle) => {
      const indices = ids.map(id => subtitles.value.findIndex(s => s.id === id)).sort((a, b) => b - a);
      
      if (indices.some(i => i === -1)) return;

      indices.forEach(index => {
        subtitles.value.splice(index, 1);
      });
      
      subtitles.value.splice(Math.min(...indices), 0, mergedSubtitle);
      subtitles.value = reorderSubtitleIds(subtitles.value);
      
      addLog(`Merged subtitles ${ids.join(', ')}`);
      message.success(`Merged subtitles ${ids.join(', ')}`);
    };

    const handleReorder = (newOrder) => {
      subtitles.value = reorderSubtitleIds(newOrder);
      addLog('Reordered subtitles');
      message.success('Reordered subtitles');
    };

    // Initialize with some sample data
    for (let i = 1; i <= 3; i++) {
      addSampleSubtitle();
    }

    return {
      subtitles,
      logs,
      stats,
      addSampleSubtitle,
      clearSubtitles,
      exportSubtitles,
      showStats,
      clearLogs,
      handleRetry,
      handleUpdateTranslation,
      handleDelete,
      handleInsert,
      handleSplit,
      handleMerge,
      handleReorder
    };
  }
});
</script>
