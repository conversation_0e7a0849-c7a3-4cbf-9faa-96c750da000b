// API utilities for handling different AI service endpoints

/**
 * Make API request with proper error handling and CORS support
 */
export async function makeApiRequest(url, options = {}) {
  try {
    // For Electron app, we can make direct requests
    // For web app, might need proxy
    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    });

    if (!response.ok) {
      const errorText = await response.text().catch(() => 'Unknown error');
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    return await response.json();
  } catch (error) {
    // Handle network errors, CORS issues, etc.
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      throw new Error('Network error - check your internet connection or API endpoint');
    }
    throw error;
  }
}

/**
 * Get models from OpenAI-compatible API
 */
export async function getOpenAIModels(baseURL, apiKey) {
  const url = `${baseURL}/models`;
  const data = await makeApiRequest(url, {
    headers: {
      'Authorization': `Bearer ${apiKey}`
    }
  });

  if (data.data && Array.isArray(data.data)) {
    // For OpenRouter, return full model objects with pricing info
    if (baseURL.includes('openrouter.ai')) {
      return data.data
        .filter(model => model.id && typeof model.id === 'string')
        .map(model => ({
          id: model.id,
          name: model.name || model.id,
          pricing: model.pricing || {},
          context_length: model.context_length || 0,
          architecture: model.architecture || {},
          top_provider: model.top_provider || {}
        }))
        .sort((a, b) => a.id.localeCompare(b.id));
    }

    // For other OpenAI-compatible APIs, return just IDs
    return data.data
      .map(model => model.id)
      .filter(id => id && typeof id === 'string')
      .sort();
  }

  return [];
}

/**
 * Get models from Gemini API
 */
export async function getGeminiModels(baseURL, apiKey) {
  const url = `${baseURL}/models?key=${apiKey}`;
  const data = await makeApiRequest(url);

  if (data.models && Array.isArray(data.models)) {
    return data.models
      .map(model => model.name ? model.name.replace('models/', '') : null)
      .filter(name => name && typeof name === 'string')
      .filter(name => name.includes('gemini')) // Filter only Gemini models
      .sort();
  }
  
  return [];
}

/**
 * Get models from Claude API
 */
export async function getClaudeModels(baseURL, apiKey) {
  const url = `${baseURL}/models`;
  const data = await makeApiRequest(url, {
    headers: {
      'x-api-key': apiKey,
      'anthropic-version': '2023-06-01'
    }
  });

  // Claude API might not have a models endpoint yet
  // Return default models for now
  if (data.models && Array.isArray(data.models)) {
    return data.models
      .map(model => model.id || model.name)
      .filter(id => id && typeof id === 'string')
      .sort();
  }
  
  // Fallback to known Claude models
  return [
    'claude-3-5-sonnet-20241022',
    'claude-3-5-haiku-20241022',
    'claude-3-opus-20240229',
    'claude-3-sonnet-20240229',
    'claude-3-haiku-20240307'
  ];
}

/**
 * Validate API key format
 */
export function validateApiKey(serviceKey, apiKey) {
  if (!apiKey || typeof apiKey !== 'string') {
    return { valid: false, message: 'API key is required' };
  }

  const trimmedKey = apiKey.trim();

  switch (serviceKey) {
    case 'openai':
      if (!trimmedKey.startsWith('sk-')) {
        return { valid: false, message: 'OpenAI API key should start with "sk-"' };
      }
      break;

    case 'deepseek':
      if (!trimmedKey.startsWith('sk-')) {
        return { valid: false, message: 'DeepSeek API key should start with "sk-"' };
      }
      break;

    case 'gemini':
      if (trimmedKey.length < 20) {
        return { valid: false, message: 'Gemini API key seems too short' };
      }
      break;

    case 'claude':
      if (!trimmedKey.startsWith('sk-ant-')) {
        return { valid: false, message: 'Claude API key should start with "sk-ant-"' };
      }
      break;

    case 'openrouter':
      if (!trimmedKey.startsWith('sk-or-')) {
        return { valid: false, message: 'OpenRouter API key should start with "sk-or-"' };
      }
      break;
  }

  return { valid: true, message: 'API key format looks valid' };
}

/**
 * Test API connection by making a simple request
 */
export async function testApiConnection(serviceKey, baseURL, apiKey) {
  const validation = validateApiKey(serviceKey, apiKey);
  if (!validation.valid) {
    throw new Error(validation.message);
  }

  try {
    switch (serviceKey) {
      case 'openai':
      case 'deepseek':
      case 'claude':
      case 'openrouter':
        await getOpenAIModels(baseURL, apiKey);
        break;

      case 'gemini':
        await getGeminiModels(baseURL, apiKey);
        break;

      default:
        throw new Error(`Unsupported service: ${serviceKey}`);
    }
    
    return { success: true, message: 'Connection successful' };
  } catch (error) {
    return { success: false, message: error.message };
  }
}
