### graphql
POST https://web.dengni.online/_graphql
Content-Type: application/json
Authorization: Bearer xxx
X-REQUEST-TYPE: GraphQL

query MyQuery {
  accounts {
      id
      password
      username
      createdAt
  }
}


###
POST https://reng.dengni.online/yeucau
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.LN47_pfVaYnaBX5UygndIC8SxSWwhxhXRK-5izT14QA

{
    "cmd": "getLastActiveAccounts",
    "args": [
        "kg1",
        "1",
        [
            "admin",
            "mod",
            "mod_semiautotw"
        ]
    ]
}

###
POST https://reng.dengni.online/yeucau
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.LN47_pfVaYnaBX5UygndIC8SxSWwhxhXRK-5izT14QA

{
    "cmd": "getOnline2",
    "args": ["notification"]
}

###
POST https://reng.dengni.online/yeucau
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.LN47_pfVaYnaBX5UygndIC8SxSWwhxhXRK-5izT14QA

{
    "cmd": "getSemiautoVoice2s",
    "args": ["notification"]
}
