<template>
  <div class="flex-1 flex flex-col ">
    <h2 class="text-xl font-semibold">Video OCR Processor</h2>

    <!-- Video upload area -->
    <div v-if="!videoFile">
      <DragDropUpload
        accept="video/*"
        :max-size="10 * 1024 * 1024 * 1024"
        :show-preview="false"
        drag-text="Drag and drop video file here"
        drop-text="Drop video file here"
        click-text="or click to select video"
        @files-selected="handleVideoSelected"
      />
    </div>

    <!-- Video preview and controls -->
    <div v-if="videoFile" class="space-y-4 w-full mx-auto">
      <!-- Video info -->
      <div class="flex justify-between items-center">
        <div class="text-sm flex items-center px-2 py-1 bg-gray-50 rounded-md flex-1 mr-2">
          <div class="font-medium truncate">
            <span class="text-gray-500 mr-1">Video:</span> {{ videoFile.name }}
          </div>
        </div>
        <a-button @click="resetVideo" size="small">Reset</a-button>
      </div>

      <!-- Video preview with crop selection -->
      <div class="relative rounded-md overflow-hidden bg-black flex items-center justify-center min-h-[300px]">
        <div ref="videoContainer" class="relative">
          <!-- Video element -->
          <video
            ref="videoRef"
            :src="videoUrl"
            class="max-w-full max-h-[400px] object-contain"
            @loadedmetadata="handleVideoLoaded"
            @timeupdate="handleTimeUpdate"
            @click="togglePlayPause"
            @error="handleVideoError"
            @loadstart="handleVideoLoadStart"
            @canplay="handleVideoCanPlay"
          ></video>

          <!-- CropSelector positioned exactly over the video -->
          <div
            v-if="displayDimensions.width > 0"
            class="absolute z-10"
            :style="{
              width: `${displayDimensions.width}px`,
              height: `${displayDimensions.height}px`,
              left: '50%',
              top: '50%',
              transform: 'translate(-50%, -50%)',
              pointerEvents: showSelectionOverlay ? 'auto' : 'none'
            }"
          >
            <CropSelector
              ref="cropSelector"
              :active="showSelectionOverlay"
              :content-width="videoDimensions.width"
              :content-height="videoDimensions.height"
              :show-coordinates="true"
              @crop-selected="onCropSelected"
              @crop-change="onCropChange"
              @crop-cleared="onCropCleared"
              class="w-full h-full"
              :style="{
                backgroundColor: showSelectionOverlay ? 'rgba(0,0,0,0.1)' : 'transparent'
              }"
            >
              <div class="w-full h-full"></div>
            </CropSelector>
          </div>
        </div>
      </div>
      <div class="flex-1 flex flex-col">
        <!-- Custom video controls -->
        <div class="p-2 bg-gray-900 rounded-md">
          <div class="flex items-center space-x-2">
            <!-- Play/Pause button -->
            <button
              @click="togglePlayPause"
              class="px-2 py-1 bg-gray-600 hover:bg-gray-500 rounded"
            >
              {{ isPlaying ? '⏸️ Pause' : '▶️ Play' }}
            </button>

            <!-- Progress bar -->
            <div
              ref="progressBarRef"
              class="flex-grow h-3 bg-gray-700 rounded-full overflow-hidden cursor-pointer relative group"
              @click="seekVideo"
              @mousedown="startSeeking"
              @mousemove="onProgressHover"
              @mouseleave="hideProgressTooltip"
            >
              <!-- Background track -->
              <div class="absolute inset-0 bg-gray-600 rounded-full"></div>

              <!-- Progress fill -->
              <div
                class="h-full bg-gradient-to-r from-blue-500 to-blue-400 rounded-full transition-all duration-75 ease-out relative"
                :style="{ width: `${videoProgress}%` }"
              >
                <!-- Progress handle -->
                <div
                  class="absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2 w-4 h-4 bg-white rounded-full shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 cursor-grab active:cursor-grabbing"
                  :class="{ 'opacity-100': isSeeking }"
                ></div>
              </div>

              <!-- Hover tooltip -->
              <div
                v-if="showProgressTooltip"
                class="absolute bottom-6 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded pointer-events-none transform -translate-x-1/2"
                :style="{ left: `${tooltipPosition}%` }"
              >
                {{ formatTime(tooltipTime) }}
              </div>
            </div>

            <!-- Time display -->
            <div class="text-xs text-gray-600">
              {{ formatTime(currentTime) }} / {{ formatTime(duration) }}
            </div>

            <!-- Volume control -->
            <div class="flex items-center space-x-1">
              <button @click="toggleMute" class="px-1 py-1 bg-gray-600 hover:bg-gray-300 rounded">
                {{ isMuted ? '🔇' : '🔊' }}
              </button>
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                v-model="volume"
                class="w-16"
              />
            </div>
          </div>
        </div>

        <!-- Selection controls -->
        <div class="mt-2 flex justify-between items-center">
          <div>
            <a-button
              size="small"
              @click="toggleSelectionOverlay"
              :type="showSelectionOverlay ? 'primary' : 'default'"
            >
              {{ showSelectionOverlay ? 'Cancel Selection' : 'Select Region' }}
            </a-button>
            <a-button
              v-if="currentCrop && currentCrop.width > 0"
              size="small"
              @click="clearSelection"
              class="ml-2"
            >
              Clear Selection
            </a-button>
              <a-button
              size="small"
              @click="convertVideo"
              class="ml-2"
              :loading="isConvert"
            >
              Convert Video
            </a-button>

          </div>
          <div v-if="currentCrop && currentCrop.width > 0" class="text-xs text-gray-500">
            Selected region: {{ formatCropValue() }}
          </div>

          <!-- convert if error encoder -->

        </div>
      </div>

      <!-- OCR settings -->
      <div class="space-y-4 p-4 border border-gray-200 rounded-md">
        <h3 class="text-lg font-medium">OCR Settings</h3>

        <!-- Language selection -->
        <div class="space-y-1">
          <label class="text-sm font-medium">Language</label>
          <a-select v-model:value="language" style="width: 100%">
            <a-select-option value="zh">Chinese</a-select-option>
            <a-select-option value="en">English</a-select-option>
            <a-select-option value="ja">Japanese</a-select-option>
            <a-select-option value="ko">Korean</a-select-option>
            <!-- <a-select-option value="vi">Vietnamese</a-select-option> -->
          </a-select>
        </div>

        <!-- Frame rate -->
        <div class="space-y-1">
          <label class="text-sm font-medium">Frame Rate (frames per second)</label>
          <a-slider
            v-model:value="frameRate"
            :min="1"
            :max="10"
            :step="1"
          />
          <div class="text-xs text-gray-500">
            Higher values capture more text but take longer to process
          </div>
        </div>

        <!-- Batch script path -->
        <div class="space-y-1">
          <label class="text-sm font-medium">OCR Script Path (Optional)</label>
          <div class="flex space-x-2">
            <a-input v-model:value="batchScriptPath" placeholder="Path to OCR batch script" />
            <a-button @click="selectBatchScript">Browse</a-button>
          </div>
          <p class="text-xs text-gray-500">Leave empty to use built-in OCR</p>
        </div>
      </div>

      <!-- Process buttons -->
      <div class="flex justify-end space-x-2">
        <a-button
          v-if="isProcessing && currentProcessId"
          danger
          @click="stopProcessing"
        >
          Stop Processing
        </a-button>
        <a-button
          type="primary"
          @click="processVideo"
          :loading="isProcessing"
          :disabled="!videoFile || isProcessing"
        >
          {{ isProcessing ? 'Processing...' : 'Extract Text with OCR' }}
        </a-button>
      </div>

      <!-- Processing status -->
      <div v-if="isProcessing" class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
        <div class="flex items-center">
          <div class="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-blue-500 mr-2"></div>
          <p class="text-blue-700">{{ processingStatus }}</p>
        </div>
      </div>

      <!-- Result -->
      <div v-if="srtFilePath" class="mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
        <div class="flex justify-between items-center">
          <div>
            <p class="text-green-700 font-medium">OCR processing complete!</p>
            <p class="text-sm text-gray-600">SRT file saved to: {{ srtFilePath }}</p>
          </div>
          <div class="space-x-2">
            <a-button type="primary" @click="openSrtFile">Open SRT</a-button>
            <a-button @click="importToSrtTable">Import to SRT Table</a-button>
          </div>
        </div>
      </div>

      <!-- Error -->
      <div v-if="error" class="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
        <p class="text-red-700">Error: {{ error }}</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onBeforeUnmount } from 'vue';
import { message } from 'ant-design-vue';
import DragDropUpload from './DragDropUpload.vue';
import CropSelector from './CropSelector.vue';
import { useSRTStore } from '../stores/srtStore';

const srtStore = useSRTStore();

// Reactive state
const videoFile = ref(null);
const videoUrl = ref(null);
const videoRef = ref(null);
const videoContainer = ref(null);
const language = ref('zh');
const frameRate = ref(3);
const batchScriptPath = ref('');
const isProcessing = ref(false);
const processingStatus = ref('');
const currentProcessId = ref(null);
const srtFilePath = ref(null);
const error = ref(null);

// Video player state
const isPlaying = ref(false);
const currentTime = ref(0);
const duration = ref(0);
const volume = ref(1);
const isMuted = ref(false);
const videoProgress = ref(0);

// Progress bar state
const progressBarRef = ref(null);
const isSeeking = ref(false);
const showProgressTooltip = ref(false);
const tooltipPosition = ref(0);
const tooltipTime = ref(0);

// Crop selection state
const showSelectionOverlay = ref(false);
const currentCrop = ref(null);
const cropSelector = ref(null);
const videoDimensions = ref({ width: 0, height: 0 });
const displayDimensions = ref({ width: 0, height: 0 });
const isConvert = ref(false);

// Handle video selection
const handleVideoSelected = (files) => {
  if (files && files.length > 0) {
    handleVideoFile(files[0]);
  }
};

// Process video file
const handleVideoFile = (file) => {
  // Check file type
  if (!file.type.startsWith('video/')) {
    message.error('Invalid file type. Please select a video file.');
    return;
  }

  // Revoke old URL to avoid memory leaks
  if (videoUrl.value) {
    URL.revokeObjectURL(videoUrl.value);
  }

  videoFile.value = file;
  // Create object URL for video preview
  videoUrl.value = URL.createObjectURL(file);

  console.log('Video file loaded:', {
    fileName: file.name,
    fileSize: file.size,
    fileType: file.type,
    videoUrl: videoUrl.value
  });

  // Reset result and error
  srtFilePath.value = null;
  error.value = null;

  // Reset selection
  clearSelection();

  // Reset display dimensions
  displayDimensions.value = { width: 0, height: 0 };
};

// Calculate actual displayed video dimensions (considering object-contain)
const calculateDisplayDimensions = () => {
  if (!videoRef.value || !videoDimensions.value.width) return;

  const videoElement = videoRef.value;
  const containerRect = videoElement.getBoundingClientRect();

  // Get the actual rendered size of the video
  const videoAspectRatio = videoDimensions.value.width / videoDimensions.value.height;
  const containerAspectRatio = containerRect.width / containerRect.height;

  let displayWidth, displayHeight;

  if (videoAspectRatio > containerAspectRatio) {
    // Video is wider than container - fit by width
    displayWidth = containerRect.width;
    displayHeight = containerRect.width / videoAspectRatio;
  } else {
    // Video is taller than container - fit by height
    displayHeight = containerRect.height;
    displayWidth = containerRect.height * videoAspectRatio;
  }

  displayDimensions.value = {
    width: displayWidth,
    height: displayHeight
  };

  console.log('Display dimensions calculated:', {
    videoDimensions: videoDimensions.value,
    containerRect: { width: containerRect.width, height: containerRect.height },
    displayDimensions: displayDimensions.value,
    videoAspectRatio,
    containerAspectRatio
  });
};

// Handle video loaded metadata
const handleVideoLoaded = () => {
  if (videoRef.value) {
    // Store video dimensions for crop calculations
    videoDimensions.value = {
      width: videoRef.value.videoWidth,
      height: videoRef.value.videoHeight
    };

    console.log('Video metadata loaded:', {
      videoDimensions: videoDimensions.value,
      duration: videoRef.value.duration,
      videoSrc: videoRef.value.src
    });

    // Calculate display dimensions
    setTimeout(() => {
      calculateDisplayDimensions();
    }, 100); // Small delay to ensure video is rendered

    // Set video duration
    duration.value = videoRef.value.duration;

    // Reset video player state
    currentTime.value = 0;
    isPlaying.value = false;
    videoProgress.value = 0;

    // Set default volume
    videoRef.value.volume = volume.value;
  }
};

// Handle video time update
const handleTimeUpdate = () => {
  if (videoRef.value) {
    currentTime.value = videoRef.value.currentTime;
    videoProgress.value = (currentTime.value / duration.value) * 100;
  }
};

// Handle video error
const handleVideoError = (event) => {
  console.error('Video error:', event);
  error.value = 'Failed to load video. Please check the file format.';
};

// Handle video load start
const handleVideoLoadStart = () => {
  console.log('Video load started');
};

// Handle video can play
const handleVideoCanPlay = () => {
  console.log('Video can play');
};

// Toggle play/pause
const togglePlayPause = () => {
  if (!videoRef.value) return;

  if (isPlaying.value) {
    videoRef.value.pause();
  } else {
    videoRef.value.play();
  }

  isPlaying.value = !isPlaying.value;
};

// Seek video to position
const seekVideo = (event) => {
  if (!videoRef.value || !progressBarRef.value) return;

  const rect = progressBarRef.value.getBoundingClientRect();
  const pos = Math.max(0, Math.min(1, (event.clientX - rect.left) / rect.width));
  videoRef.value.currentTime = pos * duration.value;
};

// Start seeking (mouse down on progress bar)
const startSeeking = (event) => {
  if (!videoRef.value || !progressBarRef.value) return;

  isSeeking.value = true;
  seekVideo(event);

  // Add global mouse events for smooth dragging
  document.addEventListener('mousemove', onSeekingMouseMove);
  document.addEventListener('mouseup', stopSeeking);

  event.preventDefault();
};

// Handle mouse move while seeking
const onSeekingMouseMove = (event) => {
  if (!isSeeking.value) return;
  seekVideo(event);
};

// Stop seeking (mouse up)
const stopSeeking = () => {
  isSeeking.value = false;
  document.removeEventListener('mousemove', onSeekingMouseMove);
  document.removeEventListener('mouseup', stopSeeking);
};

// Handle progress bar hover
const onProgressHover = (event) => {
  if (!progressBarRef.value || !duration.value) return;

  const rect = progressBarRef.value.getBoundingClientRect();
  const pos = Math.max(0, Math.min(1, (event.clientX - rect.left) / rect.width));

  tooltipPosition.value = pos * 100;
  tooltipTime.value = pos * duration.value;
  showProgressTooltip.value = true;
};

// Hide progress tooltip
const hideProgressTooltip = () => {
  showProgressTooltip.value = false;
};

// Toggle mute
const toggleMute = () => {
  if (!videoRef.value) return;

  videoRef.value.muted = !videoRef.value.muted;
  isMuted.value = videoRef.value.muted;
};

// Watch volume changes
watch(volume, (newVolume) => {
  if (videoRef.value) {
    videoRef.value.volume = newVolume;
  }
});

// Format time (seconds to MM:SS format)
const formatTime = (seconds) => {
  if (!seconds || isNaN(seconds)) return '00:00';

  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

// Reset video
const resetVideo = () => {
  // Stop video playback if playing
  if (videoRef.value && isPlaying.value) {
    videoRef.value.pause();
    isPlaying.value = false;
  }

  // Revoke object URL
  if (videoUrl.value) {
    URL.revokeObjectURL(videoUrl.value);
  }

  // Reset video state
  videoFile.value = null;
  videoUrl.value = null;
  srtFilePath.value = null;
  error.value = null;
  currentTime.value = 0;
  duration.value = 0;
  videoProgress.value = 0;

  // Reset selection
  clearSelection();

  // Reset display dimensions
  displayDimensions.value = { width: 0, height: 0 };

  // If there's an active process, stop it
  if (currentProcessId.value && isProcessing.value) {
    stopProcessing();
  } else {
    currentProcessId.value = null;
  }
};

// CropSelector event handlers
const onCropSelected = (cropData) => {
  currentCrop.value = cropData.normalized;
  console.log('Crop selected:', {
    display: cropData.display,
    normalized: cropData.normalized,
    videoDimensions: videoDimensions.value,
    displayDimensions: displayDimensions.value,
    finalCrop: formatCropValue(),
    calculatedCoords: {
      x1: cropData.normalized.x / videoDimensions.value.width,
      y1: cropData.normalized.y / videoDimensions.value.height,
      x2: (cropData.normalized.x + cropData.normalized.width) / videoDimensions.value.width,
      y2: (cropData.normalized.y + cropData.normalized.height) / videoDimensions.value.height
    }
  });
};

const onCropChange = (cropData) => {
  currentCrop.value = cropData.normalized;
};

const onCropCleared = () => {
  currentCrop.value = null;
};

// Toggle selection overlay
const toggleSelectionOverlay = () => {
  showSelectionOverlay.value = !showSelectionOverlay.value;

  console.log('Toggle selection overlay:', showSelectionOverlay.value);

  // Recalculate display dimensions when toggling selection
  if (showSelectionOverlay.value) {
    setTimeout(() => {
      calculateDisplayDimensions();
      console.log('After calculate, displayDimensions:', displayDimensions.value);
      if (cropSelector.value) {
        cropSelector.value.activate();
        console.log('CropSelector activated');
      }
    }, 50);
  } else if (cropSelector.value) {
    cropSelector.value.deactivate();
    console.log('CropSelector deactivated');
  }
};

// Clear selection
const clearSelection = () => {
  if (cropSelector.value) {
    cropSelector.value.clearSelection();
  }
  currentCrop.value = null;
};

// Format crop value for OCR
const formatCropValue = () => {
  if (!currentCrop.value || currentCrop.value.width === 0) return '';
  if (!videoDimensions.value.width) return '';

  // currentCrop.value is already in video pixel coordinates from CropSelector
  // Convert to normalized coordinates (0-1 range) for OCR script
  const x1 = currentCrop.value.x / videoDimensions.value.width;
  const y1 = currentCrop.value.y / videoDimensions.value.height;
  const x2 = (currentCrop.value.x + currentCrop.value.width) / videoDimensions.value.width;
  const y2 = (currentCrop.value.y + currentCrop.value.height) / videoDimensions.value.height;

  // Ensure all values are between 0 and 1
  const clampedX1 = Math.max(0, Math.min(1, x1));
  const clampedY1 = Math.max(0, Math.min(1, y1));
  const clampedX2 = Math.max(0, Math.min(1, x2));
  const clampedY2 = Math.max(0, Math.min(1, y2));

  return `${clampedX1.toFixed(2)},${clampedY1.toFixed(2)},${clampedX2.toFixed(2)},${clampedY2.toFixed(2)}`;
};

// Get crop value for OCR
const getCropValue = () => {
  if (!currentCrop.value || currentCrop.value.width === 0) return null;
  return formatCropValue();
};

// Select batch script
const selectBatchScript = async () => {
  try {
    const result = await window.electronAPI.openFileDialog({
      properties: ['openFile'],
      filters: [
        { name: 'Batch Files', extensions: ['bat', 'cmd', 'sh', 'py'] },
        { name: 'All Files', extensions: ['*'] }
      ]
    });

    if (!result.canceled && result.filePaths && result.filePaths.length > 0) {
      batchScriptPath.value = result.filePaths[0];
    }
  } catch (err) {
    console.error('Error selecting batch script:', err);
    message.error('Error selecting batch script');
  }
};

// Process video with OCR
const processVideo = async () => {
  if (!videoFile.value) return;

  isProcessing.value = true;
  error.value = null;
  srtFilePath.value = null;
  currentProcessId.value = null;

  try {
    processingStatus.value = 'Processing video with OCR...';

    // Get crop value if selection exists
    const cropValue = getCropValue();

    // Use batch script if provided, otherwise use built-in OCR
    let result;

    if (batchScriptPath.value) {
      // Use batch script
      result = await window.electronAPI.runOcrBatch({
        videoPath: videoFile.value.path,
        batchPath: batchScriptPath.value
      });
    } else {
      // Use built-in OCR
      result = await window.electronAPI.processVideoOcr({
        videoPath: videoFile.value.path,
        lang: language.value,
        frameRate: frameRate.value,
        crop: cropValue
      });
    }

    if (result.success) {
      currentProcessId.value = result.processId;
      srtFilePath.value = result.outputPath;

      // Wait for processing to complete
      await checkProcessStatus();

      message.success('OCR processing completed successfully');
    } else {
      throw new Error(result.error || 'Failed to process video with OCR');
    }
  } catch (err) {
    error.value = err.message || 'Unknown error occurred';
    message.error('Error processing video: ' + error.value);
  } finally {
    isProcessing.value = false;
    processingStatus.value = '';
  }
};

// Stop processing
const stopProcessing = async () => {
  if (!currentProcessId.value) return;

  try {
    const result = await window.electronAPI.stopProcess(currentProcessId.value);

    if (result.success) {
      message.success('Processing stopped');
      isProcessing.value = false;
      currentProcessId.value = null;
    } else {
      message.error('Failed to stop processing: ' + (result.error || 'Unknown error'));
    }
  } catch (err) {
    message.error('Error stopping process: ' + (err.message || 'Unknown error'));
  }
};

// Check process status periodically
const checkProcessStatus = async () => {
  if (!currentProcessId.value || !isProcessing.value) return;

  try {
    const result = await window.electronAPI.getActiveProcesses();

    if (result.success) {
      // Check if our process is still running
      const isRunning = result.processes.some(p => p.processId === currentProcessId.value);

      if (!isRunning) {
        // Process completed
        return;
      } else {
        // Check again after 2 seconds
        await new Promise(resolve => setTimeout(resolve, 2000));
        return await checkProcessStatus();
      }
    }
  } catch (err) {
    console.error('Error checking process status:', err);
    // Continue checking even if there's an error
    await new Promise(resolve => setTimeout(resolve, 2000));
    return await checkProcessStatus();
  }
};

// Open SRT file
const openSrtFile = async () => {
  if (srtFilePath.value) {
    await window.electronAPI.openFile(srtFilePath.value);
  }
};

// Import to SRT Table
const importToSrtTable = async () => {
  if (!srtFilePath.value) return;

  try {
    // Read the SRT file
    const result = await window.electronAPI.readFile({ filePath: srtFilePath.value });

    if (result.success) {
      // Create a pseudo-file object from the content
      const fileName = srtFilePath.value.split(/[\/\\]/).pop(); // Get filename without path
      const pseudoFile = {
        name: fileName,
        type: 'application/x-subrip',
        content: result.content,
        path: srtFilePath.value
      };

      // Import to SRT Table
      await srtStore.processSrtFile(pseudoFile);

      message.success('Imported to SRT Table');
    } else {
      throw new Error(result.error || 'Failed to read SRT file');
    }
  } catch (err) {
    message.error('Error importing to SRT Table: ' + (err.message || 'Unknown error'));
  }
};

// Add resize listener to recalculate display dimensions
const handleResize = () => {
  if (videoDimensions.value.width > 0) {
    calculateDisplayDimensions();
  }
};

// Set up resize listener
window.addEventListener('resize', handleResize);

// Clean up on component unmount
onBeforeUnmount(() => {
  // Stop video playback if playing
  if (videoRef.value && isPlaying.value) {
    videoRef.value.pause();
  }

  // Revoke object URL
  if (videoUrl.value) {
    URL.revokeObjectURL(videoUrl.value);
  }

  // Remove resize listener
  window.removeEventListener('resize', handleResize);

  // Clean up seeking event listeners
  document.removeEventListener('mousemove', onSeekingMouseMove);
  document.removeEventListener('mouseup', stopSeeking);

  // If there's an active process, stop it
  if (currentProcessId.value && isProcessing.value) {
    stopProcessing();
  }
});


async function convertVideo() {
  try {
    isConvert.value = true;
    const result = await window.electronAPI.convertVideoToVideo({
      input: videoFile.value.path
    });

    if (result.success) {
      // currentProcessId.value = result.processId;

      videoUrl.value = `file://${videoFile.value.path}`;
      message.success('Convert video successfully!');
 
    } else {
      throw new Error(result.error || 'Failed to convert to WAV');
    }
    isConvert.value = false;
  } catch (err) {
    message.error('Error converting video: ' + (err.message || 'Unknown error'));
    isConvert.value = false;
  }

}


</script>
