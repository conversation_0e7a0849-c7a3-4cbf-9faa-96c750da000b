/**
 * GeneralAgent - Node.js Implementation
 * Main entry point for the library
 */

const { Agent } = require('./Agent.js');
const { NormalMemory } = require('./memory/NormalMemory.js');
const { JavaScriptInterpreter } = require('./interpreter/JavaScriptInterpreter.js');
const { llmInference } = require('./llm/openai.js');
const skills = require('./skills/index.js');
const { defaultOutputCallback, defaultCheck } = require('./utils.js');

// Default export for convenience
module.exports = {
    Agent,
    NormalMemory,
    JavaScriptInterpreter,
    llmInference,
    defaultOutputCallback,
    defaultCheck,
    skills
};
