<template>
  <div class="timeline-layers-panel bg-gray-800 border-t border-gray-600">
    <!-- Header -->
    <div class="layers-header flex items-center justify-between p-2 bg-gray-900 border-b border-gray-600">
      <h4 class="text-sm font-medium text-gray-200">Timeline Layers</h4>
      <div class="flex items-center gap-2">
        <a-button size="small" @click="addTextLayer">
          <template #icon>
            <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <polyline points="4,7 4,4 20,4 20,7"/>
              <line x1="9" y1="20" x2="15" y2="20"/>
              <line x1="12" y1="4" x2="12" y2="20"/>
            </svg>
          </template>
          Text
        </a-button>

        <a-button size="small" @click="addImageLayer">
          <template #icon>
            <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
              <circle cx="8.5" cy="8.5" r="1.5"/>
              <polyline points="21,15 16,10 5,21"/>
            </svg>
          </template>
          Image
        </a-button>

        <a-button size="small" @click="addEffect">
          <template #icon>
            <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="3"/>
              <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>
            </svg>
          </template>
          Effect
        </a-button>
      </div>
    </div>

    <!-- Timeline Tracks -->
    <div class="timeline-tracks flex-1 overflow-auto max-h-40 w-full">
      <!-- Video Track -->
      <!-- <div class="timeline-track">
        <div class="track-header flex items-center p-2 bg-gray-700 border-b border-gray-600">
          <div class="track-controls flex items-center gap-2">
            <a-button size="small" type="text" @click="toggleTrack('video')">
              <template #icon>
                <svg v-if="videoTrackEnabled" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <polygon points="23 7 16 12 23 17 23 7"/>
                  <rect x="1" y="5" width="15" height="14" rx="2" ry="2"/>
                </svg>
                <svg v-else width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M16 16v1a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V7a2 2 0 0 1 2-2h2m5.66 0H14a2 2 0 0 1 2 2v3.34l1 1L23 7v10"/>
                  <line x1="1" y1="1" x2="23" y2="23"/>
                </svg>
              </template>
            </a-button>
            <span class="text-xs text-gray-300">Video</span>
          </div>
        </div>

        <div class="track-content relative h-8 bg-gray-800">
          <div
            class="timeline-item absolute h-6 mt-1 bg-blue-600 rounded flex items-center px-2"
            :style="{ width: '100%', left: '0%' }"
          >
            <span class="text-xs text-white truncate">{{ videoLayer?.name || 'Video' }}</span>
          </div>
        </div>
      </div> -->

      <!-- Subtitle Track -->
      <!-- <div class="timeline-track">
        <div class="track-header flex items-center p-2 bg-gray-700 border-b border-gray-600">
          <div class="track-controls flex items-center gap-2">
            <a-button size="small" type="text" @click="toggleTrack('subtitle')">
              <template #icon>
                <svg v-if="subtitleTrackEnabled" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                </svg>
                <svg v-else width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                  <line x1="1" y1="1" x2="23" y2="23"/>
                </svg>
              </template>
            </a-button>
            <span class="text-xs text-gray-300">Subtitles</span>
          </div>
        </div>

        <div class="track-content relative h-8 bg-gray-800">
          <div
            v-for="subtitle in subtitleItems"
            :key="subtitle.id"
            class="timeline-item absolute h-6 mt-1 bg-green-600 rounded flex items-center px-1 cursor-pointer hover:bg-green-500"
            :style="getSubtitleStyle(subtitle)"
            @click="selectSubtitle(subtitle)"
          >
            <span class="text-xs text-white truncate">{{ subtitle.translatedText?.substring(0, 20) }}...</span>
          </div>
        </div>
      </div> -->

      <!-- Layer Tracks -->
      <div
        v-for="layer in customLayers"
        :key="layer.id"
        class="timeline-track"
      >
        <div class="track-header flex items-center p-2 bg-gray-700 border-b border-gray-600">
          <div class="track-controls flex items-center gap-2">
            <a-button size="small" type="text" @click="layer.enabled = !layer.enabled">
              <template #icon>
                <svg v-if="layer.enabled" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                  <circle cx="12" cy="12" r="3"/>
                </svg>
                <svg v-else width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/>
                  <line x1="1" y1="1" x2="23" y2="23"/>
                </svg>
              </template>
            </a-button>

            <div class="layer-icon">
              <svg v-if="layer.type === 'text'" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="4,7 4,4 20,4 20,7"/>
                <line x1="9" y1="20" x2="15" y2="20"/>
                <line x1="12" y1="4" x2="12" y2="20"/>
              </svg>
              <svg v-else-if="layer.type === 'image'" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                <circle cx="8.5" cy="8.5" r="1.5"/>
                <polyline points="21,15 16,10 5,21"/>
              </svg>
              <svg v-else width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
              </svg>
            </div>

            <span class="text-xs text-gray-300 truncate">{{ layer.name }}</span>
          </div>

          <div class="track-actions flex items-center gap-1">
            <a-button size="small" type="text" @click="deleteLayer(layer.id)">
              <template #icon>
                <svg width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <polyline points="3,6 5,6 21,6"/>
                  <path d="M19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"/>
                </svg>
              </template>
            </a-button>
          </div>
        </div>

        <div class="track-content relative h-8 bg-gray-800">
          <!-- Interactive Layer Item -->
          <TimelineLayerItem
            :layer="layer"
            @select="handleLayerSelect"
            @drag-start="handleLayerDragStart"
            @drag="handleLayerDrag"
            @drag-end="handleLayerDragEnd"
            @resize-start="handleLayerResizeStart"
            @resize="handleLayerResize"
            @resize-end="handleLayerResizeEnd"
            @context-menu="handleLayerContextMenu"
          />
        </div>
      </div>


    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useTimelineStore } from '@/stores/timeline-store'
import { useTTSStore } from '@/stores/ttsStore'
import { parseTimeToSeconds } from '@/lib/utils'
import TimelineLayerItem from './TimelineLayerItem.vue'

const timelineStore = useTimelineStore()
const ttsStore = useTTSStore()

// Computed
const customLayers = computed(() => timelineStore.layerItems || [])
const subtitleItems = computed(() => timelineStore.subtitleItems || [])

// Methods
const selectLayer = (layer, event) => {
  if (event.ctrlKey || event.metaKey) {
    timelineStore.toggleLayerSelection(layer.id)
  } else {
    timelineStore.selectLayer(layer.id)
  }
}

const selectSubtitle = (subtitle) => {
  // TODO: Seek to subtitle time
  console.log('Select subtitle:', subtitle)
}

const deleteLayer = (id) => {
  timelineStore.removeLayer(id)
}

const addTextLayer = () => {
  timelineStore.addLayer({
    type: 'text',
    name: 'Text Layer',
    startTime: 0,
    endTime: timelineStore.duration,
    properties: {
      text: 'Sample Text',
      fontSize: 32,
      color: '#ffffff',
      position: { x: 50, y: 50 }
    }
  })
}

const addImageLayer = () => {
  timelineStore.addLayer({
    type: 'image',
    name: 'Image Layer',
    startTime: 0,
    endTime: timelineStore.duration,
    properties: {
      src: '',
      position: { x: 50, y: 50 },
      scale: 100
    }
  })
}

// Layer interaction handlers
const handleLayerSelect = (layer, event) => {
  selectLayer(layer, event)
}

const handleLayerDragStart = (layer, event) => {
  timelineStore.saveState()
  timelineStore.startDrag('move', layer, event.clientX, layer.startTime)
}

const handleLayerDrag = (deltaX) => {
  if (!timelineStore.isDragging || timelineStore.dragType !== 'move') return

  const deltaTime = timelineStore.pixelToTime(deltaX)
  const layer = timelineStore.dragItem
  const newStartTime = Math.max(0, timelineStore.dragStartTime + deltaTime)
  const duration = layer.endTime - layer.startTime
  const newEndTime = newStartTime + duration

  // Snap to grid if enabled
  const snappedStartTime = timelineStore.snapToGrid ?
    timelineStore.getSnapTime(newStartTime) : newStartTime
  const snappedEndTime = snappedStartTime + duration

  timelineStore.updateLayer(layer.id, {
    startTime: snappedStartTime,
    endTime: snappedEndTime
  })
}

const handleLayerDragEnd = () => {
  timelineStore.stopDrag()
}

const handleLayerResizeStart = (layer, edge, event) => {
  timelineStore.saveState()
  const resizeType = edge === 'left' ? 'resize-left' : 'resize-right'
  const startTime = edge === 'left' ? layer.startTime : layer.endTime
  timelineStore.startDrag(resizeType, layer, event.clientX, startTime)
}

const handleLayerResize = (deltaX) => {
  if (!timelineStore.isDragging || !timelineStore.dragType.startsWith('resize')) return

  const deltaTime = timelineStore.pixelToTime(deltaX)
  const layer = timelineStore.dragItem

  if (timelineStore.dragType === 'resize-left') {
    const newStartTime = Math.max(0, timelineStore.dragStartTime + deltaTime)
    const snappedStartTime = timelineStore.snapToGrid ?
      timelineStore.getSnapTime(newStartTime) : newStartTime

    // Ensure minimum duration
    if (layer.endTime - snappedStartTime >= 0.1) {
      timelineStore.updateLayer(layer.id, { startTime: snappedStartTime })
    }
  } else if (timelineStore.dragType === 'resize-right') {
    const newEndTime = Math.max(layer.startTime + 0.1, timelineStore.dragStartTime + deltaTime)
    const snappedEndTime = timelineStore.snapToGrid ?
      timelineStore.getSnapTime(newEndTime) : newEndTime

    timelineStore.updateLayer(layer.id, { endTime: snappedEndTime })
  }
}

const handleLayerResizeEnd = () => {
  timelineStore.stopDrag()
}

const handleLayerContextMenu = (event, layer) => {
  // TODO: Implement context menu for layers
  console.log('Layer context menu:', layer)
}


</script>

<style scoped>
.timeline-track {
  border-bottom: 1px solid #374151;
}

.timeline-item {
  transition: all 0.2s ease;
  min-width: 20px;
}

.timeline-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.track-content {
  position: relative;
  overflow: hidden;
}

.track-header {
  min-height: 32px;
}
</style>
