<template>
  <!-- Container chiếm full height, c<PERSON> scroll riêng -->
  <div class="flex-1 flex flex-col bg-gray-900 text-white">
    <!-- Header - c<PERSON> định không scroll -->
    <div class="flex-shrink-0 p-4 border-b border-gray-700">
      <div class="flex justify-between items-center">
        <h2 class="text-lg font-medium text-white">Layout Test Component</h2>
        <div class="flex space-x-2">
          <a-button type="primary" @click="addItem">Add Item</a-button>
          <a-button @click="clearItems">Clear All</a-button>
        </div>
      </div>
    </div>

    <!-- Content area - có thể scroll -->
    <div class="flex-1 min-h-0 overflow-auto p-4">
      <div v-if="items.length === 0" class="text-center py-8 text-gray-400">
        No items yet. Click "Add Item" to test scrolling.
      </div>

      <div v-else class="space-y-4">
        <div 
          v-for="item in items" 
          :key="item.id" 
          class="bg-gray-800 p-4 rounded-lg border border-gray-700"
        >
          <div class="flex justify-between items-start mb-2">
            <h3 class="text-lg font-medium text-white">Item {{ item.id }}</h3>
            <a-button size="small" @click="removeItem(item.id)" danger>Remove</a-button>
          </div>
          <p class="text-gray-300 mb-3">{{ item.content }}</p>
          <div class="flex space-x-2">
            <a-button size="small">Action 1</a-button>
            <a-button size="small">Action 2</a-button>
            <a-button size="small">Action 3</a-button>
          </div>
        </div>
      </div>
    </div>

    <!-- Footer - cố định ở bottom (optional) -->
    <div v-if="items.length > 0" class="flex-shrink-0 p-4 border-t border-gray-700 bg-gray-800">
      <div class="flex justify-between items-center">
        <div class="text-sm text-gray-300">
          Total items: {{ items.length }}
        </div>
        <div class="text-sm text-gray-400">
          Last updated: {{ lastUpdated }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, computed } from 'vue';

export default defineComponent({
  name: 'LayoutTestComponent',
  setup() {
    const items = ref([]);
    const nextId = ref(1);

    const lastUpdated = computed(() => {
      return new Date().toLocaleTimeString();
    });

    const addItem = () => {
      const newItem = {
        id: nextId.value++,
        content: `This is test content for item ${nextId.value - 1}. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.`
      };
      items.value.push(newItem);
    };

    const removeItem = (id) => {
      const index = items.value.findIndex(item => item.id === id);
      if (index !== -1) {
        items.value.splice(index, 1);
      }
    };

    const clearItems = () => {
      items.value = [];
      nextId.value = 1;
    };

    // Add some initial items for testing
    for (let i = 0; i < 5; i++) {
      addItem();
    }

    return {
      items,
      lastUpdated,
      addItem,
      removeItem,
      clearItems
    };
  }
});
</script>

<style scoped>
/* Custom scrollbar styling để match với dark theme */
.overflow-auto::-webkit-scrollbar {
  width: 8px;
}

.overflow-auto::-webkit-scrollbar-track {
  background: #374151;
  border-radius: 4px;
}

.overflow-auto::-webkit-scrollbar-thumb {
  background: #6B7280;
  border-radius: 4px;
}

.overflow-auto::-webkit-scrollbar-thumb:hover {
  background: #9CA3AF;
}
</style>
