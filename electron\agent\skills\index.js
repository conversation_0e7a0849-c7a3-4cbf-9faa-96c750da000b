const readlineSync = require('readline-sync');
const fs = require('fs');
const { llmInference } = require('../llm/openai.js');
const { defaultOutputCallback, defaultCheck, uniqueName } = require('../utils.js');

/**
 * Skills module - collection of utility functions for agents
 */

/**
 * Get input from user
 * @param {string} prompt 
 * @returns {string}
 */
function input(prompt = '') {
    return readlineSync.question(prompt);
}

/**
 * Output text to console
 * @param {string} text 
 */
function output(text) {
    defaultOutputCallback(text);
}

/**
 * Print text to console
 * @param {string} text 
 */
function print(text) {
    console.log(text);
}

/**
 * Check with user for confirmation
 * @param {string} checkContent 
 * @returns {string|null}
 */
function check(checkContent = null) {
    return defaultCheck(checkContent);
}

/**
 * Execute JavaScript code (simplified version of Python exec)
 * @param {string} code
 * @returns {any}
 */
function exec(code) {
    try {
        // Create a function to execute the code in a controlled environment
        const func = new Function('console', code);
        return func(console);
    } catch (error) {
        console.error('Error executing code:', error);
        return null;
    }
}

/**
 * Generate unique filename
 * @param {string} extension 
 * @returns {string}
 */
function generateUniqueFilename(extension = '') {
    const name = uniqueName();
    return extension ? `${name}.${extension}` : name;
}

/**
 * LLM inference wrapper
 * @param {Array} messages 
 * @param {Object} options 
 * @returns {Promise<string>}
 */
async function llm(messages, options = {}) {
    const {
        model = 'gpt-3.5-turbo',
        stream = false,
        temperature = null,
        apiKey = null,
        baseUrl = null,
        outputCallback = null,
        ...args
    } = options;
    
    return await llmInference(
        messages,
        model,
        stream,
        temperature,
        apiKey,
        baseUrl,
        outputCallback,
        ...args
    );
}

/**
 * Create a simple text file
 * @param {string} filename
 * @param {string} content
 */
function createFile(filename, content) {
    fs.writeFileSync(filename, content, 'utf8');
}

/**
 * Read a text file
 * @param {string} filename
 * @returns {string}
 */
function readFile(filename) {
    return fs.readFileSync(filename, 'utf8');
}

/**
 * Check if file exists
 * @param {string} filename
 * @returns {boolean}
 */
function fileExists(filename) {
    return fs.existsSync(filename);
}

// Export all functions as default object for easier importing
module.exports = {
    input,
    output,
    print,
    check,
    exec,
    generateUniqueFilename,
    llm,
    createFile,
    readFile,
    fileExists,
    llmInference
};
