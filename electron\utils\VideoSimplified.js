const { exec } = require('child_process');
const { ffmpeg: Ffmpeg } = require('../ffmpeg-config');
const path = require('path');
const fs = require('fs');
const os = require('os');

class VideoSimplified {
  BATCH_SIZE = 20;
  currentTime = 0;
  constructor(srtArray, videoPath, outputDir, outputVideo) {
    this.srtArray = srtArray;
    this.videoPath = videoPath;
    this.outputDir = outputDir;
    this.outputVideo = outputVideo;
  }

  async process() {
    // Tính toán thời lượng audio cho từng subtitle
    for (const srt of this.srtArray) {
      srt.duration = await this.getAudioDuration(srt.audioUrl.replace('file://', ''));
    }

    // Chia thành nhiều batch nếu quá nhiều subtitle
    const batches = [];
    console.log('this.BATCH_SIZE', this.BATCH_SIZE);

    for (let i = 0; i < this.srtArray.length; i += this.BATCH_SIZE) {
      batches.push(this.srtArray.slice(i, i + this.BATCH_SIZE));
    }
    this.currentTime = 0;
    const batchVideoPaths = [];
    let index = 0;
    for (const batch of batches) {
      console.log('Processing batch', batch);
      const batchOutput = path.join(this.outputDir, `batch_${index}.mp4`);

      await this.processBatch(batch, batchOutput);
      batchVideoPaths.push(batchOutput);
      index++;
    }

    // Gộp các batch lại bằng concat
    const concatListPath = path.join(this.outputDir, 'concat_list.txt');
    const concatListContent = batchVideoPaths.map((p) => `file '${p}'`).join('\n');
    fs.writeFileSync(concatListPath, concatListContent);

    const finalOutput = this.outputVideo
    const concatCmd =
      `ffmpeg -f concat -safe 0 -i "${concatListPath}" ` +
      `-c:v h264_nvenc -preset fast -crf 23 -c:a aac -b:a 192k -y "${finalOutput}"`;

    console.log('Running final concat...');
    await this.execPromise(concatCmd);

    console.log(`Final video generated: ${finalOutput}`);
    return finalOutput;
  }

  async processBatch(batch, batchOutput) {
    const inputs = [`-i "${this.videoPath}"`];
    const videoFilters = [];
    const audioFilters = [];
    let currentTime = this.currentTime;
    for (const srt of batch) {
      const audioPath = srt.audioUrl?.replace('file://', '');
      inputs.push(`-i "${audioPath}"`);

      const vStart = srt.startTime;
      const vDur = srt.endTime - srt.startTime;
      const aDur = srt.duration;
      const segmentDur = Math.max(vDur, aDur);
      const audioIndex = batch.indexOf(srt) + 1;

      videoFilters.push(
        `[0:v]trim=start=${vStart}:duration=${vDur},setpts=PTS-STARTPTS,scale=1920:1080[v${batch.indexOf(srt)}]`,
      );

      const boostDb = 14;
      const volumeMultiplier = Math.pow(10, boostDb / 20);
      audioFilters.push(
        `[${audioIndex}:a]dynaudnorm=f=150:g=15,volume=${volumeMultiplier},adelay=${currentTime * 1000}|${
          currentTime * 1000
        }[a${batch.indexOf(srt)}]`,
      );

      currentTime += segmentDur;
    }

    const videoConcat = `${videoFilters.join('; ')}; ${batch.map((_, i) => `[v${i}]`).join('')}concat=n=${
      batch.length
    }:v=1:a=0[vout]`;
    const audioMix = `${audioFilters.join('; ')}; ${batch.map((_, i) => `[a${i}]`).join('')}amix=inputs=${
      batch.length
    }:duration=longest[aout]`;

    const filterComplex = `${videoConcat}; ${audioMix}`;

    const ffmpegCmd =
      `ffmpeg ${inputs.join(' ')} ` +
      `-filter_complex "${filterComplex}" ` +
      `-map "[vout]" -map "[aout]" ` +
      `-c:v h264_nvenc -preset fast -crf 23 ` +
      `-c:a aac -ar 44100 -ac 2 -b:a 192k ` +
      `-y "${batchOutput}"`;
    await this.execPromise(ffmpegCmd);

    return batchOutput;
  }
  async getAudioDuration(audioPath) {
    try {
      const audioInfo = await new Promise((resolve, reject) => {
        Ffmpeg.ffprobe(audioPath, function (err, metadata) {
          if (err) {
            reject(err);
          } else {
            resolve(metadata);
          }
        });
      });
      const duration = parseFloat(audioInfo.format.duration);
      console.log(duration);
      return duration;
    } catch (error) {
      console.error('Error fetching audio duration:', error);
      throw error;
    }
  }
  async execPromise(cmd) {
    return new Promise((resolve, reject) => {
      exec(cmd, (err, stdout, stderr) => {
        if (err) {
          console.error('❌ FFmpeg error:', err);
          console.error(stderr);
          return reject(err);
        }
        resolve();
      });
    });
  }
}

module.exports = {
  VideoSimplified,
};



// âm thanh siu to

const processVideoSimplifiedV4 = async (event, videoPath, srtArray, outputDir, finalOutput, options = {}) => {
  if (!fs.existsSync(outputDir)) fs.mkdirSync(outputDir);
  const type = 'video-task';
  
  // 1. Tính duration cho audio từng đoạn
  for (const srt of srtArray) {
    srt.duration = await getAudioDuration(srt.audioUrl.replace('file://', ''));
    event?.sender?.send(type, {
      data: `🕐 Audio duration for segment ${srt.index}: ${srt.duration} seconds`,
      code: 0,
    });
  }

  // 2. Tính thời lượng video segment và speed ratio cho từng đoạn
  let adjustedCurrentTime = 0; // Thời gian tích luỹ sau khi điều chỉnh tốc độ
  
  for (let i = 0; i < srtArray.length; i++) {
    const currentSrt = srtArray[i];
    const nextSrt = srtArray[i + 1];

    // Thời lượng video segment gốc
    const originalVideoSegmentDuration = nextSrt
      ? nextSrt.startTime - currentSrt.startTime
      : currentSrt.endTime - currentSrt.startTime;

    currentSrt.originalVideoSegmentDuration = originalVideoSegmentDuration;

    // Tính speed ratio
    if (currentSrt.duration > originalVideoSegmentDuration) {
      currentSrt.speedRatio = originalVideoSegmentDuration / currentSrt.duration; // < 1 = chậm lại
    } else {
      currentSrt.speedRatio = 1; // không thay đổi tốc độ
    }

    // Thời lượng thực tế sau khi điều chỉnh tốc độ
    const adjustedVideoDuration = originalVideoSegmentDuration / currentSrt.speedRatio;
    
    // Thời lượng cuối cùng của đoạn này (lấy max giữa video đã điều chỉnh và audio)
    currentSrt.finalDuration = Math.max(adjustedVideoDuration, currentSrt.duration);
    
    // Cập nhật thời gian start/end mới cho SRT
    currentSrt.adjustedStartTime = adjustedCurrentTime;
    currentSrt.adjustedEndTime = adjustedCurrentTime + currentSrt.finalDuration;
    
    // Cập nhật thời gian tích luỹ
    adjustedCurrentTime += currentSrt.finalDuration;

    const logs = `📊 Segment ${i}: original=${originalVideoSegmentDuration.toFixed(2)}s, audio=${currentSrt.duration.toFixed(2)}s, speed=${currentSrt.speedRatio.toFixed(3)}, adjusted=${adjustedVideoDuration.toFixed(2)}s, final=${currentSrt.finalDuration.toFixed(2)}s, newTime=${currentSrt.adjustedStartTime.toFixed(2)}-${currentSrt.adjustedEndTime.toFixed(2)}s`;
    console.log(logs);
    event?.sender?.send(type, {
      data: logs,
      code: 0,
    });
  }

  // 3. Chia thành nhiều batch nếu quá nhiều
  const batches = [];
  for (let i = 0; i < srtArray.length; i += BATCH_SIZE) {
    batches.push(srtArray.slice(i, i + BATCH_SIZE));
  }

const batchVideoPaths = [];
const holdOriginalAudio = options.holdOriginalAudio || false;
const extractMusicOnly = options.extractMusicOnly || false;
const addVoiceAudio = true;

for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
    const batch = batches[batchIndex];
    
    const inputs = [`-i "${videoPath}"`];
    let segmentFilters = [];
    let currentTime = 0;
    
    // Thêm audio files nếu cần
    if (addVoiceAudio) {
        batch.forEach(srt => {
            const audioPath = srt.audioUrl.replace('file://', '');
            inputs.push(`-i "${audioPath}"`);
        });
    }
    
    // Tạo arrays để lưu audio và video segments
    let videoSegments = [];
    let originalAudioSegments = [];
    let voiceAudioSegments = [];
    
    batch.forEach((srt, index) => {
        const vStart = srt.startTime;
        const vDur = srt.originalVideoSegmentDuration;
        const speedRatio = srt.speedRatio || 1;
        const finalDur = srt.finalDuration;
        
        // === VIDEO PROCESSING ===
        let videoFilter = `[0:v]trim=start=${vStart}:duration=${vDur},setpts=PTS-STARTPTS`;
        
        // Điều chỉnh tốc độ video nếu cần
        if (speedRatio !== 1) {
            videoFilter += `,setpts=PTS/${speedRatio}`;
        }
        
        // Scale nếu cần
        if (options.output?.quality) {
            const resolution = getResolution(options.output.quality);
            videoFilter += `,scale=${resolution.width || 1920}:${resolution.height || 1080}`;
        }
        
        videoFilter += `[v${index}]`;
        segmentFilters.push(videoFilter);
        videoSegments.push(`[v${index}]`);
        
        // === ORIGINAL AUDIO PROCESSING ===
        if (holdOriginalAudio) {
            let originalAudioFilter = `[0:a]atrim=start=${vStart}:duration=${vDur},asetpts=PTS-STARTPTS`;
            
            // Điều chỉnh tốc độ audio để match với video
            if (speedRatio !== 1) {
                // Handle atempo limitations (0.5 to 2.0)
                let currentRatio = speedRatio;
                while (currentRatio < 0.5) {
                    originalAudioFilter += `,atempo=0.5`;
                    currentRatio /= 0.5;
                }
                while (currentRatio > 2.0) {
                    originalAudioFilter += `,atempo=2.0`;
                    currentRatio /= 2.0;
                }
                if (Math.abs(currentRatio - 1.0) > 0.001) {
                    originalAudioFilter += `,atempo=${currentRatio}`;
                }
            }
            
            originalAudioFilter += `[oa${index}]`;
            segmentFilters.push(originalAudioFilter);
            originalAudioSegments.push(`[oa${index}]`);
        }
        
        // === VOICE AUDIO PROCESSING ===
        if (addVoiceAudio) {
            const voiceAudioIndex = index + 1; // +1 vì input[0] là video
            const volume = srt.mixVolume ?? 1;
            const boostDb = options.audioBoost || 14;
            const volumeMultiplier = Math.pow(10, boostDb / 20) * volume;
            
            let voiceAudioFilter = `[${voiceAudioIndex}:a]dynaudnorm=f=150:g=15,volume=${volumeMultiplier}[va${index}]`;
            segmentFilters.push(voiceAudioFilter);
            voiceAudioSegments.push(`[va${index}]`);
        }
        
        currentTime += finalDur;
    });

    // === CONCAT VIDEO ===
    const videoConcat = `${videoSegments.join('')}concat=n=${batch.length}:v=1:a=0[vout]`;
    segmentFilters.push(videoConcat);
    
    // === CONCAT AUDIO ===
    let finalAudioOutput = null;
    
    if (holdOriginalAudio && addVoiceAudio) {
        // Concat original audio segments
        if (originalAudioSegments.length > 1) {
            const originalConcat = `${originalAudioSegments.join('')}concat=n=${batch.length}:v=0:a=1[oa_concat]`;
            segmentFilters.push(originalConcat);
        } else {
            segmentFilters.push(`[oa0]acopy[oa_concat]`);
        }
        
        // Concat voice audio segments  
        if (voiceAudioSegments.length > 1) {
            const voiceConcat = `${voiceAudioSegments.join('')}concat=n=${batch.length}:v=0:a=1[va_concat]`;
            segmentFilters.push(voiceConcat);
        } else {
            segmentFilters.push(`[va0]acopy[va_concat]`);
        }
        
        // Mix both audio streams
        segmentFilters.push(`[oa_concat][va_concat]amix=inputs=2:duration=longest[aout]`);
        finalAudioOutput = '[aout]';
        
    } else if (holdOriginalAudio) {
        // Chỉ original audio
        if (originalAudioSegments.length > 1) {
            const originalConcat = `${originalAudioSegments.join('')}concat=n=${batch.length}:v=0:a=1[aout]`;
            segmentFilters.push(originalConcat);
        } else {
            segmentFilters.push(`[oa0]acopy[aout]`);
        }
        finalAudioOutput = '[aout]';
        
    } else if (addVoiceAudio) {
        // Chỉ voice audio
        if (voiceAudioSegments.length > 1) {
            const voiceConcat = `${voiceAudioSegments.join('')}concat=n=${batch.length}:v=0:a=1[aout]`;
            segmentFilters.push(voiceConcat);
        } else {
            segmentFilters.push(`[va0]acopy[aout]`);
        }
        finalAudioOutput = '[aout]';
    }

    const filterComplex = segmentFilters.join('; ');
    const batchOutput = path.join(outputDir, `batch_${batchIndex}.mp4`);
    batchVideoPaths.push(batchOutput);

    // Build FFmpeg command
    let ffmpegCmd = `ffmpeg ${inputs.join(' ')} -filter_complex "${filterComplex}" -map "[vout]"`;
    
    if (finalAudioOutput) {
        ffmpegCmd += ` -map "${finalAudioOutput}"`;
        ffmpegCmd += ` -c:a aac -ar 44100 -ac 2 -b:a 192k`;
    } else {
        ffmpegCmd += ` -an`; // No audio
    }
    
    ffmpegCmd += ` -c:v h264_nvenc -preset fast -crf 23 -y "${batchOutput}"`;

    console.log(`🧩 Running FFmpeg for batch ${batchIndex + 1}/${batches.length}`);
    console.log(`Filter: ${filterComplex}`);
    console.log(`Command: ${ffmpegCmd}`);
    
    try {
        await execPromise(ffmpegCmd);
        console.log(`✅ Batch ${batchIndex + 1} completed successfully`);
    } catch (error) {
        console.error(`❌ Error processing batch ${batchIndex + 1}:`, error);
        throw error;
    }
}

  // 4. Gộp các batch lại bằng concat
  const concatListPath = path.join(outputDir, 'concat_list.txt');
  const concatListContent = batchVideoPaths.map((p) => `file '${p}'`).join('\n');
  fs.writeFileSync(concatListPath, concatListContent);
  const concatOutput = path.join(outputDir, 'concatenated.mp4');
  const concatCmd =
    `ffmpeg -f concat -safe 0 -i "${concatListPath}" ` +
    `-c:v h264_nvenc -preset fast -crf 23 -c:a aac -b:a 192k -y "${concatOutput}"`;

  console.log('📦 Running final concat...');
  event?.sender?.send(type, {
    data: '📦 Running final concat...',
    code: 0,
  });
  await execPromise(concatCmd);

  console.log(`✅ Final video generated: ${concatOutput}`);
  event?.sender?.send(type, {
    data: `✅ Final video generated: ${concatOutput}`,
    code: 0,
  });
  const adjustedSrtPath = './subtitles.ass'//finalOutput.replace('.mp4', '.srt');
  if (options.textSubtitle?.enabled) {
    // 5. Tạo file SRT mới với thời gian đã điều chỉnh
  // await generateAdjustedSRT(srtArray, adjustedSrtPath);
    const subtitleOptions = {
      fontSize: 56,
      textColor: cssToASSColor('#FFFFFF'),      // Chữ trắng
      backgroundColor: cssToASSColor('#000000', '80'), // Nền đen semi-transparent
      borderStyle: 3,                          // Background box
      bold: true,
      addPadding: true,                        // Thêm padding cho background box
      alignment: 2,                            // Center alignment
      marginVertical: 50                       // Khoảng cách từ bottom
    };
    
    const assContent = generateASSSubtitle(srtArray, subtitleOptions);
    fs.writeFileSync(adjustedSrtPath, assContent, 'utf8');

    // áp dụng phụ đề
    const command = `ffmpeg -i "${concatOutput}" -vf "ass=${adjustedSrtPath}" -c:a copy -c:v h264_nvenc -preset fast -crf 23 -y "${finalOutput}"`;
    await execPromise(command);
    console.log(`✅ Subtitle applied: ${finalOutput}`);
    event?.sender?.send(type, {
      data: `✅ Subtitle applied: ${finalOutput}`,
      code: 0,
    });
  } else {
    fs.renameSync(concatOutput, finalOutput);
  }
  
  console.log(`✅ Adjusted SRT generated: ${finalOutput}`);
  event?.sender?.send(type, {
    data: `✅ Adjusted SRT generated: ${finalOutput}`,
    code: 0,
  });

  // cleanup
  batchVideoPaths.forEach((p) => {
    if (fs.existsSync(p)) fs.unlinkSync(p);
  });
  if (fs.existsSync(concatListPath)) fs.unlinkSync(concatListPath);
  if (fs.existsSync(concatOutput)) fs.unlinkSync(concatOutput);
  
  return {
    videoPath: finalOutput,
    // srtPath: adjustedSrtPath,
    adjustedSrtArray: srtArray
  };
};