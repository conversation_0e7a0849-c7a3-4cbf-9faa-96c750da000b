<script setup lang="ts">
import { Modal, message, Input, Radio, Slider, Button } from 'ant-design-vue';
import {useAiManager} from '@/stores/useAiManager'

const props = defineProps({
  visible: Boolean,
  dataApiKeyAIManager: Object,
  onSaveSettings: Function,
  onCancel: Function,
});

const emit = defineEmits(['update:visible','save-settings']);
const settingsStore = useAiManager();

const isFreeTtsAvailable = ref(true); // Giả lập. Thay bằng logic thật nếu cần.

const Ro = [
  { model: "tiny", level: "1", description: "Model nhẹ, tốc độ cao, độ chính xác thấp" },
  { model: "base", level: "2", description: "Model cân bằng" },
  { model: "large", level: "3", description: "Model lớn, độ chính xác cao, tốc độ thấp hơn" }
];


// Store

// Local state
const saving = ref(false)
const localSettings = reactive(settingsStore.settings);

// Computed
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})


// Methods
const handleSave = async () => {
  try {
    saving.value = true
    console.log('Saving settings:', localSettings)
    // Validate required fields
    // if (!settingsStore.settings.apiKeyGoogleGenerativeAI?.trim()) {
    //   message.warning('Google AI API key is required for summarization')
    //   return
    // }

    // Save to store
    // await settingsStore.saveSettings(localSettings)
    
    // Emit to parent
    emit('save-settings', { ...localSettings })
    
    message.success('Settings saved successfully!')
    visible.value = false
  } catch (error) {
    console.error('Failed to save settings:', error)
    message.error('Failed to save settings')
  } finally {
    saving.value = false
  }
}

const handleCancel = () => {
  // Reset to original values
  if (props.dataApiKeyAIManager) {
    Object.assign(localSettings, props.dataApiKeyAIManager)
  }
  visible.value = false
}


const handleSelectFree = async (type, value) => {
  if (value === "EdgeTTS" && !isFreeTtsAvailable.value) {
    Modal.error({
      title: "Thiếu tài nguyên",
      content: "Tài nguyên phần mềm bị thiếu vui lòng khởi động lại ứng dụng!"
    });
    return false;
  }
  settingsStore.settings[type] = value;
  return true;
};
</script>

<template>
  <Modal
    v-model:open="props.visible"
    title="Cài đặt"
    @ok="handleSave"
    @cancel="handleCancel"
    :footer="null"
    width="600px"
    centered
  >
    <div class="p-2 flex flex-col">
      <!-- Speech to Text -->
      <div class="mb-5 mr-3">
        <label class="font-bold block mb-2 text-gray-900 dark:text-white">API speech to text *</label>
        <div class="flex items-center">
          <Radio
            id="radio-free-speech-to-text"
            value="Whisper"
            :checked="localSettings.optionAPISpeechToText === 'Whisper'"
            name="radio-speech-to-text"
            class="radio-style"
            @change="() => handleSelectFree('optionAPISpeechToText', 'Whisper')"
          />
          <label for="radio-free-speech-to-text" class="cursor-pointer ms-2 text-md text-gray-900 dark:text-gray-300">
            <b>Whisper</b> (free)
          </label>
        </div>
        <div v-if="localSettings.optionAPISpeechToText === 'Whisper'" class="flex flex-col mx-2 mt-1">
          <label class="block text-sm font-medium text-gray-300 text-center">
            {{ Ro[localSettings.selectLevelModalWhisper]?.description }}
          </label>
          <label class="cursor-pointer text-xs text-gray-300">
            <b class="capitalize">
              {{ Ro[localSettings.selectLevelModalWhisper]?.model }}({{ Ro[localSettings.selectLevelModalWhisper]?.level }})
            </b>
          </label>
          <Slider
            v-model:value="localSettings.selectLevelModalWhisper"
            :min="0"
            :max="Ro.length - 1"
            :step="1"
            class="w-full"
          />
        </div>
        <div class="mt-2">
          <div class="flex items-center">
            <Radio
              id="radio-assemblyai-speech-to-text"
              value="AssemblyAI"
              :checked="localSettings.optionAPISpeechToText === 'AssemblyAI'"
              name="radio-speech-to-text"
              class="radio-style"
              @change="() => handleSelectFree('optionAPISpeechToText', 'AssemblyAI')"
            />
            <label for="radio-assemblyai-speech-to-text" class="cursor-pointer ms-2 text-md text-gray-900 dark:text-gray-300 font-bold">AssemblyAI</label>
          </div>
          <div class="ml-8">
            <a-input-password
              class="w-full "
              placeholder="Nhập API key AssemblyAI vd: 0a88d1dd7..."
              v-model:value="localSettings.apiKeyAssemblyAI"
            />
          </div>
        </div>
      </div>

      <!-- Text to Speech -->
      <div class="mb-5 mr-3">
        <label class="font-bold block mb-2 text-gray-900 dark:text-white">API text to speech *</label>
        <div class="flex items-center">
          <Radio
            id="radio-free-text-to-speech"
            value="EdgeTTS"
            :checked="localSettings.optionAPITextToSpeech === 'EdgeTTS'"
            name="default-radio"
            class="radio-style"
            @change="() => handleSelectFree('optionAPITextToSpeech', 'EdgeTTS')"
          />
          <label for="radio-free-text-to-speech" class="cursor-pointer ms-2 text-md text-gray-900 dark:text-gray-300 ">
            <b>EdgeTTS</b> (free)
          </label>
        </div>
        <div class="mt-2">
          <div class="flex items-center">
            <Radio
              id="radio-elevenlabs-text-to-speech"
              value="ElevenLabs"
              :checked="localSettings.optionAPITextToSpeech === 'ElevenLabs'"
              name="default-radio"
              class="radio-style"
              @change="() => handleSelectFree('optionAPITextToSpeech', 'ElevenLabs')"
            />
            <label for="radio-elevenlabs-text-to-speech" class="cursor-pointer ms-2 text-md text-gray-900 dark:text-gray-300 font-bold">
              ElevenLabsClient <span class="text-xs font-normal ml-1">(Nhập nhiều key, phân tách bằng dấu ",")</span>
            </label>
          </div>
          <div class="ml-8">
            <a-input-password
              class="w-full"
              placeholder="Nhập API key ElevenLabsClient vd: AIzaSyDXUM...."
              v-model:value="localSettings.apiKeyElevenLabsClient"
            />
          </div>
        </div>
      </div>

      <!-- GoogleAIStudio -->
      <!-- <div class="mb-5 mr-3">
        <label class="font-bold block mb-2 text-gray-900 dark:text-white">API key GoogleAIStudio *</label>
        <a-input-password
          class="w-full"
          placeholder="AIzaSyDXUM...."
          v-model:value="localSettings.apiKeyGoogleGenerativeAI"
        />
      </div> -->

      <!-- History Conversation -->
      <!-- <div class="items-start mb-5 mr-3">
        <label class="block mb-2 font-bold text-gray-900 dark:text-white">History conversation</label>
        <Input.TextArea
          :rows="6"
          class="block w-full p-2.5 text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
          placeholder='[ { role: "user", parts: [ {text: "..."} ], }, { role: "model", parts: [ {text: "..."} ], }, ... ]'
          v-model:value="localSettings.conversationHistory"
        />
      </div> -->

      <!-- Conversation Text -->
      <div class="mb-5 mr-3">
        <label class="font-bold block mb-2 text-gray-900 dark:text-white">Type something</label>
        <Input
          class="bg-gray-50 border w-full border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
          placeholder="Tóm tắt nội dung sau bằng tiếng {{language}}"
          v-model:value="localSettings.conversationText"
        />
      </div>

      <!-- Button -->
      <div class="text-right">
        <Button type="primary" @click="handleSave">
          Save Settings
        </Button>
        <Button class="ml-2" @click="handleCancel">
          Hủy
        </Button>
      </div>
    </div>
  </Modal>
</template>

<style scoped>
.radio-style {
  accent-color: #1677ff;
  width: 18px;
  height: 18px;
}
</style>
