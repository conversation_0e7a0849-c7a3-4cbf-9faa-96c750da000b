let isMultiVoice = false;
let isLoadVoiceSettings = true;
let ttsServerURL = 'https://tts_12_3358.aiktp.com/tts';
let ttsServerURL_01 = 'https://tts_11_3358.aiktp.com/tts';
let docId = '';
let voiceFromURL = '';
let langFromURL = '';


let inputVoice = null;
let searchVoice = null;
let inputVoice_default = {
    server: 'azure',
    voiceId: 'DFE237',
    country: 'all',
    gender: 'male',
    rate: 1,
    isMultiVoice: false,
    speedVal:'1.00',
    pitchVal:'1.00',
    volumeVal:'1.00',
    inputText:''
};
let searchVoice_default = {
    keyword: '',
    country: _country,
    gender: 'all',
    tab: 'all'
};
inputVoice = JSON.parse(getLocalStore("tts_inputVoice"));
if (! inputVoice) inputVoice = inputVoice_default;
if (typeof voiceFromURL !== 'undefined' && voiceFromURL !== '' && voiceFromURL !== null){
    let vInfo = getVoiceInfo(voiceFromURL);
    inputVoice.voiceId =voiceFromURL;
    inputVoice.server = vInfo.server;
    inputVoice.gender = vInfo.gender;
    inputVoice.country = vInfo.country;
}

searchVoice = JSON.parse(getLocalStore("tts_searchVoice"));
if (! searchVoice) searchVoice = searchVoice_default;
if (typeof langFromURL !== 'undefined' && langFromURL !== '' && langFromURL !== null){
    searchVoice.country = langFromURL;
}

async function saveLogData(_inputText){
    let postLog = {};
    postLog.userToken = userToken?userToken:0;
    postLog.inputText = _inputText;
    postLog.totalWords = countWords(_inputText);
    if (inputVoice.isMultiVoice){
        postLog.fee = parseInt(postLog.totalWords*1.5); 
    }else {
        if (inputVoice?.server =="openai" || 
            inputVoice?.server == "openaib" || 
            inputVoice?.server == "openaiv2" || 
            inputVoice?.server == "aiVoice" || 
            inputVoice?.server == "gemini"
        ){
            postLog.fee = parseInt(postLog.totalWords*1.5);
        }else{
            postLog.fee = parseInt(postLog.totalWords*1);
        }
    }
    postLog.logType = 'AI_TTS';
    postLog.logSite = getVoiceInfo(inputVoice?.voiceId)?.voiceName||"";
    let r = await $.post(`${rootURL}/api/log.php?task=saveToLog`,postLog);
    r = JSON.parse(r);
    if (! r?.data?.docId) return false;
    return r.data.docId; 
}
async function saveLogError(docId){
    let pro =new ProtectedRequerst();
    let pDocId = await pro.encodeWithKey(docId);
    let postLog = {};
    postLog.userToken = userToken?userToken:0;
    postLog.pDocId = pDocId; 

    postLog.inputText = $(".inputText").val();
    postLog.voiceName = getVoiceInfo(inputVoice?.voiceId)?.voiceName||'';
    postLog.voiceServer = getVoiceInfo(inputVoice?.voiceId)?.server||'';
    
    let r = await $.post(`${rootURL}/api/log.php?task=logError`,postLog);
}
function getVoiceInfo(voiceId=''){
    if (voiceId == '') return {
        displayName: '',
        iconGender: '',
        lang: '',
        languageCode: '',
        voiceName: '',
        server: ''
    };
    let voiceInfo = globalVoiceData.find(item => item.voiceId == voiceId);
    let iconGender = '<i class="fa-solid fa-venus text-danger"></i>'
    if (voiceInfo?.gender == "Male" || voiceInfo?.gender == "male") {
        iconGender = '<i class="fa-solid fa-mars text-primary"></i>'
    }
    return {
        displayName: voiceInfo?.displayName||"",
        iconGender: iconGender,
        gender: voiceInfo?.gender || '',
        country: voiceInfo?.country || '',
        lang: voiceInfo?.languageCode||"vi-VN",
        languageCode: voiceInfo?.languageCode||"",
        voiceName:voiceInfo?.voiceName||"",
        server: voiceInfo?.server||""
    }
}
let favoriteVoice = [];

async function getFavoriteVoiceFromDb(){
    let res = await $.post(`${rootURL}/api/voices.php?task=getFavoriteVoice`,{userToken:userToken});
    res = JSON.parse(res);
    if (res.status == 'success'){
        return res.data;
    }else return [];
}
function saveFavoriteVoice(voiceId){   
    if (!favoriteVoice.includes(voiceId)) {
        favoriteVoice.push(voiceId);
    }
    
    $.post(`${rootURL}/api/voices.php?task=saveFavoriteVoice`,{
        favorite: JSON.stringify(favoriteVoice),
        userToken:userToken
    });
}
function removeFavoriteVoice(voiceId){
    favoriteVoice = favoriteVoice.filter(item => item != voiceId);
    $.post(`${rootURL}/api/voices.php?task=saveFavoriteVoice`,{
        favorite: JSON.stringify(favoriteVoice),
        userToken:userToken
    });
}
function checkFavoriteVoice(voiceId){
    if (!favoriteVoice) return false;
    return favoriteVoice.includes(voiceId);
}
function getFavoriteVoice(){   
    if (!favoriteVoice) return [];
    return globalVoiceData.filter(item => favoriteVoice.includes(item.voiceId));
}
function getVoiceName(_voiceId) {
    let voice = globalVoiceData.filter(item => item.voiceId === _voiceId);
    return voice[0].voiceName;
}
function getVoiceInforV2(_voiceName) {
    let voice = globalVoiceData.filter(item => item.voiceName === _voiceName);
    return voice[0];
}
const voiceCountries = {'multi':'Multilingual','us':'United States', 'cn':'China', 'hk':'Hong Kong SAR', 'tw':'Taiwan', 'za':'South Africa', 'al':'Albania', 'et':'Ethiopia', 'ae':'United Arab Emirates', 'am':'Armenia', 'az':'Azerbaijan', 'bd':'Bangladesh', 'es':'Spain', 'in':'India', 'ba':'Bosnia and Herzegovina', 'bg':'Bulgaria', 'mm':'Myanmar', 'hr':'Croatia', 'cz':'Czech Republic', 'dk':'Denmark', 'be':'Belgium', 'nl':'Netherlands', 'gb':'United Kingdom', 'au':'Australia', 'ca':'Canada', 'ie':'Ireland', 'ke':'Kenya', 'nz':'New Zealand', 'ng':'Nigeria', 'ph':'Philippines', 'sg':'Singapore', 'tz':'Tanzania', 'ee':'Estonia', 'fi':'Finland', 'fr':'France', 'ch':'Switzerland', 'ge':'Georgia', 'de':'Germany', 'at':'Austria', 'gr':'Greece', 'il':'Israel', 'hu':'Hungary', 'is':'Iceland', 'id':'Indonesia', 'it':'Italy', 'jp':'Japan', 'kz':'Kazakhstan', 'kh':'Cambodia', 'kr':'South Korea', 'la':'Laos', 'lv':'Latvia', 'lt':'Lithuania', 'mk':'North Macedonia', 'my':'Malaysia', 'mt':'Malta', 'mn':'Mongolia', 'np':'Nepal', 'no':'Norway', 'af':'Afghanistan', 'ir':'Iran', 'pl':'Poland', 'pt':'Portugal', 'br':'Brazil', 'ro':'Romania', 'ru':'Russia', 'rs':'Serbia', 'lk':'Sri Lanka', 'sk':'Slovakia', 'si':'Slovenia', 'so':'Somalia', 'ar':'Argentina', 'bo':'Bolivia', 'cl':'Chile', 'co':'Colombia', 'cr':'Costa Rica', 'cu':'Cuba', 'do':'Dominican Republic', 'ec':'Ecuador', 'sv':'El Salvador', 'gq':'Equatorial Guinea', 'gt':'Guatemala', 'hn':'Honduras', 'mx':'Mexico', 'ni':'Nicaragua', 'pa':'Panama', 'py':'Paraguay', 'pe':'Peru', 'pr':'Puerto Rico', 'uy':'Uruguay', 've':'Venezuela', 'se':'Sweden', 'th':'Thailand', 'tr':'Turkey', 'ua':'Ukraine', 'pk':'Pakistan', 'uz':'Uzbekistan', 'vn':'Vietnam'}
$(document).ready(function () {
    


    $(".btn-openSettings").click(function () {
        Swal.fire({
            showConfirmButton: false,
            showCloseButton:true,
            width:500,
            height:450,
            padding: "0px 10px 30px",
            html:`
                <div class="modal-voiceOptions">
                    <div class="voiceOptions">
                        <h2 class="text-start mb-2 fs-14 fw-bold">${getText("Voice Settings")}</h2>
                        <div class="voiceOption">                      
                            <div class="d-flex mb-2 fs-12 fw-bold justify-content-between"><span>${getText("Speed Control")}</span> <span>(<span class="speedVal">1.0</span>)</span></div>
                            <input type="range" value="1" class="form-range speedControl" min="0" max="2" step="0.1" id="speedControl">
                        </div>

                        <div class="voiceOption">               
                            <div class="d-flex mb-2 fs-12 fw-bold justify-content-between"><span>${getText("Pitch Control")}</span> <span>(<span class="pitchVal">1</span>)</span></div>
                            <input type="range" value="0" class="form-range pitchControl" min="-1" max="1" step="0.1" id="pitchControl">
                        </div>

                        <div class="voiceOption">
                            <div class="d-flex mb-2 fs-12 fw-bold justify-content-between"><span>${getText("Volume Control")}</span> <span>(<span class="volumeVal">1</span>)</span></div>
                            <input type="range" value="0" class="form-range volumeControl" min="-1" max="1" step="0.1" id="volumeControl">
                        </div>
                    </div>
                </div>
                `,
                willOpen: () => {
                    $(".speedControl").val(inputVoice.speedVal);
                    $(".speedVal").text((inputVoice.speedVal).toFixed(1));
                    
                    $(".pitchVal").text((inputVoice.pitchVal).toFixed(1));
                    $(".volumeVal").text((inputVoice.volumeVal).toFixed(1));
                    $(".pitchControl").val(inputVoice.pitchVal - 1);
                    $(".volumeControl").val(inputVoice.volumeVal - 1);
                    $(".speedControl").change(function(res){
                        let _v = parseFloat($(this).val());
                        $(".speedVal").text((_v).toFixed(1));
                        inputVoice.speedVal = _v;
                        setLocalStore("tts_inputVoice",JSON.stringify(inputVoice));
                    })
                    $(".pitchControl").change(function(res){
                        let _v = parseFloat($(this).val());
                        $(".pitchVal").text((1 + _v).toFixed(1));
                        inputVoice.pitchVal = _v + 1;
                        setLocalStore("tts_inputVoice",JSON.stringify(inputVoice));
                    })
                    $(".volumeControl").change(function(res){
                        let _v = parseFloat($(this).val());
                        $(".volumeVal").text((1 + _v).toFixed(1));
                        inputVoice.volumeVal = _v+1;
                        setLocalStore("tts_inputVoice",JSON.stringify(inputVoice));
                    })
                },
        });
    });

    
    

    function initVoiceSettings(){
       let html = `
      <div class="ui three item tabs menu tabVoiceSettings">
    <a class="item active" data-tab="azure">Free <span class="count">(608)</span></a>
    <a class="item" data-tab="all">All <span class="count">(777)</span></a>
    <a class="item" data-tab="favorites">Favorites</a>
</div>
<div class="voice-settings">
    <div class="filter-container filterVoice">
        <div class="ui input filter-search left icon input">
            <input class="findVoice" type="text" placeholder="Search..." /><i class="fa-solid fa-magnifying-glass icon"></i>
        </div>
    
        <div class="ui search selection dropdown dropdown-country">
            <input type="hidden" name="country" /><i class="dropdown icon"></i>
            <div class="default text">Select Country</div>
            <div class="menu"></div>
        </div>
          
        <button class="ui button soft-secondary btn-clearFilter">Clear</button>
    </div>
    <div class="list-voices"></div>
</div>
`
       return html;
    }
    window.loadVoiceSettings = function(){
        if ($('.voice-settings-container').length === 0 && inputVoiceId ){
            let voiceInfo = getVoiceInfo(inputVoiceId);
            $(".btn-fillterVoice .text").html(voiceInfo.displayName)
            $(".btn-fillterVoice .iconGender").html(voiceInfo.iconGender)
            return;
        } 
        getFavoriteVoiceFromDb().then(function(res){
            favoriteVoice = res;
        });
        
        let voiceInfo = getVoiceInfo(inputVoice.voiceId);
        $(".btn-fillterVoice .text").html(voiceInfo.displayName)
        $(".btn-fillterVoice .iconGender").html(voiceInfo.iconGender)
        let html = initVoiceSettings();
        $(".voice-settings-container").html(html);
        loadCountryDataToHTML(searchVoice.country );
        

        if (inputVoice.inputText != "") $(".inputText").val(inputVoice.inputText);
        
        $(".dropdown-speed").dropdown('set selected', inputVoice.speedVal);
        $(".dropdown-speed").find(".text").text(inputVoice.speedVal);
        $(".dropdown-pitch").dropdown('set selected', inputVoice.pitchVal);
        $(".dropdown-pitch").find(".text").text(inputVoice.pitchVal);
        
        $(".dropdown").dropdown();
        $(".tabVoiceSettings .item").removeClass("active");
        $(".tabVoiceSettings .item[data-tab='" + searchVoice.tab + "']").addClass("active");

        
        if (searchVoice.tab == "favorites"){
            showFavoritesTab();
        }else{
            loadVoiceDataToHTML(searchVoice.tab, searchVoice.keyword,searchVoice.country,searchVoice.gender,globalVoiceData);
        }
        if (inputVoice?.isMultiVoice){
            $(".btn-enableMultiVoice").addClass("active");
        }else{
            $(".btn-enableMultiVoice").removeClass("active");
        }

       
    }
    if (isLoadVoiceSettings) loadVoiceSettings();

    function showInputTab(tab){
        $(".nav-input-tabs .nav-link").removeClass("active");
        $(".nav-input-tabs .nav-link[id='" + tab + "']").addClass("active");
        $(".div-inputContainer").addClass("d-none");
        $("#div-inputContainer-" + tab).removeClass("d-none");
        if (tab == "input-Text"){
            $(".div-inputTextArea").removeClass("d-none");
        }else{
            $(".div-inputTextArea").addClass("d-none");
        }
       
    }
    $(".nav-input-tabs").on("click",".nav-link",function(){
        let _type = $(this).attr("id");
        showInputTab(_type);
    });
    $(".btn-tryExample").click(function(){
        let _type = $(this).attr("data-type");
        if (_type == "subTitle"){
            $("#div-inputContainer-SubTitle").show();
        }else if (_type == "delay"){    
            $("#div-inputContainer-Delay").show();
        }
    });
    function loadCountryDataToHTML(_defaultCountry = 'all'){
        let outHTML = '<div class="item" data-value="all">All languages</div>';
        let countryData = globalVoiceData
            .map(item => item.country)
            .filter((value, index, self) => self.indexOf(value) === index)
            .sort((a, b) => a.localeCompare(b)); // Sắp xếp theo alphabet
            countryData.forEach(item => {
                outHTML += `<div class="item" data-value="${item}">${voiceCountries[item]}</div>`;
            });
            $(".dropdown-country .menu").append(outHTML);
            if (_defaultCountry != 'all'){
                $(".dropdown-country").dropdown('set selected', _defaultCountry);
            }
        
    }
    
    function getVoiceGenderIcon(gender,voiceId){
        let lastNumber = voiceId.slice(-1);
        if (isNaN(lastNumber)) lastNumber = 0;
        let imgURL = '';
        if (gender == "Male" || gender == "male"){
            imgURL = `assets/images/avatars/male/${lastNumber}.svg`;
        }else if (gender == "Female" || gender == "female"){
            imgURL = `assets/images/avatars/female/${lastNumber}.svg`;
        }
        return {
            imgURL: imgURL,
            number: lastNumber
        }
    }
    function loadVoiceDataToHTML(_tab = 'all',_kw = '',defaultCountry = 'all', gender = '',_dataVoice = null){
        let outHTML = '';
        
        if (_dataVoice == null) _dataVoice = globalVoiceData;
        
        if (_tab == 'all'){
            priorityVoices = _dataVoice;
        }else{
            priorityVoices = _dataVoice.filter(
                item => item.server.toLowerCase() === _tab.toLowerCase() || 
                item.server.toLowerCase() == "gemini" ||
                item.server.toLowerCase() == "openaib"
            );
        }
        
        if (defaultCountry != 'all' && defaultCountry != null){
            priorityVoices = priorityVoices.filter(
                item => item.country.toLowerCase() === defaultCountry?.toLowerCase() ||
                item.country.toLowerCase() == "multi");
        }
        
        if (_kw != ''){
            const searchTerm = _kw.toLowerCase()?.trim();
            const filterVoices = (voices) => {
                return voices.filter(item => 
                    item.displayName.toLowerCase().includes(searchTerm) ||
                    item.language.toLowerCase().includes(searchTerm) ||
                    item.country.toLowerCase().includes(searchTerm) ||
                    item.gender.toLowerCase().includes(searchTerm) ||
                    (item.description && item.description.toLowerCase().includes(searchTerm)) ||
                    item.voiceId.toLowerCase().includes(searchTerm) ||
                    item.server.toLowerCase().includes(searchTerm) ||
                    item.voiceName.toLowerCase().includes(searchTerm)
                );
            }; 

            priorityVoices = filterVoices(globalVoiceData);
        }
        priorityVoices = priorityVoices.sort((a, b) => (a.order || 0) - (b.order || 0));
        
        if (inputVoice?.isMultiVoice){
            priorityVoices = priorityVoices.filter(item => 
                item.server == "azure" ||
                item.server == "openai" ||
                item.server == "openaib" ||
                item.server == "openaiv2" ||
                item.server == "googletts" ||
                item.server == "aiVoice" 
            );
            //priorityVoices = priorityVoices.filter(item => item.server == "azure");
        }
        console.log("priorityVoices",priorityVoices)
        let voice_Male = 0;
        let voice_Female = 0;
        let selected = 1;
        let soluong = 0;
        let voice_gender_icon = '';
        let savedVoice = inputVoice.voice;

        $(".list-voices").html(``);
        priorityVoices.forEach(voice => {
            let level = 'Vip';
            if (voice.server == "azure" || voice.server == "gemini" || voice.server == "openaib"){
                level = 'Free';
            }
            if (voice.gender == "Male" || voice.gender == "male") {
                voice_Male++;
                voice_gender_icon = '<i class="fa-solid fa-mars text-primary"></i>'
            }
            if (voice.gender == "Female" || voice.gender == "female") {
                voice_Female++;
                voice_gender_icon = '<i class="fa-solid fa-venus text-danger"></i>'
            }
            _class = "border-default";
            _voice_selected = "";
            if ((savedVoice && voice.voiceName === savedVoice) || (!savedVoice && selected == 1)) {
                _class = "border-primary";
                _voice_selected = voice.voiceName;
                selected = 0;
            }
            let _selectedClass = "";
            if (voice?.voiceId == inputVoice?.voiceId){
               _selectedClass = "selectedVoice";

               $(".btn-fillterVoice .text").html(voice.displayName)
               $(".btn-fillterVoice .iconGender").html(voice_gender_icon)

               
            }
            
            let voice_HTML = `<a ${window.location.pathname.includes('voices') ? `href="${rootURL}/voice/${voice.voiceId}"` : ``} class="selectVoice item ${_selectedClass}" server="${voice.server}" country="${voice.country}" gender="${voice.gender}" voiceId="${voice.voiceId}">
            
            <div class="item-info">
                <div class="voiceName">${voice_gender_icon}<span class="voicedisplayName">${voice.displayName}</span> </div>
                <div class="btn-useThisVoice" voiceid="${voice.voiceId}" voicename="${voice.displayName}"><i class="fa-regular fa-square-plus"></i> ${voice.voiceId} </div>
            </div>
            <div class="item-voice-body">                
                <div class="item-description">
                    <div class="des-features voiceLevel">
                        <span class="level level_${level?.toLowerCase()}">${level}</span>
                        ${(voice.server == "gemini" || voice.server == "openaib" || voice.server == "openaiv2") ? `<span class="level level_pre">${getText("New")}</span>` : ``}
                    </div>
                    ${voice.description ? `<div class="description">${getText(voice.description)}</div>` : ``}
                </div>
                <div class="avatar avatar-bg-${getVoiceGenderIcon(voice.gender,voice.voiceId).number}">
                    <img src="${voice?.avatar?`${rootURL}/assets/images/avatars/voices/${voice.avatar}`:`${rootURL}/${getVoiceGenderIcon(voice.gender,voice.voiceId).imgURL}`}">
                    <div class="country-flag"><img src="${rootURL}/assets/images/flags/${voice.country}.svg"></div>
                </div>
                
            </div>
            <div class="item-voice-footer">
                <div class="play-btn" voiceid="${voice.voiceId}">
                    <i class="btn-playDemo fas fa-play-circle"></i>
                    <span class="fs-12">${getText("Demo")}</span>
                </div>
                <div class="favorite-btn" voiceid="${voice.voiceId}">
                    <i class="${checkFavoriteVoice(voice.voiceId) ? 'fa-solid text-danger' : 'fa-regular'} fa-heart"></i>
                    <span class="fs-12">${getText("Favorite")}</span>
                </div>
            </div>
            
        </a>`
        
            $(".list-voices").append(voice_HTML);
            soluong += 1;
        });
        $(".findVoice").val(_kw);
        $(".dropdown-country .text").val(defaultCountry);
        if (gender !='') $(".dropdown-gender .text").val(gender);
    }
    let searchTimeout;
    $("body").on("keyup", ".findVoice", function(e){
        clearTimeout(searchTimeout);
        if(e.key === "Enter") {
            let _kw = $(this).val();
            searchVoice.keyword = _kw;
            setLocalStore("tts_searchVoice",JSON.stringify(searchVoice));   
            loadVoiceDataToHTML('all', _kw,searchVoice.country,searchVoice.gender,globalVoiceData);
        } else {
            searchTimeout = setTimeout(() => {
                let _kw = $(this).val();
                searchVoice.keyword = _kw;
                setLocalStore("tts_searchVoice",JSON.stringify(searchVoice));
                loadVoiceDataToHTML('all', _kw,searchVoice.country,searchVoice.gender,globalVoiceData);
            }, 500);
        }
    });

    $("body").on("click", ".btn-fillterVoice", function(){
        let htmlVoiceSettings = initVoiceSettings();
        let modalHTMLContent = `<div class="ui modal voiceSettingsModal">
  <i class="fa-regular fa-circle-xmark close"></i>
  <div class="header fs-18">
    ${getText("Change Voice")}
  </div>
  <div class="content">
        ${htmlVoiceSettings}
  </div>
</div>`;
        $(".voiceSettingsModal").remove();
        $('body').append(modalHTMLContent);
        $('.voiceSettingsModal').modal({
            duration: 100,
        }).modal('show')
        if (searchVoice.keyword != ''){
            $(".findVoice").val(searchVoice.keyword);
        }
        loadVoiceDataToHTML(searchVoice.tab,searchVoice.keyword,searchVoice.country,searchVoice.gender,globalVoiceData);
        loadCountryDataToHTML(searchVoice.country);
        if (searchVoice.gender != 'all'){
            $(".dropdown-gender").dropdown('set selected', searchVoice.gender);
        }   
        $(".dropdown").dropdown();
        
    });
 
    
    $("body").on("click", ".play-btn", function (res) {
        let voiceId = $(this).attr("voiceId");
        let voiceInfo = getVoiceInfo(voiceId);
        let voiceName = voiceInfo.voiceName;
        let languageCode = voiceInfo.languageCode;
        voiceName = voiceName.toLowerCase().replace(/[-,:]/g, '_');
        let audioURL = `https://filesaudio.aiktp.com/demo/${voiceName}.mp3`;
        if (languageCode == 'multi'){
            let currentLanguage = getCookie("lang");
            if (currentLanguage == 'vi'){
                audioURL = `https://filesaudio.aiktp.com/demo/multi/vi/${voiceName}.mp3`;
            }else{
                audioURL = `https://filesaudio.aiktp.com/demo/multi/en/${voiceName}.mp3`;
            }
        }
        console.log(audioURL);
        let playIcon = $(this).find('.btn-playDemo');
        if (playIcon.hasClass("fa-compact-disc")) {
            let audioElement = document.getElementById("demo-audio");
            if (audioElement) {
                audioElement.pause();
                audioElement.currentTime = 0;
            }
            playIcon.removeClass("fa-compact-disc playing-animation");
            playIcon.addClass("fa-play-circle");
            return;
        }
        $(".btn-playDemo").removeClass("fa-compact-disc playing-animation").addClass("fa-play-circle");
        playIcon.removeClass("fa-compact-disc");
        playIcon.addClass("fa-compact-disc playing-animation");

        if (audioURL != '') playDemoAudio(audioURL, $(this));
    });
    $("body").on("click", ".favorite-btn", function(){
        let voiceId = $(this).attr("voiceid");
        if (! checkFavoriteVoice(voiceId)){
            saveFavoriteVoice(voiceId);
            $(this).find("i").removeClass("fa-regular").addClass("fa-solid text-danger");
        }else{
            removeFavoriteVoice(voiceId);
           $(this).find("i").removeClass("fa-solid text-danger").addClass("fa-regular");
        }
        
        
    });
    $("body").on("click", ".selectVoice", function () {
        $(".selectVoice").removeClass("selectedVoice");
        $(this).addClass("selectedVoice")

        const voiceId = $(this).attr("voiceId");
        const country = $(this).attr("country");
        const gender = $(this).attr("gender");
        const server = $(this).attr("server");
        
        inputVoice.voiceId = voiceId;
        inputVoice.country = country;
        inputVoice.gender = gender;
        inputVoice.server = server;
       
        let voicedisplayName = $(this).find(".voicedisplayName").text();
        if (gender == "Male" || gender == "male"){
            iconGender = '<i class="fa-solid fa-mars text-primary"></i>';
        }else {
            iconGender = '<i class="fa-solid fa-venus text-danger"></i>';
        }
        $(".btn-fillterVoice .text").html(voicedisplayName)
        $(".btn-fillterVoice .iconGender").html(iconGender)
        setLocalStore("tts_inputVoice",JSON.stringify(inputVoice));

        $(".audioName .text").html(voicedisplayName)
        $(".audioName .iconGender").html(iconGender);
        let voiceInfo = getVoiceInfo(voiceId);
        if (inputVoice?.isMultiVoice){
            useThisVoice(voiceId,voiceInfo.displayName);
        }
    });
    window.updateWordCounts = function updateWordCounts() {
        let totalWords = countWords($('.inputText').val());
        let totalCharactor = countCharacters($('.inputText').val());
        let creditFee = parseInt(totalWords*parseFloat(inputVoice.rate));
        $(".account-status-words .totalWords").html(`${getText("Characters")}: ${numberWithCommas(totalCharactor)}/${numberWithCommas(100000)} - ${getText("Fee")}: ${numberWithCommas(creditFee)}`);
        $(".audioStatus .totalWords").html(numberWithCommas(creditFee));
    }
    $('.inputText').on('input', function () {
        updateWordCounts();
    });


    // Clear text
    $('.btn-clearText').click(function () {
        $('textarea').val('');
        $(".totalWords").html(0);
    });
    $("body").on("click", ".btn-downloadSubtitle", function(){
        let inputText = $(".inputText").val();
        
        if (inputText.includes("WEBVTT")){
            ext = ".vtt";
        }else if (inputText.includes("-->")){
            ext = ".srt";
        }else{
            ext = ".txt";
        }
        let now = new Date();
        let timestamp = now.getFullYear() + '' + 
                       String(now.getMonth() + 1).padStart(2, '0') + '' +
                       String(now.getDate()).padStart(2, '0') + '_' +
                       String(now.getHours()).padStart(2, '0') + '' +
                       String(now.getMinutes()).padStart(2, '0') + '' +
                       String(now.getSeconds()).padStart(2, '0');
        
        let filename = "aiktp_tts_" + timestamp + ext;
        
        let blob = new Blob([inputText], {type: 'text/plain'});
        
        let url = window.URL.createObjectURL(blob);
        
        let a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();      
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a); 
    });
    
    $('body').on('click', '.btn-downloadFile', function () {
        let audioUrl = $(this).attr("audioUrl");
        const a = document.createElement('a');
        a.href = audioUrl;
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        if (inputVoice.server == "openai"){
            a.download = `audio-${timestamp}.wav`;
        }else{
            a.download = `audio-${timestamp}.mp3`;
        }
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
    });

    $('#upload_background').on("change", function (event) {
        var input = event.target;
        var reader = new FileReader();

        const fileObj = event.target.files && event.target.files[0];
        if (!fileObj) {
            return;
        }
        const fileName = fileObj.name.toLowerCase();

        if (fileName.endsWith('.txt') || fileName.endsWith('.srt') || fileName.endsWith('.vtt')) {
            reader.onload = function (e) {
                let text = e.target.result;
                $(".inputText").val(text);
                showContent();
            };
            reader.readAsText(fileObj);
        } else if (fileName.endsWith('.pdf')) {
            var { pdfjsLib } = globalThis;
            pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.4.120/pdf.worker.min.js';
            pdfjsLib.getDocument(URL.createObjectURL(fileObj)).promise.then(function (pdf) {
                var pdfText = "";
                var numPages = pdf.numPages;
                var countPromises = 0;

                for (var j = 1; j <= numPages; j++) {
                    var page = pdf.getPage(j);

                    page.then(function (page) {
                        var textContent = page.getTextContent();
                        textContent.then(function (text) {
                            pdfText += text.items.map(function (s) { return s.str; }).join(' ') + "\n";
                            countPromises++;
                            if (countPromises == numPages) {
                                $(".inputText").val(pdfText);
                                showContent();
                            }
                        });
                    });
                }
            });
        } else if (fileName.endsWith('.docx') || fileName.endsWith('.doc')) {

            reader.onload = function () {
                var Document = docxyz.Document;
                var doc = new Document(this.result);
                $(".inputText").val(doc.text);
                showContent();

            };
            reader.readAsArrayBuffer(fileObj);
        }


        function showContent() {
            updateWordCounts();
            $(".div-textContent").removeClass("d-none");
            $(".upload_dropZone").addClass("d-none");
            $(".div-inputTextArea").removeClass("d-none");
            $(".div-inputContainer").addClass("d-none");
        }

    });
   
    $(".dropdown-country").dropdown({
        onChange: function(value, text, $selectedItem) {
            inputVoice.voiceLanguage = value;
            setLocalStore("tts_inputVoice",JSON.stringify(inputVoice),30);
            searchVoice.country = value;
            setLocalStore("tts_searchVoice",JSON.stringify(searchVoice),30);
            setCookie("_country",value,30);
            loadVoiceDataToHTML(searchVoice.tab,searchVoice.keyword,searchVoice.country,searchVoice.gender,globalVoiceData);
        }
    });
    $(".dropdown-speed").dropdown({
        onChange: function(value, text, $selectedItem) {
            if (value == "custom"){
                let speedVal = inputVoice?.speedVal||"1.00";
                $(".dropdown-speed").find(".text").text(speedVal);
                Swal.fire({
                    title: `<div class="d-flex justify-content-between align-items-center fs-14"><div>Custom Speed</div><div class="customSpeedValue">${speedVal}</div></div>`,
                    size: 'tiny',
                    html: `
                    <div class="ui action input fluid mb-3">
                        <input type="range" value="${speedVal}" class="form-range speedControl" min="0.1" max="2" step="0.1" id="speedControl">
                    </div>
                    `,
                    onShow: function(thisModal){
                        $("body").on("input", ".speedControl", function(){
                            let customSpeedValue = parseFloat($(this).val()).toFixed(2);
                            $(".customSpeedValue").html(customSpeedValue);
                            inputVoice.speedVal = customSpeedValue;
                            setLocalStore("tts_inputVoice",JSON.stringify(inputVoice),30);
                            $(".dropdown-speed").find(".text").text(customSpeedValue);
                        });
                    }
                });
            }else{
                inputVoice.speedVal = value;
                setLocalStore("tts_inputVoice",JSON.stringify(inputVoice),30);
            }
        }
    });
    $(".dropdown-pitch").dropdown({
        onChange: function(value, text, $selectedItem) {
            if (value == "custom"){
                let pitchVal = inputVoice?.pitchVal||"1.00";
                $(".dropdown-pitch").find(".text").text(pitchVal);
                Swal.fire({
                    title: `<div class="d-flex justify-content-between align-items-center fs-14"><div>Custom Pitch</div><div class="customPitchValue">${pitchVal}</div></div>`,
                    size: 'tiny',
                    html: `
                    <div class="ui action input fluid mb-3">
                        <input type="range" value="${pitchVal}" class="form-range pitchControl" min="0.1" max="2" step="0.1" id="pitchControl">
                    </div>
                    `,
                    onShow: function(thisModal){
                        $("body").on("input", ".pitchControl", function(){
                            let customPitchValue = parseFloat($(this).val()).toFixed(2);
                            $(".customPitchValue").html(customPitchValue);
                            inputVoice.pitchVal = customPitchValue;
                            setLocalStore("tts_inputVoice",JSON.stringify(inputVoice),30);
                            $(".dropdown-pitch").find(".text").text(customPitchValue);
                        });
                    }
                });
            }else{
                inputVoice.pitchVal = value;
                setLocalStore("tts_inputVoice",JSON.stringify(inputVoice),30);
            }
        }
    });
    $(".dropdown-breakTime").dropdown({
        onChange: function(value, text, $selectedItem) {
            let breakTime = value;
            let $input = $(".inputText");
            let input = $input.get(0);
            let start = input.selectionStart;
            let end = input.selectionEnd;
            let oldVal = $input.val();

            let pauseTag = '(pause: ' + breakTime + 's)';
            let textToInsert = pauseTag;

            if (start > 0 && oldVal[start - 1] !== '\n') {
                textToInsert = '\n' + textToInsert;
            }

            if (end < oldVal.length && oldVal[end] !== '\n') {
                textToInsert = textToInsert + '\n';
            }
            if (start > 0 && oldVal[start - 1] !== '\n' && end < oldVal.length && oldVal[end] !== '\n') {
                 textToInsert = '\n' + pauseTag + '\n';
            } else if (start > 0 && oldVal[start - 1] !== '\n') {
                 textToInsert = '\n' + pauseTag;
            } else if (end < oldVal.length && oldVal[end] !== '\n') {
                 textToInsert = pauseTag + '\n';
            } else {
                 textToInsert = pauseTag;
            }


            let newVal = oldVal.substring(0, start) + textToInsert + oldVal.substring(end);
            $input.val(newVal);
            let newCursorPos = start + textToInsert.length;
            input.setSelectionRange(newCursorPos, newCursorPos);
            updateWordCounts();
        }
    });
    function useThisVoice(voiceId,voicename){
        let $input = $(".inputText");
        let input = $input.get(0);
        let start = input.selectionStart;
        let end = input.selectionEnd;
        let oldVal = $input.val();

        let voiceTag = '(voice: ['+voiceId+'] - '+voicename+')';
        let textToInsert = voiceTag;

        if (start > 0 && oldVal[start - 1] !== '\n') {
            textToInsert = '\n' + textToInsert;
        }
        if (end < oldVal.length && oldVal[end] !== '\n') {
            textToInsert = textToInsert + '\n';
        }
        if (start > 0 && oldVal[start - 1] !== '\n' && end < oldVal.length && oldVal[end] !== '\n') {
             textToInsert = '\n' + voiceTag + '\n';
        } else if (start > 0 && oldVal[start - 1] !== '\n') {
             textToInsert = '\n' + voiceTag;
        } else if (end < oldVal.length && oldVal[end] !== '\n') {
             textToInsert = voiceTag + '\n';
        } else {
             textToInsert = voiceTag;
        }
        let newVal = oldVal.substring(0, start)  + textToInsert + oldVal.substring(end);
        $input.val(newVal);
        let newCursorPos = start + textToInsert.length;
        input.setSelectionRange(newCursorPos, newCursorPos);
        updateWordCounts();
    }
    $(".dropdown-upload").dropdown({
        onChange: function(value, text, $selectedItem) {
            switch(value){
                case "web-link":
                    Swal.fire({
                        title: 'Load from web link',
                        size: 'tiny',
                        html: `
<div class="mb-1">Enter the web link to load the content</div>
<div class="ui action input fluid mb-3">
  <input type="text" placeholder="https://domain.com..." class="input-webLink">
  <button class="ui button primary btn-loadWebLink">Load</button>
</div>`,
                        onShow: function(thisModal){
                            $("body").on("click", ".btn-loadWebLink", function(){
                                let webLink = $(".input-webLink").val();
                                let postVal =  {
                                    task:'getContent',
                                    url:webLink,
                                }
                                let thisObj = $(this);
                                thisObj.html(`Load <i class="fa-solid fa-spinner fa-spin-pulse ms-1"></i>`);
                                $.post('https://pro_4_4012.aiktp.com',postVal,function(r){
                                    
                                    if (r?.data?.description){
                                        $(".inputText").val(r?.data?.description);
                                        thisModal.modal('hide').remove();
                                    }else{
                                        $(".loadWebpageContent-status").html(`${getText("No content found. Please copy and paste the content manually.")}`);
                                    }
                                    thisObj.html(`Load`);
                                    updateWordCounts();
                                })
                            });
                        }
                        
                    });
                break;
                case "docx":
                case "text":
                case "pdf":
                    const fileInput = document.createElement('input');
                    fileInput.type = 'file';
                    fileInput.style.display = 'none';
                    
                    // Set accepted file types based on selection
                    switch(value) {
                        case 'text':
                            fileInput.accept = '.txt,.srt,.vtt';
                            break;
                        case 'pdf':
                            fileInput.accept = '.pdf';
                            break;
                        case 'docx':
                            fileInput.accept = '.doc,.docx';
                            break;
                    }

                    // Handle file selection
                    fileInput.onchange = function(event) {
                        const fileObj = event.target.files && event.target.files[0];
                        if (!fileObj) return;
                        
                        const fileName = fileObj.name.toLowerCase();
                        const reader = new FileReader();

                        if (fileName.endsWith('.txt') || fileName.endsWith('.srt') || fileName.endsWith('.vtt')) {
                            reader.onload = function(e) {
                                $(".inputText").val(e.target.result);
                                updateWordCounts();
                            };
                            reader.readAsText(fileObj);
                        } 
                        else if (fileName.endsWith('.pdf')) {
                            var { pdfjsLib } = globalThis;
                            pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.4.120/pdf.worker.min.js';
                            pdfjsLib.getDocument(URL.createObjectURL(fileObj)).promise.then(function(pdf) {
                                var pdfText = "";
                                var numPages = pdf.numPages;
                                var countPromises = 0;

                                for (var j = 1; j <= numPages; j++) {
                                    pdf.getPage(j).then(function(page) {
                                        page.getTextContent().then(function(text) {
                                            pdfText += text.items.map(function(s) { return s.str; }).join(' ') + "\n";
                                            countPromises++;
                                            if (countPromises == numPages) {
                                                $(".inputText").val(pdfText);
                                                updateWordCounts();
                                            }
                                        });
                                    });
                                }
                            });
                        }
                        else if (fileName.endsWith('.docx') || fileName.endsWith('.doc')) {
                            reader.onload = function() {
                                var Document = docxyz.Document;
                                var doc = new Document(this.result);
                                $(".inputText").val(doc.text);
                                updateWordCounts();
                            };
                            reader.readAsArrayBuffer(fileObj);
                        }
                    };

                    // Trigger file selection
                    document.body.appendChild(fileInput);
                    fileInput.click();
                    document.body.removeChild(fileInput);
                break;
                
            }
            $(".dropdown-upload").dropdown('restore');
            $(".dropdown-upload .text").html('Upload');
        }
    });
    $("body").on("click", ".tabVoiceSettings .item", function(){
        let tab = $(this).attr("data-tab");
        $(".tabVoiceSettings .item").removeClass("active");
        $(this).addClass("active");
        searchVoice.tab = tab;
        setLocalStore("tts_searchVoice",JSON.stringify(searchVoice),30);
        $(".filterVoice").removeClass("d-none");
        if (tab == "favorites"){
            
            showFavoritesTab().then();
        }else{
            loadVoiceDataToHTML(tab,searchVoice.keyword,searchVoice.country,searchVoice.gender,globalVoiceData);
        }
        
    });
    $("body").on("click", ".btn-clearFilter", function(){
        searchVoice = searchVoice_default;
        $(".findVoice").val('');
        $(".dropdown-country").dropdown('restore');
        $(".dropdown-gender").dropdown('restore');
        $(".dropdown-country").dropdown('set selected', searchVoice.country);
        $(".dropdown-gender").dropdown('set selected', searchVoice.gender);
        
        setLocalStore("tts_searchVoice",JSON.stringify(searchVoice),30);
        $(".filterVoice").removeClass("d-none");
        loadVoiceDataToHTML(searchVoice.tab,searchVoice.keyword,searchVoice.country,searchVoice.gender,globalVoiceData);
    });
    async function showFavoritesTab(){
        let _dataVoice = await getFavoriteVoice();
        loadVoiceDataToHTML('all','','all','',_dataVoice);
        $(".filterVoice").addClass("d-none");
    }
    $("body").on("click", ".btn-enableMultiVoice", function(){
        let $this = $(this);
        let isActive = $this.hasClass("active");
        $this.toggleClass("active");
        if (isActive){
            $this.html(`<i class="fa-duotone fa-solid fa-messages"></i> Multi-Voice`);    
            let inputText = $(".inputText").val();
            inputText = inputText.replace(/(\n|^)\(voice\s*:\s*\[.+?\]\s*-\s*.+?\)(\n|$)/g, (match, p1, p2) => {
                if (p1 === '\n' && p2 === '\n') {
                    return '\n';
                }
                return '';
            });
            $(".inputText").val(inputText);
            inputVoice.isMultiVoice = false;
        }else{
            $this.html(`<i class="fa-duotone fa-solid fa-square-check"></i> Multi-Voice`);
            inputVoice.isMultiVoice = true;
            let inputText = $(".inputText").val();
            const voiceTagRegex = /\(voice\s*:\s*\[.+?\]\s*-\s*.+?\)/;
            if (!voiceTagRegex.test(inputText)) {
                let defaultVoiceTag = `(voice: [${inputVoice.voiceId}] - ${getVoiceInfo(inputVoice.voiceId).displayName})`;
                if (inputText.length > 0 && inputText[0] !== '\n') {
                     defaultVoiceTag += '\n';
                } else if (inputText.length > 0 && inputText[0] === '\n'){
                } else if (inputText.length === 0) {
                }


                $(".inputText").val(defaultVoiceTag + inputText);
                
                updateWordCounts(); 
            }
        }
        setLocalStore("tts_inputVoice",JSON.stringify(inputVoice),30);
        loadVoiceDataToHTML(searchVoice.tab,searchVoice.keyword,searchVoice.country,searchVoice.gender,globalVoiceData);
    });
    if (docId != ''){
        loadDocData(docId);
    }
    function loadDocData(docId){
        $.getJSON(`${rootURL}/api/index.php?task=getDocDataFromErrorLog&docId=${docId}`,function(r){
            if (r?.data?.id){
                let voiceData = r?.data;
                $(".inputText").val(voiceData?.inputText);

                let voiceInfo = getVoiceInforV2(voiceData?.voiceName);
                inputVoice.voiceId = voiceInfo?.voiceId;
                inputVoice.country = voiceInfo?.country;
                inputVoice.gender = voiceInfo?.gender;
                inputVoice.server = voiceInfo?.server;
            
                let voicedisplayName = voiceInfo?.displayName;
                if (inputVoice.gender == "Male" || inputVoice.gender == "male"){
                    iconGender = '<i class="fa-solid fa-mars text-primary"></i>';
                }else {
                    iconGender = '<i class="fa-solid fa-venus text-danger"></i>';
                }
                $(".btn-fillterVoice .text").html(voicedisplayName)
                $(".btn-fillterVoice .iconGender").html(iconGender)
                setLocalStore("tts_inputVoice",JSON.stringify(inputVoice));

                $(".audioName .text").html(voicedisplayName)
                $(".audioName .iconGender").html(iconGender);
                useThisVoice(voiceInfo?.voiceId,voiceInfo?.displayName);
                
                updateWordCounts();
            }
        });
    }

    $("body").on("click", ".btn-reportError", function(){
        let $this = $(this);
        let inputText = $(".inputText").val();
        let voiceName = getVoiceInfo(inputVoice.voiceId).voiceName;
        Swal.fire({
            title: getText("Report Error"),
            html: `
            <div class="px-2">
                <div class="mb-2">${getText("If a Text-to-Speech conversion fails, our team will investigate the issue and refund the corresponding credits within 24 hours")}</div>
                <div class="mb-2 fw-medium">${getText("Error message")}</div>
                <div class="ui form">
                    <div class="mb-2"><textarea name="errorMessage" rows="2" class="input-errorMessage"></textarea></div>
                    <div class="mb-2 text-end"><button class="ui button right labeled icon soft-primary btn-doReportError"><i class="fa-solid fa-arrow-right icon"></i> ${getText("Report Error")}</button></div>
                </div>

                <div class="text-danger text-center status-reportError"></div>
            </div>
            `,
            onShow: function(thisModal){
                $("body").on("click", ".btn-doReportError", async function(){
                    $(this).prop("disabled",true);
                    $(this).html(`<i class="fa-solid fa-spinner fa-spin-pulse ms-1"></i> ${getText("Report Error")}`);
                    let thisObj = $(this);
                    try {
                        let audioURL = $(".btn-downloadFile").attr("audiourl");
                        let duration = $(".btn-reportError").attr("data-duration") || 0;
                        if (duration == 0){
                             duration = await getAudioDuration(audioURL);
                        }
                        const formattedDuration = formatDuration(duration);
                        let postVal = {
                            userToken:userToken,
                            audioDuration:formattedDuration,
                            inputText:inputText,
                            voiceName:voiceName,
                            audioURL:audioURL,
                            errorMessage:$(".input-errorMessage").val(),
                        }
                        postVal.docId = docId;
                        postVal.totalWords = countWords(inputText);
                        if (inputVoice?.server =="openai"){
                            postVal.fee = parseInt(postVal.totalWords*1.5);
                        }else{
                            postVal.fee = parseInt(postVal.totalWords*1);
                        }
                        let r = await $.post(`${rootURL}/api/log.php?task=saveUserErrorLog`,postVal);
                        r = JSON.parse(r);
                        if (r?.data?.status == 'success'){
                            $(".status-reportError").html(`${getText("Error reported successfully. We will check and refund the corresponding credits within 24 hours")}`);
                        }else{
                            $(".status-reportError").html(`${getText("Error reported failed. Please try again later")}`);
                        }
                        thisObj.prop("disabled",false);
                        thisObj.html(`<i class="fa-solid fa-arrow-right icon"></i> ${getText("Report Error")}`);
                    } catch (error) {
                        console.error('Error getting duration:', error);
                    }

                    let errorMessage = $(".input-errorMessage").val();
                    console.log(errorMessage);
                });
            }
        });
    });
    function formatDuration(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
    async function getAudioDuration(audioUrl) {
        try {
            // Tạo một audio element
            const audio = new Audio();
            
            // Tạo promise để đợi metadata load
            const duration = await new Promise((resolve, reject) => {
                audio.addEventListener('loadedmetadata', () => {
                    resolve(audio.duration);
                });
                
                audio.addEventListener('error', (error) => {
                    reject(error);
                });
                
                // Set source cho audio
                audio.src = audioUrl;
            });
            
            return duration;
        } catch (error) {
            throw error;
        }
    }
});