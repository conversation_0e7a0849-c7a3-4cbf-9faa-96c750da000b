// REMOTION APPROACH - Tạo video composition thay vì concat segments
const path = require('path');
const fs = require('fs');
const os = require('os');
const { getAudioDuration } = require('../ffmpegHandler.js');
const { exec } = require('child_process');
const { renderMedia, getCompositions } = require('@remotion/renderer')
const { bundle } = require('@remotion/bundler')
// const { getVideoMetadata } = require('@remotion/media-utils');
// import { bundle, renderMedia } from '@remotion/renderer';

const srtArray =[
    {
        "index": 1,
        "id": 1,
        "text": "群演一天一千去不去",
        "startTime": 0,
        "endTime": 1.6800000000000002,
        "start": "00:00:00,000",
        "end": "00:00:01,680",
        "translatedText": "<PERSON><PERSON><PERSON> vi<PERSON><PERSON> quần chúng một ngày một nghìn, đi không?",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "http://localhost:8082/assets?path=I:\\ReviewDao\\con-nho\\1111_audio\\dien-vien-quan-chung-mot1747995697765.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 2,
        "audioUrl2": "http://localhost:8082/assets?path=I:\\ReviewDao\\con-nho\\1111_audio\\dien-vien-quan-chung-mot1747995697765.mp3",
        "audioDuration2": 2742.857,
        "isGenerated2": true
    },
    {
        "index": 2,
        "id": 2,
        "text": "又想打我",
        "startTime": 1.6800000000000002,
        "endTime": 3.7,
        "start": "00:00:01,680",
        "end": "00:00:03,700",
        "translatedText": "Lại định đánh tôi nữa à?",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "http://localhost:8082/assets?path=I:\\ReviewDao\\con-nho\\1111_audio\\lai-dinh-danh-toi-nua-a1747995699414.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "http://localhost:8082/assets?path=I:\\ReviewDao\\con-nho\\1111_audio\\lai-dinh-danh-toi-nua-a1747995699414.mp3",
        "audioDuration1": 1464,
        "isGenerated1": true
    },
]

// const srtArray =[
//     {
//         "index": 1,
//         "id": 1,
//         "text": "你知道人贩子为什么喜欢偷小孩吗",
//         "startTime": 0.22,
//         "endTime": 2.3,
//         "start": "00:00:00,220",
//         "end": "00:00:02,300",
//         "translatedText": "Bạn có biết tại sao bọn buôn người thích bắt cóc trẻ em không?",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "http://localhost:8082/assets?path=F:\\Footage\\0.-any\\_-converted_audio\\ban-co-biet-tai-sao-bon-1747983044585.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "http://localhost:8082/assets?path=F:\\Footage\\0.-any\\_-converted_audio\\ban-co-biet-tai-sao-bon-1747983044585.mp3",
//         "audioDuration1": 2904,
//         "isGenerated1": true
//     },
//     {
//         "index": 2,
//         "id": 2,
//         "text": "不知道",
//         "startTime": 2.3,
//         "endTime": 2.98,
//         "start": "00:00:02,300",
//         "end": "00:00:02,980",
//         "translatedText": "Không biết",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "http://localhost:8082/assets?path=F:\\Footage\\0.-any\\_-converted_audio\\khong-biet1747983046718.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 2,
//         "audioUrl2": "http://localhost:8082/assets?path=F:\\Footage\\0.-any\\_-converted_audio\\khong-biet1747983046718.mp3",
//         "audioDuration2": 655,
//         "isGenerated2": true
//     },
//     {
//         "index": 30,
//         "id": 30,
//         "text": "知道了",
//         "startTime": 46.22,
//         "endTime": 46.76,
//         "start": "00:00:46,220",
//         "end": "00:00:46,760",
//         "translatedText": "Con hiểu rồi",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "http://localhost:8082/assets?path=F:\\Footage\\0.-any\\_-converted_audio\\con-hieu-roi1747983073898.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 2,
//         "audioUrl2": "http://localhost:8082/assets?path=F:\\Footage\\0.-any\\_-converted_audio\\con-hieu-roi1747983073898.mp3",
//         "audioDuration2": 825,
//         "isGenerated2": true
//     }
// ]





console.log('Starting Remotion render...');


const videoPath = "I:\\ReviewDao\\con-nho\\1111.mp4"
// const videoPath = "F:\\Footage\\0.-any\\batcoc\\_-converted.mp4"
let logoPath = ''
const tempDir = path.join(os.tmpdir(), 'video_segments');
const options = {
  addLogo: false,
  addText: false,
  customText: 'Video Rendered by Vue+Electron',
}



// Setup Remotion composition
const setupRemotionComposition = (srtArray, videoPath) => {
  // Tính total duration
  const totalDuration = srtArray.reduce((sum, srt) => {
    return sum + Math.max(srt.duration, srt.endTime - srt.startTime);
  }, 0);
  
  const fps = 30;
  const durationInFrames = Math.ceil(totalDuration * fps);
  // const VideoComposition = require('./VideoComposition.jsx').VideoComposition;

  return {
    id: 'TTS-Video',
    component: () => import('./Video.jsx'),
    durationInFrames,
    fps,
    width: 1920,
    height: 1080,
    props: {
      srtArray,
      videoPath
    }
  };
};

async function cutVideoSegment(videoPath, outputPath, startTime, duration, speedFactor) {
  const command = `ffmpeg -i "${videoPath}" -ss ${startTime} -t ${duration} -filter:v "setpts=${1/speedFactor}*PTS" -c:a copy "${outputPath}"`;
  return new Promise((resolve, reject) => {
    exec(command, (error, stdout, stderr) => {
      if (error) {
        reject(error);
      } else {
        resolve(outputPath);
      }
    });
  });
}
const adjustVideoSpeed = (videoSegment, audioUrl, srt, outputPath) => {
  return new Promise((resolve, reject) => {
    const audioPath = audioUrl.replace('file://', '');
    const videoDuration = srt.endTime - srt.startTime;
    const audioDuration = srt.duration;
    const speedFactor = audioDuration / videoDuration;
    srt.speedFactor = speedFactor < 1 ? speedFactor : 1;
    let command = '';

    if (speedFactor > 1) {
      // Audio dài hơn → làm chậm video để khớp
      command = `ffmpeg -i "${videoSegment}" -i "${audioPath}" ` +
        `-filter_complex "[0:v]setpts=${speedFactor}*PTS[v]" ` +
        `-map "[v]" -map 1:a -c:v h264_nvenc -preset fast -crf 23 -c:a aac -y "${outputPath}"`;
    } else {
      // Audio ngắn hơn hoặc bằng → giữ nguyên video
      command = `ffmpeg -i "${videoSegment}" -i "${audioPath}" ` +
        `-map 0:v -map 1:a -c:v copy -c:a aac -y "${outputPath}"`;
    }

    console.log('adjustVideoSpeed:', srt.speedFactor);
    exec(command, (error) => {
      if (error) reject(error);
      else resolve(outputPath);
    });
  });
};

// Main process function với Remotion
const processVideoWithRemotion = async () => {
  if (!srtArray.length) {
    throw new Error('Missing video path or SRT data');
  }

  console.log('Starting Remotion render...');
  
  try {
    // 1. Tính audio duration cho tất cả segments
    for (const srt of srtArray) {
      srt.duration = await getAudioDuration(srt.audioUrl.replace('http://localhost:8082/assets?path=', ''));
      srt.text = srt.translatedText;

      // Cắt và điều chỉnh speed trước khi đưa vào Remotion
      const segmentPath = path.join(tempDir, `segment_${srt.index}.mp4`);
      const adjustVideo = await adjustVideoSpeed(videoPath, srt.audioUrl, srt, segmentPath);
      srt.videoPath = `http://localhost:8082/assets?path=${segmentPath}`;
    }
    
    // 3. Bundle Remotion project
    const bundled = await bundle({
      entryPoint: path.join(__dirname, 'index.jsx'),
      webpackOverride: (config) => config,
    });
      const totalDuration = srtArray.reduce((sum, srt) => {
    return sum + Math.max(srt.duration, srt.endTime - srt.startTime);
  }, 0);
    // 4. Setup composition
    const videoConfig = {
      duration: totalDuration,
      fps: 30,
      height: 1920,
      width: 1080
    };
    const videoPath2 = "http://localhost:8082/1111.mp4"
    // const videoPath = "http://localhost:8082/_-converted.mp4"
    const comps = await getCompositions(bundled, { inputProps: {
        srtArray,
        videoPath: videoPath2,
        videoConfig
      } });
    const composition = comps.find((c) => c.id === 'TikTok');
    // 5. Render video
    const outputPath = path.join(os.homedir(), 'Desktop', 'final_video_remotion-ok.mp4');
    console.log(`✓ Remotion srtArray: `,srtArray);
    console.log(`✓ Remotion renderMedia: ${outputPath}`);
    await renderMedia({
      composition,
      serveUrl: bundled,
      codec: 'h264',
      outputLocation: outputPath,
      inputProps: {
        srtArray,
        videoPath,
        videoConfig
      }
    });
    
    console.log(`✓ Remotion video completed: ${outputPath}`);
    return outputPath;
    
  } catch (error) {
    console.error('Remotion render error:', error);
    throw error;
  }
};

// Alternative: Simplified FFmpeg approach với correct timeline
const BATCH_SIZE = 20;

const processVideoSimplified = async () => {
  if (!videoPath) return alert('Vui lòng chọn file video!');
  if (!fs.existsSync(tempDir)) fs.mkdirSync(tempDir);

  // 1. Tính duration cho audio từng đoạn
  for (const srt of srtArray) {
    srt.duration = await getAudioDuration(srt.audioUrl.replace('file://', ''));
  }

  // 2. Chia thành nhiều batch nếu quá nhiều
  const batches = [];
  for (let i = 0; i < srtArray.length; i += BATCH_SIZE) {
    batches.push(srtArray.slice(i, i + BATCH_SIZE));
  }

  const batchVideoPaths = [];

  for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
    const batch = batches[batchIndex];
    const inputs = [`-i "${videoPath}"`];
    let videoFilters = [];
    let audioFilters = [];
    let currentTime = 0;

    batch.forEach((srt, index) => {
        const audioPath = srt.audioUrl.replace('file://', '');
        inputs.push(`-i "${audioPath}"`);

        const vStart = srt.startTime;
        const vDur = srt.endTime - srt.startTime;
        const aDur = srt.duration;
        const segmentDur = Math.max(vDur, aDur);
        const audioIndex = index + 1;

        videoFilters.push(
            `[0:v]trim=start=${vStart}:duration=${vDur},setpts=PTS-STARTPTS,scale=1920:1080[v${index}]`
        );

        const volume = srt.mixVolume ?? 1;
        // audioFilters.push(
        //   `[${audioIndex}:a]loudnorm=I=-16:TP=-1.5:LRA=11,volume=${volume},adelay=${currentTime * 1000}|${currentTime * 1000}[a${index}]`
        // );
        const boostDb = 14;
        const volumeMultiplier = Math.pow(10, boostDb / 20); // = 2.511886...
        audioFilters.push(
          `[${audioIndex}:a]dynaudnorm=f=150:g=15,volume=${volumeMultiplier},adelay=${currentTime * 1000}|${currentTime * 1000}[a${index}]`
        );


        currentTime += segmentDur;
    });


    const videoConcat = `${videoFilters.join('; ')}; ${batch.map((_, i) => `[v${i}]`).join('')}concat=n=${batch.length}:v=1:a=0[vout]`;
    // const audioMix = `${audioFilters.join('; ')}; ${batch.map((_, i) => `[a${i}]`).join('')}amix=inputs=${batch.length}:duration=longest[aout]`;
    const weights = batch.map(srt => srt.mixVolume ?? 1).join(" ");
    const audioMix = `${audioFilters.join('; ')}; ${batch.map((_, i) => `[a${i}]`).join('')}amix=inputs=${batch.length}:duration=longest[aout]`;

    const filterComplex = `${videoConcat}; ${audioMix}`;

    const batchOutput = path.join(tempDir, `batch_${batchIndex}.mp4`);
    batchVideoPaths.push(batchOutput);

    const ffmpegCmd = `ffmpeg ${inputs.join(' ')} ` +
  `-filter_complex "${filterComplex}" ` +
  `-map "[vout]" -map "[aout]" ` +
  `-c:v h264_nvenc -preset fast -crf 23 ` +
  `-c:a aac -ar 44100 -ac 2 -b:a 192k ` +
  `-y "${batchOutput}"`;

    console.log(`🧩 Running FFmpeg for batch ${batchIndex + 1}/${batches.length}`);
    await execPromise(ffmpegCmd);
  }

  // 3. Gộp các batch lại bằng concat
  const concatListPath = path.join(tempDir, 'concat_list.txt');
  const concatListContent = batchVideoPaths.map(p => `file '${p}'`).join('\n');
  fs.writeFileSync(concatListPath, concatListContent);

  const finalOutput = path.join(os.homedir(), 'Desktop', 'final_video_simple2.mp4');
  const concatCmd = `ffmpeg -f concat -safe 0 -i "${concatListPath}" ` +
  `-c:v h264_nvenc -preset fast -crf 23 -c:a aac -b:a 192k -y "${finalOutput}"`;


  console.log('📦 Running final concat...');
  await execPromise(concatCmd);

  console.log(`✅ Final video generated: ${finalOutput}`);
  return finalOutput;
};

// Helper promisified exec
const execPromise = (cmd) =>
  new Promise((resolve, reject) => {
    exec(cmd, (err, stdout, stderr) => {
      if (err) {
        console.error('❌ FFmpeg error:', err);
        console.error(stderr);
        return reject(err);
      }
      resolve();
    });
  });

const prepareVideoData = async (videoPath, srtArray) => {
  
  // Get video metadata
  // const videoMetadata = await getVideoMetadata(videoPath);
  // const videoDuration = videoMetadata.duration;
  // const videoWidth = videoMetadata.width;
  // const videoHeight = videoMetadata.height;
  const fps = 30;
  
  let totalDuration = 0;
  const segments = [];
  
  for (let i = 0; i < srtArray.length; i++) {
    const srt = srtArray[i];
    const videoDuration = srt.endTime - srt.startTime;
    const audioDuration = srt.duration;
    
    // Xác định duration và speed cho segment
    let segmentDuration, videoSpeed;
    
    if (audioDuration > videoDuration) {
      // Audio dài hơn -> slow down video
      segmentDuration = audioDuration;
      videoSpeed = videoDuration / audioDuration; // < 1 (chậm)
    } else {
      // Audio ngắn hơn hoặc bằng -> giữ nguyên video
      segmentDuration = videoDuration;
      videoSpeed = 1;
    }
    
    segments.push({
      id: i,
      videoStart: srt.startTime,
      videoDuration: videoDuration,
      audioPath: srt.audioUrl.replace('file://', ''),
      audioDuration: audioDuration,
      segmentDuration: segmentDuration,
      videoSpeed: videoSpeed,
      volume: srt.mixVolume ?? 1,
      startFrame: Math.round(totalDuration * fps),
      durationInFrames: Math.round(segmentDuration * fps)
    });
    
    totalDuration += segmentDuration;
  }
  
  return {
    segments,
    totalDurationInFrames: Math.round(totalDuration * fps),
    totalDuration
  };
};
async function generateFullSegmentsFromSRT(srtArray, fps, videoTotalDuration, videoPath) {
  const segments = [];
  let currentTime = 0; // tính theo giây
  let currentStartFrame = 0;

  // Tạo silence audio url nếu cần, hoặc null
  const silenceAudioUrl = null; // hoặc đường dẫn file silence nếu có

  for (let i = 0; i <= srtArray.length; i++) {
    // Xử lý đoạn im lặng trước đoạn voice (trừ đoạn đầu)
    const nextStartTime = i < srtArray.length ? srtArray[i].startTime : videoTotalDuration;

    if (nextStartTime > currentTime) {
      // Đây là đoạn im lặng (video không có voice)
      const silenceDuration = nextStartTime - currentTime;
      const durationInFrames = Math.round(silenceDuration * fps);

      segments.push({
        id: `silence-${i}`,
        startFrame: currentStartFrame,
        durationInFrames,
        videoStart: currentTime,
        videoDuration: silenceDuration,
        videoSpeed: 1,
        audioPath: silenceAudioUrl, // hoặc null
        audioDuration: silenceDuration,
        segmentDuration: silenceDuration,
        volume: 0,
        videoPath,
      });

      currentStartFrame += durationInFrames;
      currentTime = nextStartTime;
    }

    if (i < srtArray.length) {
      // Đoạn có voice (voice segment)
      const item = srtArray[i];
      const isVoice = item.isVoice;
      const audioDurationMs = item[`audioDuration${isVoice}`] || 0;
      const audioDuration = audioDurationMs / 1000;

      const videoSegmentDuration = item.endTime - item.startTime;
      const segmentDuration = Math.max(audioDuration, videoSegmentDuration);

      // Tính tốc độ video
      const videoSpeed = +(videoSegmentDuration / segmentDuration).toFixed(4);

      const durationInFrames = Math.round(segmentDuration * fps);

      segments.push({
        id: item.id || i,
        startFrame: currentStartFrame,
        durationInFrames,
        videoStart: item.startTime,
        videoDuration: videoSegmentDuration,
        videoSpeed,
        audioPath: item[`audioUrl${isVoice}`],
        audioDuration,
        segmentDuration,
        volume: 1,
        videoPath,
      });

      currentStartFrame += durationInFrames;
      currentTime += segmentDuration;
    }
  }

  return segments;
}




// Export main function
module.exports = {
  processVideoWithRemotion,
  processVideoSimplified
};

// processVideoSimplified();
// processVideoWithRemotion()

// prepareVideoData(videoPath, srtArray).then(data => {
//   console.log(data);
// });

generateFullSegmentsFromSRT(srtArray, 30, 44.533333, videoPath).then(data => {
  console.log(data);
});