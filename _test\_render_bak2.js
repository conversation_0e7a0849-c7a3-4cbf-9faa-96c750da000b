const { exec } = require('child_process');
const path = require('path');
const fs = require('fs');
const { getAudioDuration, getEncoder,getVideoInfo } = require('../ffmpegHandler');
const { cssToASSColor, generateASSSubtitle, clampAtTempo } = require('./assBuild');
const {addAudioToVideo, upVolume } = require('./addAudioToVideo');
// Enhanced execPromise with better error handling
const execPromise = (cmd, options = {}) => {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    console.log(`🚀 Executing command: ${cmd.substring(0, 100)}${cmd.length > 100 ? '...' : ''}`);

    exec(cmd, { maxBuffer: 1024 * 1024 * 10, ...options }, (error, stdout, stderr) => {
      const duration = ((Date.now() - startTime) / 1000).toFixed(2);

      if (error) {
        console.error(`❌ Command failed after ${duration}s:`, error.message);
        if (stderr) {
          console.error(`❌ FFmpeg stderr:`, stderr);
        }
        if (stdout) {
          console.log(`📝 FFmpeg stdout:`, stdout);
        }

        // Create a more detailed error
        const detailedError = new Error(`FFmpeg execution failed: ${error.message}`);
        detailedError.originalError = error;
        detailedError.stderr = stderr;
        detailedError.stdout = stdout;
        detailedError.command = cmd;
        detailedError.duration = duration;

        reject(detailedError);
      } else {
        console.log(`✅ Command completed successfully in ${duration}s`);
        if (stdout && stdout.trim()) {
          console.log(`📝 FFmpeg output:`, stdout.trim());
        }
        resolve({ stdout, stderr, duration });
      }
    });
  });
};

const BATCH_SIZE = 10;



const resolutionMap = {
  '144p': 144,
  '240p': 240,
  '360p': 360,
  '480p': 480,
  '720p': 720,
  '1080p': 1080,
  '1440p': 1440,
  '2160p': 2160,
  '4k': 2160,
};

function getResolution(qualityInput) {
  if (!qualityInput) return null;

  const [qualityRaw, aspectRaw = '16:9'] = qualityInput.toLowerCase().split('/');
  const qualityKey = /^\d+$/.test(qualityRaw) ? qualityRaw + 'p' : qualityRaw;

  const size = resolutionMap[qualityKey];
  if (!size) return null;

  const [wRatio, hRatio] = aspectRaw.split(':').map(Number);
  if (!wRatio || !hRatio) return null;

  let width, height;

  if (wRatio > hRatio) {
    // Landscape → height = size
    height = size;
    width = Math.round((height * wRatio) / hRatio);
  } else {
    // Portrait → width = size
    width = size;
    height = Math.round((width * hRatio) / wRatio);
  }

  return { width, height };
}


function buildBatchAndSrt(event, srtArray, BATCH_SIZE) {
  console.log(`🔧 buildBatchAndSrt called with:`);
  console.log(`   - srtArray.length: ${srtArray.length}`);
  console.log(`   - BATCH_SIZE: ${BATCH_SIZE}`);

  if (!srtArray || srtArray.length === 0) {
    console.error('❌ buildBatchAndSrt: srtArray is empty or undefined');
    return [];
  }

  let adjustedCurrentTime = 0; // Thời gian tích luỹ sau khi điều chỉnh tốc độ

  for (let i = 0; i < srtArray.length; i++) {
    const currentSrt = srtArray[i];
    const nextSrt = srtArray[i + 1];

    // Thời lượng video segment gốc
    const originalVideoSegmentDuration = nextSrt
      ? nextSrt.startTime - currentSrt.startTime
      : currentSrt.endTime - currentSrt.startTime;

    currentSrt.originalVideoSegmentDuration = originalVideoSegmentDuration;

    // Tính speed ratio
    if (currentSrt.duration > originalVideoSegmentDuration) {
      currentSrt.speedRatio = originalVideoSegmentDuration / currentSrt.duration; // < 1 = chậm lại
    } else {
      currentSrt.speedRatio = 1; // không thay đổi tốc độ
    }

    // Thời lượng thực tế sau khi điều chỉnh tốc độ
    const adjustedVideoDuration = originalVideoSegmentDuration / currentSrt.speedRatio;

    // Thời lượng cuối cùng của đoạn này (lấy max giữa video đã điều chỉnh và audio)
    currentSrt.finalDuration = Math.max(adjustedVideoDuration, currentSrt.duration);

    // Cập nhật thời gian start/end mới cho SRT
    currentSrt.adjustedStartTime = adjustedCurrentTime;
    currentSrt.adjustedEndTime = adjustedCurrentTime + currentSrt.finalDuration;

    // Cập nhật thời gian tích luỹ
    adjustedCurrentTime += currentSrt.finalDuration;

    const logs = `📊 Segment ${i}: original=${originalVideoSegmentDuration.toFixed(
      2,
    )}s, audio=${currentSrt.duration.toFixed(2)}s, speed=${currentSrt.speedRatio.toFixed(
      3,
    )}, adjusted=${adjustedVideoDuration.toFixed(2)}s, final=${currentSrt.finalDuration.toFixed(
      2,
    )}s, newTime=${currentSrt.adjustedStartTime.toFixed(2)}-${currentSrt.adjustedEndTime.toFixed(2)}s`;
    console.log(logs);
    event?.sender?.send('video-task', {
      data: logs,
      code: 0,
    });
  }

  const batches = [];
  console.log(`🔧 Creating batches from ${srtArray.length} items with BATCH_SIZE ${BATCH_SIZE}`);

  for (let i = 0; i < srtArray.length; i += BATCH_SIZE) {
    const batch = srtArray.slice(i, i + BATCH_SIZE);
    batches.push(batch);
    console.log(`   - Batch ${batches.length}: ${batch.length} items (indices ${i} to ${i + batch.length - 1})`);
  }

  console.log(`🔧 buildBatchAndSrt returning ${batches.length} batches`);
  return batches;
}



const processVideoSimplified = async (event, videoPath, srtArray, outputDir, finalOutput, options = {}) => {
  if (!fs.existsSync(outputDir)) fs.mkdirSync(outputDir, { recursive: true });
  const type = 'video-task';
const totalVideoDuration = (await getVideoInfo(event,videoPath)).duration;
// 1. Calculate audio duration for each segment
for (const srt of srtArray) {
  srt.duration = await getAudioDuration(srt.audioUrl.replace('file://', ''));
  event?.sender?.send(type, {
    data: `🕐 Audio duration for segment ${srt.index}: ${srt.duration} seconds`,
    code: 0,
  });
}

// 2. Debug srtArray and calculate video segment duration and speed ratio
console.log(`📊 Input validation:`);
console.log(`   - srtArray length: ${srtArray.length}`);
console.log(`   - BATCH_SIZE: ${BATCH_SIZE}`);
console.log(`   - First few SRT items:`, srtArray.slice(0, 3).map(srt => ({
  index: srt.index,
  startTime: srt.startTime,
  endTime: srt.endTime,
  duration: srt.duration,
  audioUrl: srt.audioUrl ? 'present' : 'missing'
})));

if (srtArray.length === 0) {
  throw new Error('❌ No SRT items provided for processing');
}

const batches = buildBatchAndSrt(event, srtArray, BATCH_SIZE);
console.log(`📦 Batches created: ${batches.length}`);
console.log(`📦 Batch sizes: ${batches.map(batch => batch.length).join(', ')}`);

if (batches.length === 0) {
  console.error('❌ No batches were created from SRT array');
  console.error('❌ This usually means the srtArray is empty or invalid');
  console.error('❌ Attempting to create a simple copy of the input video...');

  // Fallback: just copy the input video to output
  event?.sender?.send(type, {
    data: '⚠️ No audio segments to process, copying input video...',
    code: 0,
  });

  const encoder = await getEncoder();
  const copyCmd = `ffmpeg -i "${videoPath}" -c:v ${encoder} -c:a copy -y "${finalOutput}"`;

  console.log('📝 Copy command:', copyCmd);
  await execPromise(copyCmd);

  console.log(`✅ Video copied successfully: ${finalOutput}`);
  event?.sender?.send(type, {
    data: `✅ Video copied successfully: ${finalOutput}`,
    code: 0,
  });

  return {
    videoPath: finalOutput,
    adjustedSrtArray: srtArray,
  };
}

const batchVideoPaths = [];
const holdOriginalAudio = options?.audio?.holdOriginalAudio || options?.audio?.holdMusicOnly || false;
const addVoiceAudio = true;

// ====== FIX: Calculate total processed time across all batches ======
let totalProcessedTime = 0;
for (const batch of batches) {
  const lastSrt = batch[batch.length - 1];
  const batchEndTime = lastSrt.startTime + lastSrt.originalVideoSegmentDuration;
  if (batchEndTime > totalProcessedTime) {
    totalProcessedTime = batchEndTime;
  }
}

console.log(`🎬 Starting batch processing: ${batches.length} batches total`);
event?.sender?.send(type, {
  data: `🎬 Processing ${batches.length} batches...`,
  code: 0,
});

for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
  const batch = batches[batchIndex];

  console.log(`\n🎯 Processing batch ${batchIndex + 1}/${batches.length} with ${batch.length} segments`);
  event?.sender?.send(type, {
    data: `🎯 Processing batch ${batchIndex + 1}/${batches.length} (${batch.length} segments)`,
    code: 0,
  });

  const inputs = [`-i "${videoPath}"`];
  let segmentFilters = [];
  let currentTime = 0;
  let videoSegments = [];
  let originalAudioSegments = [];
  let voiceAudioSegments = [];

  // Add audio files as inputs
  if (addVoiceAudio) {
    batch.forEach((srt) => {
      const audioPath = srt.audioUrl.replace('file://', '');
      inputs.push(`-i "${audioPath}"`);
    });
  }

  batch.forEach((srt, index) => {
    const vStart = srt.startTime;
    const vDur = srt.originalVideoSegmentDuration;
    const speedRatio = srt.speedRatio || 1;
    const finalDur = srt.finalDuration;

    // === VIDEO PROCESSING ===
    let videoFilter = `[0:v]trim=start=${vStart.toFixed(6)}:duration=${vDur.toFixed(6)},setpts=PTS-STARTPTS`;
    if (speedRatio < 1) {
      videoFilter += `,setpts=PTS/${speedRatio.toFixed(6)}`;
    }
    if (options.output?.quality) {
      const resolution = getResolution(options.output.quality);
      options.output.resolution = resolution;
      videoFilter += `,scale=${resolution.width || 1920}:${resolution.height || 1080}`;
    }
    videoFilter += `[v${index}]`;
    segmentFilters.push(videoFilter);
    videoSegments.push(`[v${index}]`);

    // === ORIGINAL AUDIO PROCESSING ===
    if (holdOriginalAudio) {
      let originalAudioFilter = `[0:a]atrim=start=${vStart.toFixed(6)}:duration=${vDur.toFixed(6)},asetpts=PTS-STARTPTS`;

      // Improved tempo adjustment with smoother transitions
      if (speedRatio < 1) {
        let currentRatio = speedRatio;
        let tempoFilters = [];

        // Break down large tempo changes into smaller steps for better quality
        while (currentRatio < 0.5) {
          tempoFilters.push('atempo=0.5');
          currentRatio /= 0.5;
        }
        while (currentRatio > 2.0) {
          tempoFilters.push('atempo=2.0');
          currentRatio /= 2.0;
        }
        if (Math.abs(currentRatio - 1.0) > 0.001) {
          tempoFilters.push(`atempo=${currentRatio.toFixed(6)}`);
        }

        if (tempoFilters.length > 0) {
          originalAudioFilter += `,${tempoFilters.join(',')}`;
        }
      }

      // Add high-quality resampling and noise reduction
      originalAudioFilter += `,aresample=44100:resampler=soxr:precision=28:cheby=1`;
      originalAudioFilter += `,highpass=f=80,lowpass=f=8000`; // Remove extreme frequencies
      originalAudioFilter += `,adelay=${currentTime * 1000}|${currentTime * 1000}[oa${index}]`;

      segmentFilters.push(originalAudioFilter);
      originalAudioSegments.push(`[oa${index}]`);
    }

    // === IMPROVED VOICE AUDIO PROCESSING ===
    if (addVoiceAudio) {
      const voiceAudioIndex = index + 1; // +1 because input[0] is video
      const volume = srt.mixVolume ?? 1;
      const boostDb = options.audioBoost || 3; // Reduced from 6 to 3 for less distortion
      const volumeMultiplier = Math.pow(10, boostDb / 20) * volume;

      // Enhanced voice processing chain
      let voiceAudioFilter = `[${voiceAudioIndex}:a]`;

      // 1. High-quality resampling first
      voiceAudioFilter += `aresample=44100:resampler=soxr:precision=28:cheby=1,`;

      // 2. Noise reduction and cleanup
      voiceAudioFilter += `highpass=f=100,lowpass=f=7000,`; // Voice frequency range

      // 3. Gentle compression before normalization
      voiceAudioFilter += `acompressor=threshold=-18dB:ratio=3:attack=5:release=50:makeup=2dB,`;

      // 4. Improved dynamic normalization with gentler settings
      voiceAudioFilter += `dynaudnorm=f=500:g=15:p=0.8:maxgain=10:targetrms=0.25,`;

      // 5. Final volume adjustment
      voiceAudioFilter += `volume=${volumeMultiplier.toFixed(6)}`;

      // Duration adjustment with crossfading for smooth transitions
      const durationDiff = Math.abs(srt.duration - finalDur);
      if (srt.duration > finalDur && durationDiff > 0.001) {
        // Fade out before trimming to avoid clicks
        const fadeStart = finalDur - 0.1; // 100ms fade
        if (fadeStart > 0) {
          voiceAudioFilter += `,afade=t=out:st=${fadeStart.toFixed(6)}:d=0.1`;
        }
        voiceAudioFilter += `,atrim=duration=${finalDur.toFixed(6)}`;
      } else if (srt.duration < finalDur && durationDiff > 0.001) {
        const padDuration = finalDur - srt.duration;
        voiceAudioFilter += `,apad=pad_dur=${padDuration.toFixed(6)}`;
      }

      // Add crossfade between segments
      if (index > 0) {
        voiceAudioFilter += `,afade=t=in:st=0:d=0.05`; // 50ms fade in
      }
      if (index < batch.length - 1) {
        voiceAudioFilter += `,afade=t=out:st=${(finalDur - 0.05).toFixed(6)}:d=0.05`; // 50ms fade out
      }

      voiceAudioFilter += `,adelay=${currentTime * 1000}|${currentTime * 1000}[va${index}]`;
      segmentFilters.push(voiceAudioFilter);
      voiceAudioSegments.push(`[va${index}]`);
    }

    currentTime += finalDur;
  });

  // ====== FIXED: Check remaining video for ALL batches, not just the last one ======
  const isLastBatch = batchIndex === batches.length - 1;

  // Calculate the end time of current batch
  let currentBatchEndTime = 0;
  if (batch.length > 0) {
    const lastSrt = batch[batch.length - 1];
    currentBatchEndTime = lastSrt.startTime + lastSrt.originalVideoSegmentDuration;
  }

  // For single batch OR last batch: add remaining video if any
  let shouldAddRemaining = false;
  let remainingStart = 0;
  let remainingDuration = 0;

  if (batches.length === 1) {
    // Single batch case: add remaining video after processed segments
    if (totalProcessedTime < totalVideoDuration) {
      shouldAddRemaining = true;
      remainingStart = totalProcessedTime;
      remainingDuration = totalVideoDuration - totalProcessedTime;
    }
  } else if (isLastBatch) {
    // Multiple batches, last batch: add remaining video
    if (totalProcessedTime < totalVideoDuration) {
      shouldAddRemaining = true;
      remainingStart = totalProcessedTime;
      remainingDuration = totalVideoDuration - totalProcessedTime;
    }
  }

  if (shouldAddRemaining && remainingDuration > 0.1) { // Only add if significant duration
    console.log(`📺 Adding remaining video: ${remainingStart.toFixed(2)}s to ${totalVideoDuration.toFixed(2)}s (${remainingDuration.toFixed(2)}s)`);

    // Add remaining video segment
    const remainingIndex = batch.length;
    let remainingVideoFilter = `[0:v]trim=start=${remainingStart.toFixed(6)}:duration=${remainingDuration.toFixed(6)},setpts=PTS-STARTPTS`;

    if (options.output?.quality) {
      const resolution = getResolution(options.output.quality);
      remainingVideoFilter += `,scale=${resolution.width || 1920}:${resolution.height || 1080}`;
    }
    remainingVideoFilter += `[v${remainingIndex}]`;
    segmentFilters.push(remainingVideoFilter);
    videoSegments.push(`[v${remainingIndex}]`);

    // Add audio for remaining video if needed
    if (holdOriginalAudio) {
      let remainingAudioFilter = `[0:a]atrim=start=${remainingStart.toFixed(6)}:duration=${remainingDuration.toFixed(6)},asetpts=PTS-STARTPTS`;
      remainingAudioFilter += `,aresample=44100:resampler=soxr:precision=28:cheby=1`;
      remainingAudioFilter += `,highpass=f=80,lowpass=f=8000`;
      remainingAudioFilter += `,adelay=${currentTime * 1000}|${currentTime * 1000}[oa${remainingIndex}]`;
      segmentFilters.push(remainingAudioFilter);
      originalAudioSegments.push(`[oa${remainingIndex}]`);
    }

    if (addVoiceAudio) {
      // Generate high-quality silence for remaining duration
      let silentAudioFilter = `aevalsrc=0:duration=${remainingDuration.toFixed(6)}:sample_rate=44100:channel_layout=stereo`;
      silentAudioFilter += `,adelay=${currentTime * 1000}|${currentTime * 1000}[va${remainingIndex}]`;
      segmentFilters.push(silentAudioFilter);
      voiceAudioSegments.push(`[va${remainingIndex}]`);
    }

    currentTime += remainingDuration;
  }

  // === CONCAT VIDEO ===
  const totalSegments = videoSegments.length;
  const videoConcat = `${videoSegments.join('')}concat=n=${totalSegments}:v=1:a=0[vout]`;
  segmentFilters.push(videoConcat);

  // === IMPROVED AUDIO MIXING ===
  let audioMix = '';
  if (holdOriginalAudio && addVoiceAudio) {
    const totalAudioSegments = Math.min(originalAudioSegments.length, voiceAudioSegments.length);
    const allAudioTags = [];
    const weights = [];

    for (let i = 0; i < totalAudioSegments; i++) {
      allAudioTags.push(`[oa${i}][va${i}]`);
      weights.push('0.4 0.8'); // Lower original audio, higher voice clarity
    }

    // Enhanced mixing with sidechaining effect (ducking original audio when voice is present)
    audioMix = `${allAudioTags.join('')}amix=inputs=${totalAudioSegments * 2}:duration=longest:weights=${weights.join(' ')},`;

    // Apply gentle limiting to prevent clipping using correct alimiter syntax
    audioMix += `alimiter=limit=0.9:attack=1:release=5,`;

    // Final EQ and cleanup
    audioMix += `equalizer=f=1000:width_type=h:width=2:g=-1,`; // Slight mid cut for clarity
    audioMix += `highpass=f=80,lowpass=f=12000`; // Clean frequency range
    audioMix += `[aout]`;

  } else if (holdOriginalAudio) {
    const originalAudioTags = originalAudioSegments.join('');
    audioMix = `${originalAudioTags}amix=inputs=${originalAudioSegments.length}:duration=longest,`;
    audioMix += `aresample=44100:resampler=soxr:precision=28,`;
    audioMix += `alimiter=limit=0.95:attack=1:release=5[aout]`;

  } else if (addVoiceAudio) {
    const voiceAudioTags = voiceAudioSegments.join('');
    audioMix = `${voiceAudioTags}amix=inputs=${voiceAudioSegments.length}:duration=longest,`;
    audioMix += `alimiter=limit=0.95:attack=1:release=5,`;
    audioMix += `aresample=44100:resampler=soxr:precision=28[aout]`;
  }

  if (audioMix) segmentFilters.push(audioMix);

  // Rest of the code remains the same...
  const filterComplex = segmentFilters.join('; ');
  const batchOutput = path.join(outputDir, `batch_${batchIndex}.mp4`);
  batchVideoPaths.push(batchOutput);

  const filterFile = path.join(outputDir, `filter_${batchIndex}.txt`);
  fs.writeFileSync(filterFile, filterComplex);

  // === IMPROVED FFMPEG COMMAND ===
  let ffmpegCmd = `ffmpeg ${inputs.join(' ')} -filter_complex_script "${filterFile}" -map "[vout]"`;
  if (audioMix) {
    // Higher quality audio encoding
    ffmpegCmd += ` -map "[aout]" -c:a aac -profile:a aac_low -ar 44100 -ac 2 -b:a 192k`;
  } else {
    ffmpegCmd += ` -an`;
  }
  // Additional quality settings
  const encoder = await getEncoder();
  // ffmpegCmd += ` -c:v ${encoder} -preset medium -crf 21 -profile:v high -level 4.1 -pix_fmt yuv420p -movflags +faststart -y "${batchOutput}"`;
  ffmpegCmd += ` -c:v ${encoder} -preset medium -crf 21 -movflags +faststart -y "${batchOutput}"`;

  console.log(`🧩 Running FFmpeg for batch ${batchIndex + 1}/${batches.length}`);
  console.log(`📊 Batch ${batchIndex + 1} expected duration: ${currentTime.toFixed(2)}s`);
  console.log(`📝 FFmpeg command: ${ffmpegCmd}`);

  event?.sender?.send(type, {
    data: `🧩 Running FFmpeg for batch ${batchIndex + 1}/${batches.length} (${currentTime.toFixed(2)}s)`,
    code: 0,
  });

  try {
    console.log(`⏳ Executing FFmpeg for batch ${batchIndex + 1}...`);
    await execPromise(ffmpegCmd);

    // Verify the batch file was created successfully
    if (!fs.existsSync(batchOutput)) {
      throw new Error(`Batch file was not created: ${batchOutput}`);
    }

    const stats = fs.statSync(batchOutput);
    if (stats.size === 0) {
      throw new Error(`Batch file is empty: ${batchOutput}`);
    }

    console.log(`✅ Batch ${batchIndex + 1} completed successfully (${(stats.size / 1024 / 1024).toFixed(2)} MB)`);
    event?.sender?.send(type, {
      data: `✅ Batch ${batchIndex + 1} completed (${(stats.size / 1024 / 1024).toFixed(2)} MB)`,
      code: 0,
    });

    // Clean up filter file
    if (fs.existsSync(filterFile)) {
      fs.unlinkSync(filterFile);
    }
  } catch (error) {
    console.error(`❌ Batch ${batchIndex + 1} processing failed:`, error.message);
    console.error(`❌ FFmpeg command was: ${ffmpegCmd}`);

    // Clean up on error
    if (fs.existsSync(filterFile)) {
      fs.unlinkSync(filterFile);
    }
    if (fs.existsSync(batchOutput)) {
      fs.unlinkSync(batchOutput);
    }

    event?.sender?.send(type, {
      data: `❌ Batch ${batchIndex + 1} failed: ${error.message}`,
      code: 1,
    });

    throw error;
  }
}

  // 4. Validate all batch files exist before concatenation
  console.log(`📋 Validating ${batchVideoPaths.length} batch files...`);
  const missingBatches = [];
  const validBatches = [];

  for (let i = 0; i < batchVideoPaths.length; i++) {
    const batchPath = batchVideoPaths[i];
    if (!fs.existsSync(batchPath)) {
      missingBatches.push(`Batch ${i + 1}: ${batchPath}`);
    } else {
      const stats = fs.statSync(batchPath);
      if (stats.size === 0) {
        missingBatches.push(`Batch ${i + 1}: ${batchPath} (empty file)`);
      } else {
        validBatches.push(batchPath);
        console.log(`✅ Batch ${i + 1} validated: ${(stats.size / 1024 / 1024).toFixed(2)} MB`);
      }
    }
  }

  if (missingBatches.length > 0) {
    const errorMsg = `❌ Missing or invalid batch files:\n${missingBatches.join('\n')}`;
    console.error(errorMsg);
    event?.sender?.send(type, {
      data: errorMsg,
      code: 1,
    });
    throw new Error(`Batch processing failed: ${missingBatches.length} batch(es) missing or invalid`);
  }

  console.log(`✅ All ${validBatches.length} batch files validated successfully`);
  event?.sender?.send(type, {
    data: `✅ All ${validBatches.length} batch files validated`,
    code: 0,
  });

  // 5. Concatenate batches
  const concatListPath = path.join(outputDir, 'concat_list.txt');
  const concatListContent = validBatches.map((p) => `file '${p}'`).join('\n');
  fs.writeFileSync(concatListPath, concatListContent);

  console.log(`📝 Concat list created with ${validBatches.length} files:`);
  console.log(concatListContent);

  const concatOutput = path.join(outputDir, 'concatenated.mp4');
  const encoder = await getEncoder();
  const concatCmd =
    `ffmpeg -f concat -safe 0 -i "${concatListPath}" ` +
    `-c:v ${encoder} -preset fast -crf 23 -c:a aac -b:a 192k -y "${concatOutput}"`;

  console.log('📦 Running final concat...');
  console.log(`📝 Concat command: ${concatCmd}`);
  event?.sender?.send(type, {
    data: '📦 Running final concat...',
    code: 0,
  });

  try {
    await execPromise(concatCmd);

    // Verify concatenated file was created
    if (!fs.existsSync(concatOutput)) {
      throw new Error(`Concatenated file was not created: ${concatOutput}`);
    }

    const concatStats = fs.statSync(concatOutput);
    if (concatStats.size === 0) {
      throw new Error(`Concatenated file is empty: ${concatOutput}`);
    }

    console.log(`✅ Concatenation completed successfully (${(concatStats.size / 1024 / 1024).toFixed(2)} MB)`);
    event?.sender?.send(type, {
      data: `✅ Concatenation completed (${(concatStats.size / 1024 / 1024).toFixed(2)} MB)`,
      code: 0,
    });
  } catch (error) {
    console.error(`❌ Concatenation failed:`, error.message);
    console.error(`❌ Concat command was: ${concatCmd}`);
    event?.sender?.send(type, {
      data: `❌ Concatenation failed: ${error.message}`,
      code: 1,
    });
    throw error;
  }

  // backgroundMusic
  const backgroundMusic = options.audio?.backgroundMusic;
  if (backgroundMusic?.enabled && backgroundMusic?.file) {
    const musicPath = options.audio?.backgroundMusic?.file;
    const musicVolume = options.audio?.backgroundMusic?.volume;
    const outputWithMusic = path.join(outputDir, 'final_with_music.mp4');
    await addAudioToVideo(event, concatOutput, musicPath, outputWithMusic, {
      audioBitrate: '192k',
      volume: musicVolume,
    });
    fs.renameSync(outputWithMusic, concatOutput);
  } else {
    // up volume 1.5x
    const outputWithMusic = path.join(outputDir, 'final_with_music.mp4');
    await upVolume(event, concatOutput, outputWithMusic, 10);
    fs.renameSync(outputWithMusic, concatOutput);
  }


  // 5. Apply subtitles if needed
  const adjustedSrtPath = 'subtitles.ass'
  if (options.textSubtitle?.enabled) {
    const textSubtitle = options.textSubtitle;
    const subtitleOptions = {
      fontSize: textSubtitle.fontSize || 48,
      // textColor: cssToASSColor(textSubtitle.color || '#000000'),
      textColor: textSubtitle.assColors?.text || '&H000000',
      backgroundColor: textSubtitle.assColors?.background || '&H000000',
      outlineColor: textSubtitle.assColors?.border || '&H000000',
      // backgroundColor: cssToASSColor(textSubtitle.backgroundColor || '#fff700', '00'),
      borderStyle: 4,
      bold: textSubtitle.bold || true,
      addPadding: true,
      alignment: 2,
      marginVertical: 50,
      resolution: options.output.resolution,
      assOptions: textSubtitle.assOptions
    };
    console.log('subtitleOptions', subtitleOptions);
    console.log('textSubtitle', textSubtitle);
    const assContent = generateASSSubtitle(srtArray, subtitleOptions);
    fs.writeFileSync(adjustedSrtPath, assContent, 'utf8');
    const encoder = await getEncoder();
    const command = `ffmpeg -i "${concatOutput}" -vf "ass=${adjustedSrtPath}" -c:a copy -c:v ${encoder} -preset fast -crf 23 -y "${finalOutput}"`;
    await execPromise(command);
    console.log(`✅ Subtitle applied: ${finalOutput}`);
    event?.sender?.send(type, {
      data: `✅ Subtitle applied: ${finalOutput}`,
      code: 0,
    });
  } else {
    fs.renameSync(concatOutput, finalOutput);
  }

  // 6. Cleanup
  batchVideoPaths.forEach((p) => {
    if (fs.existsSync(p)) fs.unlinkSync(p);
  });
  if (fs.existsSync(concatListPath)) fs.unlinkSync(concatListPath);
  if (fs.existsSync(concatOutput)) fs.unlinkSync(concatOutput);
  // if (fs.existsSync(adjustedSrtPath)) fs.unlinkSync(adjustedSrtPath);

  console.log(`✅ Final video generated: ${finalOutput}`);
  event?.sender?.send(type, {
    data: `✅ Final video generated: ${finalOutput}`,
    code: 0,
  });

  return {
    videoPath: finalOutput,
    adjustedSrtArray: srtArray,
  };
};

// Helper promisified exec
// const execPromise = (cmd) =>
//   new Promise((resolve, reject) => {
//     exec(cmd, (err, stdout, stderr) => {
//       if (err) {
//         console.error('❌ FFmpeg error:', err);
//         console.error(stderr);
//         return reject(err);
//       }
//       resolve();
//     });
//   });

module.exports = {
  processVideoSimplified,
};
