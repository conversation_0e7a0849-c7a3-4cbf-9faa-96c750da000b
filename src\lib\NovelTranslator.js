import { Chat<PERSON><PERSON>AI } from '@langchain/openai';
import { ChatGoogleGenerativeAI } from '@langchain/google-genai';
import { HumanMessage, SystemMessage } from '@langchain/core/messages';

const MAIN_INSTRUCTION = `
# Main task
You are given a chunk of text in Chinese that needs to be translated to Vietnamese.

# Input data format
You will be given a JSON with the following structure:
{
    "memory": str,
    "main_text": {
        "1": str,
        "2": str,
        ...
    }
}

## memory

This field contains your memory of previous translations as markdown-formatted text. This block is crucial for you to generate high quality translations to Vietnamese. It tracks character information, terminology, and contextual details needed for consistent translation.

### How to use the memory:

1. **Read the memory carefully** before starting translation to understand:
   - Character names and their established Vietnamese translations
   - Relationships between characters (affects formal/informal address)
   - Established translations for recurring terms
   - Overall narrative style and tone

2. **Apply memory information** during translation:
   - Use consistent character name translations
   - Maintain consistent terminology
   - Apply appropriate formality levels based on context and relationships
   - Use established Vietnamese names and forms of address

3. **Update the memory** after translation:
   - Add new characters with their Vietnamese names
   - Note new relationships and appropriate address forms
   - Add new recurring terms and their translations
   - NEVER remove existing information - only add or clarify
   - CRITICAL: NEVER use placeholders like "(không thay đổi)" or "unchanged" in any section
   - ALWAYS include the FULL content of EVERY section, even if nothing has changed
   - Remember: The memory does not accumulate between iterations - what you put in the memory field is ALL that will be available in the next iteration

If the memory is empty, fill it with all the necessary details for yourself in the next translation iterations.

### Memory format (markdown):

The memory should be structured in markdown with clear sections. **Always list the original name first, followed by the Vietnamese translation in parentheses.**

Example memory structure:
# Characters
## Main Characters
- **哈利·波特** (Harry Potter) - nam, nhân vật chính
  - Gọi Dumbledore một cách trang trọng (thầy/giáo sư)
  - Bạn bè với Ron và Hermione (gọi tên)

- **赫敏·格兰杰** (Hermione Granger) - nữ, bạn của Harry
  - Dùng cách xưng hô trang trọng với giáo viên (thầy/cô)
  - Thân mật với bạn bè (gọi tên)

## Secondary Characters
- **麦格教授** (Giáo sư McGonagall) - nữ, giáo viên nghiêm khắc
  - Học sinh gọi bà một cách trang trọng (thầy/cô giáo)

# Narrator
- Người kể chuyện ngôi thứ nhất, nữ
- Phong cách kể chuyện thân mật
- Kể ở thì quá khứ

# Terminology
## Common Terms
- **魔法棒** → đũa phép
- **咒语** → phép thuật
- **施法** → thi triển phép thuật
- **黑魔法** → Nghệ thuật Hắc ám

## Places
- **霍格沃茨** → Hogwarts
- **大礼堂** → Đại sảnh
- **魔法部** → Bộ Pháp thuật

# Relationships and Forms of Address
- Học sinh → Giáo viên: trang trọng (thầy/cô), danh hiệu (giáo sư, thầy/cô)
- Bạn bè → Bạn bè: thân mật, gọi tên
- Người lớn → Trẻ em: thân mật hoặc trang trọng tùy mối quan hệ

# Style Notes
- Đối thoại: kết hợp trang trọng và thân mật dựa trên mối quan hệ
- Tường thuật: tiếng Việt văn chương, thì quá khứ
- Thời gian: tiếng Việt hiện đại (không dùng từ cổ)

**Important**: Always record both the original Chinese term and its Vietnamese translation. This helps maintain consistency - when you encounter the Chinese term in future chunks, you'll know exactly which Vietnamese translation to use.

**WARNING**: NEVER use placeholders like "(không thay đổi)" or "no changes" in ANY section of the memory. Every section must contain FULL information, even if nothing new was added. Using placeholders or "without changes" notations will cause critical information loss because only the memory field propagates to future iterations.

The above is only an example of how memory can be formatted. You are free to use the format that most suits your needs and the specifics of the text you are translating at the moment.

## main_text

This part of the input JSON contains chunks of the text that must be translated.

# Output format

You have to respond with a JSON with the following structure:
{
  "memory": str,
  "translated_text": {
    "1": str,
    "2": str,
    ...
  }
}

The memory field must contain the updated memory with information for further translation in the upcoming iterations.
Remember, you won't see your translation from the current iteration. The only thing that will propagate to the next iteration is the "memory".
Make sure it contains all the necessary information to ensure authentic, consistent and high-quality translation to Vietnamese.

The translated_text field mirrors the "main_text" field in the input JSON with corresponding chunk keys. They must contain corresponding translations to Vietnamese.

# Translation Guidelines

- Translate from Chinese to Vietnamese naturally and fluently
- Maintain the original tone and style of the Chinese text
- Use appropriate Vietnamese grammar and sentence structure
- For Chinese proper names, use established Vietnamese transliterations or keep the original if no standard exists
- Use modern Vietnamese (avoid archaic terms unless the Chinese source text is classical)
- Maintain consistency with previous translations using the memory
- Pay attention to Chinese cultural context and adapt appropriately for Vietnamese readers
`;

export class NovelTranslator {
    constructor({
        provider = 'openai',
        model = 'gpt-4o-mini',
        apiKey,
        baseURL,
        temperature = 0.1,
        windowSize = 30,
        overlap = 10,
        abortSignal = null
    } = {}) {
        this.memory = "";
        this.provider = provider;
        this.model = model;
        this.windowSize = windowSize;
        this.overlap = overlap;
        this.isGemini = false;
        this.abortSignal = abortSignal;

        // Memory management settings
        this.maxMemoryTokens = this.getMaxMemoryTokens(provider, model);
        this.memoryCompressionThreshold = Math.floor(this.maxMemoryTokens * 0.8); // 80% threshold
        
        // Validate parameters
        if (windowSize >= 80) {
            throw new Error('Window size must be less than 80 due to API limitations');
        }
        if (overlap >= windowSize) {
            throw new Error('Overlap cannot be more than or equal to window size');
        }

        // Initialize chat model based on provider
        this.initializeChatModel(provider, model, apiKey, baseURL, temperature);

        // Create system prompt based on provider
        if (this.isGemini) {
            this.systemPrompt = MAIN_INSTRUCTION + "\n\nIMPORTANT: You must respond with valid JSON only. Do not include any text outside the JSON structure. Do not use markdown code blocks.";
        } else {
            this.systemPrompt = MAIN_INSTRUCTION + "\n\nIMPORTANT: You must respond with valid JSON only. Do not include any text outside the JSON structure.";
        }
    }

    initializeChatModel(provider, model, apiKey, baseURL, temperature) {
        const commonConfig = {
            maxRetries: 3,
            temperature: temperature
        };

        switch (provider) {
            case 'openai':
                this.chatModel = new ChatOpenAI({
                    modelName: model || 'gpt-4o-mini',
                    openAIApiKey: apiKey,
                    ...commonConfig,
                    modelKwargs: {
                        response_format: { type: "json_object" }
                    }
                });
                break;

            case 'deepseek':
                this.chatModel = new ChatOpenAI({
                    modelName: model || 'deepseek-chat',
                    openAIApiKey: apiKey,
                    ...commonConfig,
                    configuration: {
                        baseURL: baseURL || 'https://api.deepseek.com/v1'
                    },
                    modelKwargs: {
                        response_format: { type: "json_object" }
                    }
                });
                break;

            case 'openrouter':
                this.chatModel = new ChatOpenAI({
                    modelName: model || 'deepseek/deepseek-r1:free',
                    openAIApiKey: apiKey,
                    ...commonConfig,
                    configuration: {
                        baseURL: baseURL || 'https://openrouter.ai/api/v1'
                    },
                    modelKwargs: {
                        response_format: { type: "json_object" }
                    }
                });
                break;

            case 'gemini':
                try {
                    this.chatModel = new ChatGoogleGenerativeAI({
                        model: model || 'gemini-1.5-flash',
                        apiKey: apiKey,
                        ...commonConfig
                    });
                    // Gemini doesn't support JSON mode, so we need different handling
                    this.isGemini = true;
                    console.log(`Initialized Gemini model: ${model} with API key: ${apiKey ? 'provided' : 'missing'}`);
                } catch (error) {
                    console.error(`Failed to initialize Gemini model ${model}:`, error);
                    // Try fallback models
                    const fallbackModels = ['gemini-1.5-pro', 'gemini-2.0-flash-exp'];
                    for (const fallbackModel of fallbackModels) {
                        try {
                            console.log(`Trying fallback Gemini model: ${fallbackModel}`);
                            this.chatModel = new ChatGoogleGenerativeAI({
                                model: fallbackModel,
                                apiKey: apiKey,
                                ...commonConfig
                            });
                            this.isGemini = true;
                            console.log(`Successfully initialized fallback Gemini model: ${fallbackModel}`);
                            break;
                        } catch (fallbackError) {
                            console.error(`Fallback model ${fallbackModel} also failed:`, fallbackError);
                        }
                    }
                    if (!this.chatModel) {
                        throw new Error('All Gemini models failed to initialize');
                    }
                }
                break;

            default:
                throw new Error(`Unsupported provider: ${provider}`);
        }
    }

    buildPrompt(chunks) {
        const chunksDict = {};
        chunks.forEach((chunk, index) => {
            chunksDict[String(index + 1)] = chunk;
        });

        const promptDict = {
            memory: this.memory,
            main_text: chunksDict
        };

        return JSON.stringify(promptDict, null, 2);
    }

    async generateResponse(prompt) {
        const maxRetries = 3;
        let lastError = null;

        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                // Check if aborted before making API call
                if (this.abortSignal?.aborted) {
                    throw new Error('Translation aborted by user');
                }

                // console.log(`Attempt ${attempt}/${maxRetries} - Generating response...`);

                // Create messages
                const messages = [
                    new SystemMessage(this.systemPrompt),
                    new HumanMessage(prompt)
                ];

                // Use chat model
                const response = await this.chatModel.invoke(messages);

                console.log(`Response received, length: ${response.content.length} characters`);

                // Check if this is OpenRouter with reasoning field (DeepSeek R1)
                let cleanContent;
                if (response.reasoning && response.content.length < 10) {
                    console.log('Using reasoning field from OpenRouter DeepSeek R1');
                    cleanContent = response.reasoning.trim();
                } else {
                    cleanContent = response.content.trim();
                }

                // For Gemini, more aggressive cleaning might be needed
                if (this.isGemini) {
                    // Remove any markdown code blocks
                    cleanContent = cleanContent.replace(/```json\s*/g, '').replace(/```\s*/g, '');

                    // Remove any leading/trailing text that's not JSON
                    const jsonStart = cleanContent.indexOf('{');
                    const jsonEnd = cleanContent.lastIndexOf('}');

                    if (jsonStart !== -1 && jsonEnd !== -1 && jsonStart < jsonEnd) {
                        cleanContent = cleanContent.substring(jsonStart, jsonEnd + 1);
                    }
                } else {
                    // For OpenAI-compatible APIs
                    if (cleanContent.startsWith('```json')) {
                        cleanContent = cleanContent.replace(/^```json\s*/, '').replace(/\s*```$/, '');
                    }
                    if (cleanContent.startsWith('```')) {
                        cleanContent = cleanContent.replace(/^```\s*/, '').replace(/\s*```$/, '');
                    }
                }

                // Fix duplicate opening braces and control characters (common OpenRouter issue)
                cleanContent = cleanContent.replace(/^{\s*{/, '{');

                // Remove control characters that break JSON parsing
                cleanContent = cleanContent.replace(/[\x00-\x1F\x7F]/g, '');

                // Try to find JSON object in the response
                const jsonStart = cleanContent.indexOf('{');
                const jsonEnd = cleanContent.lastIndexOf('}');

                if (jsonStart === -1 || jsonEnd === -1 || jsonStart >= jsonEnd) {
                    console.error('Raw response content:', response.content);
                    console.error('Cleaned content:', cleanContent);
                    throw new Error('No valid JSON object found in response');
                }

                const jsonString = cleanContent.substring(jsonStart, jsonEnd + 1);

                // Parse JSON response
                let parsedResponse;
                try {
                    parsedResponse = JSON.parse(jsonString);
                } catch (parseError) {
                    console.error('JSON parse error:', parseError.message);
                    console.error('Attempting to parse:', jsonString.substring(0, 1000) + '...');

                    // Try to fix common JSON issues
                    try {
                        const fixedJsonString = this.fixJsonString(jsonString);
                        parsedResponse = JSON.parse(fixedJsonString);
                        console.log('Successfully parsed after JSON fix');
                    } catch (fixError) {
                        console.error('JSON fix also failed:', fixError.message);
                        throw parseError;
                    }
                }

                // Validate response structure
                if (!parsedResponse.memory || !parsedResponse.translated_text) {
                    throw new Error('Response missing required fields: memory or translated_text');
                }

                if (typeof parsedResponse.memory !== 'string') {
                    throw new Error('Memory field must be a string');
                }

                if (typeof parsedResponse.translated_text !== 'object') {
                    throw new Error('Translated_text field must be an object');
                }

                console.log(`Response parsed successfully, memory length: ${parsedResponse.memory.length}`);
                return parsedResponse;

            } catch (error) {
                console.error(`Attempt ${attempt} failed:`, error.message);
                lastError = error;

                // Special handling for Gemini model errors
                if (this.isGemini && error.message.includes('is not found for API version')) {
                    console.error('Gemini model not found, this might be a model availability issue');
                    throw new Error(`Gemini model not available: ${error.message}`);
                }

                if (attempt < maxRetries) {
                    const delay = attempt * 2000; // Increasing delay
                    console.log(`Retrying in ${delay/1000} seconds...`);
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }
        }

        console.error('All attempts failed. Last error:', lastError);
        throw lastError;
    }

    fixJsonString(jsonString) {
        // Fix common JSON issues with memory strings
        let fixed = jsonString;

        // Fix duplicate opening braces
        fixed = fixed.replace(/^{\s*{/, '{');

        // Fix duplicate closing braces
        fixed = fixed.replace(/}\s*}$/, '}');

        // Remove control characters that break JSON parsing
        fixed = fixed.replace(/[\x00-\x1F\x7F]/g, '');

        // Fix unescaped newlines in strings
        fixed = fixed.replace(/"memory":\s*"([^"]*(?:\\.[^"]*)*)"/, (match, memoryContent) => {
            // Properly escape newlines, quotes, and backslashes in memory content
            const escapedContent = memoryContent
                .replace(/\\/g, '\\\\')  // Escape backslashes first
                .replace(/"/g, '\\"')    // Escape quotes
                .replace(/\n/g, '\\n')   // Escape newlines
                .replace(/\r/g, '\\r')   // Escape carriage returns
                .replace(/\t/g, '\\t');  // Escape tabs

            return `"memory": "${escapedContent}"`;
        });

        // Fix any remaining unescaped quotes in translated_text values
        fixed = fixed.replace(/"(\d+)":\s*"([^"]*(?:\\.[^"]*)*)"(?=\s*[,}])/g, (match, key, value) => {
            const escapedValue = value
                .replace(/\\/g, '\\\\')
                .replace(/"/g, '\\"')
                .replace(/\n/g, '\\n')
                .replace(/\r/g, '\\r')
                .replace(/\t/g, '\\t');

            return `"${key}": "${escapedValue}"`;
        });

        return fixed;
    }

    generateSlidingWindows(chunks) {
        const windows = [];
        let i = 0;

        while (i < chunks.length) {
            const window = chunks.slice(i, i + this.windowSize);
            windows.push({ window, startIdx: i });

            i += this.windowSize - this.overlap;
        }

        return windows;
    }

    extractRelevantTranslations(windowTranslated, startIdx) {
        if (startIdx === 0) {
            return windowTranslated;
        }

        return windowTranslated.slice(this.overlap);
    }

    getMaxMemoryTokens(provider, model) {
        // Conservative token limits for memory to avoid context overflow
        const limits = {
            'openai': {
                'gpt-4o-mini': 8000,
                'gpt-4o': 12000,
                'gpt-4': 6000,
                'gpt-3.5-turbo': 3000
            },
            'deepseek': {
                'deepseek-chat': 6000,
                'deepseek-reasoner': 6000
            },
            'gemini': {
                'gemini-1.5-flash': 8000,
                'gemini-1.5-pro': 12000,
                'gemini-2.0-flash-exp': 8000,
                'gemini-pro': 6000
            },
            'openrouter': {
                'deepseek/deepseek-r1:free': 6000,
                'claude-3-haiku-20240307': 8000
            }
        };

        return limits[provider]?.[model] || 4000; // Conservative default
    }

    estimateTokenCount(text) {
        // Rough estimation: 1 token ≈ 4 characters for most languages
        // For Chinese/Vietnamese mix, use more conservative estimate
        return Math.ceil(text.length / 3);
    }

    compressMemory(memory) {
        console.log(`Compressing memory from ${memory.length} characters...`);

        // Split memory into sections
        const sections = this.parseMemorysections(memory);

        // Compress each section
        const compressedSections = {
            characters: this.compressCharacterSection(sections.characters),
            terminology: this.compressTerminologySection(sections.terminology),
            relationships: this.compressRelationshipSection(sections.relationships),
            style: this.compressStyleSection(sections.style)
        };

        // Rebuild memory
        const compressedMemory = this.buildCompressedMemory(compressedSections);

        console.log(`Memory compressed to ${compressedMemory.length} characters`);
        return compressedMemory;
    }

    parseMemorysections(memory) {
        const sections = {
            characters: '',
            terminology: '',
            relationships: '',
            style: ''
        };

        // Extract sections using regex
        const characterMatch = memory.match(/# Characters([\s\S]*?)(?=# |$)/);
        const terminologyMatch = memory.match(/# Terminology([\s\S]*?)(?=# |$)/);
        const relationshipMatch = memory.match(/# Relationships([\s\S]*?)(?=# |$)/);
        const styleMatch = memory.match(/# Style([\s\S]*?)(?=# |$)/);

        if (characterMatch) sections.characters = characterMatch[1].trim();
        if (terminologyMatch) sections.terminology = terminologyMatch[1].trim();
        if (relationshipMatch) sections.relationships = relationshipMatch[1].trim();
        if (styleMatch) sections.style = styleMatch[1].trim();

        return sections;
    }

    compressCharacterSection(characterText) {
        if (!characterText) return '';

        // Keep only main characters, compress descriptions
        const lines = characterText.split('\n');
        const compressed = [];

        for (const line of lines) {
            if (line.includes('**') && line.includes('**')) {
                // Character name line - keep essential info only
                const match = line.match(/\*\*(.*?)\*\* \((.*?)\) - (.*)/);
                if (match) {
                    compressed.push(`- **${match[1]}** (${match[2]}) - ${match[3].split(',')[0]}`);
                }
            } else if (line.trim().startsWith('- ') && line.length < 100) {
                // Keep short important details
                compressed.push(line);
            }
        }

        return compressed.slice(0, 20).join('\n'); // Limit to 20 most important entries
    }

    compressTerminologySection(terminologyText) {
        if (!terminologyText) return '';

        // Keep only most frequently used terms
        const lines = terminologyText.split('\n');
        const compressed = [];

        for (const line of lines) {
            if (line.includes('→') && line.length < 80) {
                compressed.push(line);
            }
        }

        return compressed.slice(0, 30).join('\n'); // Limit to 30 most important terms
    }

    compressRelationshipSection(relationshipText) {
        if (!relationshipText) return '';

        // Keep only key relationships
        const lines = relationshipText.split('\n');
        return lines.filter(line =>
            line.includes('→') && line.length < 100
        ).slice(0, 15).join('\n'); // Limit to 15 key relationships
    }

    compressStyleSection(styleText) {
        if (!styleText) return '';

        // Keep only essential style notes
        const lines = styleText.split('\n');
        return lines.filter(line =>
            line.trim().startsWith('- ') && line.length < 100
        ).slice(0, 10).join('\n'); // Limit to 10 style notes
    }

    buildCompressedMemory(sections) {
        let memory = '';

        if (sections.characters) {
            memory += '# Characters\n' + sections.characters + '\n\n';
        }

        if (sections.terminology) {
            memory += '# Terminology\n' + sections.terminology + '\n\n';
        }

        if (sections.relationships) {
            memory += '# Relationships\n' + sections.relationships + '\n\n';
        }

        if (sections.style) {
            memory += '# Style Notes\n' + sections.style + '\n\n';
        }

        return memory.trim();
    }

    checkAndCompressMemory() {
        const currentTokens = this.estimateTokenCount(this.memory);

        if (currentTokens > this.memoryCompressionThreshold) {
            console.log(`Memory size (${currentTokens} tokens) exceeds threshold (${this.memoryCompressionThreshold}). Compressing...`);
            this.memory = this.compressMemory(this.memory);

            const newTokens = this.estimateTokenCount(this.memory);
            console.log(`Memory compressed from ${currentTokens} to ${newTokens} tokens`);
        }
    }

    async translateChunks(chunks, onProgress = null) {
        const translatedChunks = [];
        const windows = this.generateSlidingWindows(chunks);

        console.log(`Processing ${windows.length} windows for ${chunks.length} chunks`);

        for (let i = 0; i < windows.length; i++) {
            const { window, startIdx } = windows[i];

            console.log(`Translating window ${i + 1}/${windows.length} (${window.length} chunks)`);

            const prompt = this.buildPrompt(window);

            try {
                const modelResponse = await this.generateResponse(prompt);
                const { translated_text: translation, memory } = modelResponse;

                // Update memory and check if compression is needed
                this.memory = memory;
                this.checkAndCompressMemory();

                // Convert numbered dictionary to ordered list
                const windowTranslated = [];
                for (let j = 1; j <= window.length; j++) {
                    if (translation[String(j)]) {
                        windowTranslated.push(translation[String(j)]);
                    } else {
                        console.warn(`Missing translation for chunk ${j} in window ${i + 1}`);
                        // windowTranslated.push(`[MISSING TRANSLATION ${j}]`);
                    }
                }

                // Skip overlapping chunks to avoid duplicates
                const relevantTranslations = this.extractRelevantTranslations(windowTranslated, startIdx);
                translatedChunks.push(...relevantTranslations);

                console.log(`✅ Completed window ${i + 1}/${windows.length}`);
                console.log(`Memory length: ${this.memory.length} characters`);
                console.log(`Translated chunks in this window: ${relevantTranslations.length}`);

                // Call progress callback with window results for real-time update
                if (onProgress) {
                    onProgress(i + 1, windows.length, `Completed window ${i + 1}/${windows.length}`, this.memory, relevantTranslations);
                }

            } catch (error) {
                console.error(`❌ Error processing window ${i + 1}:`, error);

                // Retry with smaller window size or fallback strategy
                let retrySuccess = false;

                if (window.length > 5) {
                    try {
                        console.log(`🔄 Retrying window ${i + 1} with smaller chunks...`);

                        // Split window into smaller chunks and retry
                        const smallerChunks = [];
                        const chunkSize = Math.max(1, Math.floor(window.length / 3));

                        for (let j = 0; j < window.length; j += chunkSize) {
                            const smallChunk = window.slice(j, j + chunkSize);
                            const smallPrompt = this.buildPrompt(smallChunk);

                            try {
                                // Add timeout for retry attempts too
                                const retryTimeoutPromise = new Promise((_, reject) => {
                                    setTimeout(() => reject(new Error(`Small chunk ${j} timeout after 30 seconds`)), 30000);
                                });

                                const smallResponse = await Promise.race([
                                    this.generateResponse(smallPrompt),
                                    retryTimeoutPromise
                                ]);

                                const { translated_text: smallTranslation } = smallResponse;

                                // Convert to array
                                for (let k = 1; k <= smallChunk.length; k++) {
                                    if (smallTranslation[String(k)]) {
                                        smallerChunks.push(smallTranslation[String(k)]);
                                    } else {
                                        smallerChunks.push(smallChunk[k - 1]); // Use original text as fallback
                                    }
                                }
                            } catch (smallError) {
                                console.error(`❌ Error in small chunk ${j}:`, smallError);
                                // Use original text as fallback
                                smallerChunks.push(...smallChunk);
                            }
                        }

                        const relevantTranslations = this.extractRelevantTranslations(smallerChunks, startIdx);
                        translatedChunks.push(...relevantTranslations);
                        retrySuccess = true;

                        console.log(`✅ Retry successful for window ${i + 1} with ${relevantTranslations.length} chunks`);

                        // Call progress callback for retry success
                        if (onProgress) {
                            try {
                                onProgress(i + 1, windows.length, `✅ Retry completed window ${i + 1}/${windows.length}`, this.memory, relevantTranslations);
                            } catch (callbackError) {
                                console.error(`Error in retry progress callback for window ${i + 1}:`, callbackError);
                            }
                        }

                    } catch (retryError) {
                        console.error(`❌ Retry failed for window ${i + 1}:`, retryError);
                    }
                }

                if (!retrySuccess) {
                    // Final fallback: use original text
                    console.log(`⚠️ Using original text as fallback for window ${i + 1}`);
                    const relevantTranslations = this.extractRelevantTranslations(window, startIdx);
                    translatedChunks.push(...relevantTranslations);

                    // Call progress callback for fallback
                    if (onProgress) {
                        try {
                            onProgress(i + 1, windows.length, `⚠️ Fallback completed window ${i + 1}/${windows.length}`, this.memory, relevantTranslations);
                        } catch (callbackError) {
                            console.error(`Error in fallback progress callback for window ${i + 1}:`, callbackError);
                        }
                    }
                }
            }
        }

        return translatedChunks;
    }

    // Utility method to split text into sentences/paragraphs
    static splitTextIntoChunks(text, chunkType = 'paragraph') {
        if (chunkType === 'paragraph') {
            return text.split(/\n\s*\n/).filter(chunk => chunk.trim().length > 0);
        } else if (chunkType === 'sentence') {
            return text.split(/[.!?]+/).filter(chunk => chunk.trim().length > 0);
        } else {
            throw new Error('chunkType must be either "paragraph" or "sentence"');
        }
    }

    // Utility method to join translated chunks back into text
    static joinTranslatedChunks(translatedChunks, chunkType = 'paragraph') {
        if (chunkType === 'paragraph') {
            return translatedChunks.join('\n\n');
        } else if (chunkType === 'sentence') {
            return translatedChunks.join('. ');
        } else {
            throw new Error('chunkType must be either "paragraph" or "sentence"');
        }
    }
}
