const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Function to test the merge_audio_v2.exe executable
async function testMergeAudio(audioFiles, subsData, outputPath) {
  // Create temp directory
  const tempDir = path.join(path.dirname(outputPath), 'temp');
  if (!fs.existsSync(tempDir)) {
    fs.mkdirSync(tempDir, { recursive: true });
  }

  // Save JSON files
  const audioFilesJsonPath = path.join(tempDir, 'audio_files.json');
  const subsJsonPath = path.join(tempDir, 'subs.json');

  fs.writeFileSync(audioFilesJsonPath, JSON.stringify(audioFiles, null, 2));
  fs.writeFileSync(subsJsonPath, JSON.stringify(subsData, null, 2));

  // Get executable path
  const executablePath = path.join(__dirname, 'static', 'merge_audio_v2.exe');

  console.log('Testing merge_audio_v2.exe with:');
  console.log('- Executable path:', executablePath);
  console.log('- Audio files JSON path:', audioFilesJsonPath);
  console.log('- Subs JSON path:', subsJsonPath);
  console.log('- Output path:', outputPath);

  // Check if files exist
  console.log('\nChecking if files exist:');
  console.log(`- Executable: ${executablePath} - ${fs.existsSync(executablePath) ? 'Exists' : 'Not found'}`);
  console.log(`- Audio files JSON: ${audioFilesJsonPath} - ${fs.existsSync(audioFilesJsonPath) ? 'Exists' : 'Not found'}`);
  console.log(`- Subs JSON: ${subsJsonPath} - ${fs.existsSync(subsJsonPath) ? 'Exists' : 'Not found'}`);
  console.log(`- Output directory: ${path.dirname(outputPath)} - ${fs.existsSync(path.dirname(outputPath)) ? 'Exists' : 'Not found'}`);

  // Read the content of the JSON files for debugging
  console.log('\nAudio files JSON content:');
  console.log(JSON.stringify(audioFiles, null, 2).substring(0, 500) + '...');
  
  console.log('\nSubs JSON content:');
  console.log(JSON.stringify(subsData, null, 2).substring(0, 500) + '...');

  return new Promise((resolve, reject) => {
    // Test 1: Using spawn with shell=true
    console.log('\nTest 1: Using spawn with shell=true');
    const args = [audioFilesJsonPath, subsJsonPath, outputPath];
    console.log('Command:', `"${executablePath}" "${audioFilesJsonPath}" "${subsJsonPath}" "${outputPath}"`);
    
    const process = spawn(executablePath, args, {
      stdio: ['inherit', 'pipe', 'pipe'],
      shell: true
    });
    
    let stdout = '';
    let stderr = '';
    
    process.stdout.on('data', (data) => {
      const output = data.toString();
      stdout += output;
      console.log('Process stdout:', output);
    });
    
    process.stderr.on('data', (data) => {
      const output = data.toString();
      stderr += output;
      console.error('Process stderr:', output);
    });
    
    process.on('error', (error) => {
      console.error('Spawn error:', error);
      reject(error);
    });
    
    process.on('exit', (code) => {
      console.log(`Process exited with code ${code}`);
      
      if (code === 0) {
        console.log('Success!');
        resolve({ success: true });
      } else {
        console.error('Error!');
        console.error('stdout:', stdout);
        console.error('stderr:', stderr);
        
        // Test 2: Using exec as a fallback
        console.log('\nTest 2: Using exec as a fallback');
        const { exec } = require('child_process');
        const cmdString = `"${executablePath}" "${audioFilesJsonPath}" "${subsJsonPath}" "${outputPath}"`;
        console.log('Command:', cmdString);
        
        exec(cmdString, (error, cmdStdout, cmdStderr) => {
          if (error) {
            console.error('Exec error:', error);
            reject(error);
          } else {
            console.log('Command executed successfully via exec');
            console.log('stdout:', cmdStdout);
            if (cmdStderr) console.error('stderr:', cmdStderr);
            resolve({ success: true });
          }
        });
      }
    });
  });
}

// Example usage
const audioFiles = [
  "C:/Users/<USER>/AppData/Local/Temp/segment-1.mp3",
  "C:/Users/<USER>/AppData/Local/Temp/segment-2.mp3"
];

const subsData = [
  {
    "index": 1,
    "id": 1,
    "text": "Example subtitle 1",
    "startTime": 0,
    "endTime": 5,
    "start": "00:00:00,000",
    "end": "00:00:05,000",
    "selectedSpeaker": "speaker1",
    "speechRate": 0,
    "audioUrl": "file://C:/Users/<USER>/AppData/Local/Temp/segment-1.mp3",
    "duration": 4.5,
    "isGenerated": true
  },
  {
    "index": 2,
    "id": 2,
    "text": "Example subtitle 2",
    "startTime": 6,
    "endTime": 10,
    "start": "00:00:06,000",
    "end": "00:00:10,000",
    "selectedSpeaker": "speaker1",
    "speechRate": 0,
    "audioUrl": "file://C:/Users/<USER>/AppData/Local/Temp/segment-2.mp3",
    "duration": 3.8,
    "isGenerated": true
  }
];

const outputPath = "C:/Users/<USER>/AppData/Local/Temp/combined-audio.mp3";

// Run the test
testMergeAudio(audioFiles, subsData, outputPath)
  .then(result => {
    console.log('Test completed successfully:', result);
  })
  .catch(error => {
    console.error('Test failed:', error);
  });
