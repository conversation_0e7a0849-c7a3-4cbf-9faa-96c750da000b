// Batch processing utilities for database operations
const moment = require('moment');

class BatchProcessor {
    constructor(knex) {
        this.knex = knex;
    }

    // Determine optimal batch size based on data size and complexity
    getOptimalBatchSize(dataLength, complexity = 'medium') {
        const baseSizes = {
            simple: 100,   // Simple inserts with few columns
            medium: 50,    // Medium complexity with multiple columns
            complex: 25,   // Complex inserts with many columns/relationships
            large: 10      // Very large records or high-complexity operations
        };

        let batchSize = baseSizes[complexity] || baseSizes.medium;

        // Adjust based on data size
        if (dataLength > 1000) {
            batchSize = Math.max(10, Math.floor(batchSize / 2));
        } else if (dataLength > 500) {
            batchSize = Math.max(15, Math.floor(batchSize * 0.75));
        } else if (dataLength < 50) {
            batchSize = Math.min(100, batchSize * 2);
        }

        return batchSize;
    }

    // Process array in chunks with progress reporting
    async processInChunks(array, processor, options = {}) {
        const {
            batchSize = this.getOptimalBatchSize(array.length),
            logProgress = true,
            delayBetweenBatches = 100,
            onProgress = null,
            onError = null
        } = options;

        if (!array || array.length === 0) {
            return [];
        }

        if (logProgress) {
            console.log(`🔄 Processing ${array.length} items in batches of ${batchSize}...`);
        }

        const results = [];
        let successCount = 0;
        let errorCount = 0;
        const totalBatches = Math.ceil(array.length / batchSize);

        for (let i = 0; i < array.length; i += batchSize) {
            const chunk = array.slice(i, i + batchSize);
            const batchNumber = Math.floor(i / batchSize) + 1;

            if (logProgress) {
                console.log(`📦 Processing batch ${batchNumber}/${totalBatches} (${chunk.length} items)`);
            }

            try {
                const batchResults = await processor(chunk, batchNumber, totalBatches);
                results.push(...(Array.isArray(batchResults) ? batchResults : [batchResults]));
                successCount += chunk.length;

                if (logProgress) {
                    console.log(`✅ Batch ${batchNumber} completed successfully`);
                }

                // Call progress callback if provided
                if (onProgress) {
                    onProgress({
                        batchNumber,
                        totalBatches,
                        itemsProcessed: successCount + errorCount,
                        totalItems: array.length,
                        successCount,
                        errorCount
                    });
                }

            } catch (error) {
                errorCount += chunk.length;
                
                if (logProgress) {
                    console.warn(`⚠️ Batch ${batchNumber} failed:`, error.message);
                }

                // Call error callback if provided
                if (onError) {
                    onError(error, chunk, batchNumber);
                } else {
                    // Default error handling: log and continue
                    console.warn(`Skipping batch ${batchNumber} due to error`);
                }
            }

            // Add delay between batches if specified
            if (delayBetweenBatches > 0 && i + batchSize < array.length) {
                await new Promise(resolve => setTimeout(resolve, delayBetweenBatches));
            }
        }

        if (logProgress) {
            console.log(`📊 Batch processing completed: ${successCount} successful, ${errorCount} failed`);
        }

        return results;
    }

    // Specialized method for chapter inserts with fallback to individual inserts
    async insertChaptersWithFallback(chapters, extractionSession = null, options = {}) {
        const {
            batchSize = this.getOptimalBatchSize(chapters.length, 'medium'),
            logProgress = true
        } = options;

        const processor = async (chunk, batchNumber, totalBatches) => {
            const batchData = chunk.map(chapter => ({
                book_id: chapter.book_id || null,
                title: chapter.title,
                href: chapter.href,
                text: chapter.text || chapter.title,
                full_url: chapter.fullUrl,
                chapter_index: chapter.index,
                page_number: chapter.pageNumber || null,
                source_url: chapter.sourceUrl || null,
                book_title: chapter.bookTitle || null,
                extraction_session: extractionSession || chapter.extractionSession || null,
                created_at: moment().format('YYYY-MM-DD HH:mm:ss'),
                updated_at: moment().format('YYYY-MM-DD HH:mm:ss')
            }));

            try {
                // Try batch insert
                return await this.knex('chapters').insert(batchData);
            } catch (error) {
                if (logProgress) {
                    console.warn(`Batch insert failed for batch ${batchNumber}, trying individual inserts...`);
                }

                // Fallback to individual inserts
                const results = [];
                for (const [index, chapter] of chunk.entries()) {
                    try {
                        const [id] = await this.knex('chapters').insert(batchData[index]);
                        results.push(id);
                    } catch (individualError) {
                        if (individualError.code === 'SQLITE_CONSTRAINT_UNIQUE') {
                            // Update existing record
                            await this.knex('chapters')
                                .where('full_url', chapter.fullUrl)
                                .update({
                                    ...batchData[index],
                                    updated_at: moment().format('YYYY-MM-DD HH:mm:ss')
                                });
                            results.push('updated');
                        } else {
                            console.warn(`Failed to insert chapter: ${chapter.title}`, individualError.message);
                        }
                    }
                }
                return results;
            }
        };

        return await this.processInChunks(chapters, processor, {
            batchSize,
            logProgress,
            delayBetweenBatches: 50 // Shorter delay for database operations
        });
    }

    // Method to estimate memory usage and adjust batch size accordingly
    estimateOptimalBatchSize(sampleRecord, targetMemoryMB = 10) {
        try {
            // Rough estimation of record size in bytes
            const recordSize = JSON.stringify(sampleRecord).length * 2; // UTF-16 approximation
            const targetBytes = targetMemoryMB * 1024 * 1024;
            const estimatedBatchSize = Math.floor(targetBytes / recordSize);
            
            // Apply reasonable bounds
            return Math.max(10, Math.min(100, estimatedBatchSize));
        } catch (error) {
            // Fallback to default if estimation fails
            return 50;
        }
    }
}

module.exports = BatchProcessor;
