<template>
  <div class="grid-overlay absolute inset-0 pointer-events-none">
    <!-- Rule of thirds grid -->
    <svg class="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
      <!-- Vertical lines -->
      <line x1="33.33" y1="0" x2="33.33" y2="100" stroke="rgba(255,255,255,0.3)" stroke-width="0.2" />
      <line x1="66.66" y1="0" x2="66.66" y2="100" stroke="rgba(255,255,255,0.3)" stroke-width="0.2" />
      
      <!-- Horizontal lines -->
      <line x1="0" y1="33.33" x2="100" y2="33.33" stroke="rgba(255,255,255,0.3)" stroke-width="0.2" />
      <line x1="0" y1="66.66" x2="100" y2="66.66" stroke="rgba(255,255,255,0.3)" stroke-width="0.2" />
      
      <!-- Center lines -->
      <line x1="50" y1="0" x2="50" y2="100" stroke="rgba(255,255,255,0.2)" stroke-width="0.1" />
      <line x1="0" y1="50" x2="100" y2="50" stroke="rgba(255,255,255,0.2)" stroke-width="0.1" />
      
      <!-- Corner markers -->
      <circle cx="33.33" cy="33.33" r="0.5" fill="rgba(255,255,255,0.4)" />
      <circle cx="66.66" cy="33.33" r="0.5" fill="rgba(255,255,255,0.4)" />
      <circle cx="33.33" cy="66.66" r="0.5" fill="rgba(255,255,255,0.4)" />
      <circle cx="66.66" cy="66.66" r="0.5" fill="rgba(255,255,255,0.4)" />
    </svg>
  </div>
</template>

<script setup>
defineProps({
  videoDimensions: {
    type: Object,
    required: true
  }
})
</script>

<style scoped>
.grid-overlay {
  z-index: 5;
}
</style>
