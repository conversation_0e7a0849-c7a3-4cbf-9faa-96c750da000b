<template>
  <div class="translation-error-demo p-6">
    <h1 class="text-2xl font-bold mb-6">Demo Translation Error Handling</h1>
    
    <!-- Error Simulation Controls -->
    <a-card title="Error Simulation" class="mb-6">
      <div class="mb-4">
        <a-alert 
          message="Test các trường hợp lỗi API và cách xử lý"
          type="info" 
          show-icon 
          closable
        />
      </div>
      
      <a-row :gutter="16">
        <a-col :span="8">
          <a-form-item label="Loại lỗi">
            <a-select v-model:value="errorType" style="width: 100%">
              <a-select-option value="none">Không có lỗi</a-select-option>
              <a-select-option value="api_key">API Key không hợp lệ</a-select-option>
              <a-select-option value="rate_limit">Rate limit exceeded</a-select-option>
              <a-select-option value="network">Network error</a-select-option>
              <a-select-option value="timeout">Request timeout</a-select-option>
              <a-select-option value="invalid_response">Invalid response format</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        
        <a-col :span="8">
          <a-form-item label="Batch size">
            <a-input-number 
              v-model:value="batchSize" 
              :min="1" 
              :max="50" 
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        
        <a-col :span="8">
          <a-form-item label="Max retries">
            <a-input-number 
              v-model:value="maxRetries" 
              :min="0" 
              :max="5" 
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
      </a-row>
      
      <a-button 
        type="primary" 
        @click="simulateTranslation" 
        :loading="translating"
        :disabled="sampleTexts.length === 0"
      >
        <template #icon><PlayCircleOutlined /></template>
        Simulate Translation
      </a-button>
    </a-card>

    <!-- Sample Data -->
    <a-card title="Sample Data" class="mb-6">
      <div class="mb-4">
        <a-button @click="loadSampleData" type="default">
          Load Sample Texts
        </a-button>
        <span class="ml-2 text-gray-500">{{ sampleTexts.length }} texts loaded</span>
      </div>
      
      <div class="sample-texts max-h-40 overflow-y-auto border rounded p-3 bg-gray-50">
        <div v-for="(text, index) in sampleTexts" :key="index" class="mb-1 text-sm">
          {{ index + 1 }}. {{ text }}
        </div>
      </div>
    </a-card>

    <!-- Translation Progress -->
    <a-card title="Translation Progress" class="mb-6" v-if="translating || results.length > 0">
      <div class="mb-4">
        <a-progress 
          :percent="progress" 
          :status="translationStatus"
          :stroke-color="progressColor"
        />
        <div class="text-sm text-gray-600 mt-2">
          {{ progressText }}
        </div>
      </div>
      
      <!-- Error Display -->
      <div v-if="translationError" class="mb-4">
        <a-alert 
          :message="translationError" 
          type="error" 
          show-icon 
          closable
          @close="translationError = ''"
        />
      </div>
    </a-card>

    <!-- Results -->
    <a-card title="Translation Results" v-if="results.length > 0">
      <div class="results-list max-h-60 overflow-y-auto">
        <div 
          v-for="(result, index) in results" 
          :key="index"
          class="result-item p-3 mb-2 border rounded"
          :class="{
            'bg-green-50 border-green-200': result.status === 'success',
            'bg-red-50 border-red-200': result.status === 'error',
            'bg-yellow-50 border-yellow-200': result.status === 'retry'
          }"
        >
          <div class="flex justify-between items-start mb-2">
            <span class="font-medium text-sm">Text {{ index + 1 }}</span>
            <a-tag 
              :color="result.status === 'success' ? 'green' : result.status === 'error' ? 'red' : 'orange'"
            >
              {{ result.status }}
            </a-tag>
          </div>
          
          <div class="original text-sm text-gray-700 mb-1">
            <strong>Original:</strong> {{ result.original }}
          </div>
          
          <div v-if="result.translated" class="translated text-sm text-blue-700 mb-1">
            <strong>Translated:</strong> {{ result.translated }}
          </div>
          
          <div v-if="result.error" class="error text-sm text-red-600">
            <strong>Error:</strong> {{ result.error }}
          </div>
          
          <div v-if="result.retryCount > 0" class="retry-info text-xs text-orange-600">
            Retried {{ result.retryCount }} times
          </div>
        </div>
      </div>
      
      <!-- Summary -->
      <div class="mt-4 p-3 bg-gray-50 rounded">
        <h4 class="text-sm font-medium mb-2">Summary</h4>
        <a-row :gutter="16">
          <a-col :span="6">
            <a-statistic title="Total" :value="results.length" />
          </a-col>
          <a-col :span="6">
            <a-statistic 
              title="Success" 
              :value="successCount" 
              :value-style="{ color: '#3f8600' }"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic 
              title="Failed" 
              :value="errorCount" 
              :value-style="{ color: '#cf1322' }"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic 
              title="Success Rate" 
              :value="successRate" 
              suffix="%"
              :value-style="{ color: successRate > 80 ? '#3f8600' : '#cf1322' }"
            />
          </a-col>
        </a-row>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import { PlayCircleOutlined } from '@ant-design/icons-vue'

// Reactive data
const errorType = ref('none')
const batchSize = ref(10)
const maxRetries = ref(2)
const translating = ref(false)
const progress = ref(0)
const translationError = ref('')
const sampleTexts = ref([])
const results = ref([])

// Computed properties
const translationStatus = computed(() => {
  if (translationError.value) return 'exception'
  if (progress.value === 100) return 'success'
  return 'active'
})

const progressColor = computed(() => {
  if (translationError.value) return '#ff4d4f'
  if (progress.value === 100) return '#52c41a'
  return '#1890ff'
})

const progressText = computed(() => {
  if (translating.value) return `Processing... ${progress.value}%`
  if (translationError.value) return 'Translation failed'
  if (progress.value === 100) return 'Translation completed'
  return ''
})

const successCount = computed(() => 
  results.value.filter(r => r.status === 'success').length
)

const errorCount = computed(() => 
  results.value.filter(r => r.status === 'error').length
)

const successRate = computed(() => {
  if (results.value.length === 0) return 0
  return Math.round((successCount.value / results.value.length) * 100)
})

// Methods
const loadSampleData = () => {
  sampleTexts.value = [
    "你好，世界！",
    "这是一个测试。",
    "欢迎使用我们的应用程序。",
    "人工智能正在改变世界。",
    "机器学习是未来的趋势。",
    "深度学习算法很强大。",
    "自然语言处理很有趣。",
    "计算机视觉应用广泛。",
    "数据科学很重要。",
    "云计算提供了便利。",
    "区块链技术很创新。",
    "物联网连接万物。",
    "5G网络速度很快。",
    "虚拟现实很有趣。",
    "增强现实很实用。"
  ]
  
  results.value = []
  progress.value = 0
  translationError.value = ''
  message.success(`Loaded ${sampleTexts.value.length} sample texts`)
}

const simulateError = (batchIndex, textIndex) => {
  const errorMessages = {
    api_key: 'Invalid API key provided',
    rate_limit: 'Rate limit exceeded. Please try again later',
    network: 'Network connection failed',
    timeout: 'Request timeout after 30 seconds',
    invalid_response: 'Invalid response format from API'
  }
  
  if (errorType.value === 'none') return null
  
  // Simulate random errors for some batches
  if (Math.random() < 0.3) { // 30% chance of error
    return new Error(errorMessages[errorType.value] || 'Unknown error')
  }
  
  return null
}

const simulateTranslation = async () => {
  if (sampleTexts.value.length === 0) {
    message.warning('Please load sample data first')
    return
  }
  
  translating.value = true
  progress.value = 0
  translationError.value = ''
  results.value = []
  
  try {
    // Simulate batch processing
    const batches = []
    for (let i = 0; i < sampleTexts.value.length; i += batchSize.value) {
      batches.push(sampleTexts.value.slice(i, i + batchSize.value))
    }
    
    console.log(`Processing ${batches.length} batches with size ${batchSize.value}`)
    
    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      const batch = batches[batchIndex]
      console.log(`Processing batch ${batchIndex + 1}/${batches.length}`)
      
      let retryCount = 0
      let batchSuccess = false
      
      while (retryCount <= maxRetries.value && !batchSuccess) {
        try {
          // Simulate API delay
          await new Promise(resolve => setTimeout(resolve, 1000))
          
          // Simulate error
          const error = simulateError(batchIndex, 0)
          if (error && retryCount < maxRetries.value) {
            throw error
          }
          
          // Simulate successful translation
          batch.forEach((text, textIndex) => {
            const globalIndex = batchIndex * batchSize.value + textIndex
            results.value.push({
              original: text,
              translated: `[Translated] ${text}`,
              status: retryCount > 0 ? 'retry' : 'success',
              retryCount,
              error: null
            })
          })
          
          batchSuccess = true
          
        } catch (batchError) {
          retryCount++
          console.log(`Batch ${batchIndex + 1} failed, retry ${retryCount}/${maxRetries.value}`)
          
          if (retryCount > maxRetries.value) {
            // Final failure
            batch.forEach((text, textIndex) => {
              results.value.push({
                original: text,
                translated: null,
                status: 'error',
                retryCount: retryCount - 1,
                error: batchError.message
              })
            })
            
            // Decide whether to continue or stop
            if (errorType.value === 'api_key') {
              // Critical error - stop everything
              throw new Error(`Critical error in batch ${batchIndex + 1}: ${batchError.message}`)
            }
          } else {
            // Wait before retry
            await new Promise(resolve => setTimeout(resolve, 2000))
          }
        }
      }
      
      // Update progress
      progress.value = Math.floor(((batchIndex + 1) / batches.length) * 100)
    }
    
    progress.value = 100
    message.success(`Translation completed! Success rate: ${successRate.value}%`)
    
  } catch (error) {
    console.error('Translation process failed:', error)
    translationError.value = error.message
    message.error('Translation process failed: ' + error.message)
  } finally {
    translating.value = false
  }
}

// Auto load sample data on mount
import { onMounted } from 'vue'
onMounted(() => {
  loadSampleData()
})
</script>

<style scoped>
.result-item {
  transition: all 0.2s ease;
}

.result-item:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
</style>
