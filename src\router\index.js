import { createRouter, createWebHashHistory } from 'vue-router';

import HomePage from '@/pages/HomePage.vue';
import Dashboard from '@/pages/Dashboard.vue';
import Summary from '@/pages/Summary.vue';
import SummaryIntro from '@/pages/SummaryIntro.vue';
import TrainGPT from '@/pages/TrainGPT.vue';


const routes = [
  {
    path: '/',
    name: 'Home',
    component: HomePage,
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: Dashboard,
  },
  {
    path: '/summary',
    name: 'Summary',
    component: Summary,
  },  {
    path: '/summary-intro',
    name: 'SummaryIntro',
    component: SummaryIntro,
  },
  {
    path: '/train-gpt',
    name: 'TrainGPT',
    component: TrainGPT
  }
];

const router = createRouter({
  history: createWebHashHistory(),
  routes,
});

router.beforeEach(async function (to, from, next) {
  next();
});


export default router;
