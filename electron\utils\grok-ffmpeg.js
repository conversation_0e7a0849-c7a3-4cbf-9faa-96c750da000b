const { exec, spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const os = require('os');
const ffmpeg = require('fluent-ffmpeg');




const srtArray =[
    {
        "index": 1,
        "id": 1,
        "text": "群演一天一千去不去",
        "startTime": 0,
        "endTime": 1.6800000000000002,
        "start": "00:00:00,000",
        "end": "00:00:01,680",
        "translatedText": "<PERSON>ễn viên quần chúng một ngày một nghìn, đi không?",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\1111_audio\\dien-vien-quan-chung-mot1747995697765.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 2,
        "audioUrl2": "file://I:\\ReviewDao\\con-nho\\1111_audio\\dien-vien-quan-chung-mot1747995697765.mp3",
        "audioDuration2": 2742.857,
        "isGenerated2": true
    },
    {
        "index": 2,
        "id": 2,
        "text": "又想打我",
        "startTime": 1.6800000000000002,
        "endTime": 3.7,
        "start": "00:00:01,680",
        "end": "00:00:03,700",
        "translatedText": "Lại định đánh tôi nữa à?",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\1111_audio\\lai-dinh-danh-toi-nua-a1747995699414.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://I:\\ReviewDao\\con-nho\\1111_audio\\lai-dinh-danh-toi-nua-a1747995699414.mp3",
        "audioDuration1": 1464,
        "isGenerated1": true
    },
    {
        "index": 3,
        "id": 3,
        "text": "没有这次就是",
        "startTime": 3.7,
        "endTime": 5.5,
        "start": "00:00:03,700",
        "end": "00:00:05,500",
        "translatedText": "Không có, lần này thì...",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\1111_audio\\khong-co-lan-nay-thi1747995700236.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 2,
        "audioUrl2": "file://I:\\ReviewDao\\con-nho\\1111_audio\\khong-co-lan-nay-thi1747995700236.mp3",
        "audioDuration2": 1828.571,
        "isGenerated2": true
    },
    {
        "index": 4,
        "id": 4,
        "text": "你穿的唇域封一点坐车里就行了",
        "startTime": 5.5,
        "endTime": 8,
        "start": "00:00:05,500",
        "end": "00:00:08,000",
        "translatedText": "Em bịt kín vùng môi một chút, ngồi trong xe là được rồi",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\1111_audio\\em-bit-kin-vung-moi-mot-1747995733997.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 2,
        "audioUrl1": "file://I:\\ReviewDao\\con-nho\\1111_audio\\em-bit-kin-vung-moi-mot-1747995701697.mp3",
        "audioDuration1": 2768.98,
        "isGenerated1": true,
        "audioUrl2": "file://I:\\ReviewDao\\con-nho\\1111_audio\\em-bit-kin-vung-moi-mot-1747995733997.mp3",
        "audioDuration2": 3082.449,
        "isGenerated2": true
    },
    {
        "index": 5,
        "id": 5,
        "text": "正规",
        "startTime": 8,
        "endTime": 10.38,
        "start": "00:00:08,000",
        "end": "00:00:10,380",
        "translatedText": "Nghiêm túc không",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\1111_audio\\nghiem-tuc-khong1747995704463.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://I:\\ReviewDao\\con-nho\\1111_audio\\nghiem-tuc-khong1747995704463.mp3",
        "audioDuration1": 984,
        "isGenerated1": true
    },
    {
        "index": 6,
        "id": 6,
        "text": "哪次不正规啊",
        "startTime": 10.38,
        "endTime": 13,
        "start": "00:00:10,380",
        "end": "00:00:13,000",
        "translatedText": "Lần nào mà không nghiêm túc chứ?",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\1111_audio\\lan-nao-ma-khong-nghiem-1747995705239.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 2,
        "audioUrl2": "file://I:\\ReviewDao\\con-nho\\1111_audio\\lan-nao-ma-khong-nghiem-1747995705239.mp3",
        "audioDuration2": 1525,
        "isGenerated2": true
    },
    {
        "index": 7,
        "id": 7,
        "text": "坐这个车",
        "startTime": 13.72,
        "endTime": 14.72,
        "start": "00:00:13,720",
        "end": "00:00:14,720",
        "translatedText": "Ngồi xe này",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\1111_audio\\ngoi-xe-nay1747995706442.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://I:\\ReviewDao\\con-nho\\1111_audio\\ngoi-xe-nay1747995706442.mp3",
        "audioDuration1": 895,
        "isGenerated1": true
    },
    {
        "index": 8,
        "id": 8,
        "text": "对啊",
        "startTime": 14.72,
        "endTime": 16.16,
        "start": "00:00:14,720",
        "end": "00:00:16,160",
        "translatedText": "Đúng vậy",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\1111_audio\\dung-vay1747995707288.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 2,
        "audioUrl2": "file://I:\\ReviewDao\\con-nho\\1111_audio\\dung-vay1747995707288.mp3",
        "audioDuration2": 744,
        "isGenerated2": true
    },
    {
        "index": 9,
        "id": 9,
        "text": "死人三日前一度谋反",
        "startTime": 16.16,
        "endTime": 21.46,
        "start": "00:00:16,160",
        "end": "00:00:21,460",
        "translatedText": "Người chết ba ngày trước từng mưu phản",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\1111_audio\\nguoi-chet-ba-ngay-truoc1747995707833.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://I:\\ReviewDao\\con-nho\\1111_audio\\nguoi-chet-ba-ngay-truoc1747995707833.mp3",
        "audioDuration1": 1944,
        "isGenerated1": true
    },
    {
        "index": 10,
        "id": 10,
        "text": "七日五十斩首四重",
        "startTime": 21.46,
        "endTime": 23.58,
        "start": "00:00:21,460",
        "end": "00:00:23,580",
        "translatedText": "Bảy ngày năm mươi tội chém đầu bốn lần",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\1111_audio\\bay-ngay-nam-muoi-toi-ch1747995708665.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://I:\\ReviewDao\\con-nho\\1111_audio\\bay-ngay-nam-muoi-toi-ch1747995708665.mp3",
        "audioDuration1": 2232,
        "isGenerated1": true
    },
    {
        "index": 11,
        "id": 11,
        "text": "来来来来来来",
        "startTime": 23.58,
        "endTime": 25.2,
        "start": "00:00:23,580",
        "end": "00:00:25,200",
        "translatedText": "Tới đây Tới đây Tới đây",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\1111_audio\\toi-day-toi-day-toi-day1747995709586.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 3,
        "audioUrl3": "file://I:\\ReviewDao\\con-nho\\1111_audio\\toi-day-toi-day-toi-day1747995709586.mp3",
        "audioDuration3": 1416,
        "isGenerated3": true
    },
    {
        "index": 12,
        "id": 12,
        "text": "别墅里面敞开",
        "startTime": 30,
        "endTime": 43.37,
        "start": "00:00:30,000",
        "end": "00:00:43,370",
        "translatedText": "Bên trong biệt thự mở cửa rộng",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\1111_audio\\ben-trong-biet-thu-mo-cu1747995710374.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 2,
        "audioUrl2": "file://I:\\ReviewDao\\con-nho\\1111_audio\\ben-trong-biet-thu-mo-cu1747995710374.mp3",
        "audioDuration2": 1704,
        "isGenerated2": true
    }
]

// const srtArray =[
//     {
//         "index": 1,
//         "id": 1,
//         "text": "你知道人贩子为什么喜欢偷小孩吗",
//         "startTime": 0.22,
//         "endTime": 2.3,
//         "start": "00:00:00,220",
//         "end": "00:00:02,300",
//         "translatedText": "Bạn có biết tại sao bọn buôn người thích bắt cóc trẻ em không?",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0.-any\\_-converted_audio\\ban-co-biet-tai-sao-bon-1747983044585.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0.-any\\_-converted_audio\\ban-co-biet-tai-sao-bon-1747983044585.mp3",
//         "audioDuration1": 2904,
//         "isGenerated1": true
//     },
//     {
//         "index": 2,
//         "id": 2,
//         "text": "不知道",
//         "startTime": 2.3,
//         "endTime": 2.98,
//         "start": "00:00:02,300",
//         "end": "00:00:02,980",
//         "translatedText": "Không biết",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0.-any\\_-converted_audio\\khong-biet1747983046718.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 2,
//         "audioUrl2": "file://F:\\Footage\\0.-any\\_-converted_audio\\khong-biet1747983046718.mp3",
//         "audioDuration2": 655,
//         "isGenerated2": true
//     },
//     {
//         "index": 3,
//         "id": 3,
//         "text": "因为你的脑神经值265万",
//         "startTime": 2.98,
//         "endTime": 5.76,
//         "start": "00:00:02,980",
//         "end": "00:00:05,760",
//         "translatedText": "Vì dây thần kinh não của bạn trị giá 265 triệu",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0.-any\\_-converted_audio\\vi-day-than-kinh-nao-cua1747983048332.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0.-any\\_-converted_audio\\vi-day-than-kinh-nao-cua1747983048332.mp3",
//         "audioDuration1": 3288,
//         "isGenerated1": true
//     },
//     {
//         "index": 4,
//         "id": 4,
//         "text": "小心脏值75万",
//         "startTime": 5.76,
//         "endTime": 7.92,
//         "start": "00:00:05,760",
//         "end": "00:00:07,920",
//         "translatedText": "Trái tim nhỏ trị giá 75 triệu",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0.-any\\_-converted_audio\\trai-tim-nho-tri-gia-75-1747983049581.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0.-any\\_-converted_audio\\trai-tim-nho-tri-gia-75-1747983049581.mp3",
//         "audioDuration1": 2185,
//         "isGenerated1": true
//     },
//     {
//         "index": 5,
//         "id": 5,
//         "text": "肾脏值65万",
//         "startTime": 7.92,
//         "endTime": 9.6,
//         "start": "00:00:07,920",
//         "end": "00:00:09,600",
//         "translatedText": "Thận trị giá 65 triệu",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0.-any\\_-converted_audio\\than-tri-gia-65-trieu1747983050727.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0.-any\\_-converted_audio\\than-tri-gia-65-trieu1747983050727.mp3",
//         "audioDuration1": 1824,
//         "isGenerated1": true
//     },
//     {
//         "index": 6,
//         "id": 6,
//         "text": "皮肤血液值120万",
//         "startTime": 9.6,
//         "endTime": 11.5,
//         "start": "00:00:09,600",
//         "end": "00:00:11,500",
//         "translatedText": "Da và máu trị giá 120 triệu",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0.-any\\_-converted_audio\\da-va-mau-tri-gia-120-tr1747983051622.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0.-any\\_-converted_audio\\da-va-mau-tri-gia-120-tr1747983051622.mp3",
//         "audioDuration1": 2400,
//         "isGenerated1": true
//     },
//     {
//         "index": 7,
//         "id": 7,
//         "text": "牙齿值20万",
//         "startTime": 11.5,
//         "endTime": 13.1,
//         "start": "00:00:11,500",
//         "end": "00:00:13,100",
//         "translatedText": "Răng trị giá 20 triệu",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0.-any\\_-converted_audio\\rang-tri-gia-20-trieu1747983052545.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0.-any\\_-converted_audio\\rang-tri-gia-20-trieu1747983052545.mp3",
//         "audioDuration1": 1595,
//         "isGenerated1": true
//     },
//     {
//         "index": 8,
//         "id": 8,
//         "text": "眼睛值7万",
//         "startTime": 13.1,
//         "endTime": 14.6,
//         "start": "00:00:13,100",
//         "end": "00:00:14,600",
//         "translatedText": "Mắt trị giá 7 triệu",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0.-any\\_-converted_audio\\mat-tri-gia-7-trieu1747983053379.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0.-any\\_-converted_audio\\mat-tri-gia-7-trieu1747983053379.mp3",
//         "audioDuration1": 1320,
//         "isGenerated1": true
//     },
//     {
//         "index": 9,
//         "id": 9,
//         "text": "鼻腔值10万",
//         "startTime": 14.6,
//         "endTime": 15.92,
//         "start": "00:00:14,600",
//         "end": "00:00:15,920",
//         "translatedText": "Khoang mũi trị giá 10 triệu",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0.-any\\_-converted_audio\\khoang-mui-tri-gia-10-tr1747983054125.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0.-any\\_-converted_audio\\khoang-mui-tri-gia-10-tr1747983054125.mp3",
//         "audioDuration1": 1645,
//         "isGenerated1": true
//     },
//     {
//         "index": 10,
//         "id": 10,
//         "text": "肺值30万",
//         "startTime": 15.92,
//         "endTime": 17.04,
//         "start": "00:00:15,920",
//         "end": "00:00:17,040",
//         "translatedText": "Phổi trị giá 30 triệu",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0.-any\\_-converted_audio\\phoi-tri-gia-30-trieu1747983054966.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0.-any\\_-converted_audio\\phoi-tri-gia-30-trieu1747983054966.mp3",
//         "audioDuration1": 1632,
//         "isGenerated1": true
//     },
//     {
//         "index": 11,
//         "id": 11,
//         "text": "还有我们的肝脏值90万呢",
//         "startTime": 17.04,
//         "endTime": 19.08,
//         "start": "00:00:17,040",
//         "end": "00:00:19,080",
//         "translatedText": "Gan của chúng ta cũng có giá tới 900 nghìn đấy",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0.-any\\_-converted_audio\\gan-cua-chung-ta-cung-co1747983055963.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0.-any\\_-converted_audio\\gan-cua-chung-ta-cung-co1747983055963.mp3",
//         "audioDuration1": 2445,
//         "isGenerated1": true
//     },
//     {
//         "index": 12,
//         "id": 12,
//         "text": "你要知道这些都是黑市上人体器官的一个价格",
//         "startTime": 19.08,
//         "endTime": 22.54,
//         "start": "00:00:19,080",
//         "end": "00:00:22,540",
//         "translatedText": "Bạn phải biết đây đều là giá nội tạng người trên chợ đen",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0.-any\\_-converted_audio\\ban-phai-biet-day-deu-la1747983057042.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0.-any\\_-converted_audio\\ban-phai-biet-day-deu-la1747983057042.mp3",
//         "audioDuration1": 2760,
//         "isGenerated1": true
//     },
//     {
//         "index": 13,
//         "id": 13,
//         "text": "这些要是被人贩子摘掉会被卖到全球各地的",
//         "startTime": 22.54,
//         "endTime": 25.8,
//         "start": "00:00:22,540",
//         "end": "00:00:25,800",
//         "translatedText": "Những thứ này nếu bị bọn buôn người lấy đi sẽ bị bán đi khắp nơi trên thế giới",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0.-any\\_-converted_audio\\nhung-thu-nay-neu-bi-bon1747983058363.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0.-any\\_-converted_audio\\nhung-thu-nay-neu-bi-bon1747983058363.mp3",
//         "audioDuration1": 3745,
//         "isGenerated1": true
//     },
//     {
//         "index": 14,
//         "id": 14,
//         "text": "所以你一定要注意",
//         "startTime": 25.8,
//         "endTime": 27.36,
//         "start": "00:00:25,800",
//         "end": "00:00:27,360",
//         "translatedText": "Vì vậy bạn nhất định phải cẩn thận",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0.-any\\_-converted_audio\\vi-vay-ban-nhat-dinh-pha1747983059704.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0.-any\\_-converted_audio\\vi-vay-ban-nhat-dinh-pha1747983059704.mp3",
//         "audioDuration1": 1665,
//         "isGenerated1": true
//     },
//     {
//         "index": 15,
//         "id": 15,
//         "text": "不管去哪里",
//         "startTime": 27.36,
//         "endTime": 28.12,
//         "start": "00:00:27,360",
//         "end": "00:00:28,120",
//         "translatedText": "Dù đi bất cứ đâu",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0.-any\\_-converted_audio\\du-di-bat-cu-dau1747983060697.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0.-any\\_-converted_audio\\du-di-bat-cu-dau1747983060697.mp3",
//         "audioDuration1": 1296,
//         "isGenerated1": true
//     },
//     {
//         "index": 16,
//         "id": 16,
//         "text": "一定不要单独外出",
//         "startTime": 28.12,
//         "endTime": 29.38,
//         "start": "00:00:28,120",
//         "end": "00:00:29,380",
//         "translatedText": "Tuyệt đối không đi một mình",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0.-any\\_-converted_audio\\tuyet-doi-khong-di-mot-m1747983061523.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0.-any\\_-converted_audio\\tuyet-doi-khong-di-mot-m1747983061523.mp3",
//         "audioDuration1": 1512,
//         "isGenerated1": true
//     },
//     {
//         "index": 17,
//         "id": 17,
//         "text": "不能走夜路",
//         "startTime": 29.38,
//         "endTime": 30.64,
//         "start": "00:00:29,380",
//         "end": "00:00:30,640",
//         "translatedText": "Không đi đường đêm",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0.-any\\_-converted_audio\\khong-di-duong-dem1747983062294.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0.-any\\_-converted_audio\\khong-di-duong-dem1747983062294.mp3",
//         "audioDuration1": 1248,
//         "isGenerated1": true
//     },
//     {
//         "index": 18,
//         "id": 18,
//         "text": "一定要走有摄像头的大马路",
//         "startTime": 30.64,
//         "endTime": 32.42,
//         "start": "00:00:30,640",
//         "end": "00:00:32,420",
//         "translatedText": "Luôn đi trên những con đường lớn có camera giám sát",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0.-any\\_-converted_audio\\luon-di-tren-nhung-con-d1747983063036.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0.-any\\_-converted_audio\\luon-di-tren-nhung-con-d1747983063036.mp3",
//         "audioDuration1": 2832,
//         "isGenerated1": true
//     },
//     {
//         "index": 19,
//         "id": 19,
//         "text": "最好呢",
//         "startTime": 32.42,
//         "endTime": 33.3,
//         "start": "00:00:32,420",
//         "end": "00:00:33,300",
//         "translatedText": "Tốt nhất là",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0.-any\\_-converted_audio\\tot-nhat-la1747983064293.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0.-any\\_-converted_audio\\tot-nhat-la1747983064293.mp3",
//         "audioDuration1": 960,
//         "isGenerated1": true
//     },
//     {
//         "index": 20,
//         "id": 20,
//         "text": "要结伴同行",
//         "startTime": 33.3,
//         "endTime": 34.14,
//         "start": "00:00:33,300",
//         "end": "00:00:34,140",
//         "translatedText": "Nên đi cùng người khác",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0.-any\\_-converted_audio\\nen-di-cung-nguoi-khac1747983065002.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0.-any\\_-converted_audio\\nen-di-cung-nguoi-khac1747983065002.mp3",
//         "audioDuration1": 1296,
//         "isGenerated1": true
//     },
//     {
//         "index": 21,
//         "id": 21,
//         "text": "你要远离试图接近你的陌生人",
//         "startTime": 34.14,
//         "endTime": 36.34,
//         "start": "00:00:34,140",
//         "end": "00:00:36,340",
//         "translatedText": "Bạn phải tránh xa những người lạ cố tình tiếp cận mình",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0.-any\\_-converted_audio\\ban-phai-tranh-xa-nhung-1747983065744.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0.-any\\_-converted_audio\\ban-phai-tranh-xa-nhung-1747983065744.mp3",
//         "audioDuration1": 2595,
//         "isGenerated1": true
//     },
//     {
//         "index": 22,
//         "id": 22,
//         "text": "嗯",
//         "startTime": 36.34,
//         "endTime": 36.74,
//         "start": "00:00:36,340",
//         "end": "00:00:36,740",
//         "translatedText": "Vâng",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0.-any\\_-converted_audio\\vang1747983067198.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 2,
//         "audioUrl2": "file://F:\\Footage\\0.-any\\_-converted_audio\\vang1747983067198.mp3",
//         "audioDuration2": 525,
//         "isGenerated2": true
//     },
//     {
//         "index": 23,
//         "id": 23,
//         "text": "一定要提高防范意识",
//         "startTime": 36.74,
//         "endTime": 38.54,
//         "start": "00:00:36,740",
//         "end": "00:00:38,540",
//         "translatedText": "Nhất định phải nâng cao ý thức phòng ngừa",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0.-any\\_-converted_audio\\nhat-dinh-phai-nang-cao-1747983067872.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0.-any\\_-converted_audio\\nhat-dinh-phai-nang-cao-1747983067872.mp3",
//         "audioDuration1": 2136,
//         "isGenerated1": true
//     },
//     {
//         "index": 24,
//         "id": 24,
//         "text": "保护好你自己的人身安全",
//         "startTime": 38.54,
//         "endTime": 40.64,
//         "start": "00:00:38,540",
//         "end": "00:00:40,640",
//         "translatedText": "Bảo vệ tốt an toàn cá nhân của bản thân",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0.-any\\_-converted_audio\\bao-ve-tot-an-toan-ca-nh1747983068977.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0.-any\\_-converted_audio\\bao-ve-tot-an-toan-ca-nh1747983068977.mp3",
//         "audioDuration1": 2352,
//         "isGenerated1": true
//     },
//     {
//         "index": 25,
//         "id": 25,
//         "text": "你要记住",
//         "startTime": 40.64,
//         "endTime": 41.62,
//         "start": "00:00:40,640",
//         "end": "00:00:41,620",
//         "translatedText": "Bạn phải nhớ kỹ",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0.-any\\_-converted_audio\\ban-phai-nho-ky1747983070008.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0.-any\\_-converted_audio\\ban-phai-nho-ky1747983070008.mp3",
//         "audioDuration1": 1135,
//         "isGenerated1": true
//     },
//     {
//         "index": 26,
//         "id": 26,
//         "text": "我们的生命只有一次",
//         "startTime": 41.62,
//         "endTime": 43.16,
//         "start": "00:00:41,620",
//         "end": "00:00:43,160",
//         "translatedText": "Mạng sống của chúng ta chỉ có một lần",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0.-any\\_-converted_audio\\mang-song-cua-chung-ta-c1747983070772.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0.-any\\_-converted_audio\\mang-song-cua-chung-ta-c1747983070772.mp3",
//         "audioDuration1": 1925,
//         "isGenerated1": true
//     },
//     {
//         "index": 27,
//         "id": 27,
//         "text": "嗯",
//         "startTime": 43.16,
//         "endTime": 43.72,
//         "start": "00:00:43,160",
//         "end": "00:00:43,720",
//         "translatedText": "Vâng",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0.-any\\_-converted_audio\\vang1747983071740.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 2,
//         "audioUrl2": "file://F:\\Footage\\0.-any\\_-converted_audio\\vang1747983071740.mp3",
//         "audioDuration2": 600,
//         "isGenerated2": true
//     },
//     {
//         "index": 28,
//         "id": 28,
//         "text": "所以一定要好好保护自己",
//         "startTime": 43.72,
//         "endTime": 45.76,
//         "start": "00:00:43,720",
//         "end": "00:00:45,760",
//         "translatedText": "Vì vậy nhất định phải bảo vệ bản thân thật tốt",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0.-any\\_-converted_audio\\vi-vay-nhat-dinh-phai-ba1747983072303.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0.-any\\_-converted_audio\\vi-vay-nhat-dinh-phai-ba1747983072303.mp3",
//         "audioDuration1": 2255,
//         "isGenerated1": true
//     },
//     {
//         "index": 29,
//         "id": 29,
//         "text": "知道吗",
//         "startTime": 45.76,
//         "endTime": 46.22,
//         "start": "00:00:45,760",
//         "end": "00:00:46,220",
//         "translatedText": "Hiểu chưa?",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0.-any\\_-converted_audio\\hieu-chua1747983073296.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0.-any\\_-converted_audio\\hieu-chua1747983073296.mp3",
//         "audioDuration1": 765,
//         "isGenerated1": true
//     },
//     {
//         "index": 30,
//         "id": 30,
//         "text": "知道了",
//         "startTime": 46.22,
//         "endTime": 46.76,
//         "start": "00:00:46,220",
//         "end": "00:00:46,760",
//         "translatedText": "Con hiểu rồi",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0.-any\\_-converted_audio\\con-hieu-roi1747983073898.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 2,
//         "audioUrl2": "file://F:\\Footage\\0.-any\\_-converted_audio\\con-hieu-roi1747983073898.mp3",
//         "audioDuration2": 825,
//         "isGenerated2": true
//     }
// ]
const videoPath = "I:\\ReviewDao\\con-nho\\1111.mp4"
// const videoPath = "F:\\Footage\\0.-any\\batcoc\\_-converted.mp4"
let logoPath = ''
const tempDir = path.join('I:\\ReviewDao\\con-nho', 'video_segments');
const options = {
  addLogo: false,
  addText: false,
  customText: 'Video Rendered by Vue+Electron',
}

async function getVideoDuration(videoPath) {
  return new Promise((resolve, reject) => {
    const ffprobe = spawn('ffprobe', [
      '-v', 'error',
      '-show_entries', 'format=duration',
      '-of', 'default=noprint_wrappers=1:nokey=1',
      videoPath
    ]);
    
    let output = '';
    
    ffprobe.stdout.on('data', (data) => {
      output += data.toString();
    });
    
    ffprobe.stderr.on('data', (data) => {
      console.error('ffprobe stderr:', data.toString());
    });
    
    ffprobe.on('close', (code) => {
      if (code !== 0) {
        reject(new Error(`ffprobe process exited with code ${code}`));
        return;
      }
      
      const duration = parseFloat(output.trim());
      if (isNaN(duration)) {
        reject(new Error('Failed to parse video duration'));
        return;
      }
      
      resolve(duration);
    });
  });
}

// Tính thời lượng audio từ audioUrl
const getAudioDuration = (audioUrl) => {
  return new Promise((resolve, reject) => {
    const cleanAudioUrl = audioUrl.replace('file://', '');
    const command = `ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 "${cleanAudioUrl}"`;
    exec(command, (error, stdout) => {
      if (error) reject(error);
      else resolve(parseFloat(stdout));
    });
  });
};

// Chuyển đổi thời gian từ giây sang định dạng HH:MM:SS.mmm
const secondsToTime = (seconds) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = (seconds % 60).toFixed(3);
  return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(secs).padStart(2, '0')}`;
};

// Cắt video theo startTime và endTime
const cutVideoSegment = async (srt, outputPath) => {
  const start = parseFloat(srt.startTime);
  const end = parseFloat(srt.endTime);

  const videoDuration = await getVideoDuration(videoPath);

  if (isNaN(start) || isNaN(end) || start >= videoDuration) {
    console.warn(`[cutVideoSegment] Skipping invalid segment: start=${start}, end=${end}, video=${videoDuration}`);
    return null;
  }

  const safeEnd = Math.min(end, videoDuration);
  const duration = parseFloat((safeEnd - start).toFixed(3));

  if (duration <= 0) {
    console.warn(`[cutVideoSegment] Skipping zero-duration segment: start=${start}, end=${safeEnd}`);
    return null;
  }

  const command = `ffmpeg -ss ${start} -t ${duration} -i "${videoPath}" -c:v libx264 -c:a aac -y "${outputPath}"`;
//   console.log('[cutVideoSegment]', command);

  return new Promise((resolve, reject) => {
    exec(command, (error) => {
      if (error) reject(error);
      else resolve(outputPath);
    });
  });
};


// Tạo file ASS phụ đề
const createAssFile = (srtArray, assPath) => {
  const assContent = `[Script Info]
Title: Untitled
ScriptType: v4.00+
WrapStyle: 0
PlayResX: 384
PlayResY: 288

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: Default,Arial,16,&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,2,2,2,10,10,10,1

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
${srtArray
  .map((srt) =>
    `Dialogue: 0,${srt.start.replace(/,/g, '.')},${srt.end.replace(/,/g, '.')},Default,,0,0,0,,${srt.translatedText.replace(/\n/g, '\\N')}`
  )
  .join('\n')}`;

  fs.writeFileSync(assPath, assContent);
};
function generateASS(subs) {
  const header = `
[Script Info]
ScriptType: v4.00+
PlayResX: 1920
PlayResY: 1080

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: Default,Arial,48,&H00FFFFFF,&H000000FF,&H00000000,&H64000000,-1,0,0,0,100,100,0,0,1,2,0,2,20,20,30,1

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text`.trim();

  const formatTime = t => {
    // Nếu t là "0:00:00.18", giữ nguyên
    // Nếu t là seconds: 1.5 → "0:00:01.50"
    if (typeof t === 'number') {
      const h = String(Math.floor(t / 3600)).padStart(1, '0');
      const m = String(Math.floor((t % 3600) / 60)).padStart(2, '0');
      const s = (t % 60).toFixed(2).padStart(5, '0');
      return `${h}:${m}:${s}`;
    }
    return t;
  };
  const toAssTime = (seconds) => {
    const h = String(Math.floor(seconds / 3600)).padStart(1, '0');
    const m = String(Math.floor((seconds % 3600) / 60)).padStart(2, '0');
    const s = String((seconds % 60).toFixed(2)).padStart(5, '0').replace('.', ':');
    return `${h}:${m}:${s}`;
  };
  const body = subs.map((srt) => {
    const factor = srt.speedFactor || 1;
    console.log('factor: ', factor);
    
    const adjStart = srt.startTime * factor;
    const adjEnd = srt.endTime * factor;
    return `Dialogue: 0,${formatTime(srt.adjustedStartTime)},${formatTime(srt.adjustedEndTime)},Default,,0,0,0,,${srt.translatedText.replace(/\n/g, '\\N')}`;
  }).join('\n');

  return `${header}\n${body}`;
}
//Dialogue: 0,0:00:02.00,0:00:03.75,Default,,0,0,0,,Một người khiêng quan tài với 40 năm kinh nghiệm kể rằng

// Điều chỉnh tốc độ video nếu audio dài hơn
const adjustVideoSpeed = (videoSegment, audioUrl, srt, outputPath) => {
  return new Promise((resolve, reject) => {
    const audioPath = audioUrl.replace('file://', '');
    const videoDuration = srt.endTime - srt.startTime;
    const audioDuration = srt.duration;
    const speedFactor = audioDuration / videoDuration;
    srt.speedFactor = speedFactor < 1 ? speedFactor : 1;

    const silenceDuration = (videoDuration - audioDuration).toFixed(3);
    const tempSilence = 'temp_silence.wav';
    const paddedAudio = 'temp_padded_audio.wav';

    let command = '';

    if (speedFactor > 1) {
      // Làm chậm video để khớp với âm thanh
      command = `ffmpeg -i "${videoSegment}" -i "${audioPath}" ` +
        `-filter_complex "[0:v]setpts=${speedFactor}*PTS[v];[1:a]volume=0.5[a]" ` +
        `-map "[v]" -map "[a]" -c:v libx264 -preset fast -crf 23 -c:a aac -ar 44100 -ac 2 -b:a 192k -y "${outputPath}"`;

      exec(command, (error) => {
        if (error) reject(error);
        else resolve(outputPath);
      });

    } else {
      // Audio ngắn → thêm silence vào cuối audio → nối lại → ghép vào video
      const createSilence = `ffmpeg -f lavfi -i anullsrc=r=44100:cl=stereo -t ${silenceDuration} -q:a 9 -acodec pcm_s16le ${tempSilence}`;
      const concatAudio = `ffmpeg -i "concat:${audioPath}|${tempSilence}" -c copy ${paddedAudio}`;
      const finalMerge = `ffmpeg -i "${videoSegment}" -i ${paddedAudio} ` +
        `-filter_complex "[1:a]volume=0.5[a]" -map 0:v -map "[a]" -c:v copy -c:a aac -ar 44100 -ac 2 -b:a 192k -y "${outputPath}"`;

      exec(createSilence, (err) => {
        if (err) return reject(err);
        exec(concatAudio, (err) => {
          if (err) return reject(err);
          exec(finalMerge, (err) => {
            if (err) reject(err);
            else resolve(outputPath);
          });
        });
      });
    }
  });
};



// Thêm logo hoặc text vào video
const addOverlay = (inputPath, outputPath) => {
  let filter = '';
  if (options.addLogo && logoPath) {
    filter += `overlay=10:10:enable='between(t,0,${srtArray[srtArray.length - 1].endTime})'`;
  }
  if (options.addText) {
    filter += `${filter ? ',' : ''}drawtext=text='${
      options.customText
    }':fontcolor=white:fontsize=24:box=1:boxcolor=black@0.5:x=(w-text_w)/2:y=h-th-10`;
  }
  if (!filter) return Promise.resolve(inputPath);

  return new Promise((resolve, reject) => {
    const command = `ffmpeg -i "${inputPath}" -vf "${filter}" -c:a copy -y "${outputPath}"`;
    exec(command, (error) => {
      if (error) reject(error);
      else resolve(outputPath);
    });
  });
};
const normalizeAssPath = (assPath) => {
  // Thay \ thành /
  let p = path.resolve(assPath).replace(/\\/g, '/');
  // Nếu Windows drive letter (vd: C:/...), thêm dấu / ở đầu
  if (/^[a-zA-Z]:/.test(p)) {
    p = '/' + p;
  }
  return p;
};
// Hàm chính xử lý video
const processVideo = async () => {
  if (!videoPath) {
    alert('Vui lòng chọn file video!');
    return;
  }


  if (!fs.existsSync(tempDir)) fs.mkdirSync(tempDir);

  // 1. Tính thời lượng audio
  for (const srt of srtArray) {
    srt.duration = await getAudioDuration(srt.audioUrl);
  }
    let currentTimeline = 0;
  // 2. Cắt và xử lý từng đoạn video
  const processedSegments = [];
  for (const srt of srtArray) {
    const segmentPath = path.join(tempDir, `segment_${srt.index}_cut.mp4`);
    const adjustedPath = path.join(tempDir, `segment_${srt.index}_adjusted.mp4`);

    // Cắt video
    await cutVideoSegment(srt, segmentPath);

    // Điều chỉnh tốc độ nếu cần và ghép audio
    await adjustVideoSpeed(segmentPath, srt.audioUrl, srt, adjustedPath);
    processedSegments.push(adjustedPath);
      // Tính lại thời gian sau khi điều chỉnh
  const originalDuration = srt.endTime - srt.startTime;
  const speedFactor = Math.min(1, originalDuration / srt.duration); // như trong adjustVideoSpeed
  const newDuration = originalDuration / speedFactor;

  srt.adjustedStartTime = currentTimeline;
  srt.adjustedEndTime = currentTimeline + newDuration;
  currentTimeline += newDuration;
  }

  // 3. Tạo file ASS phụ đề
  const assPath0 = path.join(tempDir, 'subtitles.ass');
  const assPath = './subtitles.ass';
  const assRelativePath = path.relative(process.cwd(), assPath0).replace(/\\/g, '/');
//   console.log('assRelativePath', assRelativePath);
//   createAssFile(srtArray, assPath);
  const assContent = generateASS(srtArray);
  fs.writeFileSync(assPath, assContent);

  // 4. Ghép các đoạn video
  const concatListPath = path.join(tempDir, 'concat.txt');
  fs.writeFileSync(concatListPath, processedSegments.map((p) => `file '${p}'`).join('\n'));
  const concatOutput = path.join(tempDir, 'concatenated.mp4');
  await new Promise((resolve, reject) => {
    const command = `ffmpeg -f concat -safe 0 -i "${concatListPath}" -c:v libx264 -c:a aac -ar 44100 -ac 2 -b:a 192k -y "${concatOutput}"`;
    exec(command, (error) => {
      if (error) reject(error);
      else resolve();
    });
  });

  // 5. Áp dụng phụ đề
  const subbedOutput = path.join(tempDir, 'subbed.mp4');
    await new Promise((resolve, reject) => {
    // const safeAssPath = normalizeAssPath(assPath);
    const command = `ffmpeg -i "${concatOutput}" -vf "ass=${assPath}" -c:a copy -y "${subbedOutput}"`;
    exec(command, (error) => {
      if (error) reject(error);
      else resolve();
    });
  });
// await new Promise((resolve, reject) => {
// //   const safeAssPath = assPath.replace(/\\/g, '/');
// const safeAssPath = normalizeAssPath(assPath);
//   ffmpeg(concatOutput)
//     .videoFilter(`ass=${safeAssPath}`)
//     .audioCodec('copy')
//     .output(subbedOutput)
//     .on('error', (err) => {
//       reject(err);
//     })
//     .on('end', () => {
//       resolve();
//     })
//     .run();
// });

  // 6. Thêm logo/text nếu có
  const finalOutput = path.join(os.homedir(), 'Desktop', 'final_video.mp4');
  await addOverlay(subbedOutput, finalOutput);

  console.log(`Video đã được render tại: ${finalOutput}`);
};

processVideo();

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});