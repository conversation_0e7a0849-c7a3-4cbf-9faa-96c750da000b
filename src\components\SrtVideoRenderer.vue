<template>
  <div class="srt-video-renderer p-4 bg-white dark:bg-gray-800 shadow rounded-lg">
    <h2 class="text-xl font-bold mb-4">SRT Video Renderer</h2>

    <!-- Input Section -->
    <div class="space-y-4">
      <!-- Video Selection -->
      <div class="mb-4">
        <div class="flex items-center mb-2">
          <span class="font-medium mr-2">Select Video:</span>
          <a-button
            type="primary"
            @click="selectVideo"
            :disabled="isProcessing"
          >
            Browse
          </a-button>
          <span v-if="videoFile" class="ml-2 text-sm">
            {{ videoFile.name }}
          </span>
        </div>

        <!-- Video preview if selected -->
        <div v-if="videoFile" class="mt-2 bg-black rounded-md overflow-hidden max-w-lg">
          <video
            ref="videoPreview"
            :src="videoUrl"
            controls
            class="w-full max-h-[300px] object-contain"
          ></video>
        </div>
      </div>

      <!-- SRT Selection -->
      <div class="mb-4">
        <div class="flex items-center mb-2">
          <span class="font-medium mr-2">SRT Data:</span>
          <a-button
            @click="openSrtModal"
            :disabled="isProcessing || !videoFile"
          >
            Select SRT
          </a-button>
          <span v-if="srtItems.length" class="ml-2 text-sm">
            {{ srtItems.length }} subtitles loaded
          </span>
        </div>

        <!-- SRT preview if selected -->
        <div v-if="srtItems.length" class="mt-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-md max-h-[200px] overflow-y-auto">
          <div class="text-sm">
            <div v-for="(item, index) in srtItems.slice(0, 5)" :key="index" class="mb-2 pb-2 border-b border-gray-200 dark:border-gray-600">
              <div class="flex justify-between">
                <span class="font-medium">#{{ item.index }}</span>
                <span class="text-gray-500">{{ item.start }} → {{ item.end }}</span>
              </div>
              <div>{{ item.text }}</div>
              <div v-if="item.translatedText" class="text-blue-600 dark:text-blue-400">{{ item.translatedText }}</div>
              <div v-if="item.audioUrl" class="text-green-600 dark:text-green-400 text-xs mt-1">
                Audio: {{ item.audioUrl.split('/').pop() }}
              </div>
            </div>
            <div v-if="srtItems.length > 5" class="text-center text-gray-500 mt-2">
              ... and {{ srtItems.length - 5 }} more subtitles
            </div>
          </div>
        </div>
      </div>

      <!-- Rendering Options -->
      <div class="mb-4">
        <h3 class="font-medium mb-2">Rendering Options</h3>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <!-- Output Directory -->
          <div>
            <label class="block text-sm text-gray-600 dark:text-gray-400 mb-1">Output Directory</label>
            <div class="flex space-x-2">
              <a-input v-model:value="outputDir" placeholder="Same as input video" />
              <a-button @click="selectOutputDir">Browse</a-button>
            </div>
          </div>

          <!-- Subtitle Style -->
          <div>
            <label class="block text-sm text-gray-600 dark:text-gray-400 mb-1">Subtitle Style</label>
            <a-select v-model:value="subtitleStyle" class="w-full">
              <a-select-option value="default">Default (White with shadow)</a-select-option>
              <a-select-option value="white">White on black</a-select-option>
              <a-select-option value="yellow">Yellow on black</a-select-option>
            </a-select>
          </div>

          <!-- Logo Overlay -->
          <div>
            <div class="flex items-center mb-1">
              <a-checkbox v-model:checked="addLogo">Add Logo Overlay</a-checkbox>
            </div>
            <div v-if="addLogo" class="flex space-x-2">
              <a-input v-model:value="logoPath" placeholder="Select logo image" disabled />
              <a-button @click="selectLogoFile">Browse</a-button>
            </div>
          </div>

          <!-- Text Overlay -->
          <div>
            <div class="flex items-center mb-1">
              <a-checkbox v-model:checked="addTextOverlay">Add Text Overlay</a-checkbox>
            </div>
            <div v-if="addTextOverlay" class="space-y-2">
              <a-input v-model:value="textOverlay" placeholder="Enter text to overlay" />
              <a-select v-model:value="textPosition" class="w-full">
                <a-select-option value="top">Top</a-select-option>
                <a-select-option value="bottom">Bottom</a-select-option>
              </a-select>
            </div>
          </div>

          <!-- Video Codec -->
          <div>
            <label class="block text-sm text-gray-600 dark:text-gray-400 mb-1">Video Codec</label>
            <a-select v-model:value="videoCodec" class="w-full">
              <a-select-option value="h264_nvenc">NVIDIA H.264 (GPU)</a-select-option>
              <a-select-option value="libx264">CPU H.264</a-select-option>
            </a-select>
          </div>

          <!-- Audio Bitrate -->
          <div>
            <label class="block text-sm text-gray-600 dark:text-gray-400 mb-1">Audio Bitrate (kbps)</label>
            <a-input-number v-model:value="audioBitrate" :min="64" :max="320" class="w-full" />
          </div>
        </div>
      </div>

      <!-- Render Button -->
      <div class="flex justify-center mt-6">
        <a-button
          type="primary"
          size="large"
          @click="renderVideo"
          :disabled="!canRender || isProcessing"
          :loading="isProcessing"
        >
          {{ isProcessing ? 'Rendering...' : 'Render Video' }}
        </a-button>
      </div>

      <!-- Progress -->
      <div v-if="isProcessing" class="mt-4">
        <a-progress :percent="progress" status="active" />
        <div class="mt-2 text-center text-gray-600 dark:text-gray-400">{{ processingStatus }}</div>
      </div>

      <!-- Result -->
      <div v-if="outputPath" class="mt-4 p-4 bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 rounded-md">
        <div class="font-medium text-green-800 dark:text-green-300 mb-2">Rendering Complete!</div>
        <div class="text-gray-700 dark:text-gray-300 mb-3 break-all">{{ outputPath }}</div>
        <div class="flex space-x-2">
          <a-button type="primary" @click="openOutputFile">Open Video</a-button>
          <a-button @click="openOutputFolder">Open Folder</a-button>
        </div>
      </div>

      <!-- Error -->
      <div v-if="error" class="mt-4 p-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md">
        <div class="font-medium text-red-800 dark:text-red-300 mb-2">Error</div>
        <div class="text-gray-700 dark:text-gray-300">{{ error }}</div>
      </div>
    </div>

    <!-- SRT Modal -->
    <a-modal
      v-model:open="srtModalVisible"
      title="Select SRT Data"
      @ok="handleSrtModalOk"
      width="800px"
    >
      <SrtLists
        buttonText="Select SRT"
        @select="handleSelectSrt"
      />
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { message } from 'ant-design-vue';
import SrtLists from './SrtLists.vue';
import { useTTSStore } from '../stores/ttsStore';

// Store
const ttsStore = useTTSStore();

// State
const videoFile = ref(null);
const videoUrl = ref(null);
const videoPreview = ref(null);
const srtItems = ref([]);
const outputDir = ref('');
const subtitleStyle = ref('default');
const addLogo = ref(false);
const logoPath = ref('');
const addTextOverlay = ref(false);
const textOverlay = ref('');
const textPosition = ref('bottom');
const videoCodec = ref('libx264');
const audioBitrate = ref(192);
const isProcessing = ref(false);
const progress = ref(0);
const processingStatus = ref('');
const error = ref('');
const outputPath = ref('');
const srtModalVisible = ref(false);

// Computed
const canRender = computed(() => {
  return videoFile.value && srtItems.value.length > 0;
});

// Methods
const selectVideo = async () => {
  try {
    const result = await window.electronAPI.openFileDialog({
      title: 'Select Video File',
      filters: [
        { name: 'Video Files', extensions: ['mp4', 'mkv', 'avi', 'mov', 'wmv'] }
      ],
      properties: ['openFile']
    });

    if (!result.canceled && result.filePaths && result.filePaths.length > 0) {
      const filePath = result.filePaths[0];

      // Create a file object
      videoFile.value = {
        name: filePath.split(/[\/\\]/).pop(),
        path: filePath
      };

      // Create URL for preview
      if (videoUrl.value) {
        URL.revokeObjectURL(videoUrl.value);
      }

      // For Electron, we can use the file:// protocol
      videoUrl.value = `file://${filePath}`;
    }
  } catch (err) {
    error.value = `Error selecting video: ${err.message}`;
    message.error(error.value);
  }
};

const selectOutputDir = async () => {
  try {
    const result = await window.electronAPI.openFileDialog({
      title: 'Select Output Directory',
      properties: ['openDirectory']
    });

    if (!result.canceled && result.filePaths && result.filePaths.length > 0) {
      outputDir.value = result.filePaths[0];
    }
  } catch (err) {
    error.value = `Error selecting output directory: ${err.message}`;
    message.error(error.value);
  }
};

const selectLogoFile = async () => {
  try {
    const result = await window.electronAPI.openFileDialog({
      title: 'Select Logo Image',
      filters: [
        { name: 'Image Files', extensions: ['png', 'jpg', 'jpeg'] }
      ],
      properties: ['openFile']
    });

    if (!result.canceled && result.filePaths && result.filePaths.length > 0) {
      logoPath.value = result.filePaths[0];
    }
  } catch (err) {
    error.value = `Error selecting logo: ${err.message}`;
    message.error(error.value);
  }
};

const openSrtModal = () => {
  srtModalVisible.value = true;
};

const handleSrtModalOk = () => {
  srtModalVisible.value = false;
};

const handleSelectSrt = (srtList) => {
  srtItems.value = srtList.items;
  srtModalVisible.value = false;
  message.success(`Loaded ${srtItems.value.length} subtitles`);
};

const renderVideo = async () => {
  if (!canRender.value) return;

  isProcessing.value = true;
  progress.value = 0;
  error.value = '';
  processingStatus.value = 'Preparing to render video...';

  try {
    // Set up event listener for progress updates
    const progressListener = (event, data) => {
      if (data.code === 0) {
        processingStatus.value = data.data;

        // Try to extract progress percentage from status message
        const progressMatch = data.data.match(/(\d+)\/(\d+)/);
        if (progressMatch && progressMatch.length === 3) {
          const current = parseInt(progressMatch[1]);
          const total = parseInt(progressMatch[2]);
          progress.value = Math.floor((current / total) * 100);
        }
      } else if (data.code === 1) {
        error.value = data.data;
      }
    };

    // Add event listener
    window.electronAPI.onRenderVideoProgress(progressListener);

    // Call the render function
    const result = await window.electronAPI.renderVideoWithSrt({
      srtArray: JSON.parse(JSON.stringify(srtItems.value)),
      videoPath: videoFile.value.path,
      outputDir: outputDir.value || undefined,
      subtitleStyle: subtitleStyle.value,
      addLogo: addLogo.value,
      logoPath: logoPath.value || undefined,
      addText: addTextOverlay.value ? textOverlay.value : '',
      textPosition: textPosition.value,
      videoCodec: videoCodec.value,
      audioBitrate: audioBitrate.value.toString()
    });

    // Remove event listener
    window.electronAPI.removeRenderVideoProgress(progressListener);

    if (result.success) {
      outputPath.value = result.outputPath;
      message.success(result.message || 'Video rendered successfully!');
      progress.value = 100;
      processingStatus.value = 'Rendering complete!';
    } else {
      error.value = result.error || 'Unknown error occurred';
      message.error('Error rendering video: ' + error.value);
    }
  } catch (err) {
    error.value = `Error rendering video: ${err.message}`;
    message.error(error.value);
  } finally {
    isProcessing.value = false;
  }
};

const openOutputFile = async () => {
  if (!outputPath.value) return;

  try {
    await window.electronAPI.openFile(outputPath.value);
  } catch (err) {
    message.error(`Error opening file: ${err.message}`);
  }
};

const openOutputFolder = async () => {
  if (!outputPath.value) return;

  try {
    const folderPath = outputPath.value.substring(0, outputPath.value.lastIndexOf('\\'));
    await window.electronAPI.openFolder(folderPath);
  } catch (err) {
    message.error(`Error opening folder: ${err.message}`);
  }
};
</script>
