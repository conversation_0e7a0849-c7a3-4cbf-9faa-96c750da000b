<template>
  <div class="text-overlay absolute inset-0 pointer-events-none">
    <div
      v-if="isVisible"
      class="text-element absolute transition-all duration-300"
      :style="textStyle"
      @click.stop="handleTextClick"
    >
      {{ layer.properties.text }}
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  layer: {
    type: Object,
    required: true
  },
  currentTime: {
    type: Number,
    required: true
  },
  videoDimensions: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['text-click'])

// Check if text should be visible at current time
const isVisible = computed(() => {
  if (!props.layer.enabled) return false

  const { timeRange } = props.layer
  return props.currentTime >= timeRange.start && props.currentTime <= timeRange.end
})

// Calculate text styling
const textStyle = computed(() => {
  if (!isVisible.value) return { display: 'none' }

  const { properties } = props.layer
  const { width, height } = props.videoDimensions

  // Use percentage positioning for responsive scaling
  const xPercent = properties.position.x
  const yPercent = properties.position.y

  // Calculate font size relative to video size
  const scaleFactor = Math.min(width / 1920, height / 1080)
  const fontSize = properties.fontSize * scaleFactor

  // Animation based on time
  const timeInLayer = props.currentTime - props.layer.timeRange.start
  const layerDuration = props.layer.timeRange.end - props.layer.timeRange.start
  const progress = Math.min(timeInLayer / layerDuration, 1)

  // Apply animation
  let animationTransform = ''
  let animationOpacity = props.layer.opacity / 100

  if (properties.animation) {
    switch (properties.animation.type) {
      case 'fade':
        if (timeInLayer < properties.animation.duration) {
          animationOpacity *= timeInLayer / properties.animation.duration
        } else if (timeInLayer > layerDuration - properties.animation.duration) {
          animationOpacity *= (layerDuration - timeInLayer) / properties.animation.duration
        }
        break

      case 'slide':
        if (timeInLayer < properties.animation.duration) {
          const slideProgress = timeInLayer / properties.animation.duration
          animationTransform += ` translateX(${(1 - slideProgress) * -100}px)`
        }
        break

      case 'typewriter':
        const visibleChars = Math.floor(progress * properties.text.length)
        // This would need special handling in the template
        break

      case 'bounce':
        if (timeInLayer < properties.animation.duration) {
          const bounceProgress = timeInLayer / properties.animation.duration
          const bounce = Math.sin(bounceProgress * Math.PI * 4) * (1 - bounceProgress) * 10
          animationTransform += ` translateY(${bounce}px)`
        }
        break
    }
  }

  return {
    left: xPercent + '%',
    top: yPercent + '%',
    position: 'absolute',
    fontSize: fontSize + 'px',
    fontFamily: properties.fontFamily || 'Arial',
    color: properties.color || '#ffffff',
    fontWeight: properties.bold ? 'bold' : 'normal',
    fontStyle: properties.italic ? 'italic' : 'normal',
    textDecoration: properties.underline ? 'underline' : 'none',
    textAlign: properties.alignment || 'center',
    textShadow: properties.shadow?.enabled ?
      `${properties.shadow.offsetX}px ${properties.shadow.offsetY}px ${properties.shadow.blur}px ${properties.shadow.color}` : 'none',
    transform: `translate(-50%, -50%) ${animationTransform}`,
    transformOrigin: 'center',
    pointerEvents: 'auto',
    cursor: 'pointer',
    zIndex: 20,
    opacity: animationOpacity,
    transition: 'all 0.1s ease',
    whiteSpace: 'pre-wrap',
    maxWidth: '80%',
    wordWrap: 'break-word'
  }
})

const handleTextClick = () => {
  emit('text-click', props.layer)
}
</script>

<style scoped>
.text-element:hover {
  filter: brightness(1.1);
  transform: translate(-50%, -50%) scale(1.05);
}
</style>
