<template>
  <div class="timeline-toolbar flex items-center justify-between p-2 bg-gray-800 border-b border-gray-600 text-sm">
    <!-- Left side - Timeline info -->
    <div class="flex items-center gap-4">
      <div class="flex items-center gap-2">
        <span class="text-gray-400">Duration:</span>
        <span class="text-white font-mono">{{ formatTime(timelineStore.duration) }}</span>
      </div>

      <div class="flex items-center gap-2">
        <span class="text-gray-400">Items:</span>
        <span class="text-white">{{ subtitleItems.length }}</span>
      </div>
      <div class="flex items-center gap-2">
        <span class="text-gray-400">Voice:</span>
        <span class="text-white" :class="{ 'text-green-500': audioProgress >= 100 }">{{ audioProgress }}%</span>
      </div>
      <div class="flex items-center gap-2">
        <span class="text-gray-400">Translated:</span>
        <span class="text-white" :class="{ 'text-green-500': translatedText >= 100 }">{{ translatedText }}%</span>
      </div>

      <div v-if="timelineStore.selectedItems.length > 0" class="flex items-center gap-2">
        <span class="text-gray-400">Selected:</span>
        <span class="text-blue-400">{{ timelineStore.selectedItems.length }}</span>
      </div>
    </div>

    <!-- Center - Playback controls -->
    <div class="flex items-center gap-2">
      <button
        @click="seekToStart"
        class="p-1 hover:bg-gray-700 rounded"
        title="Go to Start"
      >
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polygon points="19,20 9,12 19,4"/>
          <line x1="5" y1="19" x2="5" y2="5"/>
        </svg>
      </button>

      <button
        @click="seekBackward"
        class="p-1 hover:bg-gray-700 rounded"
        title="Seek Backward (5s)"
      >
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polygon points="11,19 2,12 11,5"/>
          <polygon points="22,19 13,12 22,5"/>
        </svg>
      </button>

      <button
        @click="togglePlayPause"
        class="p-2 hover:bg-gray-700 rounded bg-blue-600"
        :title="state.videoElement?.paused  == false ? 'Pause' : 'Play'"
      >
        <svg v-if="!state.videoElement?.paused == false" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polygon points="5,3 19,12 5,21"/>
        </svg>
        <svg v-else width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <rect x="6" y="4" width="4" height="16"/>
          <rect x="14" y="4" width="4" height="16"/>
        </svg>
      </button>

      <button
        @click="seekForward"
        class="p-1 hover:bg-gray-700 rounded"
        title="Seek Forward (5s)"
      >
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polygon points="13,19 22,12 13,5"/>
          <polygon points="2,19 11,12 2,5"/>
        </svg>
      </button>

      <button
        @click="seekToEnd"
        class="p-1 hover:bg-gray-700 rounded"
        title="Go to End"
      >
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polygon points="5,4 15,12 5,20"/>
          <line x1="19" y1="5" x2="19" y2="19"/>
        </svg>
      </button>


    </div>

    <!-- Right side - View controls -->
    <div class="flex items-center gap-2">
      <!-- Time display -->
      <div class="flex items-center gap-1 font-mono text-xs">
        <span class="text-gray-400">Time:</span>
        <span class="text-white bg-gray-900 px-2 py-1 rounded">
          {{ formatTime(timelineStore.currentTime) }}
        </span>
      </div>

      <div class="w-px h-4 bg-gray-600"></div>

      <!-- Grid size control -->
      <div class="flex items-center gap-1">
        <span class="text-gray-400 text-xs">Grid:</span>
        <select
          v-model="timelineStore.gridSize"
          class="bg-gray-700 text-white text-xs px-2 py-1 rounded border border-gray-600"
        >
          <option value="0.1">100ms</option>
          <option value="0.5">500ms</option>
          <option value="1">1s</option>
          <option value="5">5s</option>
          <option value="10">10s</option>
        </select>
      </div>

      <!-- Snap toggle -->
      <button
        @click="timelineStore.toggleSnapToGrid()"
        :class="[
          'p-1 rounded text-xs',
          timelineStore.snapToGrid ? 'bg-blue-600 text-white' : 'hover:bg-gray-700 text-gray-400'
        ]"
        title="Toggle Snap to Grid"
      >
        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
          <line x1="9" y1="9" x2="9" y2="15"/>
          <line x1="15" y1="9" x2="15" y2="15"/>
          <line x1="9" y1="9" x2="15" y2="9"/>
          <line x1="9" y1="15" x2="15" y2="15"/>
        </svg>
      </button>

      <!-- Zoom controls -->
      <div class="flex items-center gap-1">
        <button
          @click="timelineStore.zoomOut()"
          class="p-1 hover:bg-gray-700 rounded text-xs"
          title="Zoom Out"
        >
          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="11" cy="11" r="8"/>
            <path d="M21 21l-4.35-4.35"/>
            <line x1="8" y1="11" x2="14" y2="11"/>
          </svg>
        </button>

        <span class="text-xs text-gray-400 min-w-[35px] text-center">
          {{ Math.round(timelineStore.zoom * 100) }}%
        </span>

        <button
          @click="timelineStore.zoomIn()"
          class="p-1 hover:bg-gray-700 rounded text-xs"
          title="Zoom In"
        >
          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="11" cy="11" r="8"/>
            <path d="M21 21l-4.35-4.35"/>
            <line x1="8" y1="11" x2="14" y2="11"/>
            <line x1="11" y1="8" x2="11" y2="14"/>
          </svg>
        </button>

        <button
          @click="timelineStore.fitToView()"
          class="p-1 hover:bg-gray-700 rounded text-xs"
          title="Fit to View"
        >
          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"/>
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, watch } from 'vue'
import { useTimelineStore } from '@/stores/timeline-store'
import { useTTSStore } from '@/stores/ttsStore'
import { state } from '@/lib/state'

const timelineStore = useTimelineStore()
const ttsStore = useTTSStore()
const audioProgress = ref(0)
const translatedText = ref(0)
const countAll = ref(0)

// Computed
const subtitleItems = computed(() => timelineStore.subtitleItems)
const isPlaying = ref(state.videoElement?.paused)

// Methods
const formatTime = (seconds) => {
  if (!seconds || isNaN(seconds)) return '00:00.000'

  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  const ms = Math.floor((seconds % 1) * 1000)

  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(3, '0')}`
}

const togglePlayPause = () => {
  const video = state.videoElement
  if (video) {
    if (video.paused) {
      video.play()
    } else {
      video.pause()
    }
  }
}

const seekToStart = () => {
  timelineStore.setCurrentTime(0)
}

const seekToEnd = () => {
  timelineStore.setCurrentTime(timelineStore.duration)
}

const seekBackward = () => {
  const newTime = Math.max(0, timelineStore.currentTime - 5)
  timelineStore.setCurrentTime(newTime)
}

const seekForward = () => {
  const newTime = Math.min(timelineStore.duration, timelineStore.currentTime + 5)
  timelineStore.setCurrentTime(newTime)
}


watch(() => ttsStore.currentSrtList?.items, (val) => {
  if (val) {
    const translated = val.filter(item => item.status === 'translated').length;
    const total = val.length;
    translatedText.value = Math.floor((translated / total) * 100);
    const generated = val.filter(item => item.isVoice && item.isVoice > 0 && item[`isGenerated${item.isVoice}`]).length;
    audioProgress.value = Math.floor((generated / total) * 100);
    countAll.value = total;
  }
})


</script>

<style scoped>
.timeline-toolbar {
  user-select: none;
}

select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}
</style>
