async function fetchCapcutWeb() {
   const response = await fetch("https://edit-api-sg.capcut.com/lv/v2/intelligence/create?aid=348188&device_platform=web&region=SG&web_id=7493795276948899345", {
  "headers": {
    "accept": "application/json, text/plain, */*",
    "accept-language": "en-US,en;q=0.9,vi;q=0.8",
    "appvr": "5.8.0",
    "cache-control": "no-cache",
    "content-type": "application/json",
    "device-time": "1747490675",
    "pf": "7",
    "pragma": "no-cache",
    "priority": "u=1, i",
    "sec-ch-ua": "\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-site",
    "sign": "fb5fce693abaf8b94f0dcaded69c9e67",
    "sign-ver": "1",
    "cookie": "passport_csrf_token=15f7c620d117ba26dbabaaaf8f65e75f; passport_csrf_token_default=15f7c620d117ba26dbabaaaf8f65e75f; sid_guard=de3f8d54767d09ffd268aac8c3da3686%7C1744785973%7C34559999%7CThu%2C+21-May-2026+06%3A46%3A12+GMT; uid_tt=377c9fb0eba3e8032f83a95e0df74964e0d7710a8dd14570d80933ea9b54d1ef; uid_tt_ss=377c9fb0eba3e8032f83a95e0df74964e0d7710a8dd14570d80933ea9b54d1ef; sid_tt=de3f8d54767d09ffd268aac8c3da3686; sessionid=de3f8d54767d09ffd268aac8c3da3686; sessionid_ss=de3f8d54767d09ffd268aac8c3da3686; sid_ucp_v1=1.0.0-KDUyYjk2NGJhZmRiZjE0ODAzMWJlOWZmN2EzMWE3OTc3MTgwNDIzNTEKIAiCiLD0soDZgGUQtaT9vwYYnKAVIAwwq8iFqAY4CEASEAMaA3NnMSIgZGUzZjhkNTQ3NjdkMDlmZmQyNjhhYWM4YzNkYTM2ODY; ssid_ucp_v1=1.0.0-KDUyYjk2NGJhZmRiZjE0ODAzMWJlOWZmN2EzMWE3OTc3MTgwNDIzNTEKIAiCiLD0soDZgGUQtaT9vwYYnKAVIAwwq8iFqAY4CEASEAMaA3NnMSIgZGUzZjhkNTQ3NjdkMDlmZmQyNjhhYWM4YzNkYTM2ODY; store-idc=alisg; store-country-code=sg; store-country-code-src=uid; ttwid=1|BKGUoCKw00YUUTF0Bi6zqcoKkfChu91OC11wUyM8UtY|1747490507|114aac6ad4a55d0c7a849b13f7ac8b0bdafd68b9a1b011073d92a9fce797b760; odin_tt=18fa2e87b0063dd25e7768a6ad14d6b11c15de180cccd913581609ea72d36fb6e101fadff03f0c0f05bae0f73392398ec904a31655b84b01142fa2b3d7a76efa; msToken=S0OmwyeeBQvcRTBsMBKw3FZ2ko6m8cqgzjwcWxRTSf0sIERUmsltMedGkO0YIFbIJESSAIsmRdbkW7UUT4vGE9lcBy1vfbvqC8KyHH9_uI4Svsw-l2r5VYoo3bSO",
    "Referer": "https://www.capcut.com/",
    "Referrer-Policy": "strict-origin-when-cross-origin"
  },
  "body": "{\"workspace_id\":\"7278208573803446274\",\"smart_tool_type\":39,\"scene\":3,\"params\":\"{\\\"text\\\":\\\"Mặc định\\\",\\\"platform\\\":1}\",\"req_json\":\"{\\\"speaker\\\":\\\"BV421_vivn_streaming\\\",\\\"audio_config\\\":{},\\\"disable_caption\\\":true}\"}",
  "method": "POST"
});
return response.json();
}

fetchCapcutWeb().then(data => {
  console.log(data);
});