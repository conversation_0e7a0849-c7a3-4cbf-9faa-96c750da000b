// import en from './en';
import vi from './vi';



export const locales = {
  // en,
  vi
};

export const localeNames = {
  en: 'English',
  vi: 'Tiếng Việt'
};

/**
 * Dịch một chuỗi với các tham số
 * @param str Chuỗi cần dịch có thể chứa {param}
 * @param params Tham số để thay thế trong chuỗi
 */
export function translateWithParams(str, params = {}) {
  return str.replace(/{([^}]+)}/g, (_, key) => {
    const replacement = params[key];
    return replacement !== undefined ? String(replacement) : `{${key}}`;
  });
}

/**
 * Lấy giá trị từ một đường dẫn lồng nhau (vd: "common.buttons.save")
 * @param obj Đối tượng chứa dữ liệu đa ngôn ngữ
 * @param path Đường dẫn đến giá trị
 */
export function getNestedValue(obj, path) {
  return path.split('.').reduce((prev, curr) => {
    return prev && prev[curr] !== undefined ? prev[curr] : undefined;
  }, obj);
}

export default locales; 