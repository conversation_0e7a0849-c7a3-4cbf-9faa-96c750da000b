const path = require("path");
const fs = require("fs");
const { execWithLog,detectGPU } = require("./utils");





async function adjustSpeed(event, {
  input,
  speedPercent = 85,
  videoBitrateKbps = 2500,
  audioBitrateKbps = 128,
  processId = null
}) {
  const type = 'adjust-speed-res';
  if (!input || !fs.existsSync(input)) {
    console.error("Input file không tồn tại.");
    return { success: false, error: "Input file does not exist" };
  }

  try {
    const useGPU = await detectGPU();
    console.log('useGPU', useGPU);
    event.sender.send(type, { data: 'useGPU: ' + useGPU, code: 0 });


    // Tính toán các hệ số
    const videoRate = +(100 / speedPercent).toFixed(6);
    const audioRate = +(speedPercent / 100).toFixed(6);
    const audioFilter = `atempo=${audioRate}`;
    const filterComplex = `[0:v]setpts=${videoRate}*PTS[v];[0:a]${audioFilter}[a]`;

    // Tạo output file name
    const { dir, name, ext } = path.parse(input);
    const output = path.join(dir, `${name}-${speedPercent}${ext}`);

    // Chuẩn bị lệnh ffmpeg
    const ffmpegArgs = [
      "-i", input,
      "-filter_complex", filterComplex,
      "-map", "[v]",
      "-map", "[a]",
    ];

    if (useGPU) {
      ffmpegArgs.push(
        "-c:v", "h264_nvenc",
        "-preset", "p5",
        "-rc", "vbr",
        "-cq", "19",
        "-b:v", `${videoBitrateKbps}k`,
        "-maxrate", `${videoBitrateKbps}k`,
        "-bufsize", `${videoBitrateKbps}k`
      );
    } else {
      ffmpegArgs.push("-c:v", "libx264");
    }

    ffmpegArgs.push("-c:a", "aac", "-b:a", `${audioBitrateKbps}k`, output);

    console.log("Đang xử lý...");
    event.sender.send(type, { data: 'Đang xử lý...', code: 0 });

    // Start the process and get the process ID
    const result = await execWithLog.bind({ type })(event, "ffmpeg", ffmpegArgs, processId);
    console.log('Process started:', result);

    // Return the process ID and output path
    return {
      success: true,
      processId: result.processId,
      status: 'started',
      outputPath: output
    };
  } catch (err) {
    console.error("❌ Lỗi khi chạy ffmpeg:", err.message);
    event.sender.send(type, { data: 'Lỗi khi chạy ffmpeg: ' + err.message, code: 1 });
    return {
      success: false,
      error: err.message || "Unknown error occurred"
    };
  }
}

// Nếu gọi trực tiếp từ command line
if (require.main === module) {
  const [
    ,
    ,
    input,
    speedPercent,
    useGPU,
    videoBitrateKbps,
    audioBitrateKbps,
  ] = process.argv;

  adjustSpeed(null,{
    input,
    speedPercent: speedPercent ? Number(speedPercent) : undefined,
    useGPU,
    videoBitrateKbps: videoBitrateKbps
      ? Number(videoBitrateKbps)
      : undefined,
    audioBitrateKbps: audioBitrateKbps
      ? Number(audioBitrateKbps)
      : undefined,
  });
}

module.exports = adjustSpeed;
