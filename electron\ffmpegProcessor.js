const { ffmpeg } = require('./ffmpeg-config');
const fs = require('fs-extra');
const path = require('path');

async function getAudioDuration(audioUrl) {
  return new Promise((resolve, reject) => {
    // Remove 'file://' prefix and normalize path
    const audioPath = audioUrl.replace(/^file:\/\//, '').replace(/\\/g, '/');
    ffmpeg.ffprobe(audioPath, (err, metadata) => {
      if (err) return reject(err);
      const duration = metadata.format.duration || 0;
      resolve(duration);
    });
  });
}

async function generateAssFile(srtItem, index, outputDir, style = 'white') {
  const assPath = path.join(outputDir, `subtitle_${index}.ass`);
  const backgroundColor = style === 'yellow' ? '&H00FFFF' : '&HFFFFFF';
  const assContent = `
[Script Info]
Title: Subtitle ${index}
ScriptType: v4.00+
Collisions: Normal
PlayDepth: 0

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: Default,Arial,20,&H000000,&H000000,&H000000,${backgroundColor},-1,0,0,0,100,100,0,0,1,1,0,2,10,10,10,1

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
Dialogue: 0,${srtItem.start},${srtItem.end},Default,,0,0,0,,${srtItem.translatedText}
`;

  await fs.writeFile(assPath, assContent);
  return assPath;
}

async function cutVideoSegment(videoPath, start, end, outputPath) {
  return new Promise((resolve, reject) => {
    ffmpeg(videoPath)
      .setStartTime(start)
      .setDuration(end - start)
      .output(outputPath)
      .on('end', () => resolve(outputPath))
      .on('error', (err) => reject(err))
      .run();
  });
}

async function adjustVideoSpeed(inputVideo, targetDuration, outputVideo) {
  const videoDuration = await new Promise((resolve, reject) => {
    ffmpeg.ffprobe(inputVideo, (err, metadata) => {
      if (err) return reject(err);
      resolve(metadata.format.duration);
    });
  });

  const speed = videoDuration / targetDuration;
  return new Promise((resolve, reject) => {
    ffmpeg(inputVideo)
      .videoFilter(`setpts=${1 / speed}*PTS`)
      .output(outputVideo)
      .on('end', () => resolve(outputVideo))
      .on('error', (err) => reject(err))
      .run();
  });
}

async function mergeAudioVideo(videoPath, audioPath, outputPath, subtitlePath, options = {}) {
  const { customText, logoPath, thumbnailPath } = options;
  const filters = [];

  // Add subtitles
  if (subtitlePath) {
    // filters.push(`subtitles=${subtitlePath.replace(/\\/g, '\\\\').replace(/:/g, '\\:')}`);
  }

  // Add custom text
  if (customText) {
    filters.push(`drawtext=text='${customText}':fontcolor=white:fontsize=24:x=10:y=10`);
  }

  // Add logo
  if (logoPath) {
    filters.push(`[0:v][1:v]overlay=10:10`);
  }

  // Add thumbnail (as overlay at start)
  if (thumbnailPath) {
    filters.push(`[0:v][2:v]overlay=0:0:enable='between(t,0,5)'`);
  }

  return new Promise((resolve, reject) => {
    const command = ffmpeg(videoPath)
      .input(audioPath)
    //   .complexFilter(filters)
      .outputOptions('-c:v libx264')
      .outputOptions('-c:a aac')
      .output(outputPath);

    if (logoPath) command.input(logoPath);
    if (thumbnailPath) command.input(thumbnailPath);

    command
      .on('end', () => resolve(outputPath))
      .on('error', (err) => reject(err))
      .run();
  });
}

async function concatenateVideos(videoPaths, outputVideo) {
  const concatListPath = path.join(path.dirname(outputVideo), 'concat_list.txt');
  const concatContent = videoPaths.map(p => `file '${p.replace(/\\/g, '/')}'`).join('\n');
  await fs.writeFile(concatListPath, concatContent);

  return new Promise((resolve, reject) => {
    ffmpeg()
      .input(concatListPath)
      .inputOptions('-f concat')
      .inputOptions('-safe 0')
      .outputOptions('-c copy')
      .output(outputVideo)
      .on('end', () => resolve(outputVideo))
      .on('error', (err) => reject(err))
      .run();
  });
}

async function processSrtAndVideo({ srtArray, videoPath, outputDir, outputVideo, subtitleStyle, customText, logoPath, thumbnailPath }) {
  // Ensure output directory exists
  await fs.ensureDir(outputDir);

  // Process each SRT item
  const processedVideos = [];
  for (const [index, item] of srtArray.entries()) {
    // Get audio duration
    const audioDuration = await getAudioDuration(item.audioUrl);
    item.duration = audioDuration;

    // Calculate video segment duration
    const srtDuration = item.endTime - item.startTime;
    const segmentDuration = Math.max(srtDuration, audioDuration);

    // Cut video segment
    const tempVideo = path.join(outputDir, `segment_${index}.mp4`);
    await cutVideoSegment(videoPath, item.startTime, item.startTime + srtDuration, tempVideo);

    // Adjust video speed if audio is longer
    let adjustedVideo = tempVideo;
    if (audioDuration > srtDuration) {
      adjustedVideo = path.join(outputDir, `segment_adjusted_${index}.mp4`);
      await adjustVideoSpeed(tempVideo, audioDuration, adjustedVideo);
    }

    // Generate ASS subtitle
    const subtitlePath = await generateAssFile(item, index, outputDir, subtitleStyle);

    // Merge audio, video, and subtitle
    const mergedVideo = path.join(outputDir, `segment_merged_${index}.mp4`);
    await mergeAudioVideo(adjustedVideo, item.audioUrl.replace(/^file:\/\//, ''), mergedVideo, subtitlePath, {
      customText,
      logoPath,
      thumbnailPath,
    });

    processedVideos.push(mergedVideo);
  }

  // Concatenate all segments
  await concatenateVideos(processedVideos, outputVideo);

  // Clean up temporary files (optional)
  // await fs.remove(outputDir);
}

module.exports = { processSrtAndVideo, getAudioDuration };