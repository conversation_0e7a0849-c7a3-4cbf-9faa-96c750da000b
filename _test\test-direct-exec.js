const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// The exact command that's failing
const executablePath = path.normalize('G:/CODE/Capcut-TTS-app/capcut-tts-app/static/merge_audio_v2.exe');
const audioFilesJsonPath = path.normalize('F:/ReviewDao/11-05/ran-quy/test_audio/temp/audio_files.json');
const subsJsonPath = path.normalize('F:/ReviewDao/11-05/ran-quy/test_audio/temp/subs.json');
const outputPath = path.normalize('F:/ReviewDao/11-05/ran-quy/test_audio/combined-audio.mp3');

// Check if files exist
console.log('Checking if files exist:');
console.log(`Executable: ${executablePath} - ${fs.existsSync(executablePath) ? 'Exists' : 'Not found'}`);
console.log(`Audio files JSON: ${audioFilesJsonPath} - ${fs.existsSync(audioFilesJsonPath) ? 'Exists' : 'Not found'}`);
console.log(`Subs JSON: ${subsJsonPath} - ${fs.existsSync(subsJsonPath) ? 'Exists' : 'Not found'}`);
console.log(`Output directory: ${path.dirname(outputPath)} - ${fs.existsSync(path.dirname(outputPath)) ? 'Exists' : 'Not found'}`);

// Try using execSync
console.log('\n--- Using execSync ---');
const cmdArgs = [audioFilesJsonPath, subsJsonPath, outputPath].map(arg => `"${arg}"`).join(' ');
const command = `"${executablePath}" ${cmdArgs}`;
console.log('Command:', command);

try {
  const result = execSync(command, {
    encoding: 'utf8',
    env: { ...process.env, PYTHONIOENCODING: 'utf-8' }
  });
  console.log('Command output:', result);
  console.log('Success!');
} catch (error) {
  console.error('Error:', error.message);
  if (error.stderr) {
    console.error('stderr:', error.stderr);
  }
  if (error.stdout) {
    console.error('stdout:', error.stdout);
  }
}
