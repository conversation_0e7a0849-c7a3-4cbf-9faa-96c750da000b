const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const { get_path_bin } = require('./utils');
const { getAudioDuration, trimAudio } = require('./ffmpegHandler');

async function mergeAudioFiles(event,audioFiles, outputPath, subs, totalDuration = null) {
    console.log('Đang ghép các file âm thanh bằng pydub...');
    const type = 'merge-audio-files-res';
    event.sender.send(type, { data: 'Đang ghép các file âm thanh bằng pydub...', code: 0 });

    // Tạo thư mục tạm thời để lưu file JSON
    const tempDir = path.join(path.dirname(outputPath), 'temp');
    if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
    }
    // get duration of audio file and trim if trim > 0
    for (let i = 0; i < audioFiles.length; i++) {
        const audioFile = audioFiles[i];
        if (subs[i].trim > 0) {
            const duration = await getAudioDuration(audioFile);
            const trim = subs[i].trim;
            const newDuration = duration - trim;
            if (newDuration > 0) {
                const newAudioFile = path.join(tempDir, `trimmed_${i}.mp3`);
                await trimAudio(audioFile, newDuration, newAudioFile);
                audioFiles[i] = newAudioFile;
            } else {
                audioFiles.splice(i, 1);
                subs.splice(i, 1);
                i--;
            }
        }
    }
    // Lưu danh sách audioFiles và subs vào file tạm thời
    const audioFilesJsonPath = path.join(tempDir, 'audio_files.json');
    const subsJsonPath = path.join(tempDir, 'subs.json');

    // Write audio files JSON
    fs.writeFileSync(audioFilesJsonPath, JSON.stringify(audioFiles, null, 2));

    // Process subs to handle Vietnamese characters
    // Replace Vietnamese characters that might cause encoding issues
    // const processedSubs = JSON.parse(JSON.stringify(subs)).map(sub => {
    //     if (sub.text) {
    //         // Replace Vietnamese characters with ASCII equivalents
    //         sub.text = sub.text
    //             .replace(/Đ/g, 'D')
    //             .replace(/đ/g, 'd')
    //             .replace(/[áàảãạâấầẩẫậăắằẳẵặ]/g, 'a')
    //             .replace(/[ÁÀẢÃẠÂẤẦẨẪẬĂẮẰẲẴẶ]/g, 'A')
    //             .replace(/[éèẻẽẹêếềểễệ]/g, 'e')
    //             .replace(/[ÉÈẺẼẸÊẾỀỂỄỆ]/g, 'E')
    //             .replace(/[íìỉĩị]/g, 'i')
    //             .replace(/[ÍÌỈĨỊ]/g, 'I')
    //             .replace(/[óòỏõọôốồổỗộơớờởỡợ]/g, 'o')
    //             .replace(/[ÓÒỎÕỌÔỐỒỔỖỘƠỚỜỞỠỢ]/g, 'O')
    //             .replace(/[úùủũụưứừửữự]/g, 'u')
    //             .replace(/[ÚÙỦŨỤƯỨỪỬỮỰ]/g, 'U')
    //             .replace(/[ýỳỷỹỵ]/g, 'y')
    //             .replace(/[ÝỲỶỸỴ]/g, 'Y');
    //     }
    //     return sub;
    // });




    // Write processed subs JSON
    fs.writeFileSync(subsJsonPath, JSON.stringify(subs, null, 2));

    // Tạo lệnh gọi script Python với đường dẫn file JSON
    // let command = `node audioMerger-v3.js "${audioFilesJsonPath}" "${subsJsonPath}" "${outputPath}"`;
    let command = [audioFilesJsonPath, subsJsonPath, outputPath];

    if (totalDuration) {
        command.push(totalDuration);
    }

    try {
        await exec_path_data.bind({ type })(event, command);
        console.log(`Đã ghép file âm thanh theo thời gian phụ đề vào: ${outputPath}`);
        // Dọn dẹp file tạm thời
        // fs.rmSync(tempDir, { recursive: true, force: true });
        return outputPath;
    } catch (error) {
        console.error('Lỗi khi ghép file âm thanh bằng pydub:', error);
        // Dọn dẹp file tạm thời nếu có lỗi
        if (fs.existsSync(tempDir)) {
            // fs.rmSync(tempDir, { recursive: true, force: true });
        }
        throw error;
    }
}

async function exec_path_data(event, args) {
    const { type = 'merge-audio-files-res' } = this;
    return new Promise((resolve, reject) => {
        const executablePath = get_path_bin('merge_audio_v2');
        const child = spawn(executablePath, args);
        child.stdout.on('data', (data) => {
            // event.reply('video-task', data.toString());
            console.log('stdout', data.toString());
            // event.reply('upscayle-img-task-res', { data: data.toString(), code: 1 });
            event?.reply?.(type, { data: data.toString(), code: 0 });
            event?.sender?.send(type, { data: data.toString(), code: 0 });
        });

        child.stderr.on('data', (data) => {
            // event.reply('video-task', data.toString());
            console.log('stderr',data.toString());
            // event.reply('upscayle-img-task-res', { data: data.toString(), code: 1 });
            event?.reply?.(type, { data: data.toString(), code: 0 });
            event?.sender?.send(type, { data: data.toString(), code: 0 });
        });

        child.on('exit', (code) => {
            if (code !== 0) {
                // event.reply('video-task', `child process exited with code ${code}`);
                event?.reply?.(type, { data: `child process exited with code ${code}`, code: 1 });
                event?.sender?.send(type, { data: `child process exited with code ${code}`, code: 1 });
                console.log(`child process exited with code ${code}`);
                reject(new Error(`child process exited with code ${code}`));
            } else {
                // event.reply('video-task', 'video split successfully executed');
                event?.reply?.(type, { data: 'video split successfully executed', code: 0 });
                event?.sender?.send(type, { data: 'video split successfully executed', code: 0 });
                console.log('video split successfully executed');
                resolve('video split successfully executed');
            }
        });

        child.on('error', (error) => {
            // event.reply('video-task', `Spawn error: ${error.message}`);
            event?.reply?.(type, { data: `Spawn error: ${error.message}`, code: 1 });
            event?.sender?.send(type, { data: `Spawn error: ${error.message}`, code: 1 });
            console.log(`Spawn error: ${error.message}`);
            reject(error);
        });
    });
}


module.exports = {
    mergeAudioFiles
};