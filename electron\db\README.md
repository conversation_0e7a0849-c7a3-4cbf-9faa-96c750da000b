# Novel Extraction Database

This database system stores extracted novel chapters and book information from web scraping operations.

## Database Schema

### Books Table
Stores information about novels/books:

```sql
CREATE TABLE books (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title VARCHAR(500) NOT NULL,
    author <PERSON><PERSON><PERSON><PERSON>(200),
    description TEXT,
    source_url VARCHAR(1000) NOT NULL UNIQUE,
    base_url VARCHAR(1000),
    cover_image VARCHAR(1000),
    status VARCHAR(50) DEFAULT 'ongoing',
    total_chapters INTEGER DEFAULT 0,
    extracted_chapters INTEGER DEFAULT 0,
    extraction_session VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_extraction_at TIMESTAMP
);
```

### Chapters Table
Stores individual chapter information:

```sql
CREATE TABLE chapters (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    book_id INTEGER,
    title VARCHAR(500) NOT NULL,
    href VARCHAR(500) NOT NULL,
    text TEXT,
    full_url VARCHAR(1000) NOT NULL UNIQUE,
    chapter_index INTEGER NOT NULL,
    page_number INTEGER,
    source_url VARCHAR(1000),
    book_title VARCHAR(500),
    extraction_session VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Usage

### Initialize Database

```javascript
const Database = require('./db/index');
const db = new Database('./data/novels.db');
await db.initializeTables();
```

### Save Extraction Results

```javascript
// Example extraction data
const extractionData = {
    chapters: [
        {
            index: 1,
            title: "第1章 神奇体彩店",
            href: "39705235.html",
            fullUrl: "https://44xw.com/a/149/148289/39705235.html",
            pageNumber: 1,
            sourceUrl: "https://44xw.com/a/149/148289/"
        }
        // ... more chapters
    ],
    totalChapters: 100,
    totalPages: 5
};

// Book information
const bookInfo = {
    title: "消失20年，我归来即最强天师",
    author: "Unknown Author",
    sourceUrl: "https://44xw.com/a/149/148289/",
    status: "ongoing"
};

// Save to database
const result = await db.saveExtractionResults(extractionData, bookInfo);
console.log(`Saved ${result.chaptersInserted} chapters with session ID: ${result.sessionId}`);
```

### Query Data

```javascript
// Get all books
const books = await db.books.getAllBooks();

// Get chapters for a specific book
const chapters = await db.chapters.getChaptersByBookId(bookId);

// Search books by title
const searchResults = await db.books.searchBooksByTitle("神奇");

// Get extraction statistics
const stats = await db.getExtractionStats();
```

## Command Line Interface

Use the database manager CLI to interact with the database:

```bash
# List all books
node electron/db/db-manager.js books

# List chapters for a specific book
node electron/db/db-manager.js chapters 1

# Show extraction sessions
node electron/db/db-manager.js sessions

# Show database statistics
node electron/db/db-manager.js stats

# Search books by title
node electron/db/db-manager.js search "神奇"

# Delete a book and its chapters
node electron/db/db-manager.js delete-book 1

# Delete an extraction session
node electron/db/db-manager.js delete-session extraction_1234567890_abc123
```

## Integration with Extraction

The `testMultiPageExtraction` function automatically saves extracted data to the database:

```javascript
const { testMultiPageExtraction } = require('./playwright/puppeteerService');

// Initialize database globally
global.S = { db: new Database('./data/novels.db') };
await S.db.initializeTables();

// Run extraction with database save
const result = await testMultiPageExtraction(
    null, 
    'https://44xw.com/a/149/148289/', 
    3,  // max pages
    true // save to database
);

console.log(`Saved ${result.databaseResult.chaptersInserted} chapters`);
```

## Database Features

### Automatic Deduplication
- Books are deduplicated by `source_url`
- Chapters are deduplicated by `full_url`
- Duplicate entries are updated instead of creating new records

### Extraction Sessions
- Each extraction run gets a unique session ID
- Sessions help track and manage extraction batches
- Can delete entire sessions if needed

### Indexing
- Optimized indexes on frequently queried fields
- Fast searches by book ID, chapter index, page number
- Efficient title searches

### Error Handling
- Graceful handling of constraint violations
- Automatic fallback to individual inserts on batch failures
- Detailed error logging

## File Structure

```
electron/db/
├── index.js          # Main database class
├── books.js          # Books table operations
├── chapters.js       # Chapters table operations
├── db-manager.js     # CLI management tool
└── README.md         # This file

data/
└── novels.db         # SQLite database file (created automatically)
```

## Testing

Run the extraction test with database integration:

```bash
node electron/playwright/test-extraction.js
```

This will:
1. Initialize the database
2. Run extraction tests
3. Save results to database
4. Display statistics
5. Clean up connections

## Backup and Maintenance

### Backup Database
```bash
cp data/novels.db data/novels_backup_$(date +%Y%m%d).db
```

### View Database Size
```bash
ls -lh data/novels.db
```

### Vacuum Database (optimize)
```javascript
await db.knex.raw('VACUUM');
```

## Performance Considerations

- Use batch inserts for large datasets
- Regular VACUUM operations for optimal performance
- Consider pagination for large result sets
- Index maintenance for complex queries

## Database Schema Updates

If you encounter schema errors (missing columns), run the fix script:

```bash
# Quick fix for existing databases
node electron/db/fix-database.js

# Or run full migration
node electron/db/migrate.js

# Check database info
node electron/db/migrate.js info
```

### Common Schema Issues

**Error: "table books has no column named category"**
```bash
node electron/db/fix-database.js
```

**Error: "table books has no column named last_update_info"**
```bash
node electron/db/fix-database.js
```

## Error Recovery

If database becomes corrupted:
1. Stop all database operations
2. Restore from backup
3. Re-run failed extractions
4. Verify data integrity

If schema is outdated:
1. Run `node electron/db/fix-database.js`
2. Verify with `node electron/db/migrate.js info`
3. Test with extraction
