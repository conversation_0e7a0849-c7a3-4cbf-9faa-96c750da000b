const axios = require('axios').default;
const { <PERSON><PERSON><PERSON><PERSON> } = require('tough-cookie');
const { wrapper } = require('axios-cookiejar-support');
const { decode } = require('html-entities');

class AlibabaTranslator {
  constructor(srclang = 'auto', tgtlang = 'en', proxy = null, timeout = 10000) {
    this.srclang = srclang;
    this.tgtlang = tgtlang;
    this.proxy = proxy;
    this.timeout = timeout;
    this.csrf = null;
    this.client = null;
  }

  langmap() {
    return { cht: 'zh-tw' };
  }

  async initTranslator() {
    const jar = new CookieJar();
    this.client = wrapper(axios.create({
      jar,
      withCredentials: true,
      timeout: this.timeout,
      proxy: this.proxy || false,
    }));

    const headers = {
      'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
      'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 Chrome/105.0.0.0 Safari/537.36 Edg/105.0.1343.53',
      'cache-control': 'no-cache',
      'pragma': 'no-cache',
    };

    await this.client.get('https://translate.alibaba.com', { headers });

    const resp = await this.client.get('https://translate.alibaba.com/api/translate/csrftoken');
    this.csrf = resp.data.token;
  }

  async translate(text) {
    const headers = {
      'referer': 'https://translate.alibaba.com',
      'accept': '*/*',
      'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 Chrome/105.0.0.0 Safari/537.36 Edg/105.0.1343.53',
    };

    const params = {
      srcLang: this.srclang,
      tgtLang: this.tgtlang,
      domain: 'general',
      query: text,
      _csrf: this.csrf
    };

    const response = await this.client.post(
      'https://translate.alibaba.com/api/translate/text',
      null,
      { headers, params }
    );

    const data = response.data;

    if (!data?.data?.translateText) {
      throw new Error(JSON.stringify(data));
    }

    // Decode HTML entities like &#xxxx;
    return decode(data.data.translateText);
  }
}

(async () => {
  const ts = new AlibabaTranslator('zh', 'en');

  await ts.initTranslator();

  const result = await ts.translate('你好世界');
  console.log('Translation:', result);
})();
