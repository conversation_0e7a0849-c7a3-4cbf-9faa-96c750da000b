const { edgeTTSCmd } = require("./execHandler");
const { get_tts_generator_path } = require("./utils");

async function textToSpeech({ text, voice, rate, volume, pitch, outputFilename, outputSrtFile }) {
    const cmd = await get_tts_generator_path()
    await edgeTTSCmd(cmd, ['--voice', `${voice}`, '--rate', `+${rate}`, '--volume', `+${volume}`, '--pitch', `+${pitch}`, '-t', `${text}`, '--write-media', `${outputFilename}`, '--write-subtitles', `${outputSrtFile}`])
    return {
        success: true,
        outputFilename,
        outputSrtFile
    };
}

module.exports = {
    textToSpeech
};