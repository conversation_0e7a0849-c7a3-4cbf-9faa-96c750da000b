var F = Object.defineProperty;
var j = (a, t, e) => t in a ? F(a, t, { enumerable: !0, configurable: !0, writable: !0, value: e }) : a[t] = e;
var h = (a, t, e) => j(a, typeof t != "symbol" ? t + "" : t, e);
import { ipcMain as s, dialog as v, app as o, session as L, clipboard as P, BrowserWindow as w, protocol as q, screen as H, shell as S } from "electron";
import { createRequire as A } from "node:module";
import { fileURLToPath as M } from "node:url";
import p from "node:path";
import W from "node:os";
import y, { basename as B, join as $ } from "path";
import K from "fluent-ffmpeg";
import { exec as E } from "child_process";
import x from "request";
import N from "fs";
import { writeFileSync as z, mkdirSync as G, existsSync as J, createReadStream as Z, readFileSync as R, unlinkSync as Q } from "node:fs";
import X from "crypto";
import b from "node-machine-id";
import k from "dns";
import m from "winreg";
import _ from "asar";
function Y(a) {
  s.handle("showOpenDialogSync", async (t, e) => v.showOpenDialogSync(a, e)), s.handle("showMessageBoxSync", async (t, e) => v.showMessageBoxSync(a, e)), s.handle("showSaveDialogSync", async (t, e) => v.showSaveDialogSync(a, e));
}
class ee {
  constructor() {
    h(this, "_ffmpeg");
    h(this, "ffmpegPath");
    h(this, "ffprobePath");
    if (this._ffmpeg = K, o.isPackaged)
      this.ffmpegPath = y.join(process.resourcesPath, "ffmpeg", "ffmpeg"), this.ffprobePath = y.join(process.resourcesPath, "ffmpeg", "ffprobe");
    else {
      const t = o.getAppPath();
      this.ffmpegPath = y.join(t, "ffmpeg", "ffmpeg"), this.ffprobePath = y.join(t, "ffmpeg", "ffprobe");
    }
    this._ffmpeg.setFfmpegPath(this.ffmpegPath), this._ffmpeg.setFfprobePath(this.ffprobePath);
  }
  get ffmpeg() {
    return this._ffmpeg;
  }
  set ffmpeg(t) {
    this._ffmpeg = t;
  }
  executeFFmpegCommand(t) {
    return new Promise((e, n) => {
      t = t.replaceAll("_ffmpeg", this.ffmpegPath), t = t.replaceAll("_ffprobe", this.ffprobePath), console.log(t), E(t, (r, i, d) => {
        if (r) {
          n(`Error: ${r.message}`);
          return;
        }
        e(i);
      });
    });
  }
}
function te(a) {
  const t = new ee();
  s.handle("run-cmd-ffmpeg", async (e, n) => t.executeFFmpegCommand(n));
}
function ne(a) {
  return a.timeout || (a.timeout = 6e4), new Promise((t, e) => {
    x(a, function(n, r, i) {
      if (n)
        e(n);
      else {
        let d = JSON.parse(JSON.stringify(r));
        t(d);
      }
    });
  });
}
function V(a, t, e) {
  a.timeout || (a.timeout = 6e4);
  const n = N.createWriteStream(t);
  let r = 0, i = 0;
  return new Promise((d, f) => {
    x(a).on("response", (u) => {
      u.statusCode === 200 ? i = parseInt((u == null ? void 0 : u.headers["content-length"]) || "0", 10) : f(new Error(`Failed to download. Status code: ${u.statusCode}`));
    }).on("error", (u) => {
      f(u);
    }).on("data", (u) => {
      r += u.length;
      const O = r / i * 100;
      e(O);
    }).pipe(n).on("close", () => {
      d("");
    });
  });
}
function re(a) {
  s.handle("send-request", async (t, e) => ne(e)), s.on("request:download-file", async (t, e, n, r) => {
    try {
      await V(n, r, (i) => {
        t.sender.send(e, { status: "loading", percent: i });
      }), t.sender.send(e, { status: "success" });
    } catch (i) {
      t.sender.send(e, { status: "failure", message: i.message || "Unknown error" });
    }
  });
}
const { autoUpdater: c } = A(import.meta.url)("electron-updater");
function ae(a) {
  c.autoDownload = !1, c.disableWebInstaller = !1, c.allowDowngrade = !1, c.on("checking-for-update", function() {
  }), s.handle("check-update-app", async () => {
    if (!o.isPackaged) {
      if (process.env.VITE_DEV_SERVER_URL)
        return { update: !1, version: o.getVersion(), newVersion: null };
      throw new Error("Tính năng cập nhật chỉ khả dụng sau khi đóng gói.");
    }
    return new Promise((t, e) => {
      const n = setTimeout(() => {
        e(new Error("Đã xảy ra lỗi khi kiểm tra cập nhật"));
      }, 5e4);
      c.on("update-available", (r) => {
        clearTimeout(n), t({ update: !0, version: o.getVersion(), newVersion: r == null ? void 0 : r.version });
      }), c.on("update-not-available", (r) => {
        clearTimeout(n), t({ update: !1, version: o.getVersion(), newVersion: r == null ? void 0 : r.version });
      }), c.on("error", (r) => {
        clearTimeout(n), console.error("Lỗi khi kiểm tra bản cập nhật:", r), e(r);
      }), c.checkForUpdates();
    });
  }), s.handle("start-download", (t, e) => {
    se(
      (n, r) => {
        n ? t.sender.send(e, { error: n }) : t.sender.send(e, { percent: r == null ? void 0 : r.percent, progressInfo: r });
      },
      () => {
        t.sender.send(e, { percent: 100 });
      }
    );
  }), s.handle("quit-and-install", () => {
    c.quitAndInstall(!1, !0);
  });
}
function se(a, t) {
  c.on("download-progress", (e) => a(null, e)), c.on("error", (e) => a(e, null)), c.on("update-downloaded", t), c.downloadUpdate();
}
function oe(a) {
  s.handle("path:basename", async (t, e) => B(e)), s.handle("app:userData", async (t) => o.getPath("userData")), s.handle("path:join", async (t, ...e) => $(...e)), s.handle("fs:writeFileSync", async (t, e, n) => z(e, n)), s.handle("fs:mkdirSync", async (t, e) => G(e)), s.handle("fs:existsSync", async (t, e) => J(e)), s.handle("fs:createReadStream", async (t, e) => Z(e)), s.handle("fs:readFileSync", async (t, e, ...n) => R(e, ...n)), s.handle("fs:unlinkSync", async (t, e) => Q(e)), s.handle("crypto:hashMd5File", async (t, e) => {
    const n = R(e);
    return X.createHash("md5").update(n).digest("hex");
  });
}
class ie {
  constructor() {
    h(this, "platform", process.platform);
  }
  machineIdSync() {
    return b.machineIdSync();
  }
  lookup(t) {
    return new Promise((e, n) => {
      k.lookup(t, (r, i, d) => {
        r ? n(r) : e(i);
      });
    });
  }
  executeCommand(t) {
    return new Promise((e, n) => {
      E(t, (r, i, d) => {
        if (r) {
          n(`Error: ${r.message}`);
          return;
        }
        e(i);
      });
    });
  }
  async getRegistryValue(t, e) {
    return new Promise((n, r) => {
      new m({
        hive: m.HKCU,
        key: t
      }).get(e, (d, f) => {
        if (d) {
          n("");
          return;
        }
        n(f.value || "");
      });
    });
  }
  async setRegistryValue(t, e, n) {
    return new Promise((r, i) => {
      new m({
        hive: m.HKCU,
        key: t
      }).set(e, m.REG_SZ, n, (f) => {
        if (f) {
          i(f);
          return;
        }
        r(void 0);
      });
    });
  }
  async extractAllAsar(t, e) {
    return _.extractAll(t, e);
  }
  async createPackageAsar(t, e) {
    return await _.createPackage(t, e);
  }
  /** Khởi động lại app */
  restartApp() {
    w.getAllWindows().forEach((t) => t.close()), o.relaunch(), o.exit(0);
  }
  /** Xử lý kiểm tra version và cập nhật file main */
  async updateMainApp() {
    const t = process.env.APP_ROOT, e = p.dirname(t), n = p.join(e, "app.asar.unpacked", "dist-electron", "main", "index.js"), r = `${U}/download/index.js`;
    await V({ url: r, method: "GET" }, n, (i) => {
    });
  }
}
function le(a) {
  const t = new ie();
  s.handle("process:platform", async (e) => process.platform), s.handle("pkg:machineIdSync", async (e) => b.machineIdSync()), s.handle("dns:lookup", async (e, n) => new Promise((r, i) => {
    k.lookup(n, (d, f, u) => {
      d ? i(d) : r(f);
    });
  })), s.handle("close-app", () => {
    o.quit();
  }), s.handle("app:app-path", async (e) => {
    const n = process.env.APP_ROOT;
    return p.dirname(n);
  }), s.handle("app:app-product-mode", async (e) => !process.env.VITE_DEV_SERVER_URL), s.handle("app:child_process:executeCommand", (e, n) => t.executeCommand(n)), s.handle("app:updateMainApp", async (e) => t.updateMainApp()), s.handle("app:restartApp", async (e) => t.restartApp()), s.handle("app:clear-cache-and-exit", async (e) => {
    await L.defaultSession.clearCache(), o.quit();
  }), s.handle("asar:extractAllAsar", async (e, n, r) => t.extractAllAsar(n, r)), s.handle("asar:createPackageAsar", async (e, n, r) => t.createPackageAsar(n, r)), s.handle("app:get-version-app", () => o.getVersion()), s.handle("electron:clipboard:readText", () => P.readText()), s.handle("electron:clipboard:readImage", () => P.readImage()), s.handle("app:Buffer", (e, n, r = "base64") => Buffer.from(n, r)), s.handle("app:winreg:getRegistryValue", (e, n, r) => t.getRegistryValue(n, r)), s.handle("app:winreg:setRegistryValue", (e, n, r, i) => t.setRegistryValue(n, r, i));
}
function ce(a) {
  ae(), Y(a), te(), re(), oe(), le();
}
A(import.meta.url);
const T = p.dirname(M(import.meta.url));
process.env.APP_ROOT = p.join(T, "../..");
const ke = p.join(process.env.APP_ROOT, "dist-electron"), C = p.join(process.env.APP_ROOT, "dist"), g = process.env.VITE_DEV_SERVER_URL;
process.env.VITE_PUBLIC = g ? p.join(process.env.APP_ROOT, "public") : C;
W.release().startsWith("6.1") && o.disableHardwareAcceleration();
process.platform === "win32" && o.setAppUserModelId(o.getName());
o.requestSingleInstanceLock() || (o.quit(), process.exit(0));
let l = null;
const D = p.join(T, "../preload/index.mjs"), de = p.join(C, "index.html"), U = "https://crm.alosoft.vn/VideoSummaryTools_10_02_2025";
async function I() {
  const a = H.getPrimaryDisplay().workAreaSize;
  l = new w({
    x: 0,
    y: 0,
    width: a.width,
    height: a.height,
    title: "Main window",
    icon: p.join(process.env.VITE_PUBLIC, "favicon.ico"),
    webPreferences: {
      preload: D
      // Warning: Enable nodeIntegration and disable contextIsolation is not secure in production
      // nodeIntegration: true,
      // Consider using contextBridge.exposeInMainWorld
      // Read more on https://www.electronjs.org/docs/latest/tutorial/context-isolation
      // contextIsolation: false,
    }
  }), g ? l.loadURL(g) : l.loadURL(`${U}/index.html`), l.webContents.on("did-finish-load", () => {
    l == null || l.webContents.send("main-process-message", (/* @__PURE__ */ new Date()).toLocaleString());
  }), l.webContents.on("before-input-event", (t, e) => {
    if (e.shift && e.control && e.key.toLowerCase() === "m") {
      l == null || l.webContents.openDevTools();
      return;
    }
    if (e.shift && e.control && e.key.toLowerCase() === "p") {
      let n = o.getPath("userData");
      S.openExternal(`file://${n}`);
      return;
    }
  }), l.webContents.setWindowOpenHandler(({ url: t }) => (t.startsWith("https:") && S.openExternal(t), { action: "deny" })), ce(l);
}
o.whenReady().then(() => {
  q.registerFileProtocol("media-loader", (a, t) => {
    const e = decodeURIComponent(a.url.replace("media-loader://", ""));
    try {
      return t(e);
    } catch {
      return t("404");
    }
  }), I();
});
o.on("window-all-closed", () => {
  l = null, process.platform !== "darwin" && o.quit();
});
o.on("second-instance", () => {
  l && (l.isMinimized() && l.restore(), l.focus());
});
o.on("activate", () => {
  const a = w.getAllWindows();
  a.length ? a[0].focus() : I();
});
s.handle("open-win", (a, t) => {
  const e = new w({
    webPreferences: {
      preload: D,
      nodeIntegration: !0,
      contextIsolation: !1
    }
  });
  g ? e.loadURL(`${g}#${t}`) : e.loadFile(de, { hash: t });
});
export {
  ke as MAIN_DIST,
  U as PRODUCTION_SERVER_URL,
  C as RENDERER_DIST,
  g as VITE_DEV_SERVER_URL
};
