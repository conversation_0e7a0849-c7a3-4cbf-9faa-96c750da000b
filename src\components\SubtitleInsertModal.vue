<template>
  <a-modal
    v-model:open="visible"
    title="Chèn subtitle mới"
    :ok-text="'Chèn'"
    :cancel-text="'Hủy'"
    @ok="handleOk"
    @cancel="handleCancel"
    :width="600"
  >
    <a-form :model="form" layout="vertical">
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="Thời gian bắt đầu">
            <a-input v-model:value="form.startTime" placeholder="00:00:00,000" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="Thời gian kết thúc">
            <a-input v-model:value="form.endTime" placeholder="00:00:00,000" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-form-item label="Văn bản gốc">
        <a-textarea
          v-model:value="form.text"
          :rows="3"
          placeholder="Nhập văn bản gốc..."
        />
      </a-form-item>

      <a-form-item label="Văn bản dịch (tùy chọn)">
        <a-textarea
          v-model:value="form.translatedText"
          :rows="3"
          placeholder="Nhập văn bản dịch..."
        />
      </a-form-item>

      <a-form-item label="Vị trí chèn">
        <a-radio-group v-model:value="form.position">
          <a-radio value="before">Chèn trước subtitle #{{ targetId }}</a-radio>
          <a-radio value="after">Chèn sau subtitle #{{ targetId }}</a-radio>
        </a-radio-group>
      </a-form-item>

      <a-form-item>
        <a-checkbox v-model:checked="form.adjustTiming">
          Tự động điều chỉnh thời gian các subtitle sau
        </a-checkbox>
      </a-form-item>
    </a-form>

    <a-alert
      v-if="validationError"
      :message="validationError"
      type="error"
      show-icon
      class="mt-4"
    />
  </a-modal>
</template>

<script>
import { defineComponent, ref, watch } from 'vue';
import { parseTimeToSeconds } from '@/lib/utils';
import { secondsToSRTTime } from '@/lib/subtitleUtils';

export default defineComponent({
  name: 'SubtitleInsertModal',
  props: {
    open: {
      type: Boolean,
      default: false
    },
    targetId: {
      type: Number || null,
      default: null
    },
    targetSubtitle: {
      type: Object,
      default: null
    },
    position: {
      type: String,
      default: 'after'
    }
  },
  emits: ['update:open', 'confirm'],
  setup(props, { emit }) {
    const visible = ref(false);
    const validationError = ref('');

    const form = ref({
      startTime: '',
      endTime: '',
      text: '',
      translatedText: '',
      position: 'after',
      adjustTiming: true
    });

    // Watch for prop changes
    watch(() => props.open, (newVal) => {
      visible.value = newVal;
      if (newVal) {
        resetForm();
        calculateDefaultTiming();
      }
    });

    watch(() => props.position, (newVal) => {
      form.value.position = newVal;
      calculateDefaultTiming();
    });

    watch(visible, (newVal) => {
      emit('update:open', newVal);
    });

    const resetForm = () => {
      form.value = {
        startTime: '',
        endTime: '',
        text: '[New subtitle]',
        translatedText: '',
        position: props.position,
        adjustTiming: true
      };
      validationError.value = '';
    };

    const calculateDefaultTiming = () => {
      if (!props.targetSubtitle) return;

      const defaultDuration = 2.0; // 2 seconds

      if (props.position === 'before') {
        const targetStart = parseTimeToSeconds(props.targetSubtitle.start);
        const newEnd = Math.max(0, targetStart - 0.1);
        const newStart = Math.max(0, newEnd - defaultDuration);

        form.value.startTime = secondsToSRTTime(newStart);
        form.value.endTime = secondsToSRTTime(newEnd);
      } else {
        const targetEnd = parseTimeToSeconds(props.targetSubtitle.end);
        const newStart = targetEnd + 0.1;
        const newEnd = newStart + defaultDuration;

        form.value.startTime = secondsToSRTTime(newStart);
        form.value.endTime = secondsToSRTTime(newEnd);
      }
    };

    const validateForm = () => {
      validationError.value = '';

      if (!form.value.text.trim()) {
        validationError.value = 'Văn bản gốc không được để trống';
        return false;
      }

      try {
        const startTime = parseTimeToSeconds(form.value.startTime);
        const endTime = parseTimeToSeconds(form.value.endTime);

        if (startTime >= endTime) {
          validationError.value = 'Thời gian bắt đầu phải nhỏ hơn thời gian kết thúc';
          return false;
        }

        if (endTime - startTime < 0.5) {
          validationError.value = 'Thời lượng subtitle phải ít nhất 0.5 giây';
          return false;
        }

        if (startTime < 0) {
          validationError.value = 'Thời gian bắt đầu không được âm';
          return false;
        }

      } catch (error) {
        validationError.value = 'Định dạng thời gian không hợp lệ (HH:MM:SS,mmm)';
        return false;
      }

      return true;
    };

    const handleOk = () => {
      if (!validateForm()) return;

      const newSubtitle = {
        startTime: form.value.startTime,
        endTime: form.value.endTime,
        text: form.value.text.trim(),
        translatedText: form.value.translatedText.trim(),
        position: form.value.position,
        adjustTiming: form.value.adjustTiming
      };

      emit('confirm', newSubtitle);
      visible.value = false;
    };

    const handleCancel = () => {
      visible.value = false;
    };

    return {
      visible,
      form,
      validationError,
      handleOk,
      handleCancel
    };
  }
});
</script>
