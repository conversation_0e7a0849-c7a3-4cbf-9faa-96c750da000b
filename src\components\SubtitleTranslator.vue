<template>
  <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
    <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
      SRT Table Processor
    </h2>

    <div class="space-y-4">
      <div>
        <label
          for="srt-table-file"
          class="block text-sm font-medium text-gray-700 dark:text-gray-300"
        >
          SRT File
        </label>
        <div class="mt-1 flex items-center space-x-2 gap-1">
          <input
            type="file"
            id="srt-table-file"
            accept=".srt"
            @change="handleFileUpload"
            class="hidden"
          />
          <a-button @click.prevent="openFileInput" :loading="isProcessing">
            Import New SRT File
          </a-button>

          <!-- SRT Lists Modal -->
          <SrtLists
            buttonText="Select Existing SRT"
            @select="handleSelectSrt"
            @import-new="handleImportNew"
          />
          <a-button @click="resetFileSelected" :disabled="!srtFile"> Reset </a-button>

          <span v-if="srtFile" class="ml-2 text-sm text-gray-500">
            Current file: {{ srtFile.name }}
          </span>

          <div class="flex-1"></div>
          <a-button
            @click="handleTranslateWithGlossary"
            type="primary"
            :disabled="srtItems.length === 0"
          >
            Dịch
          </a-button>
          <a-button
            @click="handleTranslateWithSrtService"
            type="primary"
            :disabled="srtItems.length === 0"
            :loading="translating"
          >
            Dịch (SRT Service)
          </a-button>

          <a-button
            @click="continueTranslate"
            type="primary"
            :disabled="srtItems.length === 0"
          >
            Dịch Tiếp
          </a-button>
        </div>
      </div>
      <div v-if="validationError" class="mt-4 p-3 bg-red-100 text-red-700 rounded">
        {{ validationError }}
      </div>

      <div className="pt-2 border-t" v-if="translating">
        <div className="w-full" v-if="isPaused">
          <div className="flex items-center justify-between mb-1">
            <div className="text-sm font-medium text-amber-600">
              {{ t("translationSettings.translationPaused") }}
            </div>
            <div className="text-sm text-gray-500">{{ translationProgress }}%</div>
          </div>
          <LoadingIndicator :progress="translationProgress" :isPaused="isPaused" />
          <div
            className="mt-2 text-xs text-amber-600 px-2 py-1 bg-amber-50 border border-amber-100 rounded-md"
          >
            {{ t("translationSettings.translationPaused") }}
          </div>
        </div>
        <div className="w-full" v-else>
          <div className="flex items-center justify-between mb-1">
            <div className="text-sm font-medium text-blue-600">
              {{ t("translationSettings.translationInProgress") }}
            </div>
            <div className="text-sm text-gray-500">{{ translationProgress }}%</div>
          </div>
          <LoadingIndicator :progress="translationProgress" />
          <div className="mt-2 text-xs text-gray-600 flex items-center justify-between">
            <span>
              <!-- {{modelTranslations.title[locale === 'en' ? 'en' : 'vi']}}: -->
              <span className="font-medium ml-1">
                {{
                  AVAILABLE_MODELS.find((m) => m.id === ttsStore.model)?.name || ttsStore.model
                }}
              </span>
            </span>
            <span className="text-gray-500">{{
              translationProgress > 0 && translationProgress < 100
                ? `${Math.round((srtItems.length * translationProgress) / 100)}/${
                    srtItems.length
                  }`
                : ""
            }}</span>
          </div>
        </div>
      </div>

      <SubtitleTable
        v-if="srtItems.length > 0"
        :subtitles="srtItems"
        @retry="handleRetrySubtitle"
        @retryBatch="handleRetryBatch"
        @updateTranslation="handleUpdateSubtitle"
        :translating="translating"
        :batchSize="BATCH_SIZE"
        :highlightedSubtitleId="currentPlayingSubtitleId"
        @suggestTranslation="handleSuggestBetterTranslation"
      />
      <div
        v-else-if="!isProcessing && !srtFile"
        class="text-center py-8 text-gray-500 dark:text-gray-400"
      >
        <p class="mb-4">No SRT file selected.</p>
        <div class="flex justify-center space-x-4 mb-2">
          <a-button @click="openFileInput">Import New SRT File</a-button>
          <SrtLists
            buttonText="Select Existing SRT"
            @select="handleSelectSrt"
            @import-new="handleImportNew"
          />
        </div>
        <DragDropUpload
          accept=".srt,application/x-subrip,text/srt"
          :max-size="100 * 1024 * 1024"
          @files-selected="handleFileUpload"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, computed, onMounted } from "vue";
import { message } from "ant-design-vue";
import { parseSRT } from "@/lib/utils";
import { getProviderForModel } from "@/lib/modelUtils";
import { createTranslationPrompt } from "@/lib/translate";
import SubtitleTable from "./SubtitleTable.vue";
import SrtLists from "./SrtLists.vue";
import DragDropUpload from "./DragDropUpload.vue";
import { useTTSStore } from "@/stores/ttsStore";
import { useSRTStore } from "@/stores/srtStore";
import { useI18n } from "@/i18n/i18n";
// Remove old import - use allTranslateService instead
import { hasApiKey, setApiKey } from "@/lib/apiKeyManager";
import LoadingIndicator from "./LoadingIndicator.vue";
import { AVAILABLE_MODELS } from "@/lib/modelUtils";
import { translateSrtService, configureTranslateService, configureFromStore, buildTerminologyDictionary } from "@/lib/allTranslateService";

const ttsStore = useTTSStore();
const srtStore = useSRTStore();
const { t } = useI18n();

// Helper function to replace old translateText calls
async function translateTextCompat(options) {
  // Ensure service is configured
  const success = configureFromStore(ttsStore);
  if (!success) {
    throw new Error('Failed to configure translation service');
  }

  // Create mock subtitles for translateSrtService
  const mockSubtitles = options.texts.map((text, index) => ({
    id: index + 1,
    text: text,
    translatedText: '',
    status: 'pending'
  }));

  // Temporarily override prompt if provided
  const originalPrompt = ttsStore.customPrompt;
  if (options.prompt) {
    ttsStore.customPrompt = options.prompt;
  }

  try {
    const result = await translateSrtService({
      subs: mockSubtitles,
      batchSize: options.texts.length,
      terminologyDictionary: {},
      targetLanguage: options.targetLanguage || 'Vietnamese'
    });

    // Restore original prompt
    ttsStore.customPrompt = originalPrompt;

    // Convert result back to old format
    return result.map(item => ({
      text: item.translatedText || item.text,
      error: item.status === 'error' ? 'Translation failed' : null
    }));
  } catch (error) {
    // Restore original prompt in case of error
    ttsStore.customPrompt = originalPrompt;
    throw error;
  }
}

const BATCH_SIZE = 10;
const MAX_BATCH_SIZE = 30;
const translating = ref(false);
const currentPlayingSubtitleId = ref(null);
const srtFile = ref(null);
const isProcessing = ref(false);
const srtItems = ref([]);
const selectedItems = ref([]);
const currentPage = ref(1);
const defaultSpeaker = computed(() => ttsStore.selectedSpeaker);
const validationError = ref(null);
const targetLanguage = ref("Vietnamese");
const customPromptRef = ref("");
const pauseStateRef = ref(false);
const failedBatches = ref([]);
const translationError = ref(null);
const translationProgress = ref(0);
const abortControllerRef = ref(null);
const isPaused = ref(false);
const customPrompt = ref("");
const apiKeyProvided = ref(false);


function handleRetryBatch(batchIndex) {
  // Retry batch logic
}

function handleUpdateSubtitle(id, text) {
  // Update subtitle logic
  const index = srtItems.value.findIndex((s) => s.id === id);
  if (index !== -1) {
    srtItems.value[index].translatedText = text;
    srtItems.value[index].status = "translated";
  }
}

function handleSuggestBetterTranslation(id, originalText, currentTranslation) {
  // Suggest better translation logic
}

onMounted(async () => {
  if (ttsStore.model.includes("gemini")) {
    apiKeyProvided.value = hasApiKey(ttsStore.model);
    setApiKey(ttsStore.geminiKey, ttsStore.model);
  }
  if (ttsStore.model.includes("openai")) {
    apiKeyProvided.value = hasApiKey(ttsStore.model);
    setApiKey(ttsStore.openaiKey, ttsStore.model);
  }
  if (ttsStore.model.includes("deepseek")) {
    apiKeyProvided.value = hasApiKey(ttsStore.model);
    setApiKey(ttsStore.deepseekKey, ttsStore.model);
  }
});

// Watch for changes to srtItems and update the current SRT list in the store
watch(
  srtItems,
  (newItems) => {
    if (ttsStore.currentSrtList && newItems.length > 0) {
      // Find the index of the current SRT list
      const index = ttsStore.srtLists.findIndex(
        (item) => item.name === ttsStore.currentSrtList.name
      );
      if (index !== -1) {
        // Update the items in the store
        ttsStore.srtLists[index].items = [...newItems];
      }
    }
  },
  { deep: true }
);

watch(
  () => ttsStore.activeTab,
  async (newVal) => {
    if (newVal === "srt-table") {
      await handleMounded();
    }
  }
);

async function handleMounded() {
  if (srtStore.srtContent && srtStore.srtFile) {
    if (ttsStore.currentSrtList?.path === srtStore.srtFile?.path) return;
    await processSrtFile(srtStore.srtContent);
  }
}

async function processSrtFile(content) {
  try {
    isProcessing.value = true;
    // Parse SRT content
    const parsedItems = parseSRT(content);

    // Add additional properties for voice selection and speed
    srtItems.value = parsedItems.map((item) => ({
      ...item,
      translatedText: "",
      status: "pending",
      selectedSpeaker: defaultSpeaker.value,
      speechRate: 0, // Default speech rate (normal speed)
      audioUrl: "",
      duration: 0,
      isGenerated: false,
    }));

    srtFile.value = srtStore.srtFile || srtFile.value;

    // Create a new SRT list entry
    const newSrtList = {
      name: srtFile.value.name + "_" + new Date().getTime(),
      items: [...srtItems.value],
      path: srtFile.value.path,
    };

    // Add to the store
    ttsStore.srtLists.push(newSrtList);

    // Set as current SRT list
    ttsStore.currentSrtList = newSrtList;
    await electronAPI.setCurrentDir(srtFile.value.path);

    message.success(`Imported ${parsedItems.length} items from SRT file`);
    ttsStore.terminologyDictionary = {};
  } catch (error) {
    console.error("Error processing SRT file:", error);
    message.error("Error processing SRT file: " + error.message);
  } finally {
    isProcessing.value = false;
  }
}

// Function to handle file upload
async function handleFileUpload(e) {
  const file = e?.target?.files?.[0] || e?.[0];
  if (!file) return;

  srtFile.value = file;
  isProcessing.value = true;

  try {
    // Read the SRT file
    const reader = new FileReader();
    const content = await new Promise((resolve, reject) => {
      reader.onload = (e) => resolve(e.target.result);
      reader.onerror = (e) => reject(e);
      reader.readAsText(file);
    });

    // Parse SRT content
    await processSrtFile(content);

    message.success(`Imported ${srtItems.value.length} items from SRT file`);
  } catch (error) {
    console.error("Error processing SRT file:", error);
    message.error("Error processing SRT file: " + error.message);
  } finally {
    isProcessing.value = false;
  }
}

// reset file selected
async function resetFileSelected() {
  srtFile.value = null;
  srtItems.value = [];
  selectedItems.value = [];
  currentPage.value = 1;
  ttsStore.currentSrtList = null;
  await electronAPI.setCurrentDir();
}

function openFileInput() {
  document.getElementById("srt-table-file").click();
}

function handleSelectSrt(srtList) {
  if (!srtList || !srtList.items || srtList.items.length === 0) {
    message.error("Selected SRT file has no items");
    return;
  }

  // Reset current state
  srtItems.value = [];
  selectedItems.value = [];
  currentPage.value = 1;

  // Set the selected SRT file items
  srtItems.value = [...srtList.items];

  // Set the file name for display
  const fileName = srtList.name.split("_")[0];
  srtFile.value = { name: fileName };

  message.success(`Loaded ${srtItems.value.length} items from selected SRT file`);
}

function handleImportNew() {
  openFileInput();
}

// function hasApiKey(model) {
//   return !!ttsStore.model && ttsStore.model === model;
// }

// handle translate
// Xác thực đầu vào trước khi dịch
const validateBeforeTranslate = () => {
  // Kiểm tra API key cho model hiện tại
  if (!hasApiKey(ttsStore.model)) {
    const provider = getProviderForModel(ttsStore.model);
    validationError.value =
      t("errors.apiKeyRequired") + (provider ? ` (${provider})` : "");
    return false;
  }

  // Kiểm tra file SRT
  if (!srtFile.value || srtItems.value.length === 0) {
    validationError.value = t("errors.fileRequired");
    return false;
  }

  // Nếu tất cả đều hợp lệ, xóa thông báo lỗi
  validationError.value = null;
  return true;
};

// Hàm xử lý khi người dùng nhấn nút dịch với thuật ngữ
const handleTranslateWithGlossary = async () => {
  // Xác thực đầu vào và dừng nếu có lỗi
  if (!validateBeforeTranslate()) {
      return;
  }

  // if (!activeGlossary) {
  //     // Nếu chưa có glosario, mở dialog quản lý thuật ngữ
  //     setShowTermManager(true);
  //     return;
  // }

  // Tạo prompt dịch thuật với từ điển thuật ngữ
  const sourceLanguage = targetLanguage.value === "Vietnamese" ? "Chinese" : "Vietnamese";
  const newGlossaryPrompt = createTranslationPrompt(
    sourceLanguage,
    targetLanguage.value,
    true
  );

  // Lưu prompt với thuật ngữ và esperar a que se actualice
  console.log("Setting glossary prompt:", newGlossaryPrompt.substring(0, 50) + "...");

  // Actualizar el estado directamente en lugar de usar setState
  // para asegurarnos de que el valor esté disponible inmediatamente
  customPromptRef.value = newGlossaryPrompt;

  // Actualizar los estados para la UI
  // setGlossaryPrompt(newGlossaryPrompt);
  // setCustomPrompt(newGlossaryPrompt);
  // setIsUsingGlossaryPrompt(true);

  // Pequeña pausa para asegurar que el estado se actualice
  await new Promise((resolve) => setTimeout(resolve, 500));

  // Dịch với glosario
  await startTranslation();
  console.log("Translation with glossary completed", newGlossaryPrompt);
};

// Hàm bắt đầu quá trình dịch
const startTranslation = async () => {
  // Đặt lại trạng thái cho tất cả phụ đề về "pending" khi dịch lại với ngôn ngữ mới
  if (srtItems.value.some((sub) => sub.status === "translated")) {
    const resetSubtitles = srtItems.value.map((sub) => ({
      ...sub,
      status: "pending",
      translatedText: "", // Xóa bản dịch cũ
      error: undefined,
    }));
    // setSubtitles(resetSubtitles);

    srtItems.value = resetSubtitles;
  }

  translating.value = true;
  // setFailedBatches([]); // Xóa danh sách batch lỗi cũ khi bắt đầu dịch lại
  failedBatches.value = [];
  // setIsPaused(false);
  isPaused.value = false;
  pauseStateRef.value = false;
  // setTranslationError(null); // Reset thông báo lỗi dịch
  translationError.value = null;

  // Theo dõi sự kiện bắt đầu dịch
  // trackTranslation('auto', targetLanguage, srtItems.value.length, selectedModel);

  // Tạo abort controller mới
  abortControllerRef.value = new AbortController();
  const signal = abortControllerRef.value.signal;

  try {
    // Clone the subtitles array to avoid mutation during iteration
    const updatedSubtitles = [...srtItems.value];
    const pendingSubtitles = updatedSubtitles.filter(
      (sub) => sub.status === "pending" || sub.status === "error"
    );

    console.log(`Translating ${pendingSubtitles.length} subtitles to ${targetLanguage}`);

    // Process subtitles in batches
    const newFailedBatches = [];

    // Xác định kích thước batch dựa vào số lượng phụ đề
    // Nếu có nhiều phụ đề, tăng kích thước batch để giảm số lần gọi API
    const dynamicBatchSize =
      pendingSubtitles.length > 100
        ? MAX_BATCH_SIZE // Nếu có nhiều phụ đề (>100), sử dụng batch lớn
        : BATCH_SIZE; // Ngược lại, sử dụng kích thước batch mặc định

    console.log(`Using batch size: ${dynamicBatchSize}`);

    for (let i = 0; i < pendingSubtitles.length; i += dynamicBatchSize) {
      // Kiểm tra nếu đã abort
      if (signal.aborted) {
        console.log("Translation process aborted");
        throw new Error("Translation aborted");
      }

      // Đợi nếu đang tạm dừng - sử dụng ref thay vì state trực tiếp
      while (pauseStateRef.value) {
        if (signal.aborted) {
          console.log("Translation process aborted while paused");
          throw new Error("Translation aborted");
        }
        console.log("Paused, waiting...");
        // Sử dụng await-sleep dài hơn để giảm CPU usage
        await new Promise((resolve) => setTimeout(resolve, 500));
      }

      const batchIndex = Math.floor(i / dynamicBatchSize);
      const batch = pendingSubtitles.slice(i, i + dynamicBatchSize);

      console.log(
        `Processing batch ${batchIndex + 1}: ${batch.length} items, isPaused: ${
          pauseStateRef.value
        }`
      );

      // Update status to translating for this batch
      batch.forEach((sub) => {
        const index = updatedSubtitles.findIndex((s) => s.id === sub.id);
        if (index !== -1) {
          updatedSubtitles[index].status = "translating";
        }
      });
      // setSubtitles([...updatedSubtitles]);
      srtItems.value = [...updatedSubtitles];

      // Process batch with context
      try {
        await processBatchWithContext(batch, updatedSubtitles);
      } catch (batchError) {
        // Nếu lỗi là do abort thì không xử lý retry
        if (signal.aborted) {
          throw batchError;
        }

        console.error(`Batch ${batchIndex} failed:`, batchError);

        // Nếu batch lớn bị lỗi, thử chia nhỏ thành các batch nhỏ hơn
        if (batch.length > BATCH_SIZE) {
          console.log(`Retrying batch ${batchIndex} with smaller sub-batches...`);
          let anySubBatchSucceeded = false;

          // Chia thành các sub-batch nhỏ hơn
          for (let j = 0; j < batch.length; j += BATCH_SIZE) {
            const subBatch = batch.slice(j, j + BATCH_SIZE);
            const subBatchIndex =
              batchIndex * (dynamicBatchSize / BATCH_SIZE) + Math.floor(j / BATCH_SIZE);

            try {
              await processBatchWithContext(subBatch, updatedSubtitles);
              anySubBatchSucceeded = true;
            } catch (subBatchError) {
              console.error(`Sub-batch ${subBatchIndex} failed:`, subBatchError);
              // Lưu lại batch bị lỗi
              newFailedBatches.push({
                index: subBatchIndex,
                items: subBatch.map((item) => ({
                  ...item,
                  status: "error",
                  error:
                    subBatchError instanceof Error
                      ? subBatchError.message
                      : "Failed to translate sub-batch",
                })),
              });
            }
          }

          // Nếu tất cả sub-batch đều thất bại, lưu lại batch lớn ban đầu
          if (!anySubBatchSucceeded) {
            newFailedBatches.push({
              index: batchIndex,
              items: batch.map((item) => ({
                ...item,
                status: "error",
                error:
                  batchError instanceof Error
                    ? batchError.message
                    : "Failed to translate batch",
              })),
            });
          }
        } else {
          // Nếu là batch nhỏ, lưu lại luôn
          newFailedBatches.push({
            index: batchIndex,
            items: batch.map((item) => ({
              ...item,
              status: "error",
              error:
                batchError instanceof Error
                  ? batchError.message
                  : "Failed to translate batch",
            })),
          });
        }
      }

      // Update progress
      // setTranslationProgress(Math.min(100, Math.round(((i + batch.length) / pendingSubtitles.length) * 100)));
      translationProgress.value = Math.min(
        100,
        Math.round(((i + batch.length) / pendingSubtitles.length) * 100)
      );
    }

    // setTranslationProgress(100);
    translationProgress.value = 100;

    // Cập nhật danh sách batch lỗi
    if (newFailedBatches.length > 0) {
      console.log(`${newFailedBatches.length} batches failed`);
      // setFailedBatches(newFailedBatches);
      failedBatches.value = newFailedBatches;
    }
  } catch (error) {
    console.error("Translation process error:", error);

    // Kiểm tra nếu lỗi là do người dùng chủ động dừng
    const errorMessage = error instanceof Error ? error.message : "Unknown error";
    if (
      !errorMessage.includes("aborted") &&
      !errorMessage.includes("Translation aborted")
    ) {
      // Hiển thị lỗi trong UI với translationError state
      // setTranslationError(`Có lỗi xảy ra trong quá trình dịch: ${errorMessage}`);
      translationError.value = `Có lỗi xảy ra trong quá trình dịch: ${errorMessage}`;
    } else {
      console.log("Translation was stopped by user");
    }
  } finally {
    // setTranslating(false);
    translating.value = false;
  }
};

// Process batch with context
const processBatchWithContext = async (batch, allSubtitles) => {
  // Kiểm tra nếu đã abort
  if (abortControllerRef.value?.signal.aborted) {
    throw new Error("Translation aborted");
  }

  if (!batch || batch.length === 0) {
    throw new Error("Empty batch provided");
  }

  // Tạm dừng nếu người dùng đã nhấn nút tạm dừng
  if (pauseStateRef.value) {
    console.log("Translation paused, waiting to resume...");
    await new Promise((resolve) => {
      const checkPauseState = () => {
        if (!pauseStateRef.value) {
          resolve();
        } else {
          setTimeout(checkPauseState, 500);
        }
      };
      checkPauseState();
    });
    console.log("Translation resumed");
  }

  // Lấy context từ các phụ đề trước và sau batch hiện tại
  const getContextForBatch = (batch, allSubs) => {
    const firstSubInBatch = batch[0];
    const firstSubIndex = allSubs.findIndex((s) => s.id === firstSubInBatch.id);

    // Lấy tối đa 3 phụ đề trước batch hiện tại
    const startIndex = Math.max(0, firstSubIndex - 3);

    // Chỉ lấy phụ đề đã dịch làm context
    return allSubs
      .slice(startIndex, firstSubIndex)
      .filter((s) => s.status === "translated" && s.translatedText);
  };

  // Cập nhật trạng thái của một batch
  const updateBatchStatus = (batchItems, status, errorMsg) => {
    const newSubtitles = [...srtItems.value];
    batchItems.forEach((item) => {
      const index = newSubtitles.findIndex((s) => s.id === item.id);
      if (index !== -1) {
        newSubtitles[index] = {
          ...newSubtitles[index],
          status,
          error: errorMsg,
        };
      }
    });
    srtItems.value = newSubtitles;
  };

  try {
    // Extract text from batch
    const textsToTranslate = batch.map((item) => item.text);

    // Lấy context từ các phụ đề trước batch hiện tại
    const contextSubtitles = getContextForBatch(batch, allSubtitles);
    const context =
      contextSubtitles.length > 0
        ? t("translationSettings.contextPrompt") +
          "\n" +
          contextSubtitles
            .map((s) => `${s.id}. ${s.text} → ${s.translatedText}`)
            .join("\n")
        : "";

    // Update status to translating
    updateBatchStatus(batch, "translating");

    // Call translation API with the value model
    // Usar el valor de customPromptRef.value si está disponible, o customPrompt como fallback
    const promptToUse = customPromptRef.value || customPrompt.value;
    console.log("Using prompt:", promptToUse.substring(0, 50) + "...");

    const translatedResults = await translateTextCompat({
      texts: textsToTranslate,
      targetLanguage: targetLanguage.value,
      prompt: promptToUse,
      context,
      model: ttsStore.model,
      // No necesitamos pasar el glosario aquí porque ya está incluido en el prompt personalizado
      // cuando se usa handleTranslateWithGlossary
    });
    console.log("Translated results:", translatedResults);

    // Update subtitles with translations
    batch.forEach((subtitle, index) => {
      const translationResult = translatedResults[index];

      if (translationResult && !translationResult.error) {
        subtitle.translatedText = translationResult.text;
        subtitle.status = "translated";
        subtitle.error = undefined;
      } else {
        subtitle.status = "error";
        subtitle.error = translationResult?.error || "Unknown error";
      }
    });

    // Update the subtitles state with the translated batch
    const newSubtitles = [...srtItems.value];
    batch.forEach((subtitle) => {
      const index = newSubtitles.findIndex((s) => s.id === subtitle.id);
      if (index !== -1) {
        newSubtitles[index] = subtitle;
      }
    });
    srtItems.value = newSubtitles;

    // Update progress
    const totalCompleted = srtItems.value.filter(
      (s) => s.status === "translated" || s.status === "error"
    ).length;
    // setTranslationProgress(Math.floor((totalCompleted / subtitles.length) * 100));
    translationProgress.value = Math.floor(
      (totalCompleted / srtItems.value.length) * 100
    );
  } catch (error) {
    console.error("Batch translation error:", error);

    // Mark all items in the batch as failed
    updateBatchStatus(
      batch,
      "error",
      error instanceof Error ? error.message : "Translation failed"
    );

    // Tính toán batch index chính xác
    const firstSubtitleId = batch[0]?.id || 0;
    const actualBatchIndex = Math.floor((firstSubtitleId - 1) / BATCH_SIZE);

    // Add to failed batches
        const batchExists = failedBatches.value.some(existingBatch => {
            if (!existingBatch.items.length) return false;

            const existingFirstId = existingBatch.items[0]?.id;
            const existingBatchIndex = Math.floor((existingFirstId - 1) / BATCH_SIZE);

            return existingBatchIndex === actualBatchIndex;
        });

        // Chỉ thêm vào nếu batch chưa tồn tại
        if (!batchExists) {
            failedBatches.value = [...failedBatches.value, { index: actualBatchIndex, items: [...batch] }];
        }

    // Update progress
    const totalCompletedError = srtItems.value.filter(
      (s) => s.status === "translated" || s.status === "error"
    ).length;
    // setTranslationProgress(Math.floor((totalCompletedError / subtitles.length) * 100));
    translationProgress.value = Math.floor(
      (totalCompletedError / srtItems.value.length) * 100
    );

    // Theo dõi lỗi dịch
    // trackError('translation_batch',
    //     error instanceof Error ? error.message : String(error),
    //     {
    //         batchIndex: batch[0].id,
    //         subtitleCount: batch.length,
    //         targetLanguage
    //     }
    // );
  }
};



    // Handle retrying a subtitle or navigating to it
    const handleRetrySubtitle = async (id) => {
        // Check if this is just a click to navigate to this subtitle
        if (id === currentPlayingSubtitleId.value) {
            return; // Already selected, no need to retry translation
        }

        // Set the current playing subtitle immediately for navigation purposes
        // setCurrentPlayingSubtitleId(id);
        currentPlayingSubtitleId.value = id;

        // Only proceed with retry if the status is error
        const subtitleIndex = srtItems.value.findIndex(sub => sub.id === id);
        if (subtitleIndex === -1) return;

        const subtitle = srtItems.value[subtitleIndex];
        if (subtitle.status !== "error") {
            return; // Just navigation, not a retry
        }

        // If it's an actual retry (status is error), proceed with retry logic
        const updatedSubtitles = [...srtItems.value];
        updatedSubtitles[subtitleIndex].status = "translating";
        // setSubtitles(updatedSubtitles);
        srtItems.value = updatedSubtitles;

        try {
            // Get a few previous subtitles for context
            const context = [];
            for (let i = Math.max(0, subtitleIndex - 3); i < subtitleIndex; i++) {
                if (updatedSubtitles[i].status === "translated" && updatedSubtitles[i].translatedText) {
                    context.push({
                        original: updatedSubtitles[i].text,
                        translated: updatedSubtitles[i].translatedText
                    });
                }
            }

            // Create a context string
            const contextString = context.map(c => `"${c.original}" -> "${c.translated}"`).join('\n');

            // Translate this subtitle - use the current prompt (which could be glossary prompt or default)
            // Usar el valor de customPromptRef.value si está disponible, o customPrompt como fallback
            const promptToUse = customPromptRef.value || customPrompt.value;
            console.log("Retry using prompt:", promptToUse.substring(0, 50) + "...");

            const translatedResult = await translateTextCompat({
                texts: [subtitle.text],
                targetLanguage: targetLanguage.value,
                prompt: promptToUse,
                context: contextString ? `${t('translationSettings.contextPrompt')}\n${contextString}` : '',
                model: ttsStore.model
            });

            // Cập nhật kết quả
            if (translatedResult[0]?.error) {
                updatedSubtitles[subtitleIndex].status = "error";
                updatedSubtitles[subtitleIndex].error = translatedResult[0].error;
            } else {
                updatedSubtitles[subtitleIndex].translatedText = translatedResult[0]?.text || "";
                updatedSubtitles[subtitleIndex].status = "translated";
                updatedSubtitles[subtitleIndex].error = undefined;

                // Nếu dịch thành công, kiểm tra xem phụ đề này có thuộc batch nào đã thất bại không
                // và cập nhật trạng thái batch đó
                const batchIndex = Math.floor(subtitleIndex / BATCH_SIZE);
                const existingFailedBatchIndex = failedBatches.value.findIndex(b => b.index === batchIndex);

                if (existingFailedBatchIndex !== -1) {
                    // Kiểm tra xem tất cả các phụ đề trong batch này đã được dịch thành công chưa
                    const batchStart = batchIndex * BATCH_SIZE;
                    const batchEnd = Math.min(batchStart + BATCH_SIZE, updatedSubtitles.length);
                    const allTranslated = updatedSubtitles
                        .slice(batchStart, batchEnd)
                        .every(s => s.status === "translated");

                    if (allTranslated) {
                        // Nếu tất cả đã được dịch thành công, xóa batch này khỏi danh sách thất bại
                        const updatedFailedBatches = [...failedBatches.value];
                        updatedFailedBatches.splice(existingFailedBatchIndex, 1);
                        failedBatches.value = updatedFailedBatches;
                    }
                }
            }

            // setSubtitles([...updatedSubtitles]);
            srtItems.value = [...updatedSubtitles];
        } catch (error) {
            updatedSubtitles[subtitleIndex].status = "error";
            updatedSubtitles[subtitleIndex].error = error instanceof Error
                ? error.message
                : "Failed to translate";
            // setSubtitles([...updatedSubtitles]);
            srtItems.value = [...updatedSubtitles];

            // Theo dõi lỗi thử lại
            // trackError('retry_subtitle',
            //     error instanceof Error ? error.message : String(error),
            //     { subtitleId: id }
            // );
        }
    };








// Xử lý khi người dùng cung cấp API key
const handleApiKeyChange = (apiKey) => {
  // Set API key for the current model's provider
  setApiKey(apiKey, ttsStore.model);
  // setApiKeyProvided(!!apiKey);
  apiKeyProvided.value = !!apiKey;

  // Xóa thông báo lỗi nếu đã cung cấp API key
  if (!!apiKey && validationError?.includes("API key")) {
    // setValidationError(null);
    validationError.value = null;
  }
};

// Xử lý khi người dùng thay đổi model
const handleModelChange = (modelId) => {
  setSelectedModel(modelId);
  console.log(`Model changed to: ${modelId}`);

  // Check if API key is provided for this model's provider
  const provider = getProviderForModel(modelId);
  if (provider) {
    const hasKey = hasApiKey(modelId);
    // setApiKeyProvided(hasKey);
    apiKeyProvided.value = hasKey;
  }
};

    const handleStop = () => {
        if (abortControllerRef.value) {
            abortControllerRef.value.abort("Translation aborted by user.");
            abortControllerRef.value = null;
        }
        console.log("Translation stopped by user");
    };

// Hàm xử lý khi người dùng nhấn nút dịch với SRT Service
const handleTranslateWithSrtService = async () => {
  // Xác thực đầu vào và dừng nếu có lỗi
  if (!validateBeforeTranslate()) {
    return;
  }

  // Configure the translate service with the current API key and model
  let apiKey = '';
  const provider = getProviderForModel(ttsStore.model);

  if (provider === 'deepseek') {
    apiKey = ttsStore.deepseekKey;
  } else if (provider === 'gemini') {
    apiKey = ttsStore.geminiKey;
  } else if (provider === 'openai') {
    apiKey = ttsStore.openaiKey;
  }

  // Configure the translate service
  configureTranslateService(apiKey, ttsStore.model);

  translating.value = true;
  translationProgress.value = 0;
  isPaused.value = false;
  pauseStateRef.value = false;
  translationError.value = null;

  try {
    // Đặt lại trạng thái cho tất cả phụ đề về "pending" khi dịch lại
    const resetSubtitles = srtItems.value.map((sub) => ({
      ...sub,
      status: "translating",
      translatedText: "", // Xóa bản dịch cũ
      error: undefined,
    }));
    srtItems.value = resetSubtitles;

    // Chuẩn bị dữ liệu cho translateSrtService
    const subsForTranslation = srtItems.value.map(item => ({
      index: item.id,
      text: item.text,
      start: item.startTime,
      end: item.endTime
    }));

    // Cập nhật tiến độ ban đầu
    translationProgress.value = 5;

    // Hiển thị thông báo đang xây dựng từ điển thuật ngữ
    message.info("Đang xây dựng từ điển thuật ngữ...");
    const Texts = subsForTranslation.slice(0, 150).map(sub => sub.text);
    const sampleText = Texts.join('\n\n');
    ttsStore.terminologyDictionary = await buildTerminologyDictionary(
      sampleText,
      'Chinese'
    );
    console.log("ttsStore.terminologyDictionary", ttsStore.terminologyDictionary);

    // Gọi dịch vụ dịch SRT với tiến độ
    message.success("Đã xây dựng xong từ điển thuật ngữ, bắt đầu dịch...");
    const translatedTexts = await translateSrtService({
      subs: subsForTranslation,
      batchSize: BATCH_SIZE,
      terminologyDictionary: ttsStore.terminologyDictionary,
      onProgress: (current, total) => {
        if (current === 0) {
          message.info("Đang xây dựng từ điển thuật ngữ...");
        } else if (current === 1) {
          message.success("Đã xây dựng xong từ điển thuật ngữ, bắt đầu dịch...");
        }
        translationProgress.value = Math.floor((current / total) * 100);
      }
    });

    // Cập nhật kết quả dịch vào srtItems
    const updatedSubtitles = [...srtItems.value];
    translatedTexts.forEach((translatedText, index) => {
      if (index < updatedSubtitles.length) {
        updatedSubtitles[index].translatedText = translatedText;
        updatedSubtitles[index].status = "translated";
      }
    });

    srtItems.value = updatedSubtitles;
    translationProgress.value = 100;
    message.success("Dịch hoàn tất!");
  } catch (error) {
    console.error("Lỗi khi dịch với SRT Service:", error);
    translationError.value = `Có lỗi xảy ra trong quá trình dịch: ${error.message || "Unknown error"}`;

    // Đánh dấu các phụ đề chưa dịch là lỗi
    const updatedSubtitles = [...srtItems.value];
    updatedSubtitles.forEach(sub => {
      if (sub.status === "translating") {
        sub.status = "error";
        sub.error = "Dịch không thành công";
      }
    });
    srtItems.value = updatedSubtitles;
  } finally {
    translating.value = false;
  }
};

async function continueTranslate() {
  // Xác thực đầu vào và dừng nếu có lỗi
  if (!validateBeforeTranslate()) {
    return;
  }

  translating.value = true;
  translationProgress.value = 0;
  isPaused.value = false;
  pauseStateRef.value = false;
  translationError.value = null;

  try {
    // Tạo prompt dịch thuật với từ điển thuật ngữ
    const sourceLanguage = targetLanguage.value === "Vietnamese" ? "Chinese" : "Vietnamese";
    const newGlossaryPrompt = createTranslationPrompt(
      sourceLanguage,
      targetLanguage.value,
      true
    );

    // Cập nhật tiến độ ban đầu
    translationProgress.value = 5;

    // Hiển thị thông báo đang xây dựng từ điển thuật ngữ
    message.info("Đang xây dựng từ điển thuật ngữ...");

    // Dịch các phụ đề chưa dịch
    const untranslatedSubtitles = srtItems.value.filter(sub => !sub.translatedText);
    const translatedTexts = await translateTextCompat({
      texts: untranslatedSubtitles.map(sub => sub.text),
      targetLanguage: targetLanguage.value,
      prompt: newGlossaryPrompt,
      model: ttsStore.model
    });

    // Cập nhật kết quả dịch vào srtItems
    const updatedSubtitles = [...srtItems.value];
    translatedTexts.forEach((translatedText, index) => {
      const subtitleIndex = srtItems.value.findIndex(sub => !sub.translatedText);
      if (subtitleIndex !== -1) {
        updatedSubtitles[subtitleIndex].translatedText = translatedText;
        updatedSubtitles[subtitleIndex].status = "translated";
      }
    });

    srtItems.value = updatedSubtitles;
    translationProgress.value = 100;
    message.success("Dịch hoàn tất!");
  } catch (error) {
    console.error("Lỗi khi dịch:", error);
    translationError.value = `Có lỗi xảy ra trong quá trình dịch: ${error.message || "Unknown error"}`;

    // Đánh dấu các phụ đề chưa dịch là lỗi
    const updatedSubtitles = [...srtItems.value];
    updatedSubtitles.forEach(sub => {
      if (sub.status === "translating") {
        sub.status = "error";
        sub.error = "Dịch không thành công";
      }
    });
    srtItems.value = updatedSubtitles;
  } finally {
    translating.value = false;
  }
}






</script>
