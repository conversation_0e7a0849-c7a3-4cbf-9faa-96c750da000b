<template>
  <div class="flex flex-column h-full overflow-auto mt-2">
  <a-table
    :data-source="videoList"
    :columns="columns"
    :row-selection="rowSelection"
    :pagination="false"
    row-key="id"
    class="ant-table-striped"
    bordered
  >
    <!-- Thumbnail -->
    <template #bodyCell="{ column, record }">
      <template v-if="column.dataIndex === 'thumbnail'">
        <video
          :src="`file://${record.path}`"
          class="rounded object-cover"
          style="width:100px;height:100px;"
        />
      </template>
      <!-- Name -->
      <template v-else-if="column.dataIndex === 'name'">
        <a href="#" class="text-blue-600 underline">{{ record.name }}</a>
      </template>
      <!-- Transcription -->
      <template v-else-if="column.dataIndex === 'textAudio'">
        <a-textarea
          :value="record.textAudio"
          :rows="6"
          style="min-width:220px;max-width:400px;"
          @change="e => emitTextChange(record.id, 'textAudio', e.target.value)"
        />
      </template>
      <!-- Summary -->
      <template v-else-if="column.dataIndex === 'textSummary'">
        <a-textarea
          :value="record.textSummary"
          :rows="6"
          style="min-width:220px;max-width:400px;"
          @change="e => emitTextChange(record.id, 'textSummary', e.target.value)"
        />
      </template>
      <!-- Delete button -->
      <template v-else-if="column.dataIndex === 'action'">
        <a-button
          type="primary"
          danger
          shape="circle"
          @click="emitRemove(record)"
          style="background: linear-gradient(90deg, #ff5f6d 0%, #ffc371 100%); border: none;"
        >
          <template #icon>
            <delete-outlined />
          </template>
        </a-button>
        <a-button
          type="link"
          @click="openFile(record)"
          style="margin-left: 8px; background: linear-gradient(90deg, #ff5f6d 0%, #ffc371 100%); border: none;"
        ><template #icon>
          <folder-outlined />
          </template>
        </a-button>
      </template>
    </template>
  </a-table>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { DeleteOutlined, FolderOutlined } from '@ant-design/icons-vue';

const props = defineProps<{
  videoList: Array<{
    id: string,
    thumbnail: string,
    name: string,
    transcription: string,
    summary: string
  }>,
  videoMapChecked: Record<string, boolean>
}>();

const emit = defineEmits<{
  (e: 'checkbox-change', checkedKeys: string[]): void,
  (e: 'remove-item', record: any): void,
  (e: 'text-change', id: string, field: string, value: string): void,
  (e: 'check-all', checked: boolean): void,
}>();

const columns = [
  {
    title: 'Thumbnail',
    dataIndex: 'thumbnail',
    width: 100,
    align: 'center'
  },
  {
    title: 'Name',
    dataIndex: 'name',
    width: 120,
  },
  {
    title: 'Transcription',
    dataIndex: 'textAudio',
    width: 300
  },
  {
    title: 'Summary',
    dataIndex: 'textSummary',
    width: 300
  },
  {
    title: '',
    dataIndex: 'action',
    width: 70,
    align: 'center'
  }
];

// Computed property to get selected row keys from videoMapChecked
const selectedRowKeys = computed(() => {
  return Object.keys(props.videoMapChecked).filter(key => props.videoMapChecked[key]);
});

const rowSelection = computed(() => ({
  selectedRowKeys: selectedRowKeys.value,
  onChange: (selectedRowKeys: (string | number)[], _selectedRows: any[]) => {
    console.log('Selected row keys:', selectedRowKeys);
    const stringKeys = selectedRowKeys.map(key => String(key));
    emit('checkbox-change', stringKeys);
  },
  onSelectAll: (selected: boolean, _selectedRows: any[], _changeRows: any[]) => {
    console.log('Select all:', selected);
    emit('check-all', selected);
  }
}));

function emitRemove(record: any) {
  emit('remove-item', record);
}

function emitTextChange(id: string, field: string, value: string) {
  emit('text-change', id, field, value);
}

const openFile = async (record: any) => {
  console.log('Opening file:', record);
  try {
    await (window as any).electronAPI.invoke("app:shell:showItemInFolder", record.path)
  } catch (error) {
    console.error('Failed to open file:', error)
  }
}


</script>

<style scoped>
/* Optional: striped table rows */
.ant-table-striped .ant-table-tbody > tr:nth-child(2n) > td {
  background-color: #f7fafc !important;
}
</style>
