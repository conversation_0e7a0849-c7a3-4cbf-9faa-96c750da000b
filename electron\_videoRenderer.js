const path = require('path');
const fs = require('fs');
const { execWithLog } = require('./utils');
const { getAudioDuration } = require('./ffmpegHandler');
const { getVideoDuration, cutVideoSegment, handleVeryShortSegment, validateVideoFile } = require('./videoCutter');
const { spawn } = require('child_process');

// Minimum segment duration in seconds to avoid FFmpeg errors
const MIN_SEGMENT_DURATION = 0.5; // Half a second minimum

/**
 * Render video with SRT array
 * @param {Object} event - Electron event object
 * @param {Object} options - Rendering options
 * @param {Array} options.srtArray - Array of SRT items with audio URLs
 * @param {string} options.videoPath - Path to the input video
 * @param {string} options.outputDir - Directory to save output (optional, defaults to video directory)
 * @param {string} options.outputFileName - Output file name (optional)
 * @param {string} options.subtitleStyle - Subtitle style (default, white, yellow)
 * @param {boolean} options.addLogo - Whether to add logo (optional)
 * @param {string} options.logoPath - Path to logo image (optional)
 * @param {string} options.addText - Text to overlay (optional)
 * @param {string} options.textPosition - Position of text overlay (optional)
 * @param {string} options.videoCodec - Video codec to use (default: h264_nvenc)
 * @param {string} options.audioBitrate - Audio bitrate in kbps (default: 192)
 * @returns {Promise<Object>} - Result object
 */
async function renderVideoWithSrt(event, {
  srtArray,
  videoPath,
  outputDir,
  outputFileName,
  subtitleStyle = 'default',
  addLogo = false,
  logoPath = '',
  addText = '',
  textPosition = 'bottom',
  videoCodec = 'h264_nvenc',
  audioBitrate = '192'
}) {
  const type = 'video-task';

  // Validate inputs
  if (!srtArray || !Array.isArray(srtArray) || srtArray.length === 0) {
    return { success: false, error: 'Invalid SRT array' };
  }

  if (!videoPath || !fs.existsSync(videoPath)) {
    return { success: false, error: 'Video file does not exist' };
  }

  try {
    // Set default output directory to video directory if not provided
    if (!outputDir) {
      outputDir = path.dirname(videoPath);
    }

    // Create output directory if it doesn't exist
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // Set default output file name if not provided
    if (!outputFileName) {
      const videoBaseName = path.basename(videoPath, path.extname(videoPath));
      outputFileName = `${videoBaseName}_rendered${path.extname(videoPath)}`;
    } else if (!outputFileName.includes('_rendered')) {
      // Ensure the output file name has the _rendered suffix
      const fileExt = path.extname(outputFileName);
      const baseName = path.basename(outputFileName, fileExt);
      outputFileName = `${baseName}_rendered${fileExt}`;
    }

    const outputPath = path.join(outputDir, outputFileName);
    console.log(`Output path: ${outputPath}`);

    // Log the output path to the user
    event?.sender?.send(type, { data: `Output will be saved to: ${outputPath}`, code: 0 });

    // Ensure output directory exists
    if (!fs.existsSync(outputDir)) {
      console.log(`Creating output directory: ${outputDir}`);
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // Create temp directory for processed files
    const tempDir = path.join(outputDir, `temp_${Date.now()}`);
    console.log(`Creating temp directory: ${tempDir}`);
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    // Double-check that the temp directory was created
    if (!fs.existsSync(tempDir)) {
      throw new Error(`Failed to create temp directory: ${tempDir}`);
    }

    event?.sender?.send(type, { data: 'Starting video rendering process...', code: 0 });

    // Calculate audio durations for each SRT item if not already present
    event?.sender?.send(type, { data: 'Calculating audio durations...', code: 0 });
    for (let i = 0; i < srtArray.length; i++) {
      const item = srtArray[i];
      if (item.audioUrl) {
        try {
          const duration = await getAudioDuration(item.audioUrl.replace('file://', ''));
          srtArray[i].duration = Math.round(duration * 1000); // Convert to milliseconds
        } catch (error) {
          console.error(`Error getting audio duration for item ${i}:`, error);
        }
      }
    }

    // Get video duration (useful for logging and debugging)
    // const videoDuration = await getVideoDuration(videoPath);
    // console.log(`Video duration: ${videoDuration} seconds`);

    // Generate ASS subtitle file
    event?.sender?.send(type, { data: 'Generating subtitle file...', code: 0 });
    const assFilePath = await generateAssSubtitle(srtArray, tempDir, subtitleStyle);

    // Process each segment
    event?.sender?.send(type, { data: 'Processing video segments...', code: 0 });
    const processedSegments = [];
    const totalSegments = srtArray.length;

    for (let i = 0; i < totalSegments; i++) {
      const item = srtArray[i];
      // Add total segments count to the item for reference in processSegmentWithAudio
      item.totalSegments = totalSegments;
      event?.sender?.send(type, { data: `Processing segment ${i+1}/${totalSegments}...`, code: 0 });

      // Calculate segment timing
      const startTime = item.startTime;
      const endTime = item.endTime;
      const segmentDuration = endTime - startTime;

      // Format time for ffmpeg
      const startTimeFormatted = formatTimeHMS(startTime);
      const durationFormatted = formatTimeHMS(segmentDuration);

      // Cut video segment
      const result = await cutVideoSegment(event, {
        inputPath: videoPath,
        outputDir: tempDir,
        startTime: startTimeFormatted,
        duration: durationFormatted,
        partNumber: i,
        videoCodec,
        audioBitrate
      });

      if (!result.success) {
        throw new Error(`Failed to cut segment ${i}: ${result.error}`);
      }

      // Process segment with audio
      try {
        // Verify the segment file exists before processing
        if (!fs.existsSync(result.outputPath)) {
          console.error(`Segment file not found: ${result.outputPath}`);
          event?.sender?.send(type, {
            data: `Warning: Segment file not found: ${result.outputPath}. Skipping this segment.`,
            code: 1
          });
          continue; // Skip this segment
        }

        const processedSegmentPath = await processSegmentWithAudio(
          event,
          result.outputPath,
          item,
          tempDir,
          i,
          assFilePath,
          addLogo,
          logoPath,
          addText,
          textPosition,
          videoCodec,
          audioBitrate
        );

        // Verify the processed segment exists before adding to the list
        if (fs.existsSync(processedSegmentPath)) {
          processedSegments.push(processedSegmentPath);
          console.log(`Successfully processed segment ${i+1}/${srtArray.length}`);
        } else {
          console.error(`Processed segment file not found: ${processedSegmentPath}`);
          event?.sender?.send(type, {
            data: `Warning: Processed segment file not found: ${processedSegmentPath}. Skipping this segment.`,
            code: 1
          });
        }
      } catch (segmentError) {
        console.error(`Error processing segment ${i}:`, segmentError);
        event?.sender?.send(type, {
          data: `Error processing segment ${i}: ${segmentError.message}`,
          code: 1
        });
        // Continue with other segments instead of failing the entire process
      }
    }

    // Join all segments
    if (processedSegments.length === 0) {
      event?.sender?.send(type, { data: 'No valid segments to join. Rendering failed.', code: 1 });
      throw new Error('No valid segments to join. Rendering failed.');
    }

    event?.sender?.send(type, { data: `Joining ${processedSegments.length} segments...`, code: 0 });
    const finalVideoPath = await joinVideoSegments(event, processedSegments, outputPath, videoCodec, audioBitrate);

    // Clean up temp directory
    // Uncomment to enable cleanup
    // fs.rmSync(tempDir, { recursive: true, force: true });

    event?.sender?.send(type, { data: `Video rendering complete: ${finalVideoPath}`, code: 0 });

    return {
      success: true,
      outputPath: finalVideoPath,
      message: `Video rendering complete. Output saved to: ${finalVideoPath}`
    };
  } catch (error) {
    console.error('Error rendering video with SRT:', error);
    event?.sender?.send(type, { data: `Error: ${error.message}`, code: 1 });
    return {
      success: false,
      error: error.message || 'Unknown error'
    };
  }
}

/**
 * Generate ASS subtitle file from SRT array
 * @param {Array} srtArray - Array of SRT items
 * @param {string} outputDir - Directory to save the ASS file
 * @param {string} style - Subtitle style (default, white, yellow)
 * @returns {Promise<string>} - Path to the generated ASS file
 */
async function generateAssSubtitle(srtArray, outputDir, style = 'default') {
  const assFilePath = path.join(outputDir, 'subtitles.ass');

  // Define styles based on the selected style
  let primaryColor, outlineColor, backgroundColor;

  switch (style) {
    case 'white':
      primaryColor = '&HFFFFFF&'; // White
      outlineColor = '&H000000&'; // Black outline
      backgroundColor = '&H000000&'; // Black background
      break;
    case 'yellow':
      primaryColor = '&H00FFFF&'; // Yellow
      outlineColor = '&H000000&'; // Black outline
      backgroundColor = '&H000000&'; // Black background
      break;
    default:
      primaryColor = '&HFFFFFF&'; // White
      outlineColor = '&H000000&'; // Black outline
      backgroundColor = '&H000000&'; // Black background
  }

  // Create ASS header
  let assContent = `[Script Info]
ScriptType: v4.00+
PlayResX: 1920
PlayResY: 1080
ScaledBorderAndShadow: yes

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: Default,Arial,48,${primaryColor},&H000000&,${outlineColor},${backgroundColor},0,0,0,0,100,100,0,0,1,2,2,2,20,20,20,1

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
`;

  // Add subtitle events
  for (const item of srtArray) {
    const startTime = formatAssTime(item.startTime);
    const endTime = formatAssTime(item.endTime);
    const text = item.translatedText || item.text;

    assContent += `Dialogue: 0,${startTime},${endTime},Default,,0,0,0,,${text}\n`;
  }

  // Write ASS file
  fs.writeFileSync(assFilePath, assContent);

  return assFilePath;
}

/**
 * Process a video segment with its corresponding audio
 * @param {Object} event - Electron event object
 * @param {string} segmentPath - Path to the video segment
 * @param {Object} srtItem - SRT item with audio information
 * @param {string} outputDir - Directory to save the processed segment
 * @param {number} index - Segment index
 * @param {string} subtitlePath - Path to the subtitle file
 * @param {boolean} addLogo - Whether to add logo
 * @param {string} logoPath - Path to logo image
 * @param {string} addText - Text to overlay
 * @param {string} textPosition - Position of text overlay
 * @param {string} videoCodec - Video codec to use
 * @param {string} audioBitrate - Audio bitrate in kbps
 * @returns {Promise<string>} - Path to the processed segment
 */
async function processSegmentWithAudio(
  event,
  segmentPath,
  srtItem,
  outputDir,
  index,
  subtitlePath,
  addLogo = false,
  logoPath = '',
  addText = '',
  textPosition = 'bottom',
  videoCodec = 'h264_nvenc',
  audioBitrate = '192'
) {
  const type = 'render-video-srt-res';
  const outputPath = path.join(outputDir, `processed_segment_${index}.mp4`);

  try {
    // Skip processing if audio URL is missing or invalid
    if (!srtItem.audioUrl) {
      console.log(`Skipping segment ${index} - no audio URL`);
      return segmentPath;
    }

    // Clean up the audio URL (remove file:// prefix)
    const audioFilePath = srtItem.audioUrl.replace(/^file:\/\//, '');

    // Verify audio file exists
    if (!fs.existsSync(audioFilePath)) {
      console.error(`Audio file not found: ${audioFilePath}`);
      event?.sender?.send(type, { data: `Warning: Audio file not found: ${audioFilePath}. Using original segment.`, code: 1 });
      return segmentPath;
    }

    // Verify segment file exists
    if (!fs.existsSync(segmentPath)) {
      console.error(`Segment file not found: ${segmentPath}`);
      event?.sender?.send(type, {
        data: `Warning: Segment file not found: ${segmentPath}. Skipping this segment.`,
        code: 1
      });
      return segmentPath;
    }

    // Get segment duration
    try {
      // Add a delay to ensure the file is fully written
      await new Promise(resolve => setTimeout(resolve, 1000));

      // const segmentDuration = await getVideoDuration(segmentPath);
      // console.log(`Segment ${index} duration: ${segmentDuration} seconds`);

      // Get audio duration
      const audioDuration = srtItem.audioDuration2
        ? srtItem.audioDuration2 / 1000  // Convert ms to seconds if already calculated
        : await getAudioDuration(audioFilePath); // Calculate if not available

      console.log(`Audio for segment ${index} duration: ${audioDuration} seconds`);

      // Prepare FFmpeg command
      let ffmpegArgs = ['-y'];
    } catch (durationError) {
      console.error(`Error getting segment duration: ${durationError.message}`);
      event?.sender?.send(type, {
        data: `Error getting segment duration: ${durationError.message}. Using default duration.`,
        code: 1
      });

      // Skip this segment if it's an invalid video file
      if (durationError.message.includes('moov atom not found') ||
          durationError.message.includes('Invalid data found')) {
        console.error(`Skipping invalid video segment: ${segmentPath}`);
        return segmentPath;
      }

      // Continue with a default duration if possible
      throw durationError;
    }

    // Handle speed adjustment if audio is longer than video
    let ffmpegArgs = ['-y'];
    let segmentDuration = 0;
    let audioDuration = 0;

    try {
      try {
        segmentDuration = await getVideoDuration(segmentPath);
        console.log(`Segment ${index} duration: ${segmentDuration} seconds`);

        // Check if segment duration is too short
        if (segmentDuration < MIN_SEGMENT_DURATION) {
          console.warn(`Warning: Segment ${index} duration (${segmentDuration}s) is very short. This may cause processing issues.`);

          // If extremely short, we might need special handling
          if (segmentDuration < 0.1) {
            console.warn(`Extremely short segment detected (${segmentDuration}s). Using minimum duration of ${MIN_SEGMENT_DURATION}s`);
            segmentDuration = MIN_SEGMENT_DURATION;
          }
        }
      } catch (videoDurationError) {
        console.error(`Error getting video duration: ${videoDurationError.message}`);

        // Check if this is a small file - if so, use a default duration
        try {
          const stats = fs.statSync(segmentPath);
          if (stats.size < 1000) {
            console.warn(`Small video file detected (${stats.size} bytes). Using default duration of ${MIN_SEGMENT_DURATION} seconds for segment ${index}`);
            segmentDuration = MIN_SEGMENT_DURATION; // Use minimum duration instead of very small value
          } else {
            // For larger files, this is a real error
            throw videoDurationError;
          }
        } catch (statError) {
          console.error(`Error checking file size: ${statError.message}`);
          throw videoDurationError; // Re-throw the original error
        }
      }

      // Get audio duration
      try {
        audioDuration = srtItem.audioDuration2
          ? srtItem.audioDuration2 / 1000  // Convert ms to seconds if already calculated
          : await getAudioDuration(audioFilePath); // Calculate if not available

        console.log(`Audio for segment ${index} duration: ${audioDuration} seconds`);

        // Check if audio duration is too short
        if (audioDuration < MIN_SEGMENT_DURATION) {
          console.warn(`Warning: Audio for segment ${index} duration (${audioDuration}s) is very short.`);

          // If extremely short, we might need special handling
          if (audioDuration < 0.1) {
            console.warn(`Extremely short audio detected (${audioDuration}s). Using minimum duration of ${MIN_SEGMENT_DURATION}s`);
            audioDuration = MIN_SEGMENT_DURATION;
          }
        }
      } catch (audioDurationError) {
        console.error(`Error getting audio duration: ${audioDurationError.message}`);

        // Use a default audio duration
        console.warn(`Using default audio duration of ${MIN_SEGMENT_DURATION} seconds for segment ${index}`);
        audioDuration = MIN_SEGMENT_DURATION; // Use minimum duration instead of arbitrary value
      }
    } catch (durationError) {
      console.error(`Error getting duration: ${durationError.message}`);
      event?.sender?.send(type, {
        data: `Error getting duration: ${durationError.message}. Skipping segment.`,
        code: 1
      });
      return segmentPath;
    }

    // Prepare filters
    let filters = [];
    let hasComplexFilter = false;

    // Prepare input arguments
    ffmpegArgs.push('-i', segmentPath, '-i', audioFilePath);

    // Check if we're already using a complex filter
    const filterComplexIndex = ffmpegArgs.indexOf('-filter_complex');
    if (filterComplexIndex !== -1) {
      hasComplexFilter = true;
    }

    // Handle speed adjustment if audio is longer than video
    if (audioDuration > segmentDuration) {
      // Calculate speed factor (slower)
      const speedFactor = segmentDuration / audioDuration;
      console.log(`Adjusting video speed for segment ${index} by factor ${speedFactor}`);

      // Add to filters array - this will be handled in the filter section
      filters.push(`setpts=${1/speedFactor}*PTS`);

      // We need complex filtering
      hasComplexFilter = true;
    }

    // Add subtitle if available
    if (subtitlePath && fs.existsSync(subtitlePath)) {
      filters.push(`ass=${subtitlePath}`);

      // Subtitles with complex filtering need special handling
      if (hasComplexFilter) {
        console.log('Using complex filtering with subtitles - this requires special handling');
      }
    }

    // Add logo if requested
    if (addLogo && logoPath && fs.existsSync(logoPath)) {
      // We'll need to use complex filtering for logo overlay
      hasComplexFilter = true;

      // For logo overlay, we need to add it as an input
      ffmpegArgs.push('-i', logoPath);

      // We'll handle the actual overlay in the complex filter section
      // Don't add to filters array yet
    }

    // Add text overlay if requested
    if (addText) {
      const textY = textPosition === 'top' ? 10 : 'h-th-10';
      const drawTextFilter = `drawtext=text='${addText}':fontcolor=white:fontsize=24:x=(w-text_w)/2:y=${textY}:box=1:boxcolor=black@0.5:boxborderw=5`;

      // Add to filters array
      filters.push(drawTextFilter);
    }

    // Apply filters
    if (filters.length > 0 || hasComplexFilter) {
      // Determine if we have a logo
      const hasLogo = addLogo && logoPath && fs.existsSync(logoPath);

      if (hasComplexFilter) {
        // Use complex filtering
        let complexFilter;

        if (hasLogo) {
          // Handle logo overlay with complex filtering
          if (filters.length > 0) {
            // Apply filters first, then overlay logo
            complexFilter = `[0:v]${filters.join(',')}[filtered];[filtered][2:v]overlay=10:10[v]`;
          } else {
            // Just overlay logo
            complexFilter = `[0:v][2:v]overlay=10:10[v]`;
          }
        } else {
          // No logo, just apply filters
          complexFilter = `[0:v]${filters.join(',')}[v]`;
        }

        ffmpegArgs.push('-filter_complex', complexFilter);
        ffmpegArgs.push('-map', '[v]');
        ffmpegArgs.push('-map', '1:a');
      } else {
        // Use simple filtering
        ffmpegArgs.push('-vf', filters.join(','));
        ffmpegArgs.push('-map', '0:v');
        ffmpegArgs.push('-map', '1:a');
      }
    } else {
      // No filters, just map streams
      ffmpegArgs.push('-map', '0:v');
      ffmpegArgs.push('-map', '1:a');
    }

    // Add audio codec settings
    ffmpegArgs.push('-c:a', 'aac', '-b:a', `${audioBitrate}k`);

    // Set video codec
    ffmpegArgs.push('-c:v', videoCodec);

    // Add error resilience flags for short segments
    if (segmentDuration < MIN_SEGMENT_DURATION * 2 || audioDuration < MIN_SEGMENT_DURATION * 2) {
      console.log(`Adding error resilience flags for short segment ${index}`);
      ffmpegArgs.push(
        '-max_muxing_queue_size', '9999',
        '-avoid_negative_ts', 'make_zero',
        '-vsync', 'cfr'  // Constant frame rate
      );
    }

    // Add output path
    ffmpegArgs.push(outputPath);

    console.log(`Processing segment ${index} with FFmpeg...`);
    console.log(`FFmpeg command: ffmpeg ${ffmpegArgs.join(' ')}`);

    // Use a safer way to reference the total number of segments
    const totalSegments = srtItem.totalSegments || "?";
    event?.sender?.send(type, { data: `Processing segment ${index+1}/${totalSegments}...`, code: 0 });

    // Check if we need a two-pass approach (speed adjustment + subtitles)
    const needsSpeedAdjustment = audioDuration > segmentDuration;
    const hasSubtitles = subtitlePath && fs.existsSync(subtitlePath);

    // Check if we should use a fallback approach
    let useFallback = false;

    // Check if this is a very small segment - if so, skip two-pass approach
    try {
      const stats = fs.statSync(segmentPath);
      if (stats.size < 500) { // Very small file
        console.warn(`Very small segment detected (${stats.size} bytes). Skipping two-pass approach for segment ${index}`);
        useFallback = true;
      }
    } catch (statError) {
      console.error(`Error checking segment file size: ${statError.message}`);
      // Continue with normal approach
    }

    // Try to use the two-pass approach if needed and not a very small segment
    if (!useFallback && needsSpeedAdjustment && hasSubtitles) {
        console.log('Using two-pass approach for speed adjustment + subtitles');

        // First pass: adjust speed only
        const tempDir = path.dirname(outputPath);

        // Ensure temp directory exists
        if (!fs.existsSync(tempDir)) {
          console.log(`Creating temp directory: ${tempDir}`);
          fs.mkdirSync(tempDir, { recursive: true });
        }

        const tempOutputPath = path.join(tempDir, `temp_${path.basename(outputPath)}`);
        console.log(`Temporary output path: ${tempOutputPath}`);

        try {
          // Remove any existing temp file
          if (fs.existsSync(tempOutputPath)) {
            fs.unlinkSync(tempOutputPath);
            console.log(`Removed existing temp file: ${tempOutputPath}`);
          }
        } catch (unlinkError) {
          console.error(`Error removing existing temp file: ${unlinkError.message}`);
          // Continue anyway
        }

        const firstPassArgs = [
          '-y',
          '-i', segmentPath,
          '-i', audioFilePath,
          '-filter_complex', `[0:v]setpts=${1/(segmentDuration/audioDuration)}*PTS[v]`,
          '-map', '[v]',
          '-map', '1:a',
          '-c:v', videoCodec,
          '-c:a', 'aac',
          '-b:a', `${audioBitrate}k`,
          tempOutputPath
        ];

        console.log(`First pass FFmpeg command: ffmpeg ${firstPassArgs.join(' ')}`);

        try {
          await execWithLog.bind({ type })(event, 'ffmpeg', firstPassArgs);
        } catch (ffmpegError) {
          console.error(`Error in first pass FFmpeg: ${ffmpegError.message}`);
          throw new Error(`First pass FFmpeg failed: ${ffmpegError.message}`);
        }

        // Wait for the file to be created
        console.log(`Waiting for temp file to be created: ${tempOutputPath}`);
        await new Promise(resolve => setTimeout(resolve, 2000));

        if (!fs.existsSync(tempOutputPath)) {
          console.error(`Temp file not found after waiting: ${tempOutputPath}`);

          // Try waiting longer
          console.log("Waiting longer for temp file...");
          await new Promise(resolve => setTimeout(resolve, 3000));

          if (!fs.existsSync(tempOutputPath)) {
            throw new Error(`Failed to create temporary file in first pass: ${tempOutputPath}`);
          }
        }

        console.log(`Temp file created successfully: ${tempOutputPath}`);

        // Verify the temp file is valid
        try {
          const stats = fs.statSync(tempOutputPath);
          console.log(`Temp file size: ${stats.size} bytes`);

          if (stats.size < 100) { // Only fail if extremely small (less than 100 bytes)
            throw new Error(`Temp file is extremely small (${stats.size} bytes)`);
          }

          // Just log a warning for small files but don't fail
          if (stats.size < 1000) {
            console.warn(`Warning: Temp file is small (${stats.size} bytes): ${tempOutputPath}`);
            // Continue processing anyway
          }
        } catch (statError) {
          console.error(`Error checking temp file: ${statError.message}`);
          throw new Error(`Error verifying temp file: ${statError.message}`);
        }

        // Second pass: add subtitles
        const secondPassArgs = [
          '-y',
          '-i', tempOutputPath,
          '-vf', `ass=${subtitlePath}`,
          '-c:v', videoCodec,
          '-c:a', 'copy',
          outputPath
        ];

        console.log(`Second pass FFmpeg command: ffmpeg ${secondPassArgs.join(' ')}`);

        try {
          await execWithLog.bind({ type })(event, 'ffmpeg', secondPassArgs);
        } catch (ffmpegError) {
          console.error(`Error in second pass FFmpeg: ${ffmpegError.message}`);

          // If second pass fails, try a simpler approach
          console.log("Second pass failed. Trying simpler approach without subtitles...");

          // Just copy the temp file to the output
          try {
            fs.copyFileSync(tempOutputPath, outputPath);
            console.log(`Copied temp file to output: ${outputPath}`);
          } catch (copyError) {
            console.error(`Error copying temp file: ${copyError.message}`);
            throw new Error(`Second pass failed and couldn't copy temp file: ${copyError.message}`);
          }
        }

      // Wait for the output file to be created
      console.log(`Waiting for output file to be created: ${outputPath}`);
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Verify the output file exists
      if (!fs.existsSync(outputPath)) {
        console.error(`Output file not found after second pass: ${outputPath}`);

        // Try copying the temp file as a last resort
        try {
          fs.copyFileSync(tempOutputPath, outputPath);
          console.log(`Last resort: Copied temp file to output: ${outputPath}`);
        } catch (copyError) {
          console.error(`Error in last resort copy: ${copyError.message}`);
          throw new Error(`Failed to create output file in second pass: ${outputPath}`);
        }
      }

      // Clean up temporary file
      try {
        if (fs.existsSync(tempOutputPath)) {
          fs.unlinkSync(tempOutputPath);
          console.log(`Cleaned up temp file: ${tempOutputPath}`);
        }
      } catch (cleanupError) {
        console.error(`Error cleaning up temporary file: ${cleanupError.message}`);
        // Continue anyway
      }
    }

    if (useFallback || (!needsSpeedAdjustment || !hasSubtitles)) {
      // Standard single-pass approach
      console.log('Using standard single-pass approach');

      try {
        // Ensure output directory exists
        const outputDir = path.dirname(outputPath);
        if (!fs.existsSync(outputDir)) {
          console.log(`Creating output directory: ${outputDir}`);
          fs.mkdirSync(outputDir, { recursive: true });
        }

        // Try to remove any existing output file
        try {
          if (fs.existsSync(outputPath)) {
            fs.unlinkSync(outputPath);
            console.log(`Removed existing output file: ${outputPath}`);
          }
        } catch (unlinkError) {
          console.error(`Error removing existing output file: ${unlinkError.message}`);
          // Continue anyway
        }

        // Validate the segment file
        const validation = await validateVideoFile(segmentPath);

        if (!validation.valid || validation.size < 500 || validation.size === 261) {
          // Log the issue
          if (!validation.valid) {
            console.warn(`Invalid segment detected: ${validation.error}. Using special handling for segment ${index}`);
            event?.sender?.send(type, {
              data: `Warning: Invalid segment detected: ${validation.error}. Using special handling.`,
              code: 1
            });
          } else {
            console.warn(`Very small segment detected (${validation.size} bytes). Using special handling for segment ${index}`);
            event?.sender?.send(type, {
              data: `Warning: Very small segment detected (${validation.size} bytes). Using special handling.`,
              code: 1
            });
          }

          // For problematic segments, we'll use a different approach:
          // 1. First try to use the specialized handler
          // 2. If that fails, create a dummy segment with audio

          try {
            // Try to use the specialized handler first
            console.log(`Using specialized handler for problematic segment ${index}`);

            // Get a start time (doesn't matter for dummy video)
            const startTime = "00:00:00";

            // Create a temporary output path
            const tempOutputPath = path.join(outputDir, `temp_${Date.now()}_segment_${index}.mp4`);

            // Use the specialized handler
            const result = await handleVeryShortSegment(
              event,
              segmentPath, // Try to use the original as input if possible
              tempOutputPath,
              startTime,
              outputDir,
              index,
              'libx264', // Use CPU encoding for better compatibility
              audioBitrate
            );

            if (result.success) {
              console.log(`Successfully created base video with specialized handler for segment ${index}`);

              // Now add audio to the fixed segment
              if (audioFilePath && fs.existsSync(audioFilePath)) {
                console.log(`Adding audio to fixed segment ${index}`);

                // Create a simple FFmpeg command to add audio
                const audioArgs = [
                  '-y',
                  '-i', tempOutputPath,
                  '-i', audioFilePath,
                  '-c:v', 'copy',
                  '-c:a', 'aac',
                  '-b:a', `${audioBitrate}k`,
                  '-map', '0:v',
                  '-map', '1:a',
                  '-shortest',
                  '-max_muxing_queue_size', '9999',
                  '-avoid_negative_ts', 'make_zero',
                  outputPath
                ];

                try {
                  await execWithLog.bind({ type })(event, 'ffmpeg', audioArgs);

                  // Verify the output file was created
                  if (fs.existsSync(outputPath)) {
                    console.log(`Successfully added audio to fixed segment ${index}`);

                    // Clean up temp file
                    try {
                      fs.unlinkSync(tempOutputPath);
                    } catch (unlinkError) {
                      console.error(`Error removing temp file: ${unlinkError.message}`);
                    }

                    return outputPath;
                  }
                } catch (audioError) {
                  console.error(`Error adding audio to fixed segment: ${audioError.message}`);
                  // Just use the fixed segment without audio
                  fs.copyFileSync(tempOutputPath, outputPath);
                  console.log(`Using fixed segment without audio for segment ${index}`);

                  // Clean up temp file
                  try {
                    fs.unlinkSync(tempOutputPath);
                  } catch (unlinkError) {
                    console.error(`Error removing temp file: ${unlinkError.message}`);
                  }

                  return outputPath;
                }
              } else {
                // No audio file, just use the fixed segment
                fs.copyFileSync(tempOutputPath, outputPath);
                console.log(`Using fixed segment for segment ${index} (no audio)`);

                // Clean up temp file
                try {
                  fs.unlinkSync(tempOutputPath);
                } catch (unlinkError) {
                  console.error(`Error removing temp file: ${unlinkError.message}`);
                }

                return outputPath;
              }
            } else {
              console.error(`Specialized handler failed: ${result.error}`);
              // Fall back to traditional approach
            }

            // Fall back to traditional approach - try to create a slightly longer segment
            console.log(`Falling back to traditional approach for segment ${index}`);

            // Prepare ffmpeg arguments with different parameters for short segments
            const specialArgs = [
              '-hide_banner',
              '-y',
              '-i', segmentPath,
              '-i', audioFilePath,
              // Use more compatible encoding settings
              '-c:v', 'libx264',  // Use libx264 for better compatibility
              '-preset', 'ultrafast',  // Use fastest preset
              '-tune', 'fastdecode',
              '-profile:v', 'baseline',
              '-level', '3.0',
              '-pix_fmt', 'yuv420p',
              // Add error resilience flags
              '-max_muxing_queue_size', '9999',
              '-avoid_negative_ts', 'make_zero',
              '-vsync', 'cfr',  // Constant frame rate
              // Audio settings
              '-c:a', 'aac',
              '-b:a', `${audioBitrate}k`,
              '-ar', '44100',  // Standard audio sample rate
              '-map', '0:v',
              '-map', '1:a',
              '-shortest',
              tempOutputPath
            ];

            console.log(`Attempting special processing for problematic segment ${index}`);

            try {
              await execWithLog.bind({ type })(event, 'ffmpeg', specialArgs);

              // Check if the temp file was created successfully
              if (fs.existsSync(tempOutputPath)) {
                // Validate the temp file
                const validation = await validateVideoFile(tempOutputPath);

                if (validation.valid && validation.size > 100) {
                  // Copy the temp file to the output path
                  fs.copyFileSync(tempOutputPath, outputPath);
                  console.log(`Successfully processed problematic segment ${index} with special handling`);

                  // Clean up temp file
                  try {
                    fs.unlinkSync(tempOutputPath);
                  } catch (unlinkError) {
                    console.error(`Error removing temp file: ${unlinkError.message}`);
                  }

                  return outputPath;
                } else {
                  console.error(`Created temp file is invalid: ${validation.error || 'Unknown error'}`);
                }
              }
            } catch (specialError) {
              console.error(`Special processing failed for problematic segment: ${specialError.message}`);
              // Fall back to simple copy approach
            }

            // If special processing failed, create a dummy video with audio
            console.log(`Special processing failed. Creating a dummy video with audio for segment ${index}`);

            // Create a dummy video file
            const dummyPath = path.join(outputDir, `dummy_${Date.now()}_segment_${index}.mp4`);

            // First, try to create a color video
            const colorArgs = [
              '-y',
              '-f', 'lavfi',
              '-i', 'color=c=black:s=1280x720:r=30',
              '-t', '2.0',  // 2-second duration for better stability
              '-c:v', 'libx264',
              '-pix_fmt', 'yuv420p',
              dummyPath
            ];

            try {
              await execWithLog.bind({ type })(event, 'ffmpeg', colorArgs);

              if (fs.existsSync(dummyPath)) {
                // Now add audio to the dummy video
                if (audioFilePath && fs.existsSync(audioFilePath)) {
                  console.log(`Adding audio to dummy video for segment ${index}`);

                  // Create a simple FFmpeg command to add audio
                  const audioArgs = [
                    '-y',
                    '-i', dummyPath,
                    '-i', audioFilePath,
                    '-c:v', 'copy',
                    '-c:a', 'aac',
                    '-b:a', `${audioBitrate}k`,
                    '-map', '0:v',
                    '-map', '1:a',
                    '-shortest',
                    '-max_muxing_queue_size', '9999',
                    '-avoid_negative_ts', 'make_zero',
                    outputPath
                  ];

                  try {
                    await execWithLog.bind({ type })(event, 'ffmpeg', audioArgs);

                    if (fs.existsSync(outputPath)) {
                      console.log(`Successfully created dummy video with audio for segment ${index}`);

                      // Clean up dummy file
                      try {
                        fs.unlinkSync(dummyPath);
                      } catch (unlinkError) {
                        console.error(`Error removing dummy file: ${unlinkError.message}`);
                      }

                      return outputPath;
                    }
                  } catch (audioError) {
                    console.error(`Error adding audio to dummy video: ${audioError.message}`);
                    // Copy the dummy video without audio as a last resort
                    fs.copyFileSync(dummyPath, outputPath);
                    console.log(`Copied dummy video without audio for segment ${index}`);

                    // Clean up dummy file
                    try {
                      fs.unlinkSync(dummyPath);
                    } catch (unlinkError) {
                      console.error(`Error removing dummy file: ${unlinkError.message}`);
                    }

                    return outputPath;
                  }
                } else {
                  // No audio file, just use the dummy video
                  fs.copyFileSync(dummyPath, outputPath);
                  console.log(`Copied dummy video for segment ${index} (no audio)`);

                  // Clean up dummy file
                  try {
                    fs.unlinkSync(dummyPath);
                  } catch (unlinkError) {
                    console.error(`Error removing dummy file: ${unlinkError.message}`);
                  }

                  return outputPath;
                }
              }
            } catch (dummyError) {
              console.error(`Error creating dummy video: ${dummyError.message}`);
            }

            // If all else fails, fall back to copying the original segment
            console.log(`All approaches failed. Copying original segment as last resort for segment ${index}`);
            fs.copyFileSync(segmentPath, outputPath);

            // If we need to add audio, we'll do it in a separate step
            if (audioFilePath && fs.existsSync(audioFilePath)) {
              console.log(`Adding audio to original segment ${index} as last resort`);

              // Create a simple FFmpeg command to add audio only
              const audioArgs = [
                '-y',
                '-i', outputPath,
                '-i', audioFilePath,
                '-c:v', 'copy',
                '-c:a', 'aac',
                '-b:a', `${audioBitrate}k`,
                '-map', '0:v',
                '-map', '1:a',
                '-shortest',
                '-max_muxing_queue_size', '9999',
                '-avoid_negative_ts', 'make_zero',
                `${outputPath}.tmp`
              ];

              try {
                await execWithLog.bind({ type })(event, 'ffmpeg', audioArgs);

                // Replace the output file with the new one
                if (fs.existsSync(`${outputPath}.tmp`)) {
                  fs.unlinkSync(outputPath);
                  fs.renameSync(`${outputPath}.tmp`, outputPath);
                  console.log(`Successfully added audio to original segment ${index}`);
                }
              } catch (audioError) {
                console.error(`Error adding audio to original segment: ${audioError.message}`);
                // Continue with the original copy
              }
            }

            return outputPath;
          } catch (copyError) {
            console.error(`Error handling very small segment: ${copyError.message}`);
            // Fall back to normal FFmpeg processing
          }
        }

        // Execute FFmpeg for normal segments
        await execWithLog.bind({ type })(event, 'ffmpeg', ffmpegArgs);
      } catch (ffmpegError) {
        console.error(`Error in FFmpeg: ${ffmpegError.message}`);

        // If FFmpeg fails for a very small segment, try to copy the original
        try {
          const stats = fs.statSync(segmentPath);
          if (stats.size < 500) {
            console.warn(`FFmpeg failed for very small segment. Copying original as fallback for segment ${index}`);
            fs.copyFileSync(segmentPath, outputPath);
            return outputPath;
          }
        } catch (statError) {
          // Continue with the original error
        }

        throw new Error(`FFmpeg failed: ${ffmpegError.message}`);
      }
    }

    // Give the file system a moment to catch up
    await new Promise(resolve => setTimeout(resolve, 500));

    // Verify the output file was created
    if (!fs.existsSync(outputPath)) {
      console.log(`Processed segment file not found, waiting longer: ${outputPath}`);

      // Try waiting a bit longer
      await new Promise(resolve => setTimeout(resolve, 1500));

      if (!fs.existsSync(outputPath)) {
        throw new Error(`Failed to create processed segment: ${outputPath}`);
      }
    }

    console.log(`Successfully processed segment ${index}`);
    return outputPath;
  } catch (error) {
    console.error(`Error processing segment ${index}:`, error);
    event?.sender?.send(type, { data: `Error processing segment ${index}: ${error.message}`, code: 1 });
    // Return original segment if processing fails
    return segmentPath;
  }
}
/**
 * Join video segments into a single video
 * @param {Object} event - Electron event object
 * @param {Array} segmentPaths - Array of segment paths
 * @param {string} outputPath - Path to the output video
 * @param {string} videoCodec - Video codec to use
 * @param {string} audioBitrate - Audio bitrate in kbps
 * @returns {Promise<string>} - Path to the joined video
 */
async function joinVideoSegments(event, segmentPaths, outputPath, videoCodec = 'h264_nvenc', audioBitrate = '192') {
  const type = 'join-segments-res';

  try {
    // Verify all segment paths exist and have valid sizes
    const validSegmentPaths = [];
    const problematicSegments = [];

    // Process each segment path
    for (const p of segmentPaths) {
      if (!fs.existsSync(p)) {
        console.error(`Segment file not found: ${p}`);
        event?.sender?.send(type, { data: `Warning: Segment file not found: ${p}. Skipping this segment.`, code: 1 });
        continue;
      }

      // Validate the segment file
      try {
        const validation = await validateVideoFile(p);

        if (!validation.valid) {
          console.warn(`Problematic segment detected: ${validation.error}`);
          event?.sender?.send(type, {
            data: `Warning: Problematic segment detected: ${validation.error}. Will attempt to fix.`,
            code: 1
          });

          problematicSegments.push({
            path: p,
            size: validation.size,
            error: validation.error,
            is261Bytes: validation.is261Bytes,
            tooSmall: validation.tooSmall,
            tooShortDuration: validation.tooShortDuration,
            durationError: validation.durationError
          });

          // Still include it in validSegmentPaths, we'll handle it specially later
        } else if (validation.warning) {
          // Valid but with warnings
          console.warn(validation.warning);
          event?.sender?.send(type, { data: validation.warning, code: 1 });
        }

        // Add to valid segments
        validSegmentPaths.push(p);
      } catch (validationError) {
        console.error(`Error validating segment file: ${validationError.message}`);
        // Include it anyway, we'll handle errors later
        validSegmentPaths.push(p);
      }
    }

    if (validSegmentPaths.length === 0) {
      throw new Error('No valid segment files found for joining');
    }

    // If we have problematic segments, log a warning
    if (problematicSegments.length > 0) {
      console.warn(`Found ${problematicSegments.length} problematic segments that may cause issues`);
      event?.sender?.send(type, {
        data: `Warning: Found ${problematicSegments.length} very small segments that may cause issues. Will attempt to fix.`,
        code: 1
      });
    }

    // Create a temporary file list
    const tempDir = path.dirname(outputPath);
    const fileListPath = path.join(tempDir, 'filelist.txt');

    // Create a directory for fixed segments
    const fixedSegmentsDir = path.join(tempDir, 'fixed_segments');
    if (!fs.existsSync(fixedSegmentsDir)) {
      fs.mkdirSync(fixedSegmentsDir, { recursive: true });
    }

    // Fix problematic segments if needed
    let segmentsToJoin = [...validSegmentPaths]; // Start with all valid segments

    if (problematicSegments.length > 0) {
      console.log(`Attempting to fix ${problematicSegments.length} problematic segments...`);
      event?.sender?.send(type, {
        data: `Fixing ${problematicSegments.length} problematic segments before joining...`,
        code: 0
      });

      // Process each problematic segment
      for (const segment of problematicSegments) {
        const segmentPath = segment.path;
        const segmentIndex = validSegmentPaths.indexOf(segmentPath);
        if (segmentIndex === -1) continue; // Skip if not found

        // Create a replacement segment
        const fixedPath = path.join(fixedSegmentsDir, `fixed_${path.basename(segmentPath)}`);

        // Try to use handleVeryShortSegment for better results
        try {
          console.log(`Using specialized handler for problematic segment: ${segmentPath}`);
          event?.sender?.send(type, {
            data: `Fixing problematic segment with specialized handler...`,
            code: 0
          });

          // Get a start time (doesn't matter for dummy video)
          const startTime = "00:00:00";

          // Use the specialized handler
          const result = await handleVeryShortSegment(
            event,
            segmentPath, // Try to use the original as input if possible
            fixedPath,
            startTime,
            fixedSegmentsDir,
            segmentIndex,
            'libx264', // Use CPU encoding for better compatibility
            '192'
          );

          if (result.success) {
            // Replace the problematic segment with the fixed one
            segmentsToJoin[segmentIndex] = fixedPath;
            console.log(`Successfully fixed problematic segment with specialized handler`);
            continue; // Skip to next segment
          } else {
            console.error(`Specialized handler failed: ${result.error}`);
            // Fall back to simple approach
          }
        } catch (handlerError) {
          console.error(`Error using specialized handler: ${handlerError.message}`);
          // Fall back to simple approach
        }

        // Fall back to creating a simple replacement
        try {
          console.log(`Creating simple replacement for problematic segment: ${segmentPath}`);

          // Try to get dimensions from the original file
          let width = 1280;
          let height = 720;

          try {
            const inputProbe = await new Promise((resolve, reject) => {
              const ffprobe = spawn('ffprobe', [
                '-v', 'error',
                '-select_streams', 'v:0',
                '-show_entries', 'stream=width,height',
                '-of', 'json',
                segmentPath
              ]);

              let output = '';
              ffprobe.stdout.on('data', (data) => {
                output += data.toString();
              });

              ffprobe.on('close', (code) => {
                if (code === 0) {
                  try {
                    resolve(JSON.parse(output));
                  } catch (e) {
                    reject(e);
                  }
                } else {
                  reject(new Error(`ffprobe exited with code ${code}`));
                }
              });

              ffprobe.on('error', reject);
            });

            if (inputProbe && inputProbe.streams && inputProbe.streams[0]) {
              width = inputProbe.streams[0].width || width;
              height = inputProbe.streams[0].height || height;
              console.log(`Detected input dimensions: ${width}x${height}`);
            }
          } catch (probeError) {
            console.error(`Error getting input dimensions: ${probeError.message}`);
            // Continue with default dimensions
          }

          // Create a 3-second dummy video as replacement
          const dummyArgs = [
            '-y',
            '-f', 'lavfi',
            '-i', `color=c=black:s=${width}x${height}:r=30`,
            '-t', '3.0',
            '-c:v', 'libx264',
            '-pix_fmt', 'yuv420p',
            '-profile:v', 'baseline',
            '-preset', 'ultrafast',
            fixedPath
          ];

          await execWithLog.bind({ type })(event, 'ffmpeg', dummyArgs);

          if (fs.existsSync(fixedPath)) {
            // Validate the fixed segment
            const validation = await validateVideoFile(fixedPath);

            if (validation.valid) {
              // Replace the problematic segment with the fixed one
              segmentsToJoin[segmentIndex] = fixedPath;
              console.log(`Successfully replaced problematic segment with fixed version`);
            } else {
              console.error(`Fixed segment is invalid: ${validation.error}`);
              // Try a test pattern as last resort
              const testPatternArgs = [
                '-y',
                '-f', 'lavfi',
                '-i', `testsrc=duration=3:size=${width}x${height}:rate=30`,
                '-c:v', 'libx264',
                '-pix_fmt', 'yuv420p',
                fixedPath
              ];

              await execWithLog.bind({ type })(event, 'ffmpeg', testPatternArgs);

              if (fs.existsSync(fixedPath)) {
                segmentsToJoin[segmentIndex] = fixedPath;
                console.log(`Replaced problematic segment with test pattern`);
              }
            }
          }
        } catch (fixError) {
          console.error(`Error fixing problematic segment: ${fixError.message}`);
          // Keep the original segment if fixing fails
        }
      }
    }

    // Ensure the output path has _rendered suffix
    const fileExt = path.extname(outputPath);
    const baseName = path.basename(outputPath, fileExt);
    const finalOutputPath = baseName.includes('_rendered')
      ? outputPath
      : path.join(path.dirname(outputPath), `${baseName}_rendered${fileExt}`);

    console.log(`Joining ${segmentsToJoin.length} segments to: ${finalOutputPath}`);
    event?.sender?.send(type, { data: `Joining ${segmentsToJoin.length} segments to: ${finalOutputPath}`, code: 0 });

    // Write file list
    const fileListContent = segmentsToJoin.map(p => `file '${p.replace(/'/g, "'\\''")}'`).join('\n');
    fs.writeFileSync(fileListPath, fileListContent);
    console.log(`File list created at: ${fileListPath}`);
    console.log(`File list content:\n${fileListContent}`);

    // Prepare FFmpeg command
    const ffmpegArgs = [
      '-y',
      '-f', 'concat',
      '-safe', '0',
      '-i', fileListPath,
      '-c:v', videoCodec,
      '-c:a', 'aac',
      '-b:a', `${audioBitrate}k`,
      // Add error resilience flags to handle potential issues with very short segments
      '-max_muxing_queue_size', '9999',
      '-avoid_negative_ts', 'make_zero',
      finalOutputPath
    ];

    // Execute FFmpeg command
    try {
      await execWithLog.bind({ type })(event, 'ffmpeg', ffmpegArgs);
    } catch (ffmpegError) {
      console.error(`Error in FFmpeg concat: ${ffmpegError.message}`);
      event?.sender?.send(type, {
        data: `Error joining segments: ${ffmpegError.message}. Trying alternative approach...`,
        code: 1
      });

      // Try an alternative approach with a different concat method
      console.log("Trying alternative concat method...");

      // Create a temporary directory for intermediate files
      const tempConcatDir = path.join(tempDir, 'concat_temp');
      if (!fs.existsSync(tempConcatDir)) {
        fs.mkdirSync(tempConcatDir, { recursive: true });
      }

      // Try to process each segment individually first
      const processedSegments = [];
      for (let i = 0; i < segmentsToJoin.length; i++) {
        const segPath = segmentsToJoin[i];
        const processedPath = path.join(tempConcatDir, `processed_${i}${path.extname(segPath)}`);

        // Check if this is a very small segment
        let isVerySmall = false;
        try {
          const stats = fs.statSync(segPath);
          isVerySmall = stats.size < 1000;
          if (isVerySmall) {
            console.warn(`Small segment detected (${stats.size} bytes) at index ${i}. Using special handling.`);
          }
        } catch (statError) {
          console.error(`Error checking segment size: ${statError.message}`);
        }

        if (isVerySmall) {
          // For very small segments, create a dummy video instead of trying to process them
          const dummyArgs = [
            '-y',
            '-f', 'lavfi',
            '-i', 'color=c=black:s=1280x720:r=30',
            '-t', '2.0',
            '-c:v', 'libx264',
            '-pix_fmt', 'yuv420p',
            processedPath
          ];

          try {
            console.log(`Creating dummy replacement for small segment at index ${i}`);
            await execWithLog.bind({ type })(event, 'ffmpeg', dummyArgs);

            if (fs.existsSync(processedPath)) {
              const stats = fs.statSync(processedPath);
              if (stats.size > 1000) {
                processedSegments.push(processedPath);
                console.log(`Successfully created dummy replacement for segment ${i}`);
                continue; // Skip to next segment
              }
            }
          } catch (dummyError) {
            console.error(`Error creating dummy segment: ${dummyError.message}`);
            // Fall back to normal processing
          }
        }

        // Process with more compatible settings
        const processArgs = [
          '-y',
          '-i', segPath,
          '-c:v', 'libx264',  // Use libx264 for better compatibility
          '-preset', 'ultrafast',
          '-pix_fmt', 'yuv420p',
          // Force a reasonable duration
          '-t', '2.0', // Limit to 2 seconds to avoid issues with very short segments
          // Add audio settings
          '-c:a', 'aac',
          '-b:a', `${audioBitrate}k`,
          // Add error resilience flags
          '-max_muxing_queue_size', '9999',
          '-avoid_negative_ts', 'make_zero',
          '-vsync', 'cfr', // Constant frame rate
          processedPath
        ];

        try {
          await execWithLog.bind({ type })(event, 'ffmpeg', processArgs);
          if (fs.existsSync(processedPath)) {
            // Verify the processed file has a reasonable size
            const stats = fs.statSync(processedPath);
            if (stats.size > 1000) {
              processedSegments.push(processedPath);
              console.log(`Successfully processed segment ${i}`);
            } else {
              console.warn(`Processed segment ${i} is too small (${stats.size} bytes). Creating dummy replacement.`);

              // Create a dummy replacement
              const dummyArgs = [
                '-y',
                '-f', 'lavfi',
                '-i', 'testsrc=duration=2:size=1280x720:rate=30',
                '-c:v', 'libx264',
                '-pix_fmt', 'yuv420p',
                processedPath
              ];

              try {
                await execWithLog.bind({ type })(event, 'ffmpeg', dummyArgs);
                if (fs.existsSync(processedPath)) {
                  processedSegments.push(processedPath);
                  console.log(`Created test pattern replacement for segment ${i}`);
                } else {
                  // If all else fails, use the original segment
                  processedSegments.push(segPath);
                }
              } catch (dummyError) {
                console.error(`Error creating dummy replacement: ${dummyError.message}`);
                // Use the original segment as last resort
                processedSegments.push(segPath);
              }
            }
          } else {
            // If processing fails, use the original segment
            console.warn(`Processed segment file not found. Using original segment ${i}`);
            processedSegments.push(segPath);
          }
        } catch (processError) {
          console.error(`Error processing segment ${i}: ${processError.message}`);
          // Try to use the original segment
          processedSegments.push(segPath);
        }
      }

      if (processedSegments.length === 0) {
        throw new Error("Failed to process any segments for joining");
      }

      // Create a new file list
      const altFileListPath = path.join(tempConcatDir, 'alt_filelist.txt');
      const altFileListContent = processedSegments.map(p => `file '${p.replace(/'/g, "'\\''")}'`).join('\n');
      fs.writeFileSync(altFileListPath, altFileListContent);

      // Try concat again with the processed segments
      const altFfmpegArgs = [
        '-y',
        '-f', 'concat',
        '-safe', '0',
        '-i', altFileListPath,
        '-c', 'copy',  // Just copy streams without re-encoding
        '-max_muxing_queue_size', '9999',
        '-avoid_negative_ts', 'make_zero',
        finalOutputPath
      ];

      await execWithLog.bind({ type })(event, 'ffmpeg', altFfmpegArgs);
    }

    // Give the file system a moment to catch up
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Verify the output file was created
    if (!fs.existsSync(finalOutputPath)) {
      console.error(`Output file not found, waiting longer: ${finalOutputPath}`);
      event?.sender?.send(type, { data: `Waiting for output file to be created...`, code: 0 });

      // Try waiting a bit longer
      await new Promise(resolve => setTimeout(resolve, 3000));

      if (!fs.existsSync(finalOutputPath)) {
        throw new Error(`Failed to create output file: ${finalOutputPath}`);
      }
    }

    // Clean up file list
    if (fs.existsSync(fileListPath)) {
      fs.unlinkSync(fileListPath);
    }

    return finalOutputPath;
  } catch (error) {
    console.error('Error joining video segments:', error);
    event?.sender?.send(type, { data: `Error joining video segments: ${error.message}`, code: 1 });
    throw error;
  }
}

/**
 * Format time in seconds to HH:MM:SS format
 * @param {number} seconds - Time in seconds
 * @returns {string} - Formatted time
 */
function formatTimeHMS(seconds) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
}

/**
 * Format time in seconds to ASS time format (H:MM:SS.cc)
 * @param {number} seconds - Time in seconds
 * @returns {string} - Formatted time
 */
function formatAssTime(seconds) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  const centiseconds = Math.floor((seconds % 1) * 100);
  return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}.${centiseconds.toString().padStart(2, '0')}`;
}

module.exports = {
  renderVideoWithSrt
};
