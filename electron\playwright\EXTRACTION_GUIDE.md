# Novel Chapter Extraction Guide

This guide explains how to use the new `performExtraction` functionality to extract chapter data and pagination information from novel websites.

## Overview

The extraction system has been redesigned to:
- Extract chapter data using CSS selectors: `dd > a[href]`
- Extract pagination data using CSS selectors: `a[href*="p"]`
- Navigate through multiple pages automatically
- Handle browser initialization and error recovery

## Functions Available

### 1. `performExtraction(event, url)`

Extracts data from a single page.

**Parameters:**
- `event`: Event object (can be null for testing)
- `url`: Target URL (optional, uses default if not provided)

**Returns:**
```javascript
{
    chapters: [
        {
            index: 1,
            href: "39705235.html",
            title: "第1章 神奇体彩店",
            text: "第1章 神奇体彩店",
            fullUrl: "https://44xw.com/a/149/148289/39705235.html"
        }
        // ... more chapters
    ],
    pagination: {
        currentPage: 1,
        nextPages: [
            {
                href: "p2.html",
                text: "2",
                fullUrl: "https://44xw.com/a/149/148289/p2.html",
                pageNumber: 2
            }
            // ... more pages
        ],
        hasNext: true
    },
    bookInfo: {
        title: "消失20年，我归来即最强天师",
        author: "冷言leng语",
        category: "都市小说",
        status: "连载",
        lastUpdate: "2024-08-15 02:45:55",
        firstChapter: {
            title: "第1章 神奇体彩店",
            href: "39705235.html",
            fullUrl: "https://44xw.com/a/149/148289/39705235.html"
        },
        latestChapter: {
            title: "第635章 尊上首次的装逼，大结局",
            href: "44445325.html",
            fullUrl: "https://44xw.com/a/149/148289/44445325.html"
        }
    },
    currentUrl: "https://44xw.com/a/149/148289/",
    pageTitle: "消失20年，我归来即最强天师"
}
```

### 2. `performExtractionMultiplePages(event, startUrl, maxPages)`

Extracts data from multiple pages sequentially.

**Parameters:**
- `event`: Event object (can be null for testing)
- `startUrl`: Starting URL (optional, uses default if not provided)
- `maxPages`: Maximum number of pages to process (default: 10)

**Returns:**
```javascript
{
    chapters: [
        {
            index: 1,
            href: "39705235.html",
            title: "第1章 神奇体彩店",
            text: "第1章 神奇体彩店",
            fullUrl: "https://44xw.com/a/149/148289/39705235.html",
            pageNumber: 1,
            sourceUrl: "https://44xw.com/a/149/148289/"
        }
        // ... chapters from all pages
    ],
    totalPages: 3,
    totalChapters: 300,
    bookInfo: {
        title: "消失20年，我归来即最强天师",
        author: "冷言leng语",
        category: "都市小说",
        status: "连载",
        lastUpdate: "2024-08-15 02:45:55",
        firstChapter: { /* ... */ },
        latestChapter: { /* ... */ }
    }
}
```

### 3. Test Functions

#### `testExtraction(event, url)`
Test function for single page extraction with detailed logging.

#### `testMultiPageExtraction(event, startUrl, maxPages)`
Test function for multi-page extraction with detailed logging.

## Usage Examples

### Basic Single Page Extraction

```javascript
const { performExtraction } = require('./playwrightLogic');

async function extractChapters() {
    try {
        const data = await performExtraction(null, 'https://44xw.com/a/149/148289/');
        
        console.log(`Found ${data.chapters.length} chapters`);
        console.log(`Current page: ${data.pagination.currentPage}`);
        
        // Process chapters
        data.chapters.forEach(chapter => {
            console.log(`${chapter.index}. ${chapter.title} - ${chapter.fullUrl}`);
        });
        
    } catch (error) {
        console.error('Extraction failed:', error);
    }
}
```

### Multi-Page Extraction

```javascript
const { performExtractionMultiplePages } = require('./playwrightLogic');

async function extractAllChapters() {
    try {
        const data = await performExtractionMultiplePages(
            null, 
            'https://44xw.com/a/149/148289/', 
            5  // Extract from 5 pages max
        );
        
        console.log(`Total chapters: ${data.totalChapters}`);
        console.log(`Pages processed: ${data.totalPages}`);
        
        // Group chapters by page
        const chaptersByPage = {};
        data.chapters.forEach(chapter => {
            if (!chaptersByPage[chapter.pageNumber]) {
                chaptersByPage[chapter.pageNumber] = [];
            }
            chaptersByPage[chapter.pageNumber].push(chapter);
        });
        
        // Process each page
        Object.keys(chaptersByPage).forEach(pageNum => {
            console.log(`Page ${pageNum}: ${chaptersByPage[pageNum].length} chapters`);
        });
        
    } catch (error) {
        console.error('Multi-page extraction failed:', error);
    }
}
```

### Using Test Functions

```javascript
const { testExtraction, testMultiPageExtraction } = require('./puppeteerService');

// Test single page
await testExtraction(null, 'https://44xw.com/a/149/148289/');

// Test multiple pages
await testMultiPageExtraction(null, 'https://44xw.com/a/149/148289/', 3);
```

## Running Tests

To test the extraction functionality:

```bash
node electron/playwright/test-extraction.js
```

This will run both single-page and multi-page extraction tests with detailed output.

## Error Handling

The extraction functions include robust error handling:

- **Browser Initialization**: Automatically initializes browser if not available
- **Page Navigation**: Falls back to different wait strategies if navigation fails
- **Session Recovery**: Reinitializes browser context if session is closed
- **Rate Limiting**: Includes delays between page requests in multi-page extraction

## Customization

### Changing Selectors

To adapt for different websites, modify the selectors in the `performExtraction` function:

```javascript
// For chapters
const chapterElements = document.querySelectorAll('your-chapter-selector');

// For pagination
const paginationElements = document.querySelectorAll('your-pagination-selector');
```

### Changing Default URL

Modify the `navigateToPage` function to change the default URL:

```javascript
const navigateToPage = async (page, url = 'your-default-url') => {
    await page.goto(url, { waitUntil: 'networkidle' });
};
```

## Browser Requirements

- Uses Playwright for browser automation
- Requires Chrome/Chromium browser
- Supports both headless and headed modes
- Includes stealth plugins to avoid detection
