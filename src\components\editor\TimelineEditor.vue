<template>
  <div class="timeline-editor h-full w-full flex flex-col bg-gray-900 text-white">
    <!-- Timeline Toolbar -->
    <TimelineToolbar />

    <!-- Timeline Content -->
    <div class="timeline-content flex-1 flex flex-col overflow-hidden">
      <!-- Timeline Ruler -->
      <TimelineRuler />

      <!-- Timeline Tracks Container -->
      <div
        ref="timelineContainer"
        class="timeline-tracks flex-1 overflow-auto relative"
        @scroll="handleScroll"
        @wheel="handleWheel"
      >
        <!-- Timeline Content with proper width -->
        <div
          class="timeline-content relative"
          :style="{
            width: Math.max(timelineStore.totalTimelineWidth, timelineStore.timelineWidth) + 'px',
            height: timelineStore.trackHeight + 'px'
          }"
        >
          <!-- Timeline Track -->
          <TimelineTrack
            @context-menu="handleContextMenu"
            @item-action="handleItemAction"
          />

          <!-- Playhead -->
          <TimelinePlayhead />

        </div>
      </div>

      <!-- Timeline Layers Panel -->
      <!-- <TimelineLayersPanel /> -->
    </div>

    <!-- Timeline Footer -->
    <div class="timeline-footer flex items-center justify-between p-2 bg-gray-800 border-t border-gray-600 text-xs">
      <div class="flex items-center gap-4">
        <span>Time: {{ formatTime(timelineStore.currentTime) }}</span>
        <span>Duration: {{ formatTime(timelineStore.duration) }}</span>
        <span v-if="timelineStore.selectedItems.length > 0">
          Selected: {{ timelineStore.selectedItems.length }}
        </span>
      </div>

      <div class="flex items-center gap-2">
        <span>Grid: {{ timelineStore.gridSize }}s</span>
        <span>Zoom: {{ Math.round(timelineStore.zoom * 100) }}%</span>
      </div>
    </div>

    <!-- Context Menu -->
    <TimelineContextMenu
      :visible="contextMenu.visible"
      :position="contextMenu.position"
      :click-time="contextMenu.clickTime"
      @close="closeContextMenu"
      @action="handleContextMenuAction"
    />

    <!-- Subtitle Insert Modal -->
    <TimelineSubtitleInsertModal
      v-model:open="insertModal.visible"
      :click-time="insertModal.clickTime"
      @confirm="handleInsertConfirm"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useTimelineStore } from '@/stores/timeline-store'
import { state } from '@/lib/state'
import TimelineToolbar from './TimelineToolbar.vue'
import TimelineRuler from './TimelineRuler.vue'
import TimelineTrack from './TimelineTrack.vue'
import TimelinePlayhead from './TimelinePlayhead.vue'
import TimelineContextMenu from './TimelineContextMenu.vue'
import TimelineLayersPanel from './TimelineLayersPanel.vue'
import TimelineSubtitleInsertModal from './TimelineSubtitleInsertModal.vue'
import { useTTSStore } from '@/stores/ttsStore';
import { message, Modal } from 'ant-design-vue';
import { useI18n } from '@/i18n/i18n';
import { parseTimeToSeconds } from '@/lib/utils';
import { secondsToSRTTime, reorderSubtitleIds } from '@/lib/subtitleUtils';
import { useVideoLayersStore } from '@/stores/video-layers-store';


const timelineStore = useTimelineStore()
const videoLayersStore = useVideoLayersStore()
const ttsStore = useTTSStore()
const { t } = useI18n()

const props = defineProps({
  toInsert: String
})

const customLayers = computed(() => timelineStore.layerItems || [])

const timelineContainer = ref(null)
const copyData = ref(null)

// Context menu state
const contextMenu = ref({
  visible: false,
  position: { x: 0, y: 0 },
  clickTime: 0
})

// Subtitle insert modal state
const insertModal = ref({
  visible: false,
  clickTime: 0
})

// Computed
const subtitleItems = computed(() => timelineStore.subtitleItems)
const projectInfo = computed(() => timelineStore.projectInfo)

// Methods
const formatTime = (seconds) => {
  if (!seconds || isNaN(seconds)) return '00:00.000'

  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  const ms = Math.floor((seconds % 1) * 1000)

  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(3, '0')}`
}

const handleScroll = (event) => {
  const scrollLeft = event.target.scrollLeft
  timelineStore.setScrollLeft(scrollLeft)
}



const handleWheel = (event) => {
  // Ctrl + wheel for zoom
  if (event.ctrlKey) {
    event.preventDefault()
    const delta = event.deltaY > 0 ? -0.1 : 0.1
    timelineStore.setZoom(timelineStore.zoom + delta)
  } else {
    // Normal wheel for horizontal scrolling
    event.preventDefault()

    if (timelineContainer.value) {
      // Adjust scroll speed based on modifier keys
      const baseScrollSpeed = 50
      const scrollSpeed = event.shiftKey ? baseScrollSpeed * 3 : baseScrollSpeed // Faster with Shift
      const deltaX = event.deltaY // Use vertical wheel for horizontal scroll
      const newScrollLeft = timelineContainer.value.scrollLeft + (deltaX * scrollSpeed / 100)

      // Clamp scroll position
      const maxScrollLeft = timelineContainer.value.scrollWidth - timelineContainer.value.clientWidth
      const clampedScrollLeft = Math.max(0, Math.min(newScrollLeft, maxScrollLeft))

      timelineContainer.value.scrollLeft = clampedScrollLeft
      timelineStore.setScrollLeft(clampedScrollLeft)


    }
  }
}

const updateTimelineWidth = () => {
  if (timelineContainer.value) {
    timelineStore.setTimelineWidth(timelineContainer.value.clientWidth)
  }
}

const updateDuration = () => {
  if (subtitleItems.value.length > 0) {
    // Calculate duration from last subtitle end time
    const maxEndTime = Math.max(projectInfo.value?.info?.duration || 0, ...subtitleItems.value.map(item => item.endTime || 0))
    timelineStore.setDuration(Math.max(maxEndTime + 2, 30)) // Add 2 seconds buffer, minimum 30s
  } else {
    timelineStore.setDuration(30) // Default 30 seconds when no data
  }
}

// Context menu methods
const handleContextMenu = (event, clickTime = 0) => {
  event.preventDefault?.()

  contextMenu.value = {
    visible: true,
    position: { x: event.clientX, y: event.clientY },
    clickTime: clickTime
  }

  // Close context menu when clicking elsewhere
  const closeOnClick = (e) => {
    if (!e.target.closest('.timeline-context-menu')) {
      closeContextMenu()
      document.removeEventListener('click', closeOnClick)
    }
  }

  setTimeout(() => {
    document.addEventListener('click', closeOnClick)
  }, 0)
}

const closeContextMenu = () => {
  contextMenu.value.visible = false
}

const handleContextMenuAction = (action, data) => {
  console.log('Context menu action:', action, data)
  // data is id
  // TODO: Implement specific actions
  switch (action) {
    case 'edit':
      // Open edit dialog
      break
    case 'split':
      // Split subtitle
      break
    case 'duplicate':
      // Duplicate subtitle
      break
    case 'merge':
      // Merge subtitles
      break
    case 'delete':
      // Delete subtitles
      handleDeleteSubtitles(data)
      break
    case 'insert':
      // Insert new subtitle at clicked position
      // data is the clickTime from context menu, but we need to recalculate it properly
      openInsertModal(data - timelineStore.scrollLeft)
      break
    case 'copy':
      // Copy to clipboard
      timelineStore.setCopyData(data)
      break
    case 'paste':
      // Paste from clipboard
      const pasteTime = data - timelineStore.scrollLeft
      handlePasteSubtitles(pasteTime)
      break
    case 'generate-audio':
      // Generate audio for single item
      generateAudioForSubtitle(data)
      break
    case 'generate-audio-batch':
      // Generate audio for multiple items
      break
    case 'align':
      // Align timing
      break
  }
}

const handleItemAction = (action, data) => {
  console.log('Item action:', action, data)

  switch (action) {
    case 'audio-play':
      // Handle audio playback start
      handleAudioPlay(data)
      break
    case 'audio-pause':
      // Handle audio playback pause
      handleAudioPause(data)
      break
    case 'seek-to-time':
      // Handle timeline seeking
      handleSeekToTime(data.time)
      break
    default:
      console.log('Unhandled item action:', action, data)
  }
}

// Audio playback handlers
const handleAudioPlay = async (item) => {
  console.log('Timeline: Audio playing for item', item.id)

  // Update global state
  state.currentPlayingSubtitleId = item.id

  // Start synchronized playback
  await startSynchronizedPlayback(item)
}

// Synchronized playback system
const startSynchronizedPlayback = async (item) => {
  try {
    // Get video player reference
    const videoPlayer = getVideoPlayerElement()
    if (!videoPlayer) {
      console.warn('Video player not found')
      return
    }

    // Calculate timing and speed adjustments
    const subtitleDuration = item.endTime - item.startTime
    const audioUrl = getCurrentAudioUrl(item)

    if (!audioUrl) {
      console.warn('No audio URL found for item', item.id)
      return
    }

    // Get audio duration
    const audioDuration = await getAudioDuration(audioUrl)
    console.log('Audio duration:', audioDuration, 'Subtitle duration:', subtitleDuration)

    // Calculate video playback rate
    let videoPlaybackRate = 1.0
    if (audioDuration > subtitleDuration) {
      // Audio is longer than subtitle duration, slow down video
      videoPlaybackRate = subtitleDuration / audioDuration
      console.log('Slowing down video to rate:', videoPlaybackRate)
    }
    // If audio is shorter, keep video at normal speed

    // Seek video to subtitle start time
    videoPlayer.currentTime = item.startTime

    // Set video playback rate
    videoPlayer.playbackRate = videoPlaybackRate

    // Adjust video volume to balance with subtitle audio
    const originalVideoVolume = videoPlayer.volume
    videoPlayer.volume = originalVideoVolume * 0.3 // Reduce video volume to 30%

    // Start video playback
    await videoPlayer.play()

    // Store original settings for restoration
    state.originalVideoSettings = {
      playbackRate: 1.0,
      volume: originalVideoVolume
    }

    // Mark as synchronized playback
    state.isSynchronizedPlayback = true

    console.log('Synchronized playback started for item:', item.id)
    console.log('Video playback rate:', videoPlaybackRate)
    console.log('Video volume reduced to:', videoPlayer.volume)

  } catch (error) {
    console.error('Error starting synchronized playback:', error)
  }
}

const handleAudioPause = (item) => {
  console.log('Timeline: Audio paused for item', item.id)

  // Stop synchronized playback
  stopSynchronizedPlayback()

  // Clear global state if this was the playing item
  if (state.currentPlayingSubtitleId === item.id) {
    state.currentPlayingSubtitleId = null
  }
}

// Stop synchronized playback and restore video settings
const stopSynchronizedPlayback = () => {
  try {
    const videoPlayer = getVideoPlayerElement()
    if (!videoPlayer) return

    // Pause video
    videoPlayer.pause()

    // Restore original video settings
    if (state.originalVideoSettings) {
      videoPlayer.playbackRate = state.originalVideoSettings.playbackRate
      videoPlayer.volume = state.originalVideoSettings.volume
      state.originalVideoSettings = null
    }

    // Clear synchronized playback flag
    state.isSynchronizedPlayback = false

    console.log('Synchronized playback stopped and video settings restored')
  } catch (error) {
    console.error('Error stopping synchronized playback:', error)
  }
}

// Helper functions
const getVideoPlayerElement = () => {
  // Try multiple possible video player references
  if (state.videoPlayer?.$refs?.video) {
    return state.videoPlayer.$refs.video
  }

  // Try to find video element in DOM
  const videoElement = document.querySelector('video')
  if (videoElement) {
    return videoElement
  }

  return null
}

const getCurrentAudioUrl = (item) => {
  // Get the audio URL based on the selected voice
  if (item.isVoice === 1 && item.audioUrl1) {
    return item.audioUrl1
  } else if (item.isVoice === 2 && item.audioUrl2) {
    return item.audioUrl2
  } else if (item.isVoice === 3 && item.audioUrl3) {
    return item.audioUrl3
  } else if (item.audioUrl) {
    return item.audioUrl
  }
  return null
}

const getAudioDuration = (audioUrl) => {
  return new Promise((resolve, reject) => {
    const audio = new Audio()

    audio.addEventListener('loadedmetadata', () => {
      resolve(audio.duration)
    })

    audio.addEventListener('error', (error) => {
      console.error('Error loading audio:', error)
      reject(error)
    })

    audio.src = audioUrl
  })
}

const handleSeekToTime = (time) => {
  console.log('Timeline: Seeking to time', time)

  // Update timeline position
  timelineStore.setCurrentTime(time)

  // Sync with video player if available
  if (state.videoPlayer?.$refs?.video) {
    const video = state.videoPlayer.$refs.video
    if (Math.abs(video.currentTime - time) > 0.1) {
      video.currentTime = time
    }
  }
}

// Subtitle insert modal methods
const openInsertModal = (clickTime) => {
  console.log('Open insert modal with click time:', clickTime)
  console.log('Click time:', videoLayersStore.preview)
  insertModal.value = {
    visible: true,
    clickTime: videoLayersStore.preview.currentTime
  }
  closeContextMenu()
}

const handleInsertConfirm = (subtitleData) => {
  try {
    const startTime = parseTimeToSeconds(subtitleData.startTime)
    const endTime = parseTimeToSeconds(subtitleData.endTime)

    // Create new subtitle object
    const newSubtitle = {
      id: Date.now().toString(),
      start: subtitleData.startTime,
      end: subtitleData.endTime,
      startTime: startTime,
      endTime: endTime,
      text: subtitleData.text,
      translatedText: subtitleData.translatedText || '',
      status: subtitleData.translatedText ? 'translated' : 'pending',
      isEnabled: true,
      isVoice: 0,
      audioUrl: '',
      audioDuration: 0,
      isGenerated: false,
      isGenerated1: false,
      isGenerated2: false,
      isGenerated3: false,
      audioUrl1: '',
      audioUrl2: '',
      audioUrl3: '',
      audioDuration1: 0,
      audioDuration2: 0,
      audioDuration3: 0,
      duration: 0,
      selectedSpeaker: '',
      speechRate: 0,
      isPlayable: false
    }

    // Find the correct position to insert the subtitle
    let currentItems = [...ttsStore.currentSrtList.items]
    let insertIndex = currentItems.length

    // Find insertion point based on start time
    for (let i = 0; i < currentItems.length; i++) {
      if (currentItems[i].startTime > startTime) {
        insertIndex = i
        break
      }
    }

    // Adjust timing of subsequent subtitles if requested
    if (subtitleData.adjustTiming) {
      const duration = endTime - startTime
      for (let i = insertIndex; i < currentItems.length; i++) {
        currentItems[i].startTime += duration
        currentItems[i].endTime += duration
        currentItems[i].start = secondsToSRTTime(currentItems[i].startTime)
        currentItems[i].end = secondsToSRTTime(currentItems[i].endTime)
      }
    }

    // Insert the new subtitle
    currentItems.splice(insertIndex, 0, newSubtitle)
    currentItems = reorderSubtitleIds(currentItems);

    // Update the store
    ttsStore.currentSrtList.items = currentItems

    // Save state for undo/redo
    timelineStore.saveState()

    // Select the new subtitle
    timelineStore.selectItem(newSubtitle.id)

    // Seek to the new subtitle
    timelineStore.setCurrentTime(startTime)

    message.success('Đã chèn subtitle thành công')

  } catch (error) {
    console.error('Error inserting subtitle:', error)
    message.error('Lỗi khi chèn subtitle: ' + error.message)
  }
}




// Audio generation functions
const generateAudioForSubtitle = async (id, useTranslatedText = true) => {
  const subtitle = ttsStore.currentSrtList.items.find(s => s.id === id);
  // if (generatingAudio.value) return;

  const text = useTranslatedText ? subtitle.translatedText : subtitle.text;
  if (!text) {
    message.error(useTranslatedText ? 'No translated text available' : 'No text available');
    return;
  }

  // Check if any voice is selected for this subtitle
  if (!subtitle.isVoice || subtitle.isVoice === 0) {
    message.error(t('voiceConfig.selectVoice'));
    return;
  }

  try {
    // generatingAudio.value = true;
    state.currentPlayingSubtitleId = subtitle.id;

    // Generate audio for the selected voice
    const voiceNumber = subtitle.isVoice;
    const voiceConfig = ttsStore.selectedVoiceConfig[`voice${voiceNumber}`];
    await generateSingleVoiceAudio(subtitle, text, voiceConfig, voiceNumber);

    message.success(t('voiceConfig.audioGenerated'));
  } catch (error) {
    console.error('Error generating audio:', error);
    message.error('Error generating audio: ' + error.message);
  } finally {
    // generatingAudio.value = false;
    state.currentPlayingSubtitleId = null;
  }
};

const generateSingleVoiceAudio = async (subtitle, text, voiceConfig, voiceNumber) => {
  if (!voiceConfig.enabled) return;

  const audio_config = {
    speech_rate: voiceConfig.speed,
    volume_gain_db: voiceConfig.volume,
    pitch: voiceConfig.pitch,
    rate: voiceConfig.rate,
    trim: voiceConfig.trim
  };

  try {
    const response = await window.electronAPI.generateTTS({
      text: text,
      speaker: voiceConfig.speaker,
      workspaceId: ttsStore.workspaceId,
      cookie: ttsStore.cookie,
      typeEngine: ttsStore.typeEngine,
      language: ttsStore.selectedVoiceConfig.language,
      audio_config
    });

    if (response.success) {
      // Update the subtitle with the audio URL
      const updatedSubtitles = [...ttsStore.currentSrtList.items];
      const index = updatedSubtitles.findIndex(s => s.id === subtitle.id);

      if (index !== -1) {
        updatedSubtitles[index] = {
          ...updatedSubtitles[index],
          audioUrl: response.audioUrl,
          [`audioUrl${voiceNumber}`]: response.audioUrl,
          [`audioDuration${voiceNumber}`]: response.duration,
          [`isGenerated${voiceNumber}`]: true
        };


        ttsStore.currentSrtList.items = updatedSubtitles;

        // Add to generated audios in the store
        const newAudio = {
          id: Date.now() + voiceNumber,
          text: text,
          speaker: voiceConfig.speaker,
          url: response.audioUrl,
          duration: response.duration,
          timestamp: new Date().toISOString(),
          voice: ttsStore.speakers.find(s => s.id === voiceConfig.speaker)?.name || '',
          subtitleId: subtitle.id,
          voiceNumber: voiceNumber
        };

        ttsStore.generatedAudios.push(newAudio);
      }
    } else {
      throw new Error(response.message || 'Failed to generate audio');
    }
  } catch (error) {
    console.error(`Error generating audio for voice ${voiceNumber}:`, error);
    throw error;
  }
};
















// Watch for video player time updates
watch(() => state.currentTime, (newTime) => {
  if (newTime !== undefined && newTime !== timelineStore.currentTime) {
    timelineStore.setCurrentTime(newTime)
  }
})

// Watch for timeline current time changes to sync with video
watch(() => timelineStore.currentTime, (newTime) => {
  if (state.videoPlayer?.$refs?.video) {
    const video = state.videoPlaye?.$refs?.video
    if (video&&Math.abs(video.currentTime - newTime) > 0.1) {
      video.currentTime = newTime
    }
  }
})

// Watch for subtitle items changes
watch(subtitleItems, () => {
  updateDuration()
}, { immediate: true, deep: true })

// Lifecycle
onMounted(() => {
  updateTimelineWidth()
  updateDuration()

  // Save initial state
  timelineStore.saveState()

  window.addEventListener('resize', updateTimelineWidth)

  // Keyboard shortcuts
  const handleKeydown = (event) => {
    if (event.ctrlKey || event.metaKey) {
      switch (event.key) {
        case 'z':
          event.preventDefault()
          if (event.shiftKey) {
            timelineStore.redo()
          } else {
            timelineStore.undo()
          }
          break
        // case 'a':
        //   event.preventDefault()
        //   // Select all
        //   const allIds = subtitleItems.value.map(item => item.id)
        //   timelineStore.selectMultiple(allIds)
        //   break
      }
    } else {
      switch (event.key) {
        case 'Delete':
        case 'Backspace':
          // Delete selected items
          if (timelineStore.selectedItems.length > 0) {
            // TODO: Implement delete functionality
            console.log('Delete selected items:', timelineStore.selectedItems)
          }
          break
        case 'Escape':
          timelineStore.clearSelection()
          break
      }
    }
  }

  document.addEventListener('keydown', handleKeydown)

  onUnmounted(() => {
    window.removeEventListener('resize', updateTimelineWidth)
    document.removeEventListener('keydown', handleKeydown)
  })
})

const handleDeleteSubtitles = async (id) => {
  console.log('Delete subtitle:', id);
  const isConfirm = await new Promise((resolve) => {
    Modal.confirm({
      title: 'Xác nhận',
      content: 'Bạn có chắc chắn muốn xóa subtitle này?',
      okText: 'Có',
      cancelText: 'Không',
      onOk: () => resolve(true),
      onCancel: () => resolve(false),
    });
  });

  if (isConfirm) {
    // check id is array
    if (Array.isArray(id)) {
      id.forEach((id) => {
        handleDeleteSubtitle(id);
      });
    } else {
      handleDeleteSubtitle(id);
    }
  }

};

const handleDeleteSubtitle = (id) => {
  const currentItems = [...ttsStore.currentSrtList.items];
  const index = currentItems.findIndex(s => s.id === id);
  if (index !== -1) {
    currentItems.splice(index, 1);
    // Reorder IDs and update
    ttsStore.currentSrtList.items = reorderSubtitleIds(currentItems);
    message.success(`Deleted subtitle ${id}`);
  }
};

function handlePasteSubtitles(data, type = 'inside') {
  if (!timelineStore.copyData) {
    message.warning('Không có dữ liệu để paste')
    return;
  }

  try {
    const copiedItemIds = timelineStore.copyData; // is array of ids

    // Find all copied items from the current subtitle list
    const foundItems = copiedItemIds.map(id =>
      ttsStore.currentSrtList.items.find(item => item.id === id)
    ).filter(item => item !== undefined); // Remove any undefined items

    if (foundItems.length === 0) {
      message.error('Không tìm thấy dữ liệu đã copy')
      return;
    }

    // Calculate time difference based on the first item
    const firstItem = foundItems[0];
    const timeDifference = videoLayersStore.preview.currentTime - firstItem.startTime;

    // Create new items with adjusted timing and new IDs
    const newItems = foundItems.map(item => ({
      ...item,
      id: Date.now().toString() + Math.random().toString(36).substring(2, 11), // Generate unique ID
      startTime: item.startTime + timeDifference,
      endTime: item.endTime + timeDifference,
      start: secondsToSRTTime(item.startTime + timeDifference),
      end: secondsToSRTTime(item.endTime + timeDifference),
      // Reset audio generation status for pasted items
      isGenerated: false,
      isGenerated1: false,
      isGenerated2: false,
      isGenerated3: false,
      audioUrl: '',
      audioUrl1: '',
      audioUrl2: '',
      audioUrl3: '',
      audioDuration: 0,
      audioDuration1: 0,
      audioDuration2: 0,
      audioDuration3: 0,
    }));

    // Get current items and find insertion points
    let currentItems = [...ttsStore.currentSrtList.items];

    // Sort new items by start time to maintain order
    newItems.sort((a, b) => a.startTime - b.startTime);

    // Insert each new item at the correct position
    newItems.forEach(newItem => {
      let insertIndex = currentItems.length;

      // Find insertion point based on start time
      for (let i = 0; i < currentItems.length; i++) {
        if (currentItems[i].startTime > newItem.startTime) {
          insertIndex = i;
          break;
        }
      }

      // Insert the new item
      currentItems.splice(insertIndex, 0, newItem);
    });

    // Reorder subtitle IDs to maintain consistency
    currentItems = reorderSubtitleIds(currentItems);

    // Update the store
    ttsStore.currentSrtList.items = currentItems;

    // Save state for undo/redo
    timelineStore.saveState();

    // Select the pasted items
    const newItemIds = newItems.map(item => item.id);
    timelineStore.selectMultiple(newItemIds);

    // Seek to the first pasted item
    timelineStore.setCurrentTime(newItems[0].startTime);

    message.success(`Đã paste ${newItems.length} subtitle thành công`);

  } catch (error) {
    console.error('Error pasting subtitles:', error);
    message.error('Lỗi khi paste subtitle: ' + error.message);
  }
}

function handleAddSubtitles(text) {
  if (!text) {
    message.warning('Không có dữ liệu để chèn')
    return;
  }

  try {
    const copiedItemIds = timelineStore.copyData; // is array of ids

    // Find all copied items from the current subtitle list
    const foundItems = copiedItemIds.map(id =>
      ttsStore.currentSrtList.items.find(item => item.id === id)
    ).filter(item => item !== undefined); // Remove any undefined items

    if (foundItems.length === 0) {
      message.error('Không tìm thấy dữ liệu đã copy')
      return;
    }

    // Calculate time difference based on the first item
    const firstItem = foundItems[0];
    const timeDifference = videoLayersStore.preview.currentTime - firstItem.startTime;

    // Create new items with adjusted timing and new IDs
    const newItems = foundItems.map(item => ({
      ...item,
      id: Date.now().toString() + Math.random().toString(36).substring(2, 11), // Generate unique ID
      startTime: item.startTime + timeDifference,
      endTime: item.endTime + timeDifference,
      start: secondsToSRTTime(item.startTime + timeDifference),
      end: secondsToSRTTime(item.endTime + timeDifference),
      // Reset audio generation status for pasted items
      isGenerated: false,
      isGenerated1: false,
      isGenerated2: false,
      isGenerated3: false,
      audioUrl: '',
      audioUrl1: '',
      audioUrl2: '',
      audioUrl3: '',
      audioDuration: 0,
      audioDuration1: 0,
      audioDuration2: 0,
      audioDuration3: 0,
    }));

    // Get current items and find insertion points
    let currentItems = [...ttsStore.currentSrtList.items];

    // Sort new items by start time to maintain order
    newItems.sort((a, b) => a.startTime - b.startTime);

    // Insert each new item at the correct position
    newItems.forEach(newItem => {
      let insertIndex = currentItems.length;

      // Find insertion point based on start time
      for (let i = 0; i < currentItems.length; i++) {
        if (currentItems[i].startTime > newItem.startTime) {
          insertIndex = i;
          break;
        }
      }

      // Insert the new item
      currentItems.splice(insertIndex, 0, newItem);
    });

    // Reorder subtitle IDs to maintain consistency
    currentItems = reorderSubtitleIds(currentItems);

    // Update the store
    ttsStore.currentSrtList.items = currentItems;

    // Save state for undo/redo
    timelineStore.saveState();

    // Select the pasted items
    const newItemIds = newItems.map(item => item.id);
    timelineStore.selectMultiple(newItemIds);

    // Seek to the first pasted item
    timelineStore.setCurrentTime(newItems[0].startTime);

    message.success(`Đã paste ${newItems.length} subtitle thành công`);

  } catch (error) {
    console.error('Error pasting subtitles:', error);
    message.error('Lỗi khi paste subtitle: ' + error.message);
  }
}


watch(() => props.toInsert, (newText) => {
  if (newText) {
    // call insert like handlePasteSubtitles
    // handleAddSubtitles(newText);
  }
});




</script>

<style scoped>
.timeline-editor {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.timeline-tracks {
  background-image:
    linear-gradient(to right, rgba(255,255,255,0.1) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(255,255,255,0.05) 1px, transparent 1px);
  background-size: 100px 20px;
}

/* Custom scrollbar */
.timeline-tracks::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

.timeline-tracks::-webkit-scrollbar-track {
  background: #1f2937;
}

.timeline-tracks::-webkit-scrollbar-thumb {
  background: #4b5563;
  border-radius: 6px;
}

.timeline-tracks::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

.timeline-tracks::-webkit-scrollbar-corner {
  background: #1f2937;
}
</style>
