import { defineStore } from 'pinia';
import { AssemblyAI } from 'assemblyai';
import { ElevenLabsClient } from '@elevenlabs/elevenlabs-js';
import { HumanMessage, AIMessage, SystemMessage } from '@langchain/core/messages';
import { fileSystem } from '@/logic/fileSystem';
import { edgeTts } from '@/logic/edgeTts';
import AIClient from '@/lib/AIClient';
import { edgeVoices, languages } from '@/lib/edgettsvoices';
import {useTTSStore} from '@/stores/ttsStore';

// Global electronAPI declaration
declare global {
  interface Window {
    electronAPI: any;
  }
}

declare const electronAPI: any;

const whisper: any = {
  0: {
    model: 'tiny',
    level: 'nhỏ nhất',
    description: 'Tốc độ nhanh nhưng độ chính xác thấp. Phù hợp cho thiết bị yếu, có tối ưu riêng cho tiếng Anh.',
  },
  1: {
    model: 'base',
    level: 'cơ bản',
    description: "Cân bằng giữa tốc độ và độ chính xác. Tốt hơn 'tiny' nhưng vẫn nhanh, có tối ưu riêng cho tiếng Anh.",
  },
  2: {
    model: 'small',
    level: 'trung bình',
    description: 'Độ chính xác khá tốt. Phù hợp cho dự án thông thường, có tối ưu riêng cho tiếng Anh.',
  },
  3: {
    model: 'medium',
    level: 'lớn hơn',
    description: "Độ chính xác cao hơn 'small'. Cần phần cứng mạnh hơn, có tối ưu riêng cho tiếng Anh.",
  },
  4: {
    model: 'large',
    level: 'lớn nhất',
    description: 'Chính xác cao nhất nhưng chạy chậm hơn. Cần RAM mạnh.',
  },
};

export interface Language {
  code: string;
  name: string;
}

export interface Voice {
  voiceId: string;
  name: string;
  previewUrl?: string;
  language?: string;
  gender?: 'male' | 'female';
}

export interface SettingsData {
  providerAI: string | null;
  elevenLabs: string | null;
  assemblyAI: string | null;
  optionAPITextToSpeech: 'EdgeTTS' | 'ElevenLabs' | '3in1'
  optionAPISpeechToText: 'Whisper' | 'AssemblyAI' | 'Free';
  conversationText: string;
  conversationHistory: any[];
  selectLevelModalWhisper: number;
  apiKeyGoogleGenerativeAI: string;
  apiKeyElevenLabs: string;
  apiKeyAssemblyAI: string;
  apiKeyElevenLabsClient: string;
  fenjinRoleId: number;
  provider: 'gemini' | 'openai' | 'deepseek' | 'openrouter' | 'anthropic';
}

// edgeTts.getVoices().then(F.l)

// Store
export const useAiManager = defineStore(
  'aiManager',
  () => {
    // State
    const assemblyAI = ref<any>(null);
    const providerAI = ref<any>(null);
    const elevenLabs = ref<any>(null);
    const dataApi = ref<any>(null); // lưu object config đầy đủ
    const loading = ref(false);
    const ttsStore = useTTSStore();

    // Mock language data
    const freeLanguages: Language[] = languages;

    const elevenLabsLanguages: Language[] = [
      { code: 'en', name: 'English' },
      { code: 'ja', name: 'Japanese' },
      { code: 'zh', name: 'Chinese' },
      { code: 'de', name: 'German' },
      { code: 'hi', name: 'Hindi' },
      { code: 'fr', name: 'French' },
      { code: 'ko', name: 'Korean' },
      { code: 'pt', name: 'Portuguese' },
      { code: 'it', name: 'Italian' },
      { code: 'es', name: 'Spanish' },
      { code: 'id', name: 'Indonesian' },
      { code: 'nl', name: 'Dutch' },
      { code: 'tr', name: 'Turkish' },
      { code: 'pl', name: 'Polish' },
      { code: 'sv', name: 'Swedish' },
      { code: 'bg', name: 'Bulgarian' },
      { code: 'ro', name: 'Romanian' },
      { code: 'ar', name: 'Arabic' },
      { code: 'cs', name: 'Czech' },
      { code: 'el', name: 'Greek' },
      { code: 'fi', name: 'Finnish' },
      { code: 'hr', name: 'Croatian' },
      { code: 'ms', name: 'Malay' },
      { code: 'sk', name: 'Slovak' },
      { code: 'da', name: 'Danish' },
      { code: 'ta', name: 'Tamil' },
      { code: 'uk', name: 'Ukrainian' },
      { code: 'ru', name: 'Russian' },
      { code: 'hu', name: 'Hungarian' },
      { code: 'no', name: 'Norwegian' },
    ];
    const settings = reactive<SettingsData>({
      providerAI: null,
      elevenLabs: null,
      assemblyAI: null,
      optionAPITextToSpeech: 'EdgeTTS',
      optionAPISpeechToText: 'Whisper',
      conversationText: 'Tóm tắt nội dung sau bằng tiếng {{language}}',
      conversationHistory: [],
      selectLevelModalWhisper: 3,
      apiKeyGoogleGenerativeAI: '',
      apiKeyElevenLabs: '',
      apiKeyAssemblyAI: '',
      apiKeyElevenLabsClient: '',
      fenjinRoleId: 0,
        provider: 'gemini',
    });
    const showSettingsModal = ref(false);
    // ---------- LOAD API CONFIG ----------
    async function loadApiConfig() {
      try {
        loading.value = true;
        await new Promise((resolve) => setTimeout(resolve, 1000)); // Giả lập delay
        const config = settings;
        dataApi.value = config;
        setClients(config);
      } catch (err) {
        //   handleError(err)
      } finally {
        loading.value = false;
      }
    }

    // ---------- SETUP CLIENTS ----------
    function setClients(config: any) {
      const aaiKey = config.apiKeyAssemblyAI;
      const aiServiceKey = ttsStore.aiServices[settings.provider];
      const elKey = config.apiKeyElevenLabsClient;

      // Get selected model from ttsStore or use default
      const selectedModel = ttsStore.selectedModel || aiServiceKey?.defaultModels?.[0] || 'gemini-1.5-flash';

      assemblyAI.value = aaiKey ? new AssemblyAI({ apiKey: aaiKey }) : null;
      providerAI.value = aiServiceKey ? new AIClient({
        provider: settings.provider,
        apiKey: aiServiceKey.apiKey,
        model: selectedModel
      }) : null;
      elevenLabs.value = elKey ? new ElevenLabsClient({ apiKey: elKey }) : null;
    }

    // ---------- GETTERs (cũ là hàm c, d) ----------
    function getAssemblyAI() {
        // setClients
      setClients(settings);
      if (!assemblyAI.value) throw new Error('Please add an API key for AssemblyAI');
      return assemblyAI.value;
    }
    function getGoogleAI() {
        setClients(settings);
      if (!providerAI.value) throw new Error('Please add an API key for Google Generative AI');
      return providerAI.value;
    }
    const getFreeLanguages = async (): Promise<Language[]> => {
      await new Promise((resolve) => setTimeout(resolve, 100));
      return freeLanguages;
    };
    const getElevenLabsLanguages = async (): Promise<Language[]> => {
      await new Promise((resolve) => setTimeout(resolve, 100));
      return elevenLabsLanguages;
    };
    // ---------- ElevenLabs retry ----------
    async function withElevenLabs(fn: (cli: any) => Promise<any>) {
        // Kiểm tra xem đã có client ElevenLabs chưa
      setClients(settings);
      console.log('Đang sử dụng ElevenLabsClient...', elevenLabs.value);
      let client = elevenLabs.value;
      while (true) {
        try {
          if (!client) throw new Error('Please add an API key for ElevenLabsClient');
          return await fn(client);
        } catch (e: any) {
          // Nếu lỗi key, thử đổi key khác
          if (e.message.includes('Status code: 401') || e.message.includes('Status code: 400')) {
            client = await nextElevenLabsKey();
            elevenLabs.value = client;
            continue;
          }
          throw e;
        }
      }
    }
    // Đổi sang key ElevenLabs kế tiếp
    async function nextElevenLabsKey() {
      let found = false,
        nextKey: any = null;
      dataApi.value.elevenLabsKeys = dataApi.value.elevenLabsKeys.map((item: any) => {
        if (!item.expired && !found) {
          found = true;
          return { ...item, expired: true };
        }
        if (found && !nextKey) nextKey = item.apiKey;
        return item;
      });
      return nextKey ? new ElevenLabsClient({ apiKey: nextKey }) : null;
    }

    // ---------- SHOW SETTINGS MODAL ----------
    const showSettings = () => {
      showSettingsModal.value = true;
    };

    const hideSettings = () => {
      showSettingsModal.value = false;
    };

    // ---------- STT ----------
    async function transcribeAudioByAssemblyAI(path: string, lang: string) {
      const client = getAssemblyAI();
      const buffer = await fileSystem.readFileSync(path);
      return (
        (
          await client.transcripts.transcribe({
            audio: buffer,
            language_code: lang,
            format_text: true,
          })
        ).text || ''
      );
    }

    async function executeSTT(params: { type: string; path: string; lang: string }) {
      switch (params.type) {
        case 'AssemblyAI':
          return await transcribeAudioByAssemblyAI(params.path, params.lang);
        case 'Whisper':
          return await edgeTts.audioToText(params.path, params.lang, whisper[settings.selectLevelModalWhisper].model);
        default:
          throw new Error(`executeSTT Chưa xử lý cho api này ${params.type}!`);
      }
    }
    async function conversationHistory(fenjinId:number) {
      let anli = await electronAPI.invoke('database', 'GptRolesAnli.getDataById', fenjinId);
      let anli_msg = [];
      for (let i = 0; i < anli.length; i++) {
        let tmp = anli[i];
        if (tmp.type == 0) {
          anli_msg.push(new HumanMessage(tmp.text));
        } else {
          anli_msg.push(new AIMessage(tmp.text));
        }
      }
      return anli_msg;
    }

    // ---------- SUMMARIZE (Gemini) ----------
    async function summarize(text: string, lang: string) {
      const client = getGoogleAI();
      const fenjinRoleId = await electronAPI.invoke('database', 'Config.get_fenjin_role_id');
      if (!fenjinRoleId) throw new Error('Chưa có role ID của Fenjin, vui lòng cấu hình lại trong phần cài đặt');
      const fenjin = await electronAPI.invoke('database', 'GptRoles.getDataById', fenjinRoleId);
      if (!fenjin) throw new Error('Không tìm thấy role Fenjin, vui lòng cấu hình lại trong phần cài đặt');
      const conversation = await conversationHistory(fenjinRoleId);
      let sys_msg = [new SystemMessage(fenjin.prompt)];

      const prompt = settings.conversationText.replace('{{language}}', `'${lang}'`);
      const message = `${prompt}:\n\n${text}`;
      let chat_msg = [...sys_msg, ...conversation, new HumanMessage(message)];
      return (await client.call(chat_msg)).text;
    }

    // ---------- TTS ----------
    async function elevenLabsTTS(text: string, outputPath: string, voice: string) {
      return await withElevenLabs(async (cli) => {
        console.log('Đang tạo tệp âm thanh bằng ElevenLabsClient...', voice, text, outputPath);
        const stream = await cli.generate({
          voice,
          text,
          model_id: 'eleven_turbo_v2_5',
        });
        const chunks = [];
        for await (const chunk of stream) chunks.push(chunk);
        const buffer = new Uint8Array(await new Blob(chunks).arrayBuffer());
        await fileSystem.writeFileSync(outputPath, buffer);
        if (!(await fileSystem.existsSync(outputPath))) throw new Error('Tạo tệp âm thanh thất bại');
      });
    }
    async function executeTTS(params: {
      type: string;
      text: string;
      voice: string;
      outputPath: string;
      pitch?: any;
      rate?: any;
      audio_config?: any;
      openaiConfig?: any;
      workspaceId?: string;
      cookie?: string;
      language?: string;
      typeEngine?: any
    }) {
      console.log('Executing TTS with params:', params);

      // For new engines (capcut, tiktok, openai), use electronAPI.generateTTS
      if (params.type === '3in1') {
        const requestConfig: any = {
          text: params.text,
          speaker: params.voice,
          typeEngine: params.typeEngine,
          audio_config: params.audio_config || {},
          workspaceId: params.workspaceId,
          cookie: params.cookie,
          language: params.language
        };

        // Add OpenAI specific config if using OpenAI TTS
        if (params.typeEngine === 'openai' && params.openaiConfig) {
          requestConfig.openaiConfig = params.openaiConfig;
        }

        const response = await electronAPI.generateTTS(requestConfig);

        if (response.success) {
          // Copy the generated file to the desired output path if different
          if (response.audioUrl && response.audioUrl !== params.outputPath) {
            const audioPath = response.audioUrl.replace('file://', '');
            if (audioPath !== params.outputPath) {
              await fileSystem.copyFileSync(audioPath, params.outputPath);
            }
          }
          return {
            success: true,
            outputPath: params.outputPath,
            audioUrl: response.audioUrl,
            duration: response.duration
          };
        } else {
          throw new Error(response.message || 'Failed to generate audio');
        }
      }

      // Legacy engines (EdgeTTS, ElevenLabs)
      switch (params.type) {
        case 'EdgeTTS':
          return await edgeTts.generateAudioByEdgeTTS({
            text: params.text,
            voice: params.voice,
            outputPath: params.outputPath,
            pitch: params.pitch,
            rate: params.rate,
          });
        case 'ElevenLabs':
          return await elevenLabsTTS(params.text, params.outputPath, params.voice);
        default:
          throw new Error(`Chưa xử lý cho api này ${params.type}!`);
      }
    }

    // ---------- GET VOICES (ElevenLabs) ----------
    async function getVoices() {
      return await withElevenLabs(async (cli) => {
        const result = await cli.voices.getAll();
        return result.voices.map((v: any) => ({
          name: v.name,
          previewUrl: v.preview_url,
          voiceId: v.voice_id,
        }));
      });
    }

    // ---------- EXPORT ----------
    return {
      // State
      assemblyAI,
      providerAI,
      elevenLabs,
      loading,
      settings,
      showSettingsModal,
      // Actions
      loadApiConfig,
      showSettings,
      executeSTT,
      transcribeAudioByAssemblyAI,
      summarize,
      getVoices,
      executeTTS,
      getFreeLanguages,
      getElevenLabsLanguages,
      withElevenLabs,
      nextElevenLabsKey,
      getAssemblyAI,
      getGoogleAI,
      hideSettings,
      setClients,
      conversationHistory,
    };
  },
  {
    persist: {
      storage: localStorage,
      pick: ['settings'],
    },
  },
);
