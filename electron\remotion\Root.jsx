import { Composition } from "remotion";
import { VideoComposition } from "./VideoComposition";


export const RemotionRoot = () => {
  return (
    <>
      <Composition
        id="TikTok"
        component={VideoComposition}
        calculateMetadata={async ({ props }) => {
          const { videoConfig ={}, stylingConfig={} } = props;
          if (!props.videoConfig) {
            throw new Error("❌ videoConfig bị thiếu trong inputProps "+JSON.stringify(props));
          }
          const config = videoConfig;
          const playbackRate = (stylingConfig)?.playbackRate ?? 1.0;
          return {
            durationInFrames: Math.round((config.duration * config.fps) / playbackRate),
            fps: config.fps,
            height: config.height,
            width: config.width,
          };
        }}
        defaultProps={{
          srtArray: [],
          videoPath: ''
        }}
      />
    </>
  );
};
