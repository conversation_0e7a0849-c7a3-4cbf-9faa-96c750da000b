<template>
  <div class="prompt-template-manager">
    <!-- Header v<PERSON><PERSON> c<PERSON>c nút action -->
    <div class="flex justify-between items-center mb-6">
      <!-- <h2 class="text-xl font-semibold text-gray-800">Quản lý Prompt Templates</h2> -->
      <div class="flex gap-2">
        <a-button type="primary" @click="showAddModal = true">
          <template #icon><PlusOutlined /></template>
          Thêm Template
        </a-button>
        <a-button @click="showImportModal = true">
          <template #icon><ImportOutlined /></template>
          Import
        </a-button>
        <a-button @click="exportTemplates">
          <template #icon><ExportOutlined /></template>
          Export
        </a-button>
      </div>
    </div>

    <!-- Template hiện tại đang chọn -->
    <div class="current-template mb-6 p-4 bg-blue-200 rounded-lg border border-blue-200">
      <div class="flex justify-between items-center mb-2">
        <h3 class="text-lg font-medium text-blue-800">Template đang sử dụng</h3>
        <a-tag :color="selectedTemplate.isDefault ? 'gold' : 'blue'">
          {{ selectedTemplate.isDefault ? 'Mặc định' : 'Tùy chỉnh' }}
        </a-tag>
      </div>
      <p class="text-blue-700 font-medium">{{ selectedTemplate.name }}</p>
      <p class="text-blue-600 text-sm">{{ selectedTemplate.description }}</p>
    </div>

    <!-- Danh sách templates -->
    <div class="templates-list">
      <a-row :gutter="[16, 16]">
        <a-col :span="24" :lg="12" v-for="template in promptTemplates" :key="template.id">
          <a-card
            :class="['template-card', { 'selected': template.id === selectedTemplateId }]"
            size="small"
          >
            <template #title>
              <div class="flex justify-between items-center">
                <span class="truncate">{{ template.name }}</span>
                <div class="flex gap-1">
                  <a-tag v-if="template.isDefault" color="gold" size="small">Mặc định</a-tag>
                  <a-tag v-if="template.id === selectedTemplateId" color="green" size="small">Đang dùng</a-tag>
                </div>
              </div>
            </template>

            <template #extra>
              <a-dropdown :trigger="['click']">
                <a-button type="text" size="small">
                  <template #icon><MoreOutlined /></template>
                </a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item key="select" @click="selectTemplate(template.id)">
                      <CheckOutlined /> Chọn sử dụng
                    </a-menu-item>
                    <a-menu-item key="edit" @click="editTemplate(template)">
                      <EditOutlined /> Chỉnh sửa
                    </a-menu-item>
                    <a-menu-item key="duplicate" @click="duplicateTemplate(template.id)">
                      <CopyOutlined /> Sao chép
                    </a-menu-item>
                    <a-menu-item
                      key="default"
                      @click="setAsDefault(template.id)"
                      :disabled="template.isDefault"
                    >
                      <StarOutlined /> Đặt làm mặc định
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item
                      key="delete"
                      @click="deleteTemplate(template.id)"
                      :disabled="template.isDefault"
                      danger
                    >
                      <DeleteOutlined /> Xóa
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </template>

            <div class="template-content">
              <p class="text-gray-400 text-sm mb-3">{{ template.description }}</p>
              <div class="template-preview bg-gray-800 p-3 rounded text-xs font-mono max-h-32 overflow-y-auto">
                {{ template.template }}
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- Modal thêm/sửa template -->
    <a-modal
      v-model:open="showEditModal"
      :title="editingTemplate.id ? 'Chỉnh sửa Template' : 'Thêm Template mới'"
      width="800px"
      @ok="saveTemplate"
      @cancel="cancelEdit"
    >
      <a-form :model="editingTemplate" layout="vertical">
        <a-form-item label="Tên Template" required>
          <a-input v-model:value="editingTemplate.name" placeholder="Nhập tên template" />
        </a-form-item>

        <a-form-item label="Mô tả">
          <a-textarea
            v-model:value="editingTemplate.description"
            placeholder="Mô tả ngắn gọn về template này"
            :rows="2"
          />
        </a-form-item>

        <a-form-item label="Nội dung Template" required>
          <div class="mb-2">
            <a-alert
              message="Placeholder có thể sử dụng: {sourceLang}, {targetLang}, {dictionaryText}, {dictionaryRule}, {duplicateRule}"
              type="info"
              show-icon
              closable
            />
          </div>
          <a-textarea
            v-model:value="editingTemplate.template"
            placeholder="Nhập nội dung template với các placeholder"
            :rows="12"
            class="font-mono text-sm"
          />
        </a-form-item>

        <a-form-item>
          <a-checkbox v-model:checked="editingTemplate.isDefault">
            Đặt làm template mặc định
          </a-checkbox>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- Modal import -->
    <a-modal
      v-model:open="showImportModal"
      title="Import Templates"
      @ok="importTemplates"
      @cancel="showImportModal = false"
    >
      <a-form layout="vertical">
        <a-form-item label="JSON Templates">
          <a-textarea
            v-model:value="importData"
            placeholder="Paste JSON data here..."
            :rows="10"
            class="font-mono text-sm"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import { usePromptStore } from '@/stores/promptStore'
import {
  PlusOutlined,
  ImportOutlined,
  ExportOutlined,
  MoreOutlined,
  CheckOutlined,
  EditOutlined,
  CopyOutlined,
  StarOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'

const promptStore = usePromptStore()

// Computed properties
const promptTemplates = computed(() => promptStore.promptTemplates)
const selectedTemplate = computed(() => promptStore.selectedTemplate)
const selectedTemplateId = computed(() => promptStore.selectedTemplateId)

// Modal states
const showEditModal = ref(false)
const showAddModal = ref(false)
const showImportModal = ref(false)

// Form data
const editingTemplate = ref({
  id: null,
  name: '',
  description: '',
  template: '',
  isDefault: false
})

const importData = ref('')

// Methods
// Define emits
const emit = defineEmits(['template-selected'])

const selectTemplate = (id) => {
  const template = promptStore.selectTemplate(id)
  message.success('Đã chọn template')
  emit('template-selected', template)
}

const editTemplate = (template) => {
  editingTemplate.value = { ...template }
  showEditModal.value = true
}

const addTemplate = () => {
  editingTemplate.value = {
    id: null,
    name: '',
    description: '',
    template: '',
    isDefault: false
  }
  showEditModal.value = true
}

const saveTemplate = () => {
  if (!editingTemplate.value.name.trim()) {
    message.error('Vui lòng nhập tên template')
    return
  }

  if (!editingTemplate.value.template.trim()) {
    message.error('Vui lòng nhập nội dung template')
    return
  }

  try {
    if (editingTemplate.value.id) {
      // Update existing template
      promptStore.updateTemplate(editingTemplate.value.id, editingTemplate.value)
      message.success('Đã cập nhật template')
    } else {
      // Add new template
      promptStore.addTemplate(editingTemplate.value)
      message.success('Đã thêm template mới')
    }

    showEditModal.value = false
  } catch (error) {
    message.error('Lỗi: ' + error.message)
  }
}

const cancelEdit = () => {
  showEditModal.value = false
  editingTemplate.value = {
    id: null,
    name: '',
    description: '',
    template: '',
    isDefault: false
  }
}

const duplicateTemplate = (id) => {
  const newTemplate = promptStore.duplicateTemplate(id)
  if (newTemplate) {
    message.success('Đã sao chép template')
  }
}

const deleteTemplate = (id) => {
  try {
    promptStore.deleteTemplate(id)
    message.success('Đã xóa template')
  } catch (error) {
    message.error('Lỗi: ' + error.message)
  }
}

const setAsDefault = (id) => {
  promptStore.setAsDefault(id)
  message.success('Đã đặt làm template mặc định')
}

const exportTemplates = () => {
  const data = promptStore.exportTemplates()
  const blob = new Blob([data], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = 'prompt-templates.json'
  a.click()
  URL.revokeObjectURL(url)
  message.success('Đã export templates')
}

const importTemplates = () => {
  try {
    const templates = JSON.parse(importData.value)
    if (!Array.isArray(templates)) {
      throw new Error('Dữ liệu phải là một mảng templates')
    }

    const imported = promptStore.importTemplates(templates)
    message.success(`Đã import ${imported.length} templates`)
    showImportModal.value = false
    importData.value = ''
  } catch (error) {
    message.error('Lỗi import: ' + error.message)
  }
}

// Watch for add modal
import { watch } from 'vue'
watch(showAddModal, (newVal) => {
  if (newVal) {
    addTemplate()
    showAddModal.value = false
  }
})
</script>

<style scoped>
.template-card {
  transition: all 0.3s ease;
  cursor: pointer;
}

.template-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.template-card.selected {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.template-preview {
  white-space: pre-wrap;
  word-break: break-word;
}

.current-template {
  border-left: 4px solid #1890ff;
}
</style>
