<template>
  <div class="space-y-3">
    <!-- Drag and drop area -->
    <div
      :class="`border-2 rounded-md transition-colors relative ${
        isDragging ? 'border-blue-400 bg-blue-50' : 'border-gray-200 border-dashed'
      }`"
      @dragenter="handleDragEnter"
      @dragleave="handleDragLeave"
      @dragover="handleDragOver"
      @drop="handleDrop"
    >
      <div class="p-2 text-center">
        <input
          type="file"
          id="video-upload"
          accept="video/*"
          @change="handleVideoUpload"
          class="hidden"
        />
        <label
          for="video-upload"
          class="cursor-pointer flex flex-col items-center justify-center py-2"
        >
          <Upload class="h-5 w-5 mb-1 text-gray-400" />
          <p class="text-sm text-gray-500">
            {{ isDragging ? t('preview.dropVideoHere') : t('preview.dragAndDropVideo') }}
          </p>
          <p class="text-xs text-gray-400 mt-1">{{ t('fileUpload.orClickToSelect') }}</p>
        </label>
      </div>
    </div>

    <!-- Video info and controls -->
    <div class="flex justify-between items-center" v-if="videoUrl">
      <div v-if="videoName" class="text-xs flex items-center px-2 py-1 bg-gray-50 rounded-md flex-1 mr-2">
        <div class="font-medium truncate">
          <span class="text-gray-500 mr-1">{{ t('preview.videoName') }}</span> {{ videoName }}
        </div>
        <div v-if="videoDuration > 0" class="flex items-center gap-1 text-gray-500 ml-2 flex-shrink-0">
          <span class="text-gray-500 mr-1">{{ t('preview.videoDuration') }}</span>
          <Clock size="12" />
          <span>{{ formatTime(videoDuration) }}</span>
        </div>
      </div>

      <a-button
        @click="toggleSubtitles"
        variant="outline"
        size="sm"
        class="whitespace-nowrap"
      >
        {{ showSubtitles ? t('preview.hideSubtitles') : t('preview.showSubtitles') }}
      </a-button>
    </div>

    <!-- Subtitle mode selector -->
    <div v-if="subtitles.length > 0 && videoUrl">
      <Tabs :defaultValue="subtitleMode" @update:modelValue="handleSubtitleModeChange">
        <TabsList class="grid grid-cols-3 w-full">
          <TabsTrigger value="translated">{{ t('preview.translatedOnly') }}</TabsTrigger>
          <TabsTrigger value="bilingual">{{ t('preview.bilingual') }}</TabsTrigger>
          <TabsTrigger value="original">{{ t('preview.originalOnly') }}</TabsTrigger>
        </TabsList>
      </Tabs>
    </div>

    <!-- Video player -->
    <div class="relative rounded-md overflow-hidden bg-black justify-center items-center w-[50%]" v-if="videoUrl">
      <template v-if="videoUrl">
        <video
          ref="videoRef"
          :src="videoUrl"
          controls
          crossorigin="anonymous"
          @timeupdate="handleTimeUpdate"
          @loadedmetadata="handleVideoLoaded"
          @loadeddata="handleVideoLoaded"

        ></video>
        <div v-if="loading" class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
          <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-white"></div>
          <span class="text-white ml-2">{{ t('preview.loadingVideo') }}</span>
        </div>
      </template>
      <div v-else class="flex flex-col items-center justify-center h-full text-white p-4">
        <Upload class="h-8 w-8 mb-2 opacity-60" />
        <p class="text-center opacity-80">{{ t('preview.uploadVideo') }}</p>
      </div>
    </div>
    
    <!-- Subtitle hint -->
    <div class="text-xs text-gray-500 text-center" v-if="videoUrl">
      <p v-if="showSubtitles && subtitles.length > 0">{{ t('preview.fullScreenSubtitleTip') }}</p>
    </div>

    <!-- Video Balancer Component (commented out as in original) -->
    <!-- <VideoBalancer
      v-if="videoUrl && subtitles.length > 0"
      :subtitles="subtitles"
      :videoRef="videoRef"
      :videoDuration="videoDuration"
      @export-balanced-srt="handleExportBalancedSrt"
    /> -->
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onBeforeUnmount, nextTick } from 'vue';
import { Upload, Clock } from 'lucide-vue-next';
// import { Button } from '@/components/ui/button';
import { Tabs, TabsList, TabsTrigger } from '@/ui/tabs';
// import VideoBalancer from '@/components/VideoBalancer.vue';
import { useI18n } from '@/i18n/i18n';

const props = defineProps({
  subtitles: {
    type: Array,
    default: () => []
  },
  isTranslating: {
    type: Boolean,
    default: false
  },
  currentPlayingSubtitleId: {
    type: Number,
    default: null
  },
  selectedMode: {
    type: String,
    default: 'default',
    validator: (value) => ['default', 'sidebyside'].includes(value)
  }
});

const emit = defineEmits(['subtitleChange', 'modeChange']);

const { t } = useI18n();

const videoRef = ref(null);
const videoUrl = ref(null);
const currentTime = ref(0);
const showSubtitles = ref(true);
const subtitleMode = ref('original');
const loading = ref(false);
const videoName = ref('');
const videoDuration = ref(0);
const isDragging = ref(false);
const vttUrl = ref(null);
const originalVttUrl = ref(null);
const bilingualVttUrl = ref(null);
const activeSubtitleId = ref(null);
const balancedSrtUrl = ref(null);

// Convert subtitles to WebVTT format
const convertSubtitlesToVTT = (subtitles, mode = 'translated') => {
  // WebVTT header
  let vttContent = 'WEBVTT\n\n';

  // Add each subtitle
  subtitles.forEach(subtitle => {
    // Convert time format from SRT (00:00:00,000) to VTT (00:00:00.000)
    const startTime = subtitle.start.replace(',', '.');
    const endTime = subtitle.end.replace(',', '.');

    let text = '';
    if (mode === 'original') {
      text = subtitle.text;
    } else if (mode === 'translated') {
      text = subtitle.translatedText || subtitle.text;
    } else if (mode === 'bilingual') {
      text = `${subtitle.text}\n${subtitle.translatedText || (props.isTranslating ? t('subtitleTable.translating') : t('subtitleTable.waitingToTranslate'))}`;
    }

    // Format VTT string for this subtitle
    vttContent += `${subtitle.id}\n${startTime} --> ${endTime}\n${text}\n\n`;
  });

  return vttContent;
};

// Create a URL for VTT file
const createVTTUrl = (content) => {
  const blob = new Blob([content], { type: 'text/vtt' });
  return URL.createObjectURL(blob);
};

// Handle video upload
const handleVideoUpload = (e) => {
  const file = e.target.files?.[0];
  if (!file) return;

  handleVideoFile(file);
};

// Process video file
const handleVideoFile = (file) => {
  // Check file type
  if (!file.type.startsWith('video/')) {
    alert(t('preview.invalidVideoType'));
    return;
  }

  // Revoke old URL to avoid memory leaks
  if (videoUrl.value) {
    URL.revokeObjectURL(videoUrl.value);
  }

  loading.value = true;
  videoName.value = file.name;
  const newUrl = URL.createObjectURL(file);
  videoUrl.value = newUrl;
};

// Drag and drop handlers
const handleDragEnter = (e) => {
  e.preventDefault();
  e.stopPropagation();
  isDragging.value = true;
};

const handleDragLeave = (e) => {
  e.preventDefault();
  e.stopPropagation();
  isDragging.value = false;
};

const handleDragOver = (e) => {
  e.preventDefault();
  e.stopPropagation();
  isDragging.value = true;
};

const handleDrop = (e) => {
  e.preventDefault();
  e.stopPropagation();
  isDragging.value = false;

  const files = e.dataTransfer.files;
  if (files && files.length > 0) {
    handleVideoFile(files[0]);
  }
};

// Handle video loaded metadata
const handleVideoLoaded = () => {
  loading.value = false;
  if (videoRef.value) {
    videoDuration.value = videoRef.value.duration;
  }
};

// Convert time from "00:00:00,000" format to seconds
const timeToSeconds = (timeString) => {
  const [time, milliseconds] = timeString.split(',');
  const [hours, minutes, seconds] = time.split(':').map(Number);
  return hours * 3600 + minutes * 60 + seconds + Number(milliseconds) / 1000;
};

// Format time from seconds to hh:mm:ss
const formatTime = (seconds) => {
  const h = Math.floor(seconds / 3600);
  const m = Math.floor((seconds % 3600) / 60);
  const s = Math.floor(seconds % 60);
  return [h, m, s].map(v => v.toString().padStart(2, '0')).join(':');
};

// Update active subtitle based on current time
const updateActiveSubtitle = (currentTime) => {
  if (props.subtitles.length === 0) return;

  // Find the subtitle currently being displayed
  const activeSubtitle = props.subtitles.find(sub => {
    const startTime = timeToSeconds(sub.start);
    const endTime = timeToSeconds(sub.end);
    // Extend a bit to avoid boundary cases
    return currentTime >= startTime - 0.1 && currentTime <= endTime + 0.1;
  });

  // Update active subtitle ID
  const newActiveId = activeSubtitle?.id || null;
  if (newActiveId !== activeSubtitleId.value) {
    activeSubtitleId.value = newActiveId;
    // Notify parent component
    emit('subtitleChange', newActiveId);
  }
};

// Handle video time update
const handleTimeUpdate = () => {
  if (videoRef.value) {
    currentTime.value = videoRef.value.currentTime;
    updateActiveSubtitle(videoRef.value.currentTime);
  }
};

// Toggle subtitles visibility
const toggleSubtitles = () => {
  showSubtitles.value = !showSubtitles.value;
  updateSubtitleTrack();
};

// Handle subtitle mode change
const handleSubtitleModeChange = (value) => {
  subtitleMode.value = value;
  updateSubtitleTrack();
};

// Update the subtitle track based on mode changes
const updateSubtitleTrack = () => {
  if (!videoRef.value) return;

  // Remove all current tracks
  const tracks = videoRef.value.querySelectorAll('track');
  tracks.forEach(track => track.remove());

  // If subtitles are hidden, don't add new track
  if (!showSubtitles.value) return;

  // Add new track based on selected mode
  if (videoRef.value && props.subtitles.length > 0) {
    const track = document.createElement('track');
    track.kind = 'subtitles';
    track.label = 'Subtitles';
    track.default = true;

    if (subtitleMode.value === 'translated' && vttUrl.value) {
      track.src = vttUrl.value;
    } else if (subtitleMode.value === 'original' && originalVttUrl.value) {
      track.src = originalVttUrl.value;
    } else if (subtitleMode.value === 'bilingual' && bilingualVttUrl.value) {
      track.src = bilingualVttUrl.value;
    }

    videoRef.value.appendChild(track);

    // Activate track
    setTimeout(() => {
      if (videoRef.value && videoRef.value.textTracks[0]) {
        videoRef.value.textTracks[0].mode = 'showing';
      }
    }, 100);
  }
};

// Handle exporting balanced SRT
const handleExportBalancedSrt = (srtContent) => {
  // Revoke previous URL if exists
  if (balancedSrtUrl.value) {
    URL.revokeObjectURL(balancedSrtUrl.value);
  }

  // Create a new blob and URL
  const blob = new Blob([srtContent], { type: 'text/plain' });
  const newUrl = URL.createObjectURL(blob);
  balancedSrtUrl.value = newUrl;

  // Automatically trigger download
  const downloadLink = document.createElement('a');
  downloadLink.href = newUrl;
  downloadLink.download = `${videoName.value.replace(/\.[^/.]+$/, '')}_balanced.srt`;
  document.body.appendChild(downloadLink);
  downloadLink.click();
  document.body.removeChild(downloadLink);
};

// Watch for changes in subtitles to update VTT URLs
watch(() => [props.subtitles, subtitleMode.value, props.isTranslating], () => {
  if (props.subtitles.length > 0) {
    // Revoke old URLs to avoid memory leaks
    if (vttUrl.value) URL.revokeObjectURL(vttUrl.value);
    if (originalVttUrl.value) URL.revokeObjectURL(originalVttUrl.value);
    if (bilingualVttUrl.value) URL.revokeObjectURL(bilingualVttUrl.value);

    // Create new URLs for each mode
    const translatedVtt = convertSubtitlesToVTT(props.subtitles, 'translated');
    const originalVtt = convertSubtitlesToVTT(props.subtitles, 'original');
    const bilingualVtt = convertSubtitlesToVTT(props.subtitles, 'bilingual');

    vttUrl.value = createVTTUrl(translatedVtt);
    originalVttUrl.value = createVTTUrl(originalVtt);
    bilingualVttUrl.value = createVTTUrl(bilingualVtt);

    // Update subtitle track when VTT changes
    nextTick(() => {
      updateSubtitleTrack();
    });
  }
}, { deep: true });

// Watch for changes in subtitle mode or visibility
watch([subtitleMode, showSubtitles], () => {
  updateSubtitleTrack();
});

// Watch for external changes to currentPlayingSubtitleId
watch(() => props.currentPlayingSubtitleId, (newId) => {
  if (newId !== activeSubtitleId.value) {
    activeSubtitleId.value = newId;

    // If we have a video loaded, try to seek to the timestamp of this subtitle
    if (videoRef.value && videoUrl.value && newId) {
      const subtitle = props.subtitles.find(sub => sub.id === newId);
      if (subtitle) {
        const startTime = timeToSeconds(subtitle.start);

        // Start a short delayed playback after seeking
        videoRef.value.currentTime = startTime;

        // Optionally auto-play after seeking
        // videoRef.value.play().catch(err => {
        //   // Auto-play might be blocked by browser policy, that's fine
        //   console.log("Auto-play after seeking failed:", err);
        // });
      }
    }
  }
});

// Set up interval for more frequent time tracking
let timeTrackingInterval = null;

onMounted(() => {
    console.log(props.subtitles);
    
  timeTrackingInterval = setInterval(() => {
    if (videoRef.value && videoUrl.value && props.subtitles.length > 0 && !videoRef.value.paused) {
      updateActiveSubtitle(videoRef.value.currentTime);
    }
  }, 100);
});

onBeforeUnmount(() => {
  // Clear interval
  if (timeTrackingInterval) {
    clearInterval(timeTrackingInterval);
  }
  
  // Revoke object URLs to avoid memory leaks
  if (videoUrl.value) URL.revokeObjectURL(videoUrl.value);
  if (vttUrl.value) URL.revokeObjectURL(vttUrl.value);
  if (originalVttUrl.value) URL.revokeObjectURL(originalVttUrl.value);
  if (bilingualVttUrl.value) URL.revokeObjectURL(bilingualVttUrl.value);
  if (balancedSrtUrl.value) URL.revokeObjectURL(balancedSrtUrl.value);
});
</script>