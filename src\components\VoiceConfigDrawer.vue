<template>
  <a-drawer
    :open="visible"
    :title="t('voiceConfig.title')"
    placement="right"
    width="400"
    @close="onClose"
  >
    <div class="space-y-6">
      <!-- Language Selection -->
      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          {{ t('voiceConfig.language') }}
        </label>
        <a-select
          v-model:value="config.language"
          class="w-full"
          @change="onLanguageChange"
        >
          <a-select-option value="Tiếng Việt">Tiếng Việt</a-select-option>
          <a-select-option value="English">English</a-select-option>
          <a-select-option value="中文">中文</a-select-option>
        </a-select>
      </div>

      <!-- Voice 1 Configuration -->
      <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
        <div class="flex items-center justify-between mb-3">
          <h3 class="text-md font-medium text-gray-900 dark:text-white">
            {{ t('voiceConfig.voice1') }}
          </h3>
          <a-checkbox v-model:checked="config.voice1.enabled"></a-checkbox>
        </div>

        <div class="space-y-4" :class="{ 'opacity-50': !config.voice1.enabled }">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {{ t('voiceConfig.voiceType') }}
            </label>
            <a-select
              v-model:value="config.voice1.speaker"
              class="w-full"
              :disabled="!config.voice1.enabled"
              @change="testConfig(config.voice1)"
            >
              <a-select-option v-for="speaker in filteredSpeakers" :key="speaker.id" :value="speaker.id">
                {{ speaker.name }}
              </a-select-option>
            </a-select>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.speed') }}
              </label>
              <span class="text-sm text-gray-500">{{ config.voice1.speed }}</span>
            </div>
            <a-slider
              v-model:value="config.voice1.speed"
              :min="0"
              :max="2"
              :step="0.1"
              :disabled="!config.voice1.enabled"
            />
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.pitch') }}
              </label>
              <span class="text-sm text-gray-500">{{ config.voice1.pitch }}</span>
            </div>
            <a-slider
              v-model:value="config.voice1.pitch"
              :min="pitch_rate_min"
              :max="pitch_rate_max"
              :step="1"
              :disabled="!config.voice1.enabled"
            />
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.reverb') }}
              </label>
              <span class="text-sm text-gray-500">{{ config.voice1.reverb }}</span>
            </div>
            <a-slider
              v-model:value="config.voice1.reverb"
              :min="0"
              :max="100"
              :step="10"
              :disabled="!config.voice1.enabled"
            />
          </div>
        </div>
      </div>

      <!-- Voice 2 Configuration (similar structure) -->
      <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
        <div class="flex items-center justify-between mb-3">
          <h3 class="text-md font-medium text-gray-900 dark:text-white">
            {{ t('voiceConfig.voice2') }}
          </h3>
          <a-checkbox v-model:checked="config.voice2.enabled"></a-checkbox>
        </div>

        <div class="space-y-4" :class="{ 'opacity-50': !config.voice2.enabled }">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {{ t('voiceConfig.voiceType') }}
            </label>
            <a-select
              v-model:value="config.voice2.speaker"
              class="w-full"
              :disabled="!config.voice2.enabled"
              @change="testConfig(config.voice2)"
            >
              <a-select-option v-for="speaker in filteredSpeakers" :key="speaker.id" :value="speaker.id">
                {{ speaker.name }}
              </a-select-option>
            </a-select>
          </div>

          <!-- Same sliders as Voice 1 -->
         <div class="grid grid-cols-2 gap-4">
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.speed') }}
              </label>
              <span class="text-sm text-gray-500">{{ config.voice2.speed }}</span>
            </div>
            <a-slider
              v-model:value="config.voice2.speed"
              :min="0"
              :max="2"
              :step="0.1"
              :disabled="!config.voice2.enabled"
            />
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.pitch') }}
              </label>
              <span class="text-sm text-gray-500">{{ config.voice2.pitch }}</span>
            </div>
            <a-slider
              v-model:value="config.voice2.pitch"
              :min="pitch_rate_min"
              :max="pitch_rate_max"
              :step="1"
              :disabled="!config.voice2.enabled"
            />
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.reverb') }}
              </label>
              <span class="text-sm text-gray-500">{{ config.voice2.reverb }}</span>
            </div>
            <a-slider
              v-model:value="config.voice2.reverb"
              :min="0"
              :max="100"
              :step="10"
              :disabled="!config.voice2.enabled"
            />
          </div>
        </div>
      </div>

      <!-- Voice 3 Configuration (similar structure) -->
      <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
        <div class="flex items-center justify-between mb-3">
          <h3 class="text-md font-medium text-gray-900 dark:text-white">
            {{ t('voiceConfig.voice3') }}
          </h3>
          <a-checkbox v-model:checked="config.voice3.enabled"></a-checkbox>
        </div>

        <div class="space-y-4" :class="{ 'opacity-50': !config.voice3.enabled }">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {{ t('voiceConfig.voiceType') }}
            </label>
            <a-select
              v-model:value="config.voice3.speaker"
              class="w-full"
              :disabled="!config.voice3.enabled"
              @change="testConfig(config.voice3)"
            >
              <a-select-option v-for="speaker in filteredSpeakers" :key="speaker.id" :value="speaker.id">
                {{ speaker.name }}
              </a-select-option>
            </a-select>
          </div>

          <!-- Same sliders as Voice 1 and 2 -->
          <div class="grid grid-cols-2 gap-4">
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.speed') }}
              </label>
              <span class="text-sm text-gray-500">{{ config.voice3.speed }}</span>
            </div>
            <a-slider
              v-model:value="config.voice3.speed"
              :min="0"
              :max="2"
              :step="0.1"
              :disabled="!config.voice3.enabled"
            />
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.pitch') }}
              </label>
              <span class="text-sm text-gray-500">{{ config.voice3.pitch }}</span>
            </div>
            <a-slider
              v-model:value="config.voice3.pitch"
              :min="pitch_rate_min"
              :max="pitch_rate_max"
              :step="1"
              :disabled="!config.voice3.enabled"
            />
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.reverb') }}
              </label>
              <span class="text-sm text-gray-500">{{ config.voice3.reverb }}</span>
            </div>
            <a-slider
              v-model:value="config.voice3.reverb"
              :min="0"
              :max="100"
              :step="10"
              :disabled="!config.voice3.enabled"
            />
          </div>
        </div>
      </div>
      <!-- Voice 4 Configuration (similar structure) -->
      <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
        <div class="flex items-center justify-between mb-3">
          <h3 class="text-md font-medium text-gray-900 dark:text-white">
            {{ t('voiceConfig.voice4') }}
          </h3>
          <a-checkbox v-model:checked="config.voice4.enabled"></a-checkbox>
        </div>

        <div class="space-y-4" :class="{ 'opacity-50': !config.voice4.enabled }">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {{ t('voiceConfig.voiceType') }}
            </label>
            <a-select
              v-model:value="config.voice4.speaker"
              class="w-full"
              :disabled="!config.voice4.enabled"
              @change="testConfig(config.voice4)"
            >
              <a-select-option v-for="speaker in filteredSpeakers" :key="speaker.id" :value="speaker.id">
                {{ speaker.name }}
              </a-select-option>
            </a-select>
          </div>

          <!-- Same sliders as Voice 1 and 2 -->
          <div class="grid grid-cols-2 gap-4">
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.speed') }}
              </label>
              <span class="text-sm text-gray-500">{{ config.voice4.speed }}</span>
            </div>
            <a-slider
              v-model:value="config.voice4.speed"
              :min="0"
              :max="2"
              :step="0.1"
              :disabled="!config.voice4.enabled"
            />
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.pitch') }}
              </label>
              <span class="text-sm text-gray-500">{{ config.voice4.pitch }}</span>
            </div>
            <a-slider
              v-model:value="config.voice4.pitch"
              :min="pitch_rate_min"
              :max="pitch_rate_max"
              :step="1"
              :disabled="!config.voice4.enabled"
            />
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.reverb') }}
              </label>
              <span class="text-sm text-gray-500">{{ config.voice4.reverb }}</span>
            </div>
            <a-slider
              v-model:value="config.voice4.reverb"
              :min="0"
              :max="100"
              :step="10"
              :disabled="!config.voice4.enabled"
            />
          </div>
        </div>
      </div>

            <!-- Voice 5 Configuration (similar structure) -->
      <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
        <div class="flex items-center justify-between mb-3">
          <h3 class="text-md font-medium text-gray-900 dark:text-white">
            {{ t('voiceConfig.voice5') }}
          </h3>
          <a-checkbox v-model:checked="config.voice5.enabled"></a-checkbox>
        </div>

        <div class="space-y-4" :class="{ 'opacity-50': !config.voice5.enabled }">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {{ t('voiceConfig.voiceType') }}
            </label>
            <a-select
              v-model:value="config.voice5.speaker"
              class="w-full"
              :disabled="!config.voice5.enabled"
              @change="testConfig(config.voice5)"
            >
              <a-select-option v-for="speaker in filteredSpeakers" :key="speaker.id" :value="speaker.id">
                {{ speaker.name }}
              </a-select-option>
            </a-select>
          </div>

          <!-- Same sliders as Voice 1 and 2 -->
          <div class="grid grid-cols-2 gap-4">
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.speed') }}
              </label>
              <span class="text-sm text-gray-500">{{ config.voice5.speed }}</span>
            </div>
            <a-slider
              v-model:value="config.voice5.speed"
              :min="0"
              :max="2"
              :step="0.1"
              :disabled="!config.voice5.enabled"
            />
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.pitch') }}
              </label>
              <span class="text-sm text-gray-500">{{ config.voice5.pitch }}</span>
            </div>
            <a-slider
              v-model:value="config.voice5.pitch"
              :min="pitch_rate_min"
              :max="pitch_rate_max"
              :step="1"
              :disabled="!config.voice5.enabled"
            />
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.reverb') }}
              </label>
              <span class="text-sm text-gray-500">{{ config.voice5.reverb }}</span>
            </div>
            <a-slider
              v-model:value="config.voice5.reverb"
              :min="0"
              :max="100"
              :step="10"
              :disabled="!config.voice5.enabled"
            />
          </div>
        </div>
      </div>
      <!-- Volume Control -->
      <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
        <h3 class="text-md font-medium text-gray-900 dark:text-white mb-3">
          {{ t('voiceConfig.masterVolume') }}
        </h3>
        <div class="flex justify-between mb-1">
          <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
            {{ t('voiceConfig.volume') }}
          </label>
          <span class="text-sm text-gray-500">{{ config.masterVolume }}%</span>
        </div>
        <a-slider
          v-model:value="config.masterVolume"
          :min="0"
          :max="200"
          :step="5"
        />
      </div>

      <!-- Apply Button -->
      <div class="flex justify-end">
        <a-button type="primary" @click="applyConfig">
          {{ t('common.apply') }}
        </a-button>
      </div>
    </div>
  </a-drawer>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted } from 'vue';
import { useTTSStore } from '@/stores/ttsStore';
import { useI18n } from '@/i18n/i18n';
import { message } from 'ant-design-vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['close', 'apply']);
const ttsStore = useTTSStore();
const { t } = useI18n();

// Reactive configuration object
const config = reactive({ ...ttsStore.selectedVoiceConfig });


const pitch_rate_max = 12;
const pitch_rate_min = -12;


// Filtered speakers based on selected language
const filteredSpeakers = computed(() => {
  console.log(ttsStore.speakers);

  // If OpenAI TTS is enabled, return OpenAI voices
  if (ttsStore.typeEngine === 'openai') {
    const languageMap = {
      'Tiếng Việt': 'vi',
      'English': 'en',
      '中文': 'zh'
    };
    const langCode = languageMap[config.language] || 'en';
    return ttsStore.getOpenAIVoicesByLanguage(langCode).map(voice => ({
      id: voice.id,
      name: voice.name
    }));
  }

  // If Mimimax TTS is enabled, return Mimimax voices
  if (ttsStore.typeEngine === 'mimimax') {
    const mimimaxVoices = ttsStore.getMimimaxVoices();
console.log(mimimaxVoices);

    // Filter by language if needed
    const languageMap = {
      'vi': 'Tiếng Việt',
      'en': 'English',
      'zh': '中文'
    };
    const langCode = languageMap[config.language] || 'zh';

    return mimimaxVoices
      .filter(voice => voice.language === langCode || voice.language === 'vi') // Default to Chinese if no match
      .map(voice => ({
        id: voice.id,
        name: voice.name
      }));
  }

  return ttsStore.speakers.filter(speaker => {
    // Filter logic based on language
    if (config.language === 'Tiếng Việt') {
      return speaker.name.includes('Việt') ||
             speaker.name.includes('VN') ||
             speaker.name.includes('Vietnamese');
    } else if (config.language === 'English') {
      return speaker.name.includes('EN') ||
             speaker.name.includes('English') ||
             speaker.name.includes('GG-');
    } else if (config.language === '中文') {
      return speaker.name.includes('CN') ||
             speaker.name.includes('Chinese');
    }
    return true;
  });
});

// Watch for language changes to update default speakers
watch(() => config.language, (newLanguage) => {
  if (filteredSpeakers.value.length > 0) {
    config.voice1.speaker = filteredSpeakers.value[0].id;
    config.voice2.speaker = filteredSpeakers.value.length > 1 ? filteredSpeakers.value[1].id : filteredSpeakers.value[0].id;
    config.voice3.speaker = filteredSpeakers.value.length > 2 ? filteredSpeakers.value[2].id : filteredSpeakers.value[0].id;
    config.voice4.speaker = filteredSpeakers.value.length > 3 ? filteredSpeakers.value[3].id : filteredSpeakers.value[0].id;
    config.voice5.speaker = filteredSpeakers.value.length > 4 ? filteredSpeakers.value[4].id : filteredSpeakers.value[0].id;
  }
});

// Handle language change
function onLanguageChange(value) {
  config.language = value;
}

// Close drawer
function onClose() {
  emit('close');
}

// Apply configuration
function applyConfig() {
  // console.log('Applying config:', config);
  ttsStore.selectedVoiceConfig = config;
  // emit('apply', { ...config });
  message.success(t('voiceConfig.configApplied'));
  emit('close');
}

onMounted(() => {
  // Initialize with current config
  applyConfig();
});


// test listener if config changed
async function testConfig(params) {
  const text =  `Tìm hiểu về các giọng nói tổng hợp khác nhau có sẵn để sử dụng trong Chuyển văn bản thành giọng nói`
      const audio_config= {}
      if(params.pitch > 0) audio_config.pitch_rate = params.pitch
      if(params.speech > 0) audio_config.speech_rate = params.speech

      try {
        let requestConfig = {
          text,
          speaker: params.speaker,
          typeEngine: ttsStore.typeEngine,
          audio_config
        };

        // Add OpenAI specific config if using OpenAI TTS
        if (ttsStore.typeEngine === 'openai') {
          ttsStore.openaiConfig.selectedVoice = params.speaker
          requestConfig.openaiConfig = {
            apiKey: ttsStore.openaiTTS.apiKey,
            baseURL: ttsStore.openaiTTS.baseURL,
            speed: params.speed || ttsStore.openaiTTS.speed,
            format: ttsStore.openaiTTS.format
          };
        }

        // Add Mimimax specific config if using Mimimax TTS
        if (ttsStore.typeEngine === 'mimimax') {
          ttsStore.mimimaxTTS.selectedVoice = params.speaker
          requestConfig.mimimaxConfig = {
            apiKey: ttsStore.mimimaxTTS.apiKey,
            groupId: ttsStore.mimimaxTTS.groupId,
            selectedVoice: ttsStore.mimimaxTTS.selectedVoice,
            voiceSettings: {
              speed: params.speed || ttsStore.mimimaxTTS.voiceSettings.speed,
              vol: ttsStore.mimimaxTTS.voiceSettings.vol,
              pitch: params.pitch || ttsStore.mimimaxTTS.voiceSettings.pitch
            },
            audioSettings: ttsStore.mimimaxTTS.audioSettings
          };
        }

        // const response = await electronAPI.generateTTS(JSON.parse(JSON.stringify(requestConfig)));

        // if (response.success) {
        //   const audio = new Audio(response.audioUrl);
        //   audio.play();
        // }else{
        //   message.error('Error generating TTS: ' + response.message);
        // }
      } catch (error) {
        console.error('Error generating TTS:', error);
        message.error('Error generating TTS: ' + error.message);
      } finally {
      }
}


</script>
