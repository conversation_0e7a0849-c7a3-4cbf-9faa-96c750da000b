fetch("https://www.iesdouyin.com/web/api/v2/aweme/post/?sec_uid=MS4wLjABAAAAB5L8xoKl5adKWgS0qz0aut93Aavd_TyRZ7MD3WlhiYo&count=10&max_cursor=0", {
  "headers": {
    "accept": "application/json, text/plain, */*",
    "accept-language": "en-US,en;q=0.9,vi;q=0.8",
    "cache-control": "no-cache",
    "pragma": "no-cache",
    "priority": "u=1, i",
    "sec-ch-ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "uifid": "2eb4f745f9fe6544447c1d68cb43a44931f67e23b1951fd9ca8b76ce94a62236461c8022afe9da43257cc4d0346627d657069535ded53cefc1709b7dd5e96f5626befd3f6e386c5f9e44caa9f474849d59c0b927f66d03f1f60fcef06b0465ad797bfd932cd77333dec23b2725d0e0fea3d612a941a7e17ff02e46dbe43c5212306b6e6941234c6dddafd6c212330b3f80cdfd67a23b726a45232def17bb896e68722c4e6eef61e104eb4e7aab052f0f60ba5b8c2c578e96a85c194eff726bd2",
    "cookie": "s_v_web_id=verify_mb2wb2xg_8af201d3_339c_d40a_55a7_05070aeb364c;",
    "Referer": "https://www.douyin.com/search/%E6%88%91%E7%88%B1%E7%9D%A1%E5%A4%A7%E8%A7%89?modal_id=7499094113636207908&type=general",
    "Referrer-Policy": "strict-origin-when-cross-origin"
  },
  "body": null,
  "method": "POST"
}).then(response => {
    console.log(`Response status: ${response.status}`, response);
    if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
})
    .then(data => {
        console.log(data);
        if (data && data.aweme_list) {
            data.aweme_list.forEach(video => {
                const videoUrl = `https://www.douyin.com/video/${video.aweme_id}`;
                result.push(videoUrl);
            });
        }
    })
    .catch(error => console.error('Error fetching video data:', error));