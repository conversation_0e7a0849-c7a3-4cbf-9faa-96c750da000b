const fs = require('fs');


function formatTimestamp(timeInSeconds) {
  const hours = Math.floor(timeInSeconds / 3600);
  const minutes = Math.floor((timeInSeconds % 3600) / 60);
  const seconds = Math.floor(timeInSeconds % 60);
  const centiseconds = Math.floor((timeInSeconds - Math.floor(timeInSeconds)) * 100);
  return `${hours}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}.${String(centiseconds).padStart(2, '0')}`;
}

function highlightCurrentWord(sentence, startIndex, currentWord) {
  const before = sentence.slice(0, startIndex);
  const after = sentence.slice(startIndex);
  const highlighted = after.replace(
    currentWord,
    `{\\c&H00FF8877&}${currentWord}{\\c&HFFFFFF&}`,
  );
  return before + highlighted;
}

function createSubtitles(srtArray) {
  let subtitles = '[Events]\n';
  subtitles += 'Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text\n';

  const fullText = srtArray.map(d => d.translatedText).join('');

  const sentences = fullText.split(/(?<=[。.,、，])\s*/);
  console.log('sentences: ', sentences);
  let currentSentence = sentences.shift();
  console.log(currentSentence);
  let ca = 0;

  for (const chunk of srtArray) {
    const startTime = formatTimestamp(chunk.startTime);
    const endTime = formatTimestamp(chunk.endTime);

    const chunkNoPunctuation = chunk.translatedText.replace(/[。.,、，!?]/g, '');

    if (!currentSentence.slice(ca).includes(chunkNoPunctuation) && sentences.length && sentences[0].includes(chunkNoPunctuation)) {
      currentSentence = sentences.shift();
      ca = 0;
    }

    const index = currentSentence.indexOf(chunk.translatedText, ca);
    if (index !== -1) ca = index;

    const highlightedSentence = highlightCurrentWord(currentSentence, ca, chunk.translatedText);

    subtitles += `Dialogue: 0,${startTime},${endTime},Default,,0000,0000,0000,,${highlightedSentence}\n`;
  }

  return subtitles;
}

function subtitleSync(srtArray, chinese = false, outputPath = './subtitles.ass') {
  const font = chinese ? 'Hanzi-Pinyin-Font' : 'Arial';

  const assHeader = `[Script Info]
ScriptType: v4.00+
Collisions: Normal
PlayDepth: 0
PlayResY: 1080
PlayResX: 1920

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: Default,${font},64,&H00FFFFFF,&H00000000,&H00000000,&H64000000,0,0,0,0,100,100,0,0,1,1,0,2,10,10,20,1
`;

  const subtitleContent = createSubtitles(srtArray);
  fs.writeFileSync(outputPath, assHeader + subtitleContent);

  return outputPath;
}

module.exports = {
  subtitleSync,
};

const srtArray =[
    {
        "index": 1,
        "id": 1,
        "text": "你知道人贩子为什么喜欢偷小孩吗?",
        "startTime": 0.24,
        "endTime": 2.7199999999999998,
        "start": "00:00:00,240",
        "end": "00:00:02,720",
        "translatedText": "Con có biết tại sao bọn buôn người thích bắt cóc trẻ em không?",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\con-co-biet-tai-sao-bon-1747960196878.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\con-co-biet-tai-sao-bon-1747960196878.mp3",
        "audioDuration1": 2745,
        "isGenerated1": true
    },
    {
        "index": 2,
        "id": 2,
        "text": "不知道",
        "startTime": 3.12,
        "endTime": 3.48,
        "start": "00:00:03,120",
        "end": "00:00:03,480",
        "translatedText": "Con không biết",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\con-khong-biet1747960198880.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 2,
        "audioUrl2": "file://F:\\Footage\\0. any\\_-converted_audio\\con-khong-biet1747960198880.mp3",
        "audioDuration2": 745,
        "isGenerated2": true
    },
    {
        "index": 3,
        "id": 3,
        "text": "因为你的脑神经值265万",
        "startTime": 3.48,
        "endTime": 6.74,
        "start": "00:00:03,480",
        "end": "00:00:06,740",
        "translatedText": "Vì dây thần kinh não của con trị giá 2 phẩy 65 triệu tệ, khoảng 9 phẩy 5 tỷ tiền việt",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\vi-day-than-kinh-nao-cua1747960199621.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\vi-day-than-kinh-nao-cua1747960199621.mp3",
        "audioDuration1": 5120,
        "isGenerated1": true
    },
    {
        "index": 4,
        "id": 4,
        "text": "小心脏值75万",
        "startTime": 6.74,
        "endTime": 9.32,
        "start": "00:00:06,740",
        "end": "00:00:09,320",
        "translatedText": "Tim nhỏ trị giá 750 nghìn",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\tim-nho-tri-gia-750-nghi1747960201734.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\tim-nho-tri-gia-750-nghi1747960201734.mp3",
        "audioDuration1": 2304,
        "isGenerated1": true
    },
    {
        "index": 5,
        "id": 5,
        "text": "肾脏值65万",
        "startTime": 9.32,
        "endTime": 11.26,
        "start": "00:00:09,320",
        "end": "00:00:11,260",
        "translatedText": "Thận trị giá 650 nghìn",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\than-tri-gia-650-nghin1747960202846.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\than-tri-gia-650-nghin1747960202846.mp3",
        "audioDuration1": 2075,
        "isGenerated1": true
    },
    {
        "index": 6,
        "id": 6,
        "text": "皮肤血液值120万",
        "startTime": 11.26,
        "endTime": 13.52,
        "start": "00:00:11,260",
        "end": "00:00:13,520",
        "translatedText": "Máu da trị giá 1.2 triệu",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\mau-da-tri-gia-12-trieu1747960722180.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\mau-da-tri-gia-12-trieu1747960722180.mp3",
        "audioDuration1": 1959.184,
        "isGenerated1": true
    },
    {
        "index": 7,
        "id": 7,
        "text": "牙齿值20万",
        "startTime": 13.52,
        "endTime": 15.36,
        "start": "00:00:13,520",
        "end": "00:00:15,360",
        "translatedText": "Răng trị giá 200 nghìn",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\rang-tri-gia-200-nghin1747960761500.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\rang-tri-gia-200-nghin1747960761500.mp3",
        "audioDuration1": 1800,
        "isGenerated1": true
    },
    {
        "index": 8,
        "id": 8,
        "text": "眼睛值7万",
        "startTime": 15.36,
        "endTime": 17.14,
        "start": "00:00:15,360",
        "end": "00:00:17,140",
        "translatedText": "Mắt trị giá 70 nghìn",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\mat-tri-gia-70-nghin1747960204809.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\mat-tri-gia-70-nghin1747960204809.mp3",
        "audioDuration1": 1728,
        "isGenerated1": true
    },
    {
        "index": 9,
        "id": 9,
        "text": "鼻腔值10万",
        "startTime": 17.14,
        "endTime": 18.76,
        "start": "00:00:17,140",
        "end": "00:00:18,760",
        "translatedText": "Khoang mũi trị giá 100 nghìn",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\khoang-mui-tri-gia-100-n1747960772043.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\khoang-mui-tri-gia-100-n1747960772043.mp3",
        "audioDuration1": 1935,
        "isGenerated1": true
    },
    {
        "index": 10,
        "id": 10,
        "text": "肺值30万",
        "startTime": 18.76,
        "endTime": 20.02,
        "start": "00:00:18,760",
        "end": "00:00:20,020",
        "translatedText": "Phổi trị giá 300 nghìn",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\phoi-tri-gia-300-nghin1747960205883.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\phoi-tri-gia-300-nghin1747960205883.mp3",
        "audioDuration1": 1705,
        "isGenerated1": true
    },
    {
        "index": 11,
        "id": 11,
        "text": "还有我们的肝脏值90万呢",
        "startTime": 20.02,
        "endTime": 22.4,
        "start": "00:00:20,020",
        "end": "00:00:22,400",
        "translatedText": "Gan của chúng ta cũng có giá tới 900.000 đấy",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\gan-cua-chung-ta-cung-co1747960775756.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\gan-cua-chung-ta-cung-co1747960775756.mp3",
        "audioDuration1": 3343.673,
        "isGenerated1": true
    },
    {
        "index": 12,
        "id": 12,
        "text": "你要知道这些都是黑市上人体器官的一个价格",
        "startTime": 22.4,
        "endTime": 26.44,
        "start": "00:00:22,400",
        "end": "00:00:26,440",
        "translatedText": "Bạn phải biết đây đều là giá nội tạng người trên chợ đen",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\ban-phai-biet-day-deu-la1747960207205.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\ban-phai-biet-day-deu-la1747960207205.mp3",
        "audioDuration1": 2665,
        "isGenerated1": true
    },
    {
        "index": 13,
        "id": 13,
        "text": "这些要是被人贩子摘掉",
        "startTime": 26.44,
        "endTime": 28.74,
        "start": "00:00:26,440",
        "end": "00:00:28,740",
        "translatedText": "Nếu mấy thứ này bị bọn buôn người lấy đi",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\neu-may-thu-nay-bi-bon-b1747960225248.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\neu-may-thu-nay-bi-bon-b1747960225248.mp3",
        "audioDuration1": 2184,
        "isGenerated1": true
    },
    {
        "index": 14,
        "id": 14,
        "text": "会被卖到全球各地的",
        "startTime": 28.74,
        "endTime": 30.32,
        "start": "00:00:28,740",
        "end": "00:00:30,320",
        "translatedText": "Chúng sẽ bị bán đi khắp nơi trên thế giới",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\chung-se-bi-ban-di-khap-1747960779845.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\chung-se-bi-ban-di-khap-1747960779845.mp3",
        "audioDuration1": 2280,
        "isGenerated1": true
    },
    {
        "index": 15,
        "id": 15,
        "text": "所以你一定要注意",
        "startTime": 30.32,
        "endTime": 32.18,
        "start": "00:00:30,320",
        "end": "00:00:32,180",
        "translatedText": "Vì vậy bạn nhất định phải cẩn thận",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\vi-vay-ban-nhat-dinh-pha1747960209044.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\vi-vay-ban-nhat-dinh-pha1747960209044.mp3",
        "audioDuration1": 1665,
        "isGenerated1": true
    },
    {
        "index": 16,
        "id": 16,
        "text": "不管去哪里",
        "startTime": 32.18,
        "endTime": 33.08,
        "start": "00:00:32,180",
        "end": "00:00:33,080",
        "translatedText": "Dù đi bất cứ đâu",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\du-di-bat-cu-dau1747960209804.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\du-di-bat-cu-dau1747960209804.mp3",
        "audioDuration1": 1215,
        "isGenerated1": true
    },
    {
        "index": 17,
        "id": 17,
        "text": "一定不要单独外出",
        "startTime": 33.08,
        "endTime": 34.56,
        "start": "00:00:33,080",
        "end": "00:00:34,560",
        "translatedText": "Tuyệt đối không đi một mình",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\tuyet-doi-khong-di-mot-m1747960782836.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\tuyet-doi-khong-di-mot-m1747960782836.mp3",
        "audioDuration1": 1425,
        "isGenerated1": true
    },
    {
        "index": 18,
        "id": 18,
        "text": "不能走夜路",
        "startTime": 34.56,
        "endTime": 36.04,
        "start": "00:00:34,560",
        "end": "00:00:36,040",
        "translatedText": "Không được đi đường đêm",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\khong-duoc-di-duong-dem1747960210672.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\khong-duoc-di-duong-dem1747960210672.mp3",
        "audioDuration1": 1344,
        "isGenerated1": true
    },
    {
        "index": 19,
        "id": 19,
        "text": "一定要走有摄像头的大马路",
        "startTime": 36.04,
        "endTime": 38.12,
        "start": "00:00:36,040",
        "end": "00:00:38,120",
        "translatedText": "Luôn đi trên những con đường lớn có camera giám sát",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\luon-di-tren-nhung-con-d1747960785148.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\luon-di-tren-nhung-con-d1747960785148.mp3",
        "audioDuration1": 2755,
        "isGenerated1": true
    },
    {
        "index": 20,
        "id": 20,
        "text": "最好呢",
        "startTime": 38.12,
        "endTime": 39.16,
        "start": "00:00:38,120",
        "end": "00:00:39,160",
        "translatedText": "Tốt nhất là...",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\tot-nhat-la1747962181580.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\tot-nhat-la1747962181580.mp3",
        "audioDuration1": 865,
        "isGenerated1": true
    },
    {
        "index": 21,
        "id": 21,
        "text": "要结伴同行",
        "startTime": 39.16,
        "endTime": 40.16,
        "start": "00:00:39,160",
        "end": "00:00:40,160",
        "translatedText": "Hãy đi cùng nhau",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\hay-di-cung-nhau1747960212641.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\hay-di-cung-nhau1747960212641.mp3",
        "audioDuration1": 1272,
        "isGenerated1": true
    },
    {
        "index": 22,
        "id": 22,
        "text": "你要远离",
        "startTime": 40.16,
        "endTime": 41.1,
        "start": "00:00:40,160",
        "end": "00:00:41,100",
        "translatedText": "Bạn phải tránh xa",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\ban-phai-tranh-xa1747960789365.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\ban-phai-tranh-xa1747960789365.mp3",
        "audioDuration1": 1135,
        "isGenerated1": true
    },
    {
        "index": 23,
        "id": 23,
        "text": "试图接近你的陌生人",
        "startTime": 41.1,
        "endTime": 42.68,
        "start": "00:00:41,100",
        "end": "00:00:42,680",
        "translatedText": "Những người lạ cố tình tiếp cận bạn",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\nhung-nguoi-la-co-tinh-t1747960213896.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\nhung-nguoi-la-co-tinh-t1747960213896.mp3",
        "audioDuration1": 1765,
        "isGenerated1": true
    },
    {
        "index": 24,
        "id": 24,
        "text": "一定要提高防范意识",
        "startTime": 42.68,
        "endTime": 45.32,
        "start": "00:00:42,680",
        "end": "00:00:45,320",
        "translatedText": "Nhất định phải nâng cao ý thức phòng ngừa",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\nhat-dinh-phai-nang-cao-1747960231447.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\nhat-dinh-phai-nang-cao-1747960231447.mp3",
        "audioDuration1": 2045,
        "isGenerated1": true
    },
    {
        "index": 25,
        "id": 25,
        "text": "保护好你自己的人身安全",
        "startTime": 45.32,
        "endTime": 47.8,
        "start": "00:00:45,320",
        "end": "00:00:47,800",
        "translatedText": "Bảo vệ an toàn cá nhân của chính mình",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\bao-ve-an-toan-ca-nhan-c1747960234148.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\bao-ve-an-toan-ca-nhan-c1747960234148.mp3",
        "audioDuration1": 2160,
        "isGenerated1": true
    },
    {
        "index": 26,
        "id": 26,
        "text": "你要记住",
        "startTime": 47.8,
        "endTime": 48.96,
        "start": "00:00:47,800",
        "end": "00:00:48,960",
        "translatedText": "Bạn phải nhớ kỹ",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\ban-phai-nho-ky1747960285018.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\ban-phai-nho-ky1747960285018.mp3",
        "audioDuration1": 1224,
        "isGenerated1": true
    },
    {
        "index": 27,
        "id": 27,
        "text": "我们的生命只有一次",
        "startTime": 48.96,
        "endTime": 50.78,
        "start": "00:00:48,960",
        "end": "00:00:50,780",
        "translatedText": "Mạng sống chúng ta chỉ có một lần",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\mang-song-chung-ta-chi-c1747960215660.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\mang-song-chung-ta-chi-c1747960215660.mp3",
        "audioDuration1": 1920,
        "isGenerated1": true
    },
    {
        "index": 28,
        "id": 28,
        "text": "所以一定要好好保护自己",
        "startTime": 50.78,
        "endTime": 53.82,
        "start": "00:00:50,780",
        "end": "00:00:53,820",
        "translatedText": "Vì vậy nhất định phải tự bảo vệ mình thật tốt",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\vi-vay-nhat-dinh-phai-tu1747960216460.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\vi-vay-nhat-dinh-phai-tu1747960216460.mp3",
        "audioDuration1": 2328,
        "isGenerated1": true
    },
    {
        "index": 29,
        "id": 29,
        "text": "知道吗",
        "startTime": 53.82,
        "endTime": 54.34,
        "start": "00:00:53,820",
        "end": "00:00:54,340",
        "translatedText": "Hiểu chưa?",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\hieu-chua1747960217412.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\hieu-chua1747960217412.mp3",
        "audioDuration1": 765,
        "isGenerated1": true
    },
    {
        "index": 30,
        "id": 30,
        "text": "知道了",
        "startTime": 54.34,
        "endTime": 54.98,
        "start": "00:00:54,340",
        "end": "00:00:54,980",
        "translatedText": "Con hiểu rồi",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\con-hieu-roi1747960794644.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 2,
        "audioUrl2": "file://F:\\Footage\\0. any\\_-converted_audio\\con-hieu-roi1747960794644.mp3",
        "audioDuration2": 835,
        "isGenerated2": true
    }
]
function generateASS(subs) {
  const header = `
[Script Info]
; Script generated by JS
Title: Default Aegisub file
ScriptType: v4.00+
WrapStyle: 0
ScaledBorderAndShadow: yes
PlayResX: 384
PlayResY: 288
YCbCr Matrix: TV.601

[Aegisub Project Garbage]
Last Style Storage: Default
Video AR Mode: 4
Video AR Value: 1.777778
Active Line: 0
Video Position: 0

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: JP1,Roboto,15,&H00FFFFFF,&H00808080,&H00FFFFFF,&H00222222,0,0,0,0,100,100,0,0,1,0,2,7,10,10,10,1
Style: JP2,Roboto,15,&H00FFFFFF,&H00A0A0A0,&H00FFFFFF,&H006A4D74,0,0,0,0,100,100,0,0,1,0,2,7,10,10,10,1
Style: JP3,Roboto,15,&H00FFFFFF,&H00A8A8A8,&H00FFFFFF,&H006F63E8,0,0,0,0,100,100,0,0,1,0,2,7,10,10,10,1
Style: EN1,Roboto,15,&H00FFFFFF,&HFFFFFFFF,&H00FFFFFF,&H00222222,0,0,0,0,100,100,0,0,1,0,2,3,10,10,10,1
Style: EN2,Roboto,15,&H00FFFFFF,&HFFFFFFFF,&H00FFFFFF,&H006A4D74,0,0,0,0,100,100,0,0,1,0,2,3,10,10,10,1
Style: EN3,Roboto,15,&H00FFFFFF,&HFFFFFFFF,&H00FFFFFF,&H006F63E8,0,0,0,0,100,100,0,0,1,0,2,3,10,10,10,1

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text`.trim();

  const formatTime = t => {
    // Nếu t là "0:00:00.18", giữ nguyên
    // Nếu t là seconds: 1.5 → "0:00:01.50"
    if (typeof t === 'number') {
      const h = String(Math.floor(t / 3600)).padStart(1, '0');
      const m = String(Math.floor((t % 3600) / 60)).padStart(2, '0');
      const s = (t % 60).toFixed(2).padStart(5, '0');
      return `${h}:${m}:${s}`;
    }
    return t;
  };

  const body = subs.map(sub => {
    return `Dialogue: 0,${formatTime(sub.startTime)},${formatTime(sub.endTime)},${sub.style || 'EN1'},,0,0,0,,${sub.translatedText}`;
  }).join('\n');

  return `${header}\n${body}`;
}

function run(){
   const res= generateASS(srtArray)
   console.log(res);
}

run()