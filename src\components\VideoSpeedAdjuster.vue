<template>
  <!-- Container chiếm full height, có scroll riêng -->
  <div class="flex-1 flex flex-col bg-gray-900 text-white">
    <!-- Header - c<PERSON> định không scroll -->
    <div class="flex-shrink-0 p-4 border-b border-gray-700">
      <h2 class="text-lg font-medium text-white">Video Speed Adjuster</h2>
    </div>

    <!-- Content area - có thể scroll -->
    <div class="flex-1 min-h-0 overflow-auto p-4">
      <!-- Video upload area -->
      <div v-if="!videoFile" class="max-w-2xl mx-auto">
        <DragDropUpload
          accept="video/*"
          :max-size="10 * 1024 * 1024 * 1024"
          :show-preview="false"
          drag-text="Drag and drop video file here"
          drop-text="Drop video file here"
          click-text="or click to select video"
          @files-selected="handleVideoSelected"
        />
      </div>

      <!-- Video preview and controls -->
      <div v-if="videoFile" class="space-y-4 max-w-4xl mx-auto">
        <!-- Video info -->
        <div class="flex justify-between items-center">
          <div class="text-sm flex items-center px-3 py-2 bg-gray-800 rounded-md flex-1 mr-2 border border-gray-700">
            <div class="font-medium truncate text-white">
              <span class="text-gray-400 mr-1">Video:</span> {{ videoFile.name }}
            </div>
            <div v-if="videoDuration > 0" class="flex items-center gap-1 text-gray-400 ml-2 flex-shrink-0">
              <span class="text-gray-400 mr-1">Duration:</span>
              <span class="text-white">{{ formatTime(videoDuration) }}</span>
            </div>
          </div>
          <a-button @click="resetVideo" size="small">Reset</a-button>
        </div>

      <!-- Video preview -->
      <div class="relative rounded-md overflow-hidden bg-black">
        <video
          ref="videoRef"
          :src="videoUrl"
          controls
          class="w-full max-h-[400px]"
          @loadedmetadata="handleVideoLoaded"
        ></video>
        <div v-if="loading" class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
          <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-white"></div>
          <span class="text-white ml-2">Loading video...</span>
        </div>
      </div>

        <!-- Speed controls -->
        <div class="space-y-4 bg-gray-800 p-4 rounded-lg border border-gray-700">
          <div class="flex items-center justify-between">
            <span class="text-sm font-medium text-white">Speed: {{ speedPercent }}%</span>
          <div class="space-x-2">
            <a-button
              size="small"
              @click="speedPercent = Math.max(50, speedPercent - 10)"
              :disabled="speedPercent <= 50"
            >
              Slower
            </a-button>
            <a-button
              size="small"
              @click="speedPercent = 100"
            >
              Reset (100%)
            </a-button>
            <a-button
              size="small"
              @click="speedPercent = Math.min(200, speedPercent + 10)"
              :disabled="speedPercent >= 200"
            >
              Faster
            </a-button>
          </div>
        </div>

        <a-slider
          v-model:value="speedPercent"
          :min="50"
          :max="200"
          :step="5"
        />

          <!-- Video bitrate control -->
          <div>
            <span class="text-sm font-medium text-white">Video Bitrate: {{ videoBitrateKbps }} kbps</span>
            <a-slider
              v-model:value="videoBitrateKbps"
              :min="1000"
              :max="8000"
              :step="500"
            />
          </div>

          <!-- Audio bitrate control -->
          <div>
            <span class="text-sm font-medium text-white">Audio Bitrate: {{ audioBitrateKbps }} kbps</span>
            <a-slider
              v-model:value="audioBitrateKbps"
              :min="64"
              :max="320"
              :step="32"
            />
          </div>
        </div>

        <!-- Process button -->
        <div class="flex justify-end space-x-2 gap-2">
          <a-button
            v-if="isProcessing && currentProcessId"
            danger
            @click="stopVideoProcessing"
          >
            Stop Processing
          </a-button>
          <a-button
            type="primary"
            @click="processVideo"
            :loading="isProcessing"
            :disabled="!videoFile || isProcessing"
          >
            {{ isProcessing ? 'Processing...' : 'Adjust Video Speed' }}
          </a-button>
        </div>

        <!-- Result -->
        <div v-if="resultPath" class="mt-4 p-4 bg-green-900/50 border border-green-700 rounded-md">
          <div class="flex justify-between items-center">
            <div>
              <p class="text-green-300 font-medium">Processing complete!</p>
              <p class="text-sm text-gray-400">Output saved to: {{ resultPath }}</p>
            </div>
            <a-button type="primary" @click="openOutputFile">Open Video</a-button>
          </div>
        </div>

        <!-- Error -->
        <div v-if="error" class="mt-4 p-3 bg-red-900/50 border border-red-700 rounded-md">
          <p class="text-red-300">Error: {{ error }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { message } from 'ant-design-vue';
import DragDropUpload from './DragDropUpload.vue';

// Reactive state
const videoFile = ref(null);
const videoUrl = ref(null);
const videoDuration = ref(0);
const loading = ref(false);
const videoRef = ref(null);
const speedPercent = ref(100);
const videoBitrateKbps = ref(2500);
const audioBitrateKbps = ref(128);
const isProcessing = ref(false);
const resultPath = ref(null);
const error = ref(null);
const currentProcessId = ref(null);

// Handle video selection
const handleVideoSelected = (files) => {
  if (files && files.length > 0) {
    handleVideoFile(files[0]);
  }
};

// Process video file
const handleVideoFile = (file) => {
  // Check file type
  if (!file.type.startsWith('video/')) {
    message.error('Invalid video file type');
    return;
  }

  // Revoke old URL to avoid memory leaks
  // if (videoUrl.value) {
  //   URL.revokeObjectURL(videoUrl.value);
  // }

  loading.value = true;
  videoFile.value = file;
  // const newUrl = URL.createObjectURL(file);
  videoUrl.value =`file://${file.path}`;

  // Reset result and error
  resultPath.value = null;
  error.value = null;
};

// Handle video loaded metadata
const handleVideoLoaded = () => {
  loading.value = false;
  if (videoRef.value) {
    videoDuration.value = videoRef.value.duration;
  }
};

// Format time (seconds to MM:SS format)
const formatTime = (seconds) => {
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

// Reset video
const resetVideo = () => {
  // if (videoUrl.value) {
  //   URL.revokeObjectURL(videoUrl.value);
  // }
  videoFile.value = null;
  videoUrl.value = null;
  videoDuration.value = 0;
  resultPath.value = null;
  error.value = null;

  // If there's an active process, stop it
  if (currentProcessId.value && isProcessing.value) {
    stopVideoProcessing();
  } else {
    currentProcessId.value = null;
  }
};

// Process video with adjustSpeed
const processVideo = async () => {
  if (!videoFile.value) return;

  isProcessing.value = true;
  error.value = null;
  resultPath.value = null;
  currentProcessId.value = null;

  try {
    console.log('Processing video...', videoFile.value);

    // Call the adjustSpeed function
    const result = await window.electronAPI.adjustSpeed({
      input: videoFile.value.path,
      speedPercent: speedPercent.value,
      videoBitrateKbps: videoBitrateKbps.value,
      audioBitrateKbps: audioBitrateKbps.value
    });

    if (result.success) {
      // Store the process ID for stopping later if needed
      currentProcessId.value = result.processId;
      resultPath.value = result.outputPath;

      // Start checking process status
      checkProcessStatus();

      message.success('Video processing started');
    } else {
      error.value = result.error || 'Unknown error occurred';
      message.error('Failed to start video processing');
      isProcessing.value = false;
    }
  } catch (err) {
    error.value = err.message || 'Unknown error occurred';
    message.error('Error processing video: ' + error.value);
    isProcessing.value = false;
  }
};

// Stop video processing
const stopVideoProcessing = async () => {
  if (!currentProcessId.value) return;

  try {
    const result = await window.electronAPI.stopProcess(currentProcessId.value);

    if (result.success) {
      message.success('Video processing stopped');
      isProcessing.value = false;
      currentProcessId.value = null;
    } else {
      message.error('Failed to stop processing: ' + (result.error || 'Unknown error'));
    }
  } catch (err) {
    message.error('Error stopping process: ' + (err.message || 'Unknown error'));
  }
};

// Check process status periodically
const checkProcessStatus = async () => {
  if (!currentProcessId.value || !isProcessing.value) return;

  try {
    const result = await window.electronAPI.getActiveProcesses();

    if (result.success) {
      // Check if our process is still running
      const isRunning = result.processes.some(p => p.processId === currentProcessId.value);

      if (!isRunning) {
        // Process completed
        message.success('Video processing completed');
        isProcessing.value = false;
        currentProcessId.value = null;
      } else {
        // Check again after 2 seconds
        setTimeout(checkProcessStatus, 2000);
      }
    }
  } catch (err) {
    console.error('Error checking process status:', err);
    // Continue checking even if there's an error
    setTimeout(checkProcessStatus, 2000);
  }
};

// Open output file
const openOutputFile = async () => {
  if (resultPath.value) {
    await window.electronAPI.openFile(resultPath.value);
  }
};

// Clean up on component unmount
onBeforeUnmount(() => {
  // if (videoUrl.value) {
  //   URL.revokeObjectURL(videoUrl.value);
  // }
});
</script>

<style scoped>
/* Custom scrollbar styling để match với dark theme */
.overflow-auto::-webkit-scrollbar {
  width: 8px;
}

.overflow-auto::-webkit-scrollbar-track {
  background: #374151;
  border-radius: 4px;
}

.overflow-auto::-webkit-scrollbar-thumb {
  background: #6B7280;
  border-radius: 4px;
}

.overflow-auto::-webkit-scrollbar-thumb:hover {
  background: #9CA3AF;
}
</style>
