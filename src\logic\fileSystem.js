class FileSystem {
    async basename(t) {
        return window.electronAPI.invoke("path:basename", t)
    }
    async dirname(t) {
        return window.electronAPI.invoke("path:dirname", t)
    }
    async appData(...t) {
        const r = await window.electronAPI.invoke("app:userData");
        return this.join(r, ...t)
    }
    async mkdirSync(t) {
        return window.electronAPI.invoke("fs:mkdirSync", t)
    }
    async join(...t) {
        return window.electronAPI.invoke("path:join", ...t)
    }
    async existsSync(t) {
        return window.electronAPI.invoke("fs:existsSync", t)
    }
    async unlinkSync(t) {
        return window.electronAPI.invoke("fs:unlinkSync", t)
    }
    async isFile(t) {
        return window.electronAPI.invoke("fs:isFile", t)
    }
    async rmdirSync(t) {
        return window.electronAPI.invoke("fs:rmdirSync", t)
    }
    async readFileSync(t) {
        return window.electronAPI.invoke("fs:readFileSync", t)
    }
    async readFileSyncUtf8(t) {
        return window.electronAPI.invoke("fs:readFileSync", t, "utf-8")
    }
    async writeFileSync(t, r) {
        return window.electronAPI.invoke("fs:writeFileSync", t, r)
    }
    async copyFileSync(t, r) {
        return window.electronAPI.invoke("fs:copyFileSync", t, r)
    }
    async unlinkSyncs(t) {
        const r = t.map(async n => {
            if (await this.existsSync(n))
                return this.unlinkSync(n)
        }
        );
        await Promise.all(r)
    }
    async md5File(t) {
        return window.electronAPI.invoke("crypto:hashMd5File", t)
    }
}
export const fileSystem = new FileSystem();