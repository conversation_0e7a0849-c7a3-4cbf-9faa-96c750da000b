class HistoryDB {
    constructor() {
        this.dbName = 'ttsHistoryDB';
        this.dbVersion = 1;
        this.storeName = 'ttsHistory';
        this.db = null;
        this.init();
    }

    async init() {
        try {
            return new Promise((resolve, reject) => {
                const request = indexedDB.open(this.dbName, this.dbVersion);

                request.onerror = (event) => {
                    console.error('IndexedDB error:', event.target.error);
                    reject(event.target.error);
                };

                request.onsuccess = (event) => {
                    this.db = event.target.result;
                    resolve(this.db);
                };

                request.onupgradeneeded = (event) => {
                    const db = event.target.result;
                    if (!db.objectStoreNames.contains(this.storeName)) {
                        const store = db.createObjectStore(this.storeName, { keyPath: 'id' });
                        store.createIndex('timestamp', 'timestamp', { unique: false });
                    }
                };
            });
        } catch (error) {
            console.error('Error initializing database:', error);
        }
    }

    async saveToHistory(inputVoice, audioBlob) {
        try {
            await this.init();
            
            const historyEntry = {
                id: Date.now(),
                timestamp: new Date().toISOString(),
                text: inputVoice.inputText,
                voiceId: inputVoice.voiceId,
                server: inputVoice.server,
                settings: {
                    speed: inputVoice.speedVal,
                    pitch: inputVoice.pitchVal,
                    volume: inputVoice.volumeVal
                },
                audioBlob: audioBlob
            };

            return new Promise((resolve, reject) => {
                const transaction = this.db.transaction([this.storeName], 'readwrite');
                const store = transaction.objectStore(this.storeName);

                // First, get the count of entries
                const countRequest = store.count();
                
                countRequest.onsuccess = () => {
                    // If we have more than 30 entries, remove the oldest ones
                    if (countRequest.result > 30) {
                        const getOldestRequest = store.index('timestamp').openCursor();
                        let deletedCount = 0;
                        const toDelete = countRequest.result - 30;

                        getOldestRequest.onsuccess = (event) => {
                            const cursor = event.target.result;
                            if (cursor && deletedCount < toDelete) {
                                cursor.delete();
                                deletedCount++;
                                cursor.continue();
                            }
                        };
                    }

                    // Add the new entry
                    const addRequest = store.add(historyEntry);
                    
                    addRequest.onsuccess = () => resolve(historyEntry.id);
                    addRequest.onerror = () => reject(addRequest.error);
                };

                transaction.onerror = (event) => reject(event.target.error);
            });
        } catch (error) {
            console.error('Error saving to history:', error);
            return null;
        }
    }

    async getHistory() {
        try {
            await this.init();
            
            return new Promise((resolve, reject) => {
                const transaction = this.db.transaction([this.storeName], 'readonly');
                const store = transaction.objectStore(this.storeName);
                const request = store.index('timestamp').openCursor(null, 'prev');
                const results = [];

                request.onsuccess = (event) => {
                    const cursor = event.target.result;
                    if (cursor) {
                        results.push(cursor.value);
                        cursor.continue();
                    } else {
                        resolve(results);
                    }
                };

                request.onerror = () => reject(request.error);
            });
        } catch (error) {
            console.error('Error getting history:', error);
            return [];
        }
    }

    async deleteHistoryItem(id) {
        try {
            await this.init();
            
            return new Promise((resolve, reject) => {
                const transaction = this.db.transaction([this.storeName], 'readwrite');
                const store = transaction.objectStore(this.storeName);
                const request = store.delete(id);

                request.onsuccess = () => resolve(true);
                request.onerror = () => reject(request.error);
            });
        } catch (error) {
            console.error('Error deleting history item:', error);
            return false;
        }
    }
}
