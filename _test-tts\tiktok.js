// tiktokTTS.js
const axios = require('axios');
const fs = require('fs');
const path = require('path');
const { randomInt } = require('crypto');

const settings = {
    config: {
        settings: {
            tts: {
                tiktok_sessionid: 'sid_tt_6970100111060011111',
                tiktok_voice: 'BV074_streaming'
            }
        }
    }
}
const ENDPOINT_DATA = [
  { url: 'https://tiktok-tts.weilnet.workers.dev/api/generation', response: 'data' },
  { url: 'https://countik.com/api/text/speech', response: 'v_data' },
  { url: 'https://gesserit.co/api/tiktok-tts', response: 'base64' },
];

const VOICES = [
  'en_us_ghostface', 'en_us_chewbacca', 'en_us_c3po', 'en_us_stitch', 'en_us_stormtrooper', 'en_us_rocket',
  'en_au_001', 'en_au_002', 'en_uk_001', 'en_uk_003', 'en_us_001', 'en_us_002',
  'en_us_006', 'en_us_007', 'en_us_009', 'en_us_010', 'fr_001', 'fr_002',
  'de_001', 'de_002', 'es_002', 'es_mx_002', 'br_001', 'br_003', 'br_004', 'br_005',
  'id_001', 'jp_001', 'jp_003', 'jp_005', 'jp_006', 'kr_002', 'kr_003', 'kr_004',
  'en_female_f08_salut_damour', 'en_male_m03_lobby', 'en_female_f08_warmy_breeze',
  'en_male_m03_sunshine_soon', 'en_male_narration', 'en_male_funny', 'en_female_emotional'
];

class TikTokTTS {
  constructor() {
    this.maxChars = 200;
    this.uriBase = 'https://api16-normal-c-useast1a.tiktokv.com/media/api/text/speech/invoke/';
    this.headers = {
      'User-Agent': 'com.zhiliaoapp.musically/2022600030 (Linux; U; Android 7.1.2; es_ES; SM-G988N; Build/NRD90M;tt-ok/*********)',
      'Cookie': `sessionid=${settings.config.settings.tts.tiktok_sessionid}`
    };
    this.session = axios.create({ headers: this.headers });
  }

  async run(text, filepath, randomVoice = false, playSound = false) {
    const voice = randomVoice
      ? this.randomVoice()
      : settings.config.settings.tts.tiktok_voice;

    const chunks = this._splitText(text);

    for (const entry of ENDPOINT_DATA) {
      let endpointValid = true;
      const audioData = new Array(chunks.length).fill('');
      console.log('entry: ', entry);
      await Promise.all(chunks.map(async (chunk, index) => {
        if (!endpointValid) return;

        try {
          const res = await axios.post(entry.url, {
            text: chunk,
            voice: voice
          });

          if (res.status === 200 && res.data[entry.response]) {
            audioData[index] = res.data[entry.response];
          } else {
            endpointValid = false;
          }
        } catch (error) {
          console.error('Error:', error.message);
          endpointValid = false;
        }
      }));

      if (!endpointValid) continue;

      const base64Combined = audioData.join('');
      const buffer = Buffer.from(base64Combined, 'base64');
      fs.writeFileSync(filepath, buffer);
      console.log(`File '${filepath}' has been generated successfully.`);
      if (playSound) {
        const player = require('play-sound')();
        player.play(filepath);
      }

      break;
    }
  }

  _splitText(text) {
    const separatedChunks = [...text.matchAll(/.*?[.,!?:;-]|.+/g)].map(m => m[0]);
    const finalChunks = [];

    for (let chunk of separatedChunks) {
      if (chunk.length > 300) {
        finalChunks.push(...[...chunk.matchAll(/.*?[ ]|.+/g)].map(m => m[0]));
      } else {
        finalChunks.push(chunk);
      }
    }

    const mergedChunks = [];
    let merged = '';

    for (const chunk of finalChunks) {
      if ((merged + chunk).length <= 300) {
        merged += chunk;
      } else {
        if (merged) mergedChunks.push(merged);
        merged = chunk;
      }
    }
    if (merged) mergedChunks.push(merged);
    return mergedChunks;
  }

  randomVoice() {
    return VOICES[randomInt(VOICES.length)];
  }
}

module.exports = TikTokTTS;


const tts = new TikTokTTS();
tts.run('Tôi chỉ là một con yêu rắn nhỏ không có linh chất, Nhưng lại bắt chước loài người dâng lễ vật lên Miếu Sơn Thần, Món đồ cúng tôi bày biện.', './_test-tts/cache/tts/output1.mp3', false, true);