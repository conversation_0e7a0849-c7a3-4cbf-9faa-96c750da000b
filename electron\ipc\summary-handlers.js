// IPC handlers for Summary page functionality
const { ipcMain } = require('electron');
const ChapterContentService = require('../services/chapter-content-service');

let chapterService = null;

// Initialize services using global S.db
async function initializeSummaryServices() {
    try {
        if (!global.S || !global.S.db) {
            throw new Error('Global database S.db not available');
        }

        if (!chapterService) {
            chapterService = new ChapterContentService(global.S.db);
            console.log('🔧 Chapter content service initialized with S.db');
        }

        return true;
    } catch (error) {
        console.error('❌ Error initializing summary services:', error);
        return false;
    }
}

// Register IPC handlers
function registerSummaryHandlers() {
    // Get all books
    ipcMain.handle('db-get-books', async () => {
        try {
            await initializeSummaryServices();
            const books = await global.S.db.books.getAllBooks(100);
            return { success: true, data: books };
        } catch (error) {
            console.error('Error getting books:', error);
            return { success: false, error: error.message };
        }
    });

    // Get summary statistics for a book
    ipcMain.handle('db-get-summary-stats', async (event, bookId) => {
        try {
            await initializeSummaryServices();
            const stats = await global.S.db.chapterSummaries.getSummaryStats(bookId);
            return { success: true, data: stats };
        } catch (error) {
            console.error('Error getting summary stats:', error);
            return { success: false, error: error.message };
        }
    });

    // Process chapters range (extract content and/or generate summaries)
    ipcMain.handle('process-chapters-range', async (event, params) => {
        try {
            await initializeSummaryServices();
            
            const { bookId, startIndex, endIndex, options } = params;
            
            console.log(`🚀 Starting chapter processing: Book ${bookId}, Chapters ${startIndex}-${endIndex}`);
            
            // Set up progress callback to send updates to renderer
            const onProgress = (progress) => {
                event.sender.send('processing-progress', progress);
            };
            
            const result = await chapterService.processChaptersRange(
                bookId, 
                startIndex, 
                endIndex, 
                {
                    ...options,
                    onProgress
                }
            );
            
            return { success: true, data: result };
            
        } catch (error) {
            console.error('Error processing chapters range:', error);
            return { success: false, error: error.message };
        }
    });

    // Stop current processing
    ipcMain.handle('stop-processing', async () => {
        try {
            if (chapterService) {
                await chapterService.stopProcessing();
                return { success: true };
            }
            return { success: false, error: 'No processing service available' };
        } catch (error) {
            console.error('Error stopping processing:', error);
            return { success: false, error: error.message };
        }
    });

    // Get processing progress
    ipcMain.handle('get-processing-progress', async () => {
        try {
            if (chapterService) {
                const progress = chapterService.getProgress();
                return { success: true, data: progress };
            }
            return { success: false, error: 'No processing service available' };
        } catch (error) {
            console.error('Error getting processing progress:', error);
            return { success: false, error: error.message };
        }
    });

    // Get chapters without content for a book
    ipcMain.handle('db-get-chapters-without-content', async (event, params) => {
        try {
            await initializeSummaryServices();

            const { bookId, startIndex, endIndex } = params;
            const chapters = await global.S.db.chapterSummaries.getChaptersWithoutSummaries(bookId, startIndex, endIndex);

            return { success: true, data: chapters };
        } catch (error) {
            console.error('Error getting chapters without content:', error);
            return { success: false, error: error.message };
        }
    });

    // Get summaries by book
    ipcMain.handle('db-get-summaries-by-book', async (event, params) => {
        try {
            await initializeSummaryServices();

            const { bookId, limit = 100, offset = 0 } = params;
            const summaries = await global.S.db.chapterSummaries.getSummariesByBook(bookId, limit, offset);

            return { success: true, data: summaries };
        } catch (error) {
            console.error('Error getting summaries by book:', error);
            return { success: false, error: error.message };
        }
    });

    // Export summaries
    ipcMain.handle('export-summaries', async (event, params) => {
        try {
            await initializeSummaryServices();

            const { bookId, format = 'json' } = params;
            const summaries = await chapterService.exportSummaries(bookId, format);

            return { success: true, data: summaries };
        } catch (error) {
            console.error('Error exporting summaries:', error);
            return { success: false, error: error.message };
        }
    });

    // Get book details with chapters
    ipcMain.handle('db-get-book-details', async (event, bookId) => {
        try {
            await initializeSummaryServices();

            const book = await global.S.db.books.getBookById(bookId);
            if (!book) {
                return { success: false, error: 'Book not found' };
            }

            const chapters = await global.S.db.chapters.getChaptersByBookId(bookId);
            const summaryStats = await global.S.db.chapterSummaries.getSummaryStats(bookId);

            return {
                success: true,
                data: {
                    book,
                    chapters,
                    summaryStats
                }
            };
        } catch (error) {
            console.error('Error getting book details:', error);
            return { success: false, error: error.message };
        }
    });

    // Delete book and all related data
    ipcMain.handle('db-delete-book', async (event, bookId) => {
        try {
            await initializeSummaryServices();

            // Delete summaries first
            await global.S.db.chapterSummaries.deleteSummariesByBook(bookId);

            // Delete book (this will cascade delete chapters)
            await global.S.db.books.deleteBook(bookId);

            return { success: true };
        } catch (error) {
            console.error('Error deleting book:', error);
            return { success: false, error: error.message };
        }
    });

    // Test chapter content extraction
    ipcMain.handle('test-chapter-extraction', async (event, chapterUrl) => {
        try {
            await initializeSummaryServices();
            
            const content = await chapterService.extractChapterContent(chapterUrl);
            
            return { success: true, data: { content, length: content.length } };
        } catch (error) {
            console.error('Error testing chapter extraction:', error);
            return { success: false, error: error.message };
        }
    });

    // Test summary generation
    ipcMain.handle('test-summary-generation', async (event, text) => {
        try {
            await initializeSummaryServices();

            const summary = await chapterService.generateSummary(text);

            return { success: true, data: { summary, length: summary.length } };
        } catch (error) {
            console.error('Error testing summary generation:', error);
            return { success: false, error: error.message };
        }
    });

    // Extract chapters from URL
    ipcMain.handle('extract-from-url', async (event, params) => {
        try {
            const { url } = params;
            console.log(`🔗 Starting extraction from URL: ${url}`);

            // Use the existing testMultiPageExtraction function
            const { testMultiPageExtraction } = require('../playwright/puppeteerService');

            // Extract all pages (set to a high number to get all)
            const result = await testMultiPageExtraction(
                event,
                url,
                999, // Extract many pages
                true // Save to database
            );

            console.log(`✅ Extraction completed: ${result.totalChapters} chapters`);

            return { success: true, data: result };
        } catch (error) {
            console.error('Error extracting from URL:', error);
            return { success: false, error: error.message };
        }
    });



    console.log('📋 Summary IPC handlers registered');
}

// Clean up resources
function cleanupSummaryServices() {
    chapterService = null;
    console.log('🧹 Summary services cleaned up');
}

module.exports = {
    registerSummaryHandlers,
    cleanupSummaryServices,
    initializeSummaryServices
};
